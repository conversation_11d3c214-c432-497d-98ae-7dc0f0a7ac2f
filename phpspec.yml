suites:
  main:
    src_path: project/
  router:
    src_path: vendor/made_simple/msg-framework/router_module/src
    namespace: RouterModule
    spec_path: vendor/made_simple/msg-framework/router_module
  feature:
    psr4_prefix: FeatureModule
    namespace: FeatureModule
    spec_path: vendor/made_simple/msg-framework/feature_module
  utils:
    src_path: vendor/made_simple/msg-framework/utils/src
    namespace: Utils
    spec_path: vendor/made_simple/msg-framework/utils
  user_module:
    src_path: vendor/made_simple/msg-framework/user_module/src
    namespace: UserModule
    spec_path: vendor/made_simple/msg-framework/user_module
  payment_module:
    src_path: vendor/made_simple/msg-framework/payment_module/src
    namespace: PaymentModule
    spec_path: vendor/made_simple/msg-framework/payment_module
  sagepay_rest_api:
    psr4_prefix: SagePayRestApi
    src_path: vendor/made_simple/msg-framework/sagepay_rest_api/src
    namespace: SagePayRestApi
    spec_path: vendor/made_simple/msg-framework/sagepay_rest_api
  template_module:
    src_path: vendor/made_simple/msg-framework/utils/src
    namespace: TemplateModule
    spec_path: vendor/made_simple/msg-framework/utils
  workflow_engine:
    namespace: WorkflowEngineModule
    spec_path: vendor/made_simple/msg-framework/workflow_engine_module
  basket_module:
    src_path: vendor/made_simple/msg-framework/utils/src
    namespace: BasketModule
    spec_path: vendor/made_simple/msg-framework/utils
  notification_module:
    src_path: vendor/made_simple/msg-framework/utils/src
    namespace: NotificationModule
    spec_path: vendor/made_simple/msg-framework/utils
  company_module:
    src_path: vendor/made_simple/msg-framework/utils/src
    namespace: CompanyModule
    spec_path: vendor/made_simple/msg-framework/utils
  content_module:
    src_path: vendor/made_simple/msg-framework/content_module/src
    namespace: ContentModule
    spec_path: vendor/made_simple/msg-framework/content_module
  companies-house:
    src_path: vendor/made_simple/msg-framework/companies-house/src
    namespace: CompaniesHouse
    spec_path: vendor/made_simple/msg-framework/companies-house
  id_module:
    psr4_prefix: IdModule
    namespace: IdModule
    spec_path: vendor/made_simple/msg-framework/id_module
  storage_module:
      psr4_prefix: StorageModule
      namespace: StorageModule
      spec_path: vendor/made_simple/msg-framework/storage_module

extensions:
  TestModule\Extensions\PhpSpecLoader: ~

