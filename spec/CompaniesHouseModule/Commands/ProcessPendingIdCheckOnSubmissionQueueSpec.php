<?php

namespace spec\CompaniesHouseModule\Commands;

use CompaniesHouseModule\Commands\ProcessPendingIdCheckOnSubmissionQueue;
use CompaniesHouseModule\Entities\IdSubmissionQueue;
use CompaniesHouseModule\Repositories\IdSubmissionQueueRepository;
use CompaniesHouseModule\Services\SubmissionHandler;
use Entities\Company;
use Entities\Customer;
use PhpSpec\ObjectBehavior;
use Psr\Log\LoggerInterface;

class ProcessPendingIdCheckOnSubmissionQueueSpec extends ObjectBehavior
{
    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @var IdSubmissionQueueRepository
     */
    private $idSubmissionQueueRepository;

    /**
     * @var SubmissionHandler
     */
    private $submissionHandler;

    function let(
        LoggerInterface $logger,
        IdSubmissionQueueRepository $idSubmissionQueueRepository,
        SubmissionHandler $submissionHandler,
    ) {
        $this->logger = $logger;
        $this->idSubmissionQueueRepository = $idSubmissionQueueRepository;
        $this->submissionHandler = $submissionHandler;

        $this->beConstructedWith($logger, $idSubmissionQueueRepository, $submissionHandler);
    }

    function it_is_initializable()
    {
        $this->shouldHaveType(ProcessPendingIdCheckOnSubmissionQueue::class);
    }

    function it_should_inform_that_there_are_no_companies_in_the_queue()
    {
        $this->idSubmissionQueueRepository->getAll()->willReturn([]);
        $this->logger->debug('Processing Form Submissions pending on ID check in LIVE mode.')->shouldBeCalled();
        $this->logger->info('No companies in the queue to process.')->shouldBeCalled();

        $this->processReadyFormSubmissions(false);
    }

    function it_should_process_the_queue(
        IdSubmissionQueue $queue1,
        IdSubmissionQueue $queue2,
        IdSubmissionQueue $queue3,
        Company $company1,
        Company $company2,
        Company $company3,
        Customer $customer1,
        Customer $customer2,
        Customer $customer3
    ) {
        # -- STARTING LOG
        $this->logger->debug('Processing Form Submissions pending on ID check in LIVE mode.')->shouldBeCalled();

        $this->idSubmissionQueueRepository->getAll()->willReturn([$queue1, $queue2, $queue3]);

        # -- QUEUE 01 - SCENARIO IS SUCCESS -----------------------------------------------------------------------------------------------------------
        $queue1->getCompany()->willReturn($company1);
        $queue1->getCustomer()->willReturn($customer1);
        $queue1->getFormSubmissionId()->willReturn(1);
        $company1->getId()->willReturn(1);

        $this->logger->debug('Processing Company 1.')->shouldBeCalled();
        $this->logger->info('ID check for company 1 is valid - submitting the company to CH.')->shouldBeCalled();
        $this->submissionHandler->canSubmitCompany($company1)->willReturn(true);
        $this->logger->info('Company 1 submitted to CH.')->shouldBeCalled();

        $this->submissionHandler->submit($queue1)->shouldBeCalled();

        # -- QUEUE 02 - SCENARIO IS PENDING ID --------------------------------------------------------------------------------------------------------
        $queue2->getCompany()->willReturn($company2);
        $queue2->getCustomer()->willReturn($customer2);
        $queue2->getFormSubmissionId()->willReturn(2);
        $company2->getId()->willReturn(2);

        $this->logger->debug('Processing Company 2.')->shouldBeCalled();
        $this->logger->info('ID for Company 2 not yet validated.')->shouldBeCalled();
        $this->submissionHandler->canSubmitCompany($company2)->willReturn(false);
        $this->submissionHandler->isCompanyIdValid($company2)->willReturn(false);
        $this->submissionHandler->isExtraFeeChargeApplicable($company2)->willReturn(false);


        $this->submissionHandler->submit($queue2)->shouldNotBeCalled();

        $this->submissionHandler->withHoldSubmission($company2, 2);

        # -- QUEUE 03 - SCENARIO IS SUCCESS -----------------------------------------------------------------------------------------------------------
        $queue3->getCompany()->willReturn($company3);
        $queue3->getCustomer()->willReturn($customer3);
        $queue3->getFormSubmissionId()->willReturn(3);
        $company3->getId()->willReturn(3);

        $this->logger->debug('Processing Company 3.')->shouldBeCalled();
        $this->logger->info('ID check for company 3 is valid - submitting the company to CH.')->shouldBeCalled();
        $this->submissionHandler->canSubmitCompany($company3)->willReturn(true);
        $this->logger->info('Company 3 submitted to CH.')->shouldBeCalled();

        $this->submissionHandler->submit($queue3)->shouldBeCalled();

//        # -- SUMMARY ----------------------------------------------------------------------------------------------------------------------------------
        $this->logger->info(sprintf("Summary: %s submissions sent, %d submissions pending, %d errors", 2, 1, 0))->shouldBeCalled();

        $this->processReadyFormSubmissions(false);
    }

}
