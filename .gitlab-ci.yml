include: "/vendor/made_simple/msg_docker/project-gitlab-ci.yml"

default:
  before_script:
    - git config --global --add safe.directory $CI_PROJECT_DIR

variables:
  SHARED_TAG: "v8.1.4"
  NODE_CACHE_KEY: "js-babel-update-09-05-2025"
  DEVELOPER_IMAGE: "developer-82"

deploy_to_prod:
  variables:
    DEPLOY_PROJECT_FOLDER: /var/www/html/cms/
    SSH_CONNECT: msg-reader@***********

deploy_review:
  variables:
    PROJECT: cms
    HOSTS_WITH_SSL: "${CI_ENVIRONMENT_SLUG}.msg.cool"
    DEV_DOMAIN: "${CI_ENVIRONMENT_SLUG}.msg.cool"
    REVIEW_SUBDOMAIN: "${CI_ENVIRONMENT_SLUG}.msg.cool"

deploy_master_review:
  variables:
    PROJECT: cms
    HOSTS_WITH_SSL: "review-cms-master.msg.cool"
    REVIEW_SUBDOMAIN: "review-cms-master.msg.cool"
    DEV_DOMAIN: "review-cms-master.msg.cool"

stop_review:
  variables:
    PROJECT: cms

stop_master_review:
  variables:
    PROJECT: cms
