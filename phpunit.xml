<?xml version="1.0" encoding="UTF-8"?>

<phpunit backupGlobals="false"
         backupStaticAttributes="false"
         colors="true"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         processIsolation="false"
         stopOnFailure="false"
         bootstrap="tests/index.php"
>
    <!--<listeners>-->
        <!--<listener class="JohnKary\PHPUnit\Listener\SpeedTrapListener" />-->
    <!--</listeners>-->
    <testsuites>
        <testsuite name="Cms Test Suite">
            <directory>tests</directory>

            <directory>project/OfferModule/Tests</directory>
            <directory>vendor/made_simple/msg-framework/notifier/tests</directory>
            <directory>vendor/made_simple/msg-framework/cron/tests</directory>
            <!--<directory>../vendor/made_simple/msg-framework/sagepaytoken/tests</directory>-->
            <directory>vendor/made_simple/msg-framework/ui_helper/tests</directory>
            <directory>vendor/made_simple/msg-framework/utils/tests</directory>
            <directory>vendor/made_simple/msg-framework/error_module/tests</directory>
            <directory>vendor/made_simple/msg-framework/companies-house/tests</directory>
            <directory>vendor/made_simple/msg-framework/feature_module/tests</directory>
            <directory>vendor/made_simple/msg-framework/bootstrap_module/tests</directory>
            <directory>vendor/made_simple/msg-framework/test_module/tests</directory>
            <directory>vendor/made_simple/msg-framework/form_module/tests</directory>
            <directory>vendor/made_simple/msg-framework/console/tests</directory>
            <directory>vendor/made_simple/msg-framework/workflow_engine_module/tests</directory>
            <directory>vendor/made_simple/msg-framework/id_module/tests</directory>
            <directory>vendor/made_simple/msg-framework/payment_module/tests</directory>
            <directory>vendor/made_simple/msg-framework/storage_module/tests</directory>
            <directory>vendor/made_simple/msg-framework/functional_module/tests</directory>
            <directory>vendor/made_simple/msg-framework/router_module/tests</directory>
        </testsuite>
    </testsuites>
</phpunit>
