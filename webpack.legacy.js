const path = require("path");

const MergeIntoSingle = require("webpack-merge-and-include-globally");
const uglifyJS = require("uglify-js");
const CleanCSS = require('clean-css');

module.exports = {
    mode: 'production',
    entry: {
        legacy: "./client/legacy.ts",
    },
    output: {
        path: path.resolve(__dirname, "www/dist"),
        filename: '[name]',
    },
    plugins: [
        new MergeIntoSingle({
            files: {
                "legacy-common.css": [
                    './www/components/jquery-ui/themes/base/jquery-ui.css',
                    './www/front/fontawesome-pro-5.9.0-web/css/all.css',
                    './www/front/fontawesome-pro-5.9.0-web/css/v4-shims.css',
                    './www/front/css/newhome2014.css',
                    './www/front/css/project.css',
                    './www/front/css/htmltable.css',
                    './www/front/css/accordion.css',
                    './www/front/css/stylesheet.css',
                    './www/front/css/basketNew.css',
                    './www/front/css/forms.css',
                    './www/front/css/wizard.css',
                    './www/components/ui_server/css/main.css',
                    './www/components/ladda/dist/ladda-themeless.min.css',
                    './www/components/ui_server/css/button.css',
                    './www/components/datatables/media/css/jquery.dataTables.css',
                    './www/components/bootstrap/dist/css/bootstrap.min.css',
                    './www/front/css/bootstrap_fix.css',
                    './www/components/pca_predict/address-3.50.css',
                    './www/components/select2/dist/css/select2.css',
                    './www/front/css/registered-office-formation.css',
                    './www/front/css/service-widget.css'
                ],
                "legacy-responsive.css": [
                    './www/components/jquery-ui/themes/base/jquery-ui.css',
                    './www/components/bootstrap/dist/css/bootstrap.min.css',
                    './www/front/fontawesome-pro-5.9.0-web/css/all.css',
                    './www/front/fontawesome-pro-5.9.0-web/css/v4-shims.css',
                    './www/components/ladda/dist/ladda-themeless.min.css',
                    './www/components/datatables/media/css/dataTables.bootstrap.css',
                    './www/front/css/progress-wizard.min.css',
                    './www/components/ui_server/css/button.css',
                    './www/components/ui_server/css/MegaNavbar.min.css',
                    './www/components/ui_server/css/main_bt.css',
                    './www/front/css/font_bt.css',
                    './www/front/css/app_bt.css',
                    './www/front/css/custom.css',
                    './www/front/css/homepage.css',
                    './www/components/pca_predict/address-3.50.css',
                    './www/components/select2/dist/css/select2.css',
                    './www/front/css/padcard.css',
                    './www/front/css/ltdCalculator.css',
                    './www/front/css/taxAssist.css',
                    './www/front/css/pearl.css',
                    './www/front/css/anna.css'
                ],
                "legacy-responsive.js": [
                    './www/components/jquery/dist/jquery.js',
                    './www/components/jquery-ui/ui/jquery-ui.js',
                    './www/components/jquery-migrate/jquery-migrate.min.js',
                    './www/components/sightglass/index.js',
                    './www/components/rivets/dist/rivets.js',
                    './www/components/jquery-validation/dist/jquery.validate.js',
                    './www/components/jquery-validation/dist/additional-methods.js',
                    './www/components/urijs/src/URI.js',
                    './www/components/moment/moment.js',
                    './www/components/datatables/media/js/jquery.dataTables.js',
                    './www/components/datatables/media/js/dataTables.bootstrap.js',
                    './www/components/ui_server/js/validation.default.js',
                    './www/components/ui_server/js/customer-availability.js',
                    './www/components/ui_server/js/payment.js',
                    './www/front/js/main.js',
                    './www/front/js/jquery.tools.min.js',
                    './www/components/ladda/js/spin.js',
                    './www/components/ladda/js/ladda.js',
                    './www/components/ladda/js/ladda.jquery.js',
                    './www/components/ui_server/js/ui.js',
                    './www/components/ui_server/js/lib.js',
                    './www/components/bootstrap/dist/js/bootstrap.min.js',
                    './www/front/js/validations.js',
                    './www/front/js/nameSearch/validation.js',
                    './www/front/js/questionnaireForm.js',
                    './www/components/mailcheck/src/mailcheck.min.js',
                    './www/components/ui_server/js/mailcheck.default.js',
                    './www/components/ui_server/js/default.js',
                    './www/components/hideshowpassword/hideShowPassword.js',
                    './www/components/pca_predict/address-3.50.js',
                    './www/components/select2/dist/js/select2.js',
                ],
                "legacy-common.js": [
                    './www/components/jquery/dist/jquery.js',
                    './www/components/jquery-ui/ui/jquery-ui.js',
                    './www/components/jquery-migrate/jquery-migrate.min.js',
                    './www/components/sightglass/index.js',
                    './www/components/rivets/dist/rivets.js',
                    './www/components/ui_server/js/lib.js',
                    './www/front/js/jquery.tools.min.js',
                    './www/components/mailcheck/src/mailcheck.min.js',
                    './www/components/ui_server/js/mailcheck.default.js',
                    './www/front/js/payment.js',
                    './www/front/js/plugins/fancycombo.js',
                    './www/front/js/libs.js',
                    './www/front/js/plugins/FormCache.js',
                    './www/components/ladda/js/spin.js',
                    './www/components/ladda/js/ladda.js',
                    './www/components/ladda/js/ladda.jquery.js',
                    './www/components/ui_server/js/ui.js',
                    './www/components/datatables/media/js/jquery.dataTables.js',
                    './www/components/jquery-validation/dist/jquery.validate.js',
                    './www/components/jquery-validation/dist/additional-methods.js',
                    './www/front/js/validations.js',
                    './www/components/moment/moment.js',
                    './www/front/js/customerCompanies.js',
                    './www/components/ui_server/js/default.js',
                    './www/front/js/idcheck.js',
                    './www/components/ui_server/js/plugins/ajaxform.js',
                    './www/components/bootstrap/dist/js/bootstrap.min.js',
                    './www/front/js/bootstrap_fix.js',
                    './www/front/js/Validator.js',
                    './www/front/js/bankOptions.js',
                    './www/front/js/nameSearch/validation.js',
                    './www/front/js/RenewalForm.js',
                    './www/components/pca_predict/address-3.50.js',
                    './www/components/select2/dist/js/select2.js',
                ],
                "legacy-formation.css": [
                    './www/components/datatables/media/css/jquery.dataTables.css',
                    './www/front/css/formation_sic.css',
                    './www/front/css/formation-header-and-navbar.css',
                    './www/front/css/bootstrap-custom-btn-outlined.css',
                    './www/front/css/bootstrap-vertical-aligned-contents.css'
                ],
                "legacy-transition.css": [
                    './www/front/fontawesome-pro-5.9.0-web/css/all.css',
                    './www/front/fontawesome-pro-5.9.0-web/css/v4-shims.css',
                    './www/components/ui_server/css/MegaNavbar.min.css',
                    './www/components/ui_server/css/main_bt.css',
                    './www/front/css/bootstrap_transition.css',
                    './www/front/css/font_bt.css',
                    './www/front/css/custom.css',
                ],
            },
            transform:{
              'legacy-common.js': code => uglifyJS.minify(code).code,
              'legacy-responsive.js': code => uglifyJS.minify(code).code,
              'legacy-common.css': code => new CleanCSS({}).minify(code).styles,
              'legacy-responsive.css': code => new CleanCSS({}).minify(code).styles,
              'legacy-formation.css': code => new CleanCSS({}).minify(code).styles,
              'legacy-transition.css': code => new CleanCSS({}).minify(code).styles,
            },

            hash: false,
        }, (filesMap) => {
            console.log('generated files: ', filesMap); // eslint-disable-line no-console
        }),

    ]
};
