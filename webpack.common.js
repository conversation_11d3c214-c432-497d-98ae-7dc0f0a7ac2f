const path = require("path");
const webpack = require("webpack");
const fs = require('fs-extra');

const VueLoaderPlugin = require('vue-loader/lib/plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const LiveReloadPlugin = require('webpack-livereload-plugin');

const packageJson = require('./package');

module.exports = {
    entry: {
        main: "./client/main.ts",
        xdna: "./client/xdna.ts",
        numbers: "./client/mp-numbers.ts",
        formation: "./client/formation.ts",
        secondary: "./client/secondary.ts",
        tertiary: "./client/js/modules/exports.js",
        modal: "./client/modal.ts",
    },
    resolve: {
        extensions: ['.ts', '.js', '.vue', '.json', '.sass', '.scss', '.css'],
        alias: {
            'vue$': 'vue/dist/vue.esm.js',
            '@': path.resolve(__dirname, 'client'),
        }
    },
    devtool: "source-map",
    output: {
        path: path.resolve(__dirname, "www/dist"),
        filename: "[name].bundle.js",
    },
    module: {
        rules: [
            {
                test: /\.(png|jpg|jpeg|gif|htc)$/i,
                use: [
                    {
                        loader: 'url-loader',
                        options: {
                            limit: 8192,
                            name: "assets/images/[hash].[ext]",
                            publicPath: "/dist"
                        },
                    },
                ],
            },
            {
                test: /\.vue$/,
                loader: 'vue-loader',
                options: {
                    loaders: {
                        'scss': 'vue-style-loader!css-loader!sass-loader',
                        'sass': 'vue-style-loader!css-loader!sass-loader?indentedSyntax',
                    },
                },
            },
            {
                test: /\.tsx?$/,
                exclude: /node_modules/,
                use: {
                    loader: "ts-loader",
                    options: {
                        transpileOnly: true,
                        appendTsSuffixTo: [/\.vue$/],
                        compilerOptions: {
                            "newLine": "lf",
                            "sourceMap": false
                        }
                    }
                }
            },
            {
                test: /\.js$/,
                exclude: /node_modules/,
                use: {
                    loader: "babel-loader",
                    options: packageJson.babel
                }
            },
            {
                test: /\.js$/,
                use: "source-map-loader"
            },
            {
                test: /\.s[ac]ss$/i,
                use: [
                    MiniCssExtractPlugin.loader,
                    'css-loader',
                    {
                        loader: 'postcss-loader', // Run postcss actions
                        options: {
                            plugins: function () { // postcss plugins, can be exported to postcss.config.js
                                return [
                                    require('autoprefixer')
                                ];
                            }
                        }
                    },
                    'sass-loader',
                ],
            },
            {
                test: /\.css$/,
                use: [
                    MiniCssExtractPlugin.loader,
                    'css-loader',
                    {
                        loader: 'postcss-loader', // Run postcss actions
                        options: {
                            plugins: function () { // postcss plugins, can be exported to postcss.config.js
                                return [
                                    require('autoprefixer')
                                ];
                            }
                        }
                    },
                    'sass-loader',
                ],
            },
            {
                test: /\.(woff|woff2|eot|ttf|otf)$/,
                use: [
                    {
                        loader: 'file-loader',
                        options: {
                            name: 'assets/fonts/[name].[ext]',
                            publicPath: "/dist"
                        }
                    }
                ]
            },
            {
                test: /\.svg$/,
                use: [
                    {
                        loader: 'file-loader',
                        options: {
                            name: 'assets/images/[name].[ext]',
                            publicPath: "/dist"
                        }
                    }
                ]
            }
        ],
    },
    externals: packageJson.externals,
    plugins: [
        {
            apply: (compiler) => {
                compiler.hooks.emit.tapAsync('ClearWebTempPlugin', (compilation, callback) => {
                    const tempFolder = path.resolve(__dirname, 'www/webtemp');
                    if (fs.existsSync(tempFolder)) {
                        fs.emptyDirSync(tempFolder);
                        console.log('Cleared webtemp during emit');
                    }
                    callback();
                });
            },
        },
        new VueLoaderPlugin(),
        new MiniCssExtractPlugin({
            filename: 'css/[name].css',
            chunkFilename: 'css/[id].css',
        }),
        new LiveReloadPlugin(),
    ],
    node: {
        global: true
    },
    // stats: {
    //     logging: 'verbose',
    // }
};
