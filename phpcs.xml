<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="PSR2" xsi:noNamespaceSchemaLocation="../../../phpcs.xsd">
    <description>Made Simple Group Coding Standards</description>

    <!--
    MISSING AND MAPPED:
    - MORE THEN ONE EMPTY LINE BETWEEN SOME CODE
        i.e.:
        $var = 1;


        $var = 2;
    - Variables show not be written in uppercase but in camelCase
    - Switch case must have a default value
    - MISSING RETURN TYPE OF FUNCTION
    - MISSING TYPE OF A PARAMETER IN FUNCTION

    -->
    <!-- The PHP constants true, false, and null MUST be in lower case. -->
    <rule ref="Generic.PHP.LowerCaseConstant"/>

    <!-- The length of each line should not exceed 120 characters -->
    <rule ref="Generic.Files.LineLength">
        <properties>
            <property name="lineLimit" value="120"/>
            <property name="absoluteLineLimit" value="0"/>
        </properties>
    </rule>

    <!-- The extends and implements keywords MUST be declared on the same line as the class name. -->
    <!-- The opening brace for the class MUST go on its own line; the closing brace for the class MUST go on the next line after the body. -->
    <!-- Opening braces MUST be on their own line and MUST NOT be preceded or followed by a blank line. -->
    <!-- Closing braces MUST be on their own line and MUST NOT be preceded by a blank line. -->
    <!-- Lists of implements and, in the case of interfaces, extends MAY be split across multiple lines, where each subsequent line is indented once. When doing so, the first item in the list MUST be on the next line, and there MUST be only one interface per line. -->
    <rule ref="PSR2.Classes.ClassDeclaration"/>

    <!-- The body of each structure MUST be enclosed by braces. This standardizes how the structures look, and reduces the likelihood of introducing errors as new lines get added to the body. -->
    <rule ref="Generic.ControlStructures.InlineControlStructure"/>

    <!-- All constantes should be written in UPPER_CASE -->
    <rule ref="Generic.NamingConventions.UpperCaseConstantName"/>

    <!-- Method names MUST NOT be declared with a space after the method name. The opening brace MUST go on its own line, and the closing brace MUST go on the next line following the body. There MUST NOT be a space after the opening parenthesis, and there MUST NOT be a space before the closing parenthesis. -->
    <!-- checked by PSR2.Methods.FunctionClosingBrace -->
    <rule ref="Squiz.Functions.FunctionDeclaration"/>

    <!-- In the argument list, there MUST NOT be a space before each comma, and there MUST be one space after each comma. -->
    <!-- When using the reference operator & before an argument, there MUST NOT be a space after it. -->
    <!-- There MUST NOT be a space between the variadic three dot operator and the argument name. -->
    <!-- When combining both the reference operator and the variadic three dot operator, there MUST NOT be any space between the two of them. -->
    <rule ref="Squiz.Functions.FunctionDeclarationArgumentSpacing">
        <properties>
            <property name="equalsSpacing" value="1"/>
        </properties>
    </rule>
    <rule ref="Squiz.Functions.FunctionDeclarationArgumentSpacing.SpacingAfterHint">
        <severity>0</severity>
    </rule>

    <!-- There MUST NOT be more than one statement per line. -->
    <rule ref="Generic.Formatting.DisallowMultipleStatements"/>

    <!-- When making a method or function call, there MUST NOT be a space between the method or function name and the opening parenthesis, there MUST NOT be a space after the opening parenthesis, and there MUST NOT be a space before the closing parenthesis. In the argument list, there MUST NOT be a space before each comma, and there MUST be one space after each comma.
    Argument lists MAY be split across multiple lines, where each subsequent line is indented once. When doing so, the first item in the list MUST be on the next line, and there MUST be only one argument per line. -->
    <rule ref="Generic.Functions.FunctionCallArgumentSpacing"/>

    <rule ref="PSR2.Methods.FunctionCallSignature.SpaceAfterCloseBracket">
        <severity>0</severity>
    </rule>

    <rule ref="PSR2.Methods.FunctionCallSignature.OpeningIndent">
        <severity>0</severity>
    </rule>

    <rule ref="Generic.WhiteSpace.ScopeIndent">
        <properties>
            <property name="ignoreIndentationTokens" type="array">
                <element value="T_COMMENT"/>
                <element value="T_DOC_COMMENT_OPEN_TAG"/>
            </property>
        </properties>
    </rule>
    <rule ref="Generic.WhiteSpace.DisallowTabIndent"/>

    <!-- Code MUST use an indent of 4 spaces for each indent level, and MUST NOT use tabs for indenting. -->
    <rule ref="Generic.WhiteSpace.ScopeIndent">
        <properties>
            <property name="ignoreIndentationTokens" type="array">
                <element value="T_COMMENT"/>
                <element value="T_DOC_COMMENT_OPEN_TAG"/>
            </property>
        </properties>
    </rule>
    <rule ref="Generic.WhiteSpace.DisallowTabIndent"/>

    <!-- MISSING THE RULE DESCRIPTION -->
    <rule ref="Generic.PHP.DisallowShortOpenTag"/>

    <!-- MISSING THE RULE DESCRIPTION -->
    <rule ref="Squiz.Commenting.DocCommentAlignment"/>

    <!-- All PHP reserved keywords and types [1][2] MUST be in lower case. Any new types and keywords added to future PHP versions MUST be in lower case. -->
    <rule ref="Generic.PHP.LowerCaseKeyword"/>

    <!-- MISSING THE RULE DESCRIPTION -->
    <rule ref="Generic.PHP.LowerCaseType"/>

    <!-- MISSING THE RULE DESCRIPTION -->
    <rule ref="Squiz.Functions.LowercaseFunctionKeywords"/>

    <!-- There MUST NOT be trailing whitespace at the end of lines.
    Blank lines MAY be added to improve readability and to indicate related blocks of code except where explicitly forbidden. -->
    <rule ref="Squiz.WhiteSpace.SuperfluousWhitespace">
        <properties>
            <!--            <property name="ignoreBlankLines" value="true"/>-->
        </properties>
    </rule>
    <rule ref="Squiz.WhiteSpace.SuperfluousWhitespace.StartFile">
        <severity>0</severity>
    </rule>
    <rule ref="Squiz.WhiteSpace.SuperfluousWhitespace.EndFile">
        <severity>0</severity>
    </rule>
    <rule ref="Squiz.WhiteSpace.SuperfluousWhitespace.EmptyLines">
        <severity>0</severity>
    </rule>
    <!-- ................................................................... -->

    <!-- Visibility MUST be declared on all properties. -->
    <!-- The var keyword MUST NOT be used to declare a property. -->
    <!-- There MUST NOT be more than one property declared per statement. -->
    <!-- Property names MUST NOT be prefixed with a single underscore to indicate protected or private visibility.
    That is, an underscore prefix explicitly has no meaning. -->
    <!-- There MUST be a space between type declaration and property name. -->
    <rule ref="PSR2.Classes.PropertyDeclaration"/>

    <rule ref="PSR2.Classes.PropertyDeclaration.SpacingAfterType">
        <severity>0</severity>
    </rule>
    <!-- ................................................................... -->

    <!-- Method names MUST NOT be prefixed with a single underscore to indicate protected or private visibility. That is, an underscore prefix explicitly has no meaning. -->
    <rule ref="PSR2.Methods.MethodDeclaration"/>
    <rule ref="PSR2.Methods.MethodDeclaration.Underscore">
        <type>error</type>
        <message>Method name "%s" must not be prefixed with an underscore to indicate visibility</message>
    </rule>
    <!-- ................................................................... -->

    <!-- Method arguments with default values MUST go at the end of the argument list. -->
    <rule ref="PEAR.Functions.ValidDefaultValue"/>

    <!-- Visibility MUST be declared on all methods. -->
    <rule ref="Squiz.Scope.MethodScope"/>
    <rule ref="Squiz.WhiteSpace.ScopeKeywordSpacing"/>

    <!-- The general style rules for control structures are as follows:
    There MUST be one space after the control structure keyword
    There MUST NOT be a space after the opening parenthesis
    There MUST NOT be a space before the closing parenthesis
    There MUST be one space between the closing parenthesis and the opening brace
    The structure body MUST be indented once
    The body MUST be on the next line after the opening brace
    The closing brace MUST be on the next line after the body
    The body of each structure MUST be enclosed by braces. This standardizes how the structures look and reduces the likelihood of introducing errors as new lines get added to the body. -->
    <rule ref="Squiz.ControlStructures.ControlSignature"/>
    <rule ref="Squiz.WhiteSpace.ControlStructureSpacing"/>
    <rule ref="Squiz.WhiteSpace.ControlStructureSpacing.SpacingAfterOpen"/>
    <rule ref="Squiz.WhiteSpace.ControlStructureSpacing.SpacingBeforeClose"/>
    <rule ref="Squiz.WhiteSpace.ScopeClosingBrace"/>
    <rule ref="Squiz.ControlStructures.ForEachLoopDeclaration"/>
    <rule ref="Squiz.ControlStructures.ForLoopDeclaration">
        <properties>
            <property name="ignoreNewlines" value="true"/>
        </properties>
    </rule>
    <rule ref="Squiz.ControlStructures.ForLoopDeclaration.SpacingAfterOpen">
        <severity>0</severity>
    </rule>
    <rule ref="Squiz.ControlStructures.ForLoopDeclaration.SpacingBeforeClose">
        <severity>0</severity>
    </rule>
    <rule ref="Squiz.ControlStructures.ForEachLoopDeclaration.SpaceBeforeClose">
        <severity>0</severity>
    </rule>
    <rule ref="Squiz.ControlStructures.LowercaseDeclaration"/>
    <!-- ................................................................... -->

    <!-- Argument lists MAY be split across multiple lines, where each subsequent line is indented once. When doing so, the first item in the list MUST be on the next line, and there MUST be only one argument per line.
    When the argument list is split across multiple lines, the closing parenthesis and opening brace MUST be placed together on their own line with one space between them. -->
    <rule ref="Squiz.Functions.MultiLineFunctionDeclaration"/>

    <!-- All PHP files MUST use the Unix LF (linefeed) line ending only. -->
    <rule ref="Generic.Files.LineEndings">
        <properties>
            <property name="eolChar" value="\n"/>
        </properties>
    </rule>

    <!-- All PHP files MUST end with a non-blank line, terminated with a single LF. -->
    <rule ref="PSR2.Files.EndFileNewline"/>

    <!-- The closing ?> tag MUST be omitted from files containing only PHP. -->
    <rule ref="PSR2.Files.ClosingTag"/>

    <!-- Method and function names MUST NOT be declared with space after the method name. The opening brace MUST go on its own line, and the closing brace MUST go on the next line following the body. There MUST NOT be a space after the opening parenthesis, and there MUST NOT be a space before the closing parenthesis. -->
    <rule ref="PSR2.Methods.FunctionClosingBrace"/>

    <!-- The keyword elseif SHOULD be used instead of else if so that all control keywords look like single words. -->
    <rule ref="PSR2.ControlStructures.ElseIfDeclaration"/>

    <!-- The case statement MUST be indented once from switch, and the break keyword (or other terminating keywords) MUST be indented at the same level as the case body. There MUST be a comment such as // no break when fall-through is intentional in a non-empty case body. -->
    <rule ref="PSR2.ControlStructures.SwitchDeclaration"/>

    <!-- The increment/decrement operators MUST NOT have any space between the operator and operand. -->
    <rule ref="Generic.WhiteSpace.IncrementDecrementSpacing"/>

    <!-- Type casting operators MUST NOT have any space within the parentheses. -->
    <rule ref="Squiz.WhiteSpace.CastSpacing"/>

    <!-- This message is not required as spaces are allowed for alignment -->
    <rule ref="Generic.Functions.FunctionCallArgumentSpacing.TooMuchSpaceAfterComma">
        <severity>0</severity>
    </rule>

    <rule ref="Squiz.NamingConventions.ValidVariableName.NotCamelCaps"/>

    <rule ref="Squiz.NamingConventions.ValidVariableName.MemberNotCamelCaps"/>
    <rule ref="Squiz.NamingConventions.ValidVariableName.StringNotCamelCaps"/>

    <rule ref="Squiz.NamingConventions.ValidVariableName"/>
    <rule ref="Squiz.NamingConventions.ValidVariableName.PublicHasUnderscore">
        <severity>0</severity>
    </rule>
    <rule ref="Squiz.NamingConventions.ValidVariableName.PrivateNoUnderscore">
        <severity>0</severity>
    </rule>

    <!-- ############################## RULES TO BE REVIEWS !!!!!!!! -->

    <!-- Private methods MUST not be prefixed with an underscore -->
    <rule ref="Squiz.NamingConventions.ValidFunctionName.PrivateNoUnderscore">
        <severity>0</severity>
    </rule>
    <rule ref="PSR2.Methods.MethodDeclaration.Underscore">
        <type>error</type>
    </rule>

    <!-- We use custom indent rules for arrays -->
    <rule ref="Generic.Arrays.ArrayIndent"/>
    <rule ref="Squiz.Arrays.ArrayDeclaration.KeyNotAligned">
        <severity>0</severity>
    </rule>
    <rule ref="Squiz.Arrays.ArrayDeclaration.ValueNotAligned">
        <severity>0</severity>
    </rule>
    <rule ref="Squiz.Arrays.ArrayDeclaration.CloseBraceNotAligned">
        <severity>0</severity>
    </rule>
    <rule ref="Squiz.Arrays.ArrayDeclaration.CloseBraceNewLine">
        <severity>0</severity>
    </rule>


    <rule ref="Generic.Classes.DuplicateClassName"/>
    <rule ref="Generic.CodeAnalysis.EmptyStatement"/>
    <rule ref="Generic.CodeAnalysis.UnconditionalIfStatement"/>
    <rule ref="Generic.CodeAnalysis.UnnecessaryFinalModifier"/>
    <rule ref="Generic.CodeAnalysis.UnusedFunctionParameter"/>
    <rule ref="Generic.CodeAnalysis.UselessOverridingMethod"/>
    <rule ref="Generic.ControlStructures.InlineControlStructure">
        <properties>
            <property name="error" value="false"/>
        </properties>
    </rule>
    <rule ref="Generic.Files.LineEndings"/>
    <rule ref="Generic.Formatting.DisallowMultipleStatements"/>
    <rule ref="Generic.Formatting.SpaceAfterCast"/>
    <rule ref="Generic.Functions.CallTimePassByReference"/>
    <rule ref="Generic.Functions.FunctionCallArgumentSpacing"/>
    <rule ref="Generic.Functions.OpeningFunctionBraceBsdAllman"/>
    <rule ref="Generic.NamingConventions.ConstructorName"/>
    <rule ref="Generic.NamingConventions.UpperCaseConstantName"/>
    <rule ref="Generic.PHP.DeprecatedFunctions"/>
    <rule ref="Generic.PHP.DisallowShortOpenTag"/>
    <rule ref="Generic.PHP.ForbiddenFunctions"/>
    <rule ref="Generic.PHP.NoSilencedErrors"/>
    <rule ref="Generic.WhiteSpace.DisallowTabIndent"/>
    <rule ref="Generic.WhiteSpace.ScopeIndent"/>
    <rule ref="MySource.PHP.EvalObjectFactory"/>
    <rule ref="PEAR.Classes.ClassDeclaration"/>
    <rule ref="PEAR.Commenting.InlineComment"/>
    <rule ref="PEAR.ControlStructures.ControlSignature"/>
    <rule ref="PEAR.ControlStructures.MultiLineCondition"/>
    <rule ref="PEAR.Functions.FunctionCallSignature"/>
    <rule ref="PEAR.Functions.ValidDefaultValue"/>
    <rule ref="PEAR.WhiteSpace.ObjectOperatorIndent"/>
    <rule ref="PEAR.WhiteSpace.ScopeClosingBrace"/>
    <rule ref="PSR2.Classes.PropertyDeclaration"/>
    <rule ref="PSR2.ControlStructures.ControlStructureSpacing"/>
    <rule ref="PSR2.ControlStructures.ElseIfDeclaration"/>
    <rule ref="PSR2.ControlStructures.SwitchDeclaration"/>
    <rule ref="PSR2.Methods.MethodDeclaration"/>
    <rule ref="PSR2.Namespaces.NamespaceDeclaration"/>
    <rule ref="PSR2.Namespaces.UseDeclaration"/>
    <rule ref="Squiz.PHP.Eval"/>
    <rule ref="Squiz.PHP.GlobalKeyword"/>
    <rule ref="Squiz.PHP.InnerFunctions"/>
    <rule ref="Squiz.PHP.LowercasePHPFunctions"/>
    <rule ref="Squiz.PHP.NonExecutableCode"/>
    <rule ref="Squiz.Scope.MemberVarScope"/>
    <rule ref="Squiz.Scope.MethodScope"/>
    <rule ref="Squiz.Scope.StaticThisUsage"/>
    <rule ref="Zend.Files.ClosingTag"/>

    <rule ref="PSR12.Functions.ReturnTypeDeclaration"/>



    <rule ref="PEAR">
        <exclude name="Generic.Commenting.DocComment.MissingShort"/>
        <exclude name="Generic.Formatting.MultipleStatementAlignment.NotSame"/>
        <exclude name="Generic.Formatting.SpaceAfterCast"/>
        <exclude name="PEAR.Commenting.ClassComment.Missing"/>
        <exclude name="PEAR.Commenting.ClassComment.MissingAuthorTag"/>
        <exclude name="PEAR.Commenting.ClassComment.MissingCategoryTag"/>
        <exclude name="PEAR.Commenting.ClassComment.MissingLicenseTag"/>
        <exclude name="PEAR.Commenting.ClassComment.MissingLinkTag"/>
        <exclude name="PEAR.Commenting.ClassComment.MissingPackageTag"/>
        <exclude name="PEAR.Commenting.FileComment.Missing"/>
        <exclude name="PEAR.Commenting.FunctionComment.Missing"/>
        <exclude name="PEAR.Commenting.FunctionComment.MissingParamComment"/>
        <exclude name="PEAR.Commenting.FunctionComment.MissingParamTag"/>
        <exclude name="PEAR.Commenting.FunctionComment.MissingReturn"/>
        <exclude name="PEAR.Commenting.FunctionComment.SpacingAfterParamName"/>
        <exclude name="PEAR.Commenting.FunctionComment.SpacingAfterParamType"/>
        <exclude name="PEAR.ControlStructures.ControlSignature.Found"/>
        <exclude name="PEAR.Functions.FunctionCallSignature.CloseBracketLine"/>
        <exclude name="PEAR.Functions.FunctionCallSignature.ContentAfterOpenBracket"/>
        <exclude name="PEAR.Functions.FunctionCallSignature.MultipleArguments"/>
        <exclude name="PEAR.NamingConventions.ValidFunctionName.FunctionNameInvalid"/>
        <exclude name="PEAR.NamingConventions.ValidFunctionName.FunctionNoCapital"/>
        <exclude name="PEAR.NamingConventions.ValidFunctionName.PrivateNoUnderscore"/>
        <exclude name="PEAR.NamingConventions.ValidFunctionName.ScopeNotCamelCaps"/>
        <exclude name="PEAR.NamingConventions.ValidVariableName.PrivateNoUnderscore"/>
        <exclude name="PEAR.WhiteSpace.ScopeIndent.Incorrect"/>
        <exclude name="PEAR.WhiteSpace.ScopeIndent.IncorrectExact"/>
    </rule>
    <rule ref="PSR1">
        <exclude name="PSR1.Classes.ClassDeclaration.MissingNamespace">
            <exclude-pattern>*/database/migrations/*</exclude-pattern>
        </exclude>
        <exclude name="PSR1.Methods.CamelCapsMethodName"/>
    </rule>
    <rule ref="PSR2"/>
    <rule ref="Squiz">
        <exclude name="Squiz.Arrays.ArrayDeclaration.DoubleArrowNotAligned"/>
        <exclude name="Squiz.Classes.ClassFileName.NoMatch">
            <exclude-pattern>*/database/migrations/*</exclude-pattern>
        </exclude>
        <exclude name="Squiz.Arrays.ArrayDeclaration.MultiLineNotAllowed"/>
        <exclude name="Squiz.Arrays.ArrayDeclaration.SingleLineNotAllowed"/>
        <exclude name="Squiz.Commenting.ClassComment.Missing"/>
        <exclude name="Squiz.Commenting.ClassComment.TagNotAllowed"/>
        <exclude name="Squiz.Commenting.ClosingDeclarationComment.Missing"/>
        <exclude name="Squiz.Commenting.FileComment.Missing"/>
        <exclude name="Squiz.Commenting.FunctionComment.EmptyThrows"/>
        <exclude name="Squiz.Commenting.FunctionComment.IncorrectParamVarName"/>
        <exclude name="Squiz.Commenting.FunctionComment.InvalidReturn"/>
        <exclude name="Squiz.Commenting.FunctionCommentThrowTag.Missing"/>
        <exclude name="Squiz.Commenting.InlineComment.InvalidEndChar"/>
        <exclude name="Squiz.Commenting.InlineComment.SpacingAfter"/>
        <exclude name="Squiz.Commenting.InlineComment.SpacingBefore"/>
        <exclude name="Squiz.Commenting.InlineComment."/>
        <exclude name="Squiz.Commenting.LongConditionClosingComment.Missing"/>
        <exclude name="Squiz.Commenting.VariableComment.IncorrectVarType"/>
        <exclude name="Squiz.ControlStructures.InlineIfDeclaration.NoBrackets"/>
        <exclude name="Squiz.ControlStructures.InlineIfDeclaration.NotSingleLine"/>
        <exclude name="Squiz.ControlStructures.SwitchDeclaration.BreakIndent"/>
        <exclude name="Squiz.Files.FileExtension.ClassFound"/>
        <exclude name="Squiz.Formatting.OperatorBracket.MissingBrackets"/>
        <exclude name="Squiz.NamingConventions.ValidFunctionName.NotCamelCaps"/>
        <exclude name="Squiz.NamingConventions.ValidFunctionName.PrivateNoUnderscore"/>
        <exclude name="Squiz.NamingConventions.ValidFunctionName.ScopeNotCamelCaps"/>
        <exclude name="Squiz.NamingConventions.ValidVariableName.NotCamelCaps"/>
        <exclude name="Squiz.NamingConventions.ValidVariableName.PrivateNoUnderscore"/>
        <exclude name="Squiz.Objects.ObjectInstantiation.NotAssigned"/>
        <exclude name="Squiz.Operators.ComparisonOperatorUsage.ImplicitTrue"/>
        <exclude name="Squiz.Operators.ComparisonOperatorUsage.NotAllowed"/>
        <exclude name="Squiz.PHP.DisallowBooleanStatement.Found"/>
        <exclude name="Squiz.PHP.DisallowComparisonAssignment.AssignedComparison"/>
        <exclude name="Squiz.PHP.DisallowInlineIf"/>
        <exclude name="Squiz.PHP.DisallowMultipleAssignments"/>
        <exclude name="Squiz.PHP.DisallowSizeFunctionsInLoops"/>
        <exclude name="Squiz.WhiteSpace.FunctionClosingBraceSpace.SpacingBeforeClose"/>
        <exclude name="Squiz.WhiteSpace.FunctionSpacing.After"/>
        <exclude name="Squiz.WhiteSpace.FunctionSpacing.Before"/>
        <exclude name="Squiz.WhiteSpace.ObjectOperatorSpacing.Before"/>
    </rule>

    <!--Individual rules-->

    <rule ref="Generic.Classes.DuplicateClassName"/>
    <rule ref="Generic.CodeAnalysis.EmptyStatement"/>
    <rule ref="Generic.CodeAnalysis.UnconditionalIfStatement"/>
    <rule ref="Generic.CodeAnalysis.UnnecessaryFinalModifier"/>
    <rule ref="Generic.CodeAnalysis.UselessOverridingMethod"/>
    <rule ref="Generic.Commenting.Fixme"/>
    <rule ref="Generic.Formatting.NoSpaceAfterCast"/>
<!--    <rule ref="SlevomatCodingStandard.Arrays.TrailingArrayComma"/>-->
    <rule ref="SlevomatCodingStandard.Classes.ClassConstantVisibility"/>
    <rule ref="SlevomatCodingStandard.Classes.ModernClassNameReference"/>
    <rule ref="SlevomatCodingStandard.Classes.SuperfluousAbstractClassNaming"/>
    <rule ref="SlevomatCodingStandard.Classes.SuperfluousInterfaceNaming"/>
    <rule ref="SlevomatCodingStandard.Commenting.EmptyComment"/>
    <rule ref="SlevomatCodingStandard.Commenting.InlineDocCommentDeclaration"/>
    <rule ref="SlevomatCodingStandard.Commenting.UselessInheritDocComment"/>
<!--    <rule ref="SlevomatCodingStandard.ControlStructures.DisallowEmpty"/>-->
    <rule ref="SlevomatCodingStandard.ControlStructures.DisallowYodaComparison"/>
<!--    <rule ref="SlevomatCodingStandard.ControlStructures.NewWithoutParentheses"/>-->
    <rule ref="SlevomatCodingStandard.ControlStructures.RequireNullCoalesceOperator"/>
    <rule ref="SlevomatCodingStandard.Exceptions.DeadCatch"/>
    <rule ref="SlevomatCodingStandard.Exceptions.ReferenceThrowableOnly"/>
    <rule ref="SlevomatCodingStandard.Namespaces.AlphabeticallySortedUses"/>
    <rule ref="SlevomatCodingStandard.Namespaces.DisallowGroupUse"/>
<!--    <rule ref="SlevomatCodingStandard.Namespaces.FullyQualifiedClassNameInAnnotation"/>-->
    <rule ref="SlevomatCodingStandard.Namespaces.NamespaceDeclaration"/>
    <rule ref="SlevomatCodingStandard.Namespaces.NamespaceSpacing"/>
    <rule ref="SlevomatCodingStandard.Namespaces.RequireOneNamespaceInFile"/>
    <rule ref="SlevomatCodingStandard.Namespaces.UseDoesNotStartWithBackslash"/>
    <rule ref="SlevomatCodingStandard.Namespaces.UseFromSameNamespace"/>
    <rule ref="SlevomatCodingStandard.Namespaces.UselessAlias"/>
    <rule ref="SlevomatCodingStandard.Operators.DisallowEqualOperators"/>
    <rule ref="SlevomatCodingStandard.PHP.ShortList"/>
    <rule ref="SlevomatCodingStandard.PHP.TypeCast"/>
<!--    <rule ref="SlevomatCodingStandard.PHP.UselessParentheses"/>-->
    <rule ref="SlevomatCodingStandard.PHP.UselessSemicolon"/>
    <rule ref="SlevomatCodingStandard.TypeHints.LongTypeHints"/>
    <rule ref="SlevomatCodingStandard.TypeHints.NullableTypeForNullDefaultValue"/>
    <rule ref="SlevomatCodingStandard.TypeHints.NullTypeHintOnLastPosition"/>
    <rule ref="SlevomatCodingStandard.TypeHints.ParameterTypeHintSpacing"/>
    <rule ref="SlevomatCodingStandard.Variables.UselessVariable"/>
    <rule ref="Squiz.Arrays.ArrayBracketSpacing"/>
    <rule ref="Squiz.ControlStructures.ElseIfDeclaration"/>
    <rule ref="Squiz.Strings.DoubleQuoteUsage"/>
    <rule ref="Squiz.Strings.DoubleQuoteUsage.ContainsVar"/>
    <rule ref="Squiz.WhiteSpace.ControlStructureSpacing"/>
    <rule ref="Squiz.WhiteSpace.SuperfluousWhitespace"/>

    <!--Rule configuration-->

    <!--The testing bootstrap file uses string concatenations to stop IDEs seeing the class aliases-->
    <rule ref="Generic.Strings.UnnecessaryStringConcat">
        <exclude-pattern>tests/bootstrap.php</exclude-pattern>
    </rule>

    <rule ref="Generic.Files.LineLength">
        <properties>
            <property name="lineLimit" value="300"/>
            <property name="absoluteLineLimit" value="300"/>
        </properties>
    </rule>

    <rule ref="PEAR.Commenting.FunctionComment.SpacingAfterParamType">
        <properties>
            <property name="spacing" value="1"/>
        </properties>
    </rule>


    <!-- PEAR uses warnings for inline control structures, so switch back to errors -->
    <rule ref="Generic.ControlStructures.InlineControlStructure">
        <properties>
            <property name="error" value="true"/>
        </properties>
    </rule>

    <!--Don't hide tokenizer exceptions-->
    <rule ref="Internal.Tokenizer.Exception">
        <type>error</type>
    </rule>

    <rule ref="PEAR.Functions.FunctionCallSignature">
        <properties>
            <property name="allowMultipleArguments" value="false"/>
        </properties>
    </rule>

    <rule ref="SlevomatCodingStandard.Classes.TraitUseSpacing">
        <properties>
            <property name="linesCountBeforeFirstUse" value="0"/>
            <property name="linesCountBetweenUses" value="0"/>
            <property name="linesCountAfterLastUse" value="1"/>
            <property name="linesCountAfterLastUseWhenLastInClass" value="0"/>
        </properties>
    </rule>

    <rule ref="SlevomatCodingStandard.Commenting.DocCommentSpacing">
        <properties>
            <property name="linesCountBeforeFirstContent" value="0"/>
            <property name="linesCountAfterLastContent" value="0"/>
            <property name="linesCountBetweenDescriptionAndAnnotations" value="1"/>
            <property name="linesCountBetweenDifferentAnnotationsTypes" value="1"/>
        </properties>
        <exclude-pattern>*/**/tests</exclude-pattern>
    </rule>

<!--    <rule ref="SlevomatCodingStandard.TypeHints.DeclareStrictTypes">-->
<!--        <exclude-pattern>*/config/*</exclude-pattern>-->
<!--        <exclude-pattern>*/resources/*</exclude-pattern>-->
<!--        <properties>-->
<!--            <property name="newlinesCountBetweenOpenTagAndDeclare" value="2"/>-->
<!--            <property name="newlinesCountAfterDeclare" value="2"/>-->
<!--            <property name="spacesCountAroundEqualsSign" value="0"/>-->
<!--        </properties>-->
<!--    </rule>-->

    <!-- relative path from PHPCS source location -->
    <config name="installed_paths" value="vendor/slevomat/coding-standard"/>

    <!-- specific sniffs to include -->
    <rule ref="vendor/slevomat/coding-standard/SlevomatCodingStandard/Sniffs/TypeHints/DeclareStrictTypesSniff.php" />

    <rule ref="SlevomatCodingStandard.TypeHints.ReturnTypeHintSpacing">
        <properties>
            <property name="spacesCountBeforeColon" value="1"/>
        </properties>
    </rule>

    <rule ref="SlevomatCodingStandard.Classes.EmptyLinesAroundClassBraces">
        <properties>
            <property name="linesCountAfterOpeningBrace" value="0"/>
            <property name="linesCountBeforeClosingBrace" value="0"/>
        </properties>
    </rule>

    <rule ref="Squiz.Commenting.FunctionComment">
        <exclude name="Squiz.Commenting.FunctionComment.Missing"/>
        <exclude name="Squiz.Commenting.FunctionComment.MissingParamComment"/>
        <exclude name="Squiz.Commenting.FunctionComment.ScalarTypeHintMissing"/>
        <exclude name="Squiz.Commenting.FunctionComment.SpacingAfterParamName"/>
        <exclude name="Squiz.Commenting.FunctionComment.SpacingAfterParamType"/>
        <exclude name="Squiz.Commenting.FunctionComment.TypeHintMissing"/>
    </rule>

    <rule ref="Squiz.Commenting.FunctionComment.MissingParamTag">
        <exclude-pattern>*/**/tests</exclude-pattern>
    </rule>

    <rule ref="Squiz.Commenting.FunctionComment.MissingReturn">
        <exclude-pattern>*/**/tests</exclude-pattern>
    </rule>

    <rule ref="Squiz.Strings.ConcatenationSpacing">
        <properties>
            <property name="spacing" value="1"/>
            <property name="ignoreNewlines" value="true"/>
        </properties>
    </rule>

    <rule ref="Squiz.WhiteSpace.FunctionSpacing">
        <properties>
            <property name="spacingBeforeFirst" value="0"/>
            <property name="spacingAfterLast" value="0"/>
        </properties>
    </rule>

    <rule ref="Squiz.WhiteSpace.MemberVarSpacing">
        <properties>
            <property name="spacing" value="1"/>
            <property name="spacingBeforeFirst" value="0"/>
        </properties>
    </rule>

    <rule ref="Squiz.WhiteSpace.OperatorSpacing">
        <properties>
            <property name="ignoreNewlines" value="true"/>
        </properties>
    </rule>

    <rule ref="Zend.NamingConventions.ValidVariableName">
        <exclude name="Zend.NamingConventions.ValidVariableName.NotCamelCaps"/>
        <exclude name="Zend.NamingConventions.ValidVariableName.MemberVarContainsNumbers"/>
        <exclude name="Zend.NamingConventions.ValidVariableName.ContainsNumbers"/>
        <exclude name="Zend.NamingConventions.ValidVariableName.StringVarContainsNumbers"/>
        <exclude name="Zend.NamingConventions.ValidVariableName.PrivateNoUnderscore"/>
    </rule>
</ruleset>