### 2019-10-01

EventExt deprecated 

event listeners are added using compiler pass
and di tag <tag name="kernel.event_subscriber" />

```
<service class="CashplusModule\EventSubscribers\UpdateProductEventSubscriber" id="cashplus_module.event_subscribers.update_product_event_subscriber">
    <argument id="cashplus_module.product_repository" type="service"/>
    <tag name="kernel.event_subscriber" />
</service>
```

### 2018-10-29

grunt removed in favor of npm build scripts (required double maintenance)

use (check README)

```
npm run build
npm run build:watch
```

### 2018-09-05

Introduced `CompanyModule\Entities\Events\EmailEvent` for given company.

**CompanyModule\Repositories\CompanyEventRepository**

```
$companyEventRepository->save(new EmailEvent($company, $emailLog, 'IncorporationListener'));
$companyEventRepository->hasEmailEvent($company, $emailId);
```

### 2018-04-17

introduced a flag to hide google tag manager, analytics code if necessary (e.g. selenium)

add it to your local configurations:

```
hide_external_js: true
```
