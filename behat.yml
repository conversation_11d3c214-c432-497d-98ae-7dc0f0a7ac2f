default:
    gherkin:
        cache: ~

    suites:
        mycompanies_search_web_features:
            paths:
                - "%paths.base%/vendor/made_simple/msg-features/cms/my_companies_search/web_search.feature"
            contexts:
                - Features\Bootstrap\MyCompaniesSearchWebContext
        mycompanies_search_domain_features:
            paths:
                - "%paths.base%/vendor/made_simple/msg-features/cms/my_companies_search/domain_search.feature"
            contexts:
                - Features\Bootstrap\MyCompaniesSearchDomainContext
        payment_features:
            paths:
                - "%paths.base%/vendor/made_simple/msg-features/cms/payment/payment.feature"
            contexts:
                - Features\Bootstrap\PaymentContext:
                    - payment_module.payment_types.sage_payment
                    - repositories.payment.token_repository
                    - payment_module.services.sage_transaction_finder
        payment_confirmation_features:
            paths:
                - "%paths.base%/vendor/made_simple/msg-features/cms/payment/payment_confirmation.feature"
            contexts:
                - Features\Bootstrap\PaymentContext:
                    - payment_module.payment_types.sage_payment
                    - repositories.payment.token_repository
                    - payment_module.services.sage_transaction_finder
        payment_method_features:
            paths:
                - "%paths.base%/vendor/made_simple/msg-features/cms/payment/payment_method.feature"
            contexts:
                - Features\Bootstrap\PaymentContext:
                    - payment_module.payment_types.sage_payment
                    - repositories.payment.token_repository
                    - payment_module.services.sage_transaction_finder
        company_settings_features:
            paths:
                - "%paths.base%/vendor/made_simple/msg-features/cms/company/company_settings.feature"
            contexts:
                - Features\Bootstrap\CompanySettingsContext
        bdg_banking_features:
            paths:
                - "%paths.base%/vendor/made_simple/msg-features/cms/banking/bdg_banking.feature"
                - "%paths.base%/vendor/made_simple/msg-features/cms/banking/company_summary_actions.feature"
            contexts:
                - Features\Bootstrap\BusinessData\BdgContext:
                    - test_module.helpers.database_helper
                    - data_generation.company_generator
                    - router_module.helpers.controller_helper
                    - services.submission_service
        retail_purchase_flow_features:
            paths:
                - "%paths.base%/vendor/made_simple/msg-features/cms/retail/search_basket_flow.feature"
            contexts:
                - Features\Bootstrap\RetailPurchaseFlowContext
        wholesale_purchase_flow_features:
            paths:
                - "%paths.base%/vendor/made_simple/msg-features/cms/wholesale/search_basket_flow.feature"
            contexts:
                - Features\Bootstrap\WholesalePurchaseFlowContext
        wholesale_feature:
            paths:
                - "%paths.base%/vendor/made_simple/msg-features/cms/wholesale/payment_role.feature"
            contexts:
                - Features\Bootstrap\WholesaleContext

        renewal_payment_features:
            paths:
                - "%paths.base%/vendor/made_simple/msg-features/cms/payment/renewal_payment.feature"
            contexts:
                - Features\Bootstrap\PaymentContext:
                    - payment_module.payment_types.sage_payment
                    - repositories.payment.token_repository
                    - payment_module.services.sage_transaction_finder

        entity_validity_feature:
            paths:
                - "%paths.base%/vendor/made_simple/msg-features/cms/id_check/entity_validity.feature"
            contexts:
                - IdModule\Contexts\IdEntityContext
        diligence_level_feature:
            paths:
                - "%paths.base%/vendor/made_simple/msg-features/cms/id_check/diligence_level.feature"
                - "%paths.base%/vendor/made_simple/msg-features/cms/id_check/diligence_level_advanced.feature"
            contexts:
                - IdModule\Contexts\IdEntityContext
                - IdModule\Contexts\DiligenceLevelContext
        id3global_verifier:
            paths:
                - "%paths.base%/vendor/made_simple/msg-features/cms/id_check/id_3global_verification.feature"
            contexts:
                - IdModule\Contexts\Id3GlobalProfileContext:
                  - id_module.verifiers.id3global_verifier
                  - test_module.helpers.database_helper
        entity_retrieval_feature:
            paths:
                - "%paths.base%/vendor/made_simple/msg-features/cms/id_check/entity_retrieval.feature"
                - "%paths.base%/vendor/made_simple/msg-features/cms/id_check/idcheck_overall_status.feature"
                - "%paths.base%/vendor/made_simple/msg-features/cms/id_check/entity_validity_behaviour.feature"
            contexts:
                - IdModule\Contexts\IdEntityContext
                - IdModule\Contexts\IdEntityRetrievalContext:
                  - test_module.helpers.database_helper
                  - id_module.repositories.entity_provider
                  - id_module.repositories.entity_provider
                  - id_module.repositories.id_info_repository
                  - id_module.verification.id_validator
        id_execution_feature:
            paths:
                - "%paths.base%/vendor/made_simple/msg-features/cms/id_check/check_execution.feature"
            contexts:
                - IdModule\Contexts\IdEntityContext
                - IdModule\Contexts\CheckExecutionContext
        id_page_feature:
            paths:
                - "%paths.base%/vendor/made_simple/msg-features/cms/id_check/ui_tests/id_status_page.feature"
                #- %paths.base%/vendor/made_simple/msg-features/cms/id_check/ui_tests/id_status_page_regulated_body.feature
            contexts:
                - IdModule\Contexts\Ui\IdStatusContext:
                  - url_generator
                  - test_module.helpers.database_helper
                - IdModule\Contexts\IdEntityRetrievalContext:
                  - test_module.helpers.database_helper
                  - id_module.repositories.entity_provider
                  - id_module.repositories.entity_provider
                  - id_module.repositories.id_info_repository
                  - id_module.verification.id_validator
                - IdModule\Contexts\IdEntityContext
        incorporation_submission_feature:
            paths:
                - "%paths.base%/vendor/made_simple/msg-features/cms/incorporation/submission/by_shares.feature"
                - "%paths.base%/vendor/made_simple/msg-features/cms/incorporation/submission/by_guar.feature"
                - "%paths.base%/vendor/made_simple/msg-features/cms/incorporation/submission/llp.feature"
            contexts:
                - Features\Bootstrap\CompaniesHouse\Incorporation\SubmissionContext:
                  - test_module.helpers.database_helper
                  - data_generation.company_generator
        customer_details_feature:
            paths:
                - "%paths.base%/vendor/made_simple/msg-features/cms/customer/my_details.feature"
            contexts:
                - Features\Bootstrap\Customer\DetailsContext:
                  - test_module.helpers.database_helper
                  - id_module.verification.id_validator
        cachbacks_cashback_context:
            paths:
                - "%paths.base%/vendor/made_simple/msg-features/cms/cashback/toggle_cashback_status.feature"
            contexts:
                - Features\Bootstrap\Cashbacks\CashbackContext:
                    - cash_back_module.commands.toggle_cashback_status_no_stdout
                    - test_module.helpers.database_helper
                    - id_module.repositories.id_info_repository
                    - id_module.verification.id_validator
        mail_scan_release_context:
            paths:
                - "%paths.base%/vendor/made_simple/msg-features/cms/mailroom/mailroom.feature"
            contexts:
                - Features\Bootstrap\MailScan\PostItemReleaseContext
        basket_context:
            paths:
                - "%paths.base%/vendor/made_simple/msg-features/cms/basket/package.feature"
            contexts:
                - Features\Bootstrap\BasketContext:
                    - url_generator
        smoke_tests:
            paths:
                - "%paths.base%/temp/smoke_tests/from_page_repo"
                - "%paths.base%/temp/smoke_tests/from_routes"
                - "%paths.base%/temp/smoke_tests/from_routes_api"
                - "%paths.base%/temp/smoke_tests/from_dynamic_pages"
                - "%paths.base%/temp/smoke_tests/manually_maintained"
            contexts:
                - Features\Bootstrap\SmokeTests\SmokeTestsContext:
                    - smoke_tests_module.providers.config_provider
                    - test_module.helpers.database_helper

    extensions:
        TestModule\Extensions\BehatLoader: ~
#        DMore\ChromeExtension\Behat\ServiceContainer\ChromeExtension: ~
        Behat\MinkExtension:
            # use environment variable: export BEHAT_PARAMS='{"extensions" : {"Behat\\MinkExtension" : {"base_url" : "https://test.cms"}}}'
            goutte:
                server_parameters:
                    verify_host: false
                    verify_peer: false
            selenium2:
                browser: chrome
            files_path: "%paths.base%/features/fixtures"

#            browser_name: chrome
#            base_url: https://test.cms
#            sessions:
#              default:
#                chrome:
#                  api_url: "http://localhost:9222"


