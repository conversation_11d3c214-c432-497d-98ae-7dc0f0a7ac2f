.nip-close-modal-icon {
    width: 20px;
    height: 20px;
    margin-left: auto;
}

.nip-link-light {
    color: #2680eb;
    text-decoration: underline;
    font-weight: 600
}

.btn-close {
    box-sizing: content-box;
    width: 1em;
    height: 1em;
    padding: .25em .25em;
    color: #000;
    background: rgba(0,0,0,0) url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/%3e%3c/svg%3e") center/1em auto no-repeat;
    border: 0;
    border-radius: .375rem;
    opacity: .5
}

.modal-title {
    margin-bottom: 0;
    line-height: var(--bs-modal-title-line-height)
}

.nip-text-color {
    color: #2b2b2b;
    font-family: "Open Sans",sans-serif;
    font-weight: 400;
    font-size: 16px
}

.fw-semibold {
    font-weight: 600 !important
}

.modal-header {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: space-between;
}

.btn-view-formation {
    display: flex;
    gap: 6px;
    justify-content: center;
    align-items: center;
    color: #fff;
    text-decoration: none !important;
    font-weight: 600 !important;
    width: 256px;
    height: 56px
}

.modal-dialog {
    position: relative;
    width: auto;
}

.custom-modal-width {
    max-width: 900px;
    max-height: 450px;
    width: 100%;
    height: 100%
}

.modal-dialog-centered {
    display: flex;
    align-items: center;
    min-height: calc(100% - var(--bs-modal-margin)*2);
}