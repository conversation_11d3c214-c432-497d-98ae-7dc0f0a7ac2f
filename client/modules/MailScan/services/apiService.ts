export async function fetchPostItems(customerId: number, page: number, legacyItemsCount: number): Promise<any> {
    const response = await fetch(`/api/v1/company-inbox/list-items/${customerId}/`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json"
        },
        body: JSON.stringify({ page, legacyItemsCount })
    });
    return response.json();
}

export async function generateInlinePaymentBlock(companyId: number,  postItemId: number): Promise<any> {
    const response = await fetch(`/api/v1/company-inbox/generate-inline-payment-block/${companyId}/`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json"
        },
        body: JSON.stringify({
            postItemId: postItemId
        })
    });
    return response.json();
}

export async function buyItem(companyId: number, postItemId: number, orderId: string): Promise<any> {
    const response = await fetch(`/api/v1/company-inbox/mark-as-purchased/${companyId}/`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json"
        },
        body: JSON.stringify({
            postItemId,
            orderId
        })
    });
    return response.json();
}