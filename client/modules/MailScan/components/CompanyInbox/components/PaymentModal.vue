<template>
    <div
        class="modal fade"
        :id="modalId"
        tabindex="-1"
        aria-hidden="true"
        data-bs-backdrop="static"
        data-bs-keyboard="false"
    >
        <div class="modal-dialog modal-dialog-centered modal-lg">
            <div class="modal-content" style="max-width: 600px">
                <div class="modal-header">
                    <h5 class="modal-title fw-bold">
                        {{postItem.cta}} (£{{postItem.inboxPrice.toFixed(2)}} <small>+VAT</small>)
                    </h5>
                </div>
                <div class="modal-body d-flex align-items-center justify-content-center" style="min-height: 198px; width: 100%;">
                    <template v-if="loading">
                        <div class="d-flex justify-content-center align-items-center my-4" style="min-height: 198px; width: 100%;">
                            <div class="spinner-border" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                        </div>
                    </template>
                    <template v-else-if="errorMessage">
                        <div class="d-flex justify-content-center align-items-center my-4" style="min-height: 198px; width: 100%;">
                            <p>{{errorMessage}}</p>
                        </div>
                    </template>
                    <template v-else-if="processing">
                        <div class="d-flex justify-content-center align-items-center my-4" style="min-height: 198px; width: 100%;">
                            <div class="spinner-border" role="status">
                                <span class="sr-only">Processing...</span>
                            </div>
                            <div class="ml-4">Processing payment</div>
                        </div>
                    </template>
                    <template v-else>
                        <div :id="inlinePaymentBlockId" class="ipb" v-if="componentData" style="min-height: 198px; width: 100%;">
                            <div :data-inline-payment="componentData"></div>
                            <component is="script" :src="omnipayUrl" defer />
                        </div>
                    </template>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { generateInlinePaymentBlock, buyItem } from "../../../services/apiService";

export default {
    name: "PaymentModal",
    props: {
        postItem: { type: Object, required: true },
    },
    data() {
        return {
            componentData: null,
            omnipayUrl: null,
            loading: false,
            processing: false,
            errorMessage: null,
        };
    },
    computed: {
        modalId() {
            return `${this.postItem.id}Modal`;
        },
        inlinePaymentBlockId() {
            return `${this.postItem.id}ipb`;
        },
    },
    methods: {
        async fetchInlinePaymentBlock() {
            try {
                const result = await generateInlinePaymentBlock(this.postItem.companyId, this.postItem.id);
                this.componentData = result.componentData;
                this.omnipayUrl = `${result.omnipayUrl}packs/js/inline_payment_component_from_selector.js`;
            } catch (error) {
                console.error("Error generating inline payment block:", error);
                this.errorMessage = "Error loading payment block. Please contact support.";
            } finally {
                this.loading = false;
            }
        },
        async attemptBuyItem(orderId) {
            try {
                const response = await buyItem(this.postItem.companyId, this.postItem.id, orderId);
                if (response.success && response.status === "success") {
                    this.$emit("postItemBuySuccess", this.postItem.id);
                    this.closeModal();
                } else {
                    this.errorMessage = `Error saving Post Item to database. Please contact support. Order: ${orderId}`;
                }
            } catch (error) {
                console.error("Error marking item as purchased:", error);
                this.errorMessage = "Error saving Post Item to database. Please contact support.";
            }
        },
        listenToPayment() {
            document.addEventListener("inline-payment-succeeded", async ({ detail }) => {
                try {
                    this.processing = true;

                    const orderId = detail.orderId;

                    if (!orderId) {
                        throw new Error("No order found");
                    }

                    await this.attemptBuyItem(orderId);
                } catch (error) {
                    console.error(error);
                    this.errorMessage = "Error processing payment. Please contact support.";
                } finally {
                    this.processing = false;
                    document.removeEventListener("inline-payment-succeeded", () => {});
                    this.listenToPayment();
                }
            });
        },
        closeModal() {
            this.$nextTick(() => {
                const modalElement = this.$el;
                let modalInstance = bootstrap.Modal.getInstance(modalElement);
                if (!modalInstance) {
                    modalInstance = new bootstrap.Modal(modalElement);
                }
                modalInstance.hide();
                this.$emit("closeModal");
            });
        }
    },
    watch: {
        postItem: {
            immediate: true,
            async handler() {
                this.loading = true;
                this.componentData = null;
                this.omnipayUrl = null;
                await this.fetchInlinePaymentBlock();
            }
        }
    },
    mounted() {
        this.listenToPayment();
        this.errorMessage = null;
    }
};
</script>

<style>
#stripe-component {
    max-width: 512px;
}
</style>
