<template>
    <div>
        <div class="alert alert-danger mt-4" v-if="errorMessage">
            {{ errorMessage }}
        </div>

        <div class="mt-4 table-responsive table-borderless" style="max-width: calc(100vw - 30px)">
            <table class="table table-responsive grid2 mb-3">
                <thead>
                <tr>
                    <th class="fw-semibold">Company Number</th>
                    <th class="fw-semibold" v-if="debugMode">ID (last 8 digits)</th>
                    <th class="fw-semibold" v-if="debugMode">Type</th>
                    <th class="fw-semibold">Company Name</th>
                    <th class="fw-semibold">Sender</th>
                    <th class="fw-semibold">Date Received</th>
                    <th class="fw-semibold" style="min-width: 156px !important;">Post Status</th>
                    <th class="fw-semibold">Action</th>
                </tr>
                </thead>
                <tbody>
                <template v-if="postItems && postItems.length">
                    <post-item-row
                        v-for="postItem in postItems"
                        :key="postItem.id"
                        :post-item="postItem"
                        :debug-mode="debugMode"
                        @openModal="handleOpenModal"
                        @errorOccurred="handleError"
                    />
                </template>
                <template v-else>
                    <no-results />
                </template>
                </tbody>
            </table>
        </div>

        <payment-modal
            v-if="activePostItem"
            :postItem="activePostItem"
            ref="paymentModal"
            @closeModal="handleCloseModal"
            @postItemBuySuccess="handleSuccessfulPurchase"

        />
    </div>
</template>

<script>
import PostItemRow from "./PostItemRow.vue";
import NoResults from "./NoResults.vue";
import PaymentModal from "./PaymentModal.vue";

export default {
    name: "PostItemsTable",
    components: {
        PaymentModal,
        PostItemRow,
        NoResults
    },
    props: {
        postItems: {
            type: Array,
            default: () => []
        },
        debugMode: {
            type: Boolean,
            default: false
        }
    },
    data: function () {
        return {
            activePostItem: null,
            errorMessage: null,
        };
    },
    methods: {
        handleOpenModal(postItem) {
            this.activePostItem = postItem;

            this.$nextTick(() => {
                const modalElement = this.$refs.paymentModal.$el;
                const modalInstance = new bootstrap.Modal(modalElement);
                modalInstance.show();
            });
        },
        handleSuccessfulPurchase(postItemId) {
            for (let i = 0; i < this.postItems.length; i++) {
                if (this.postItems[i].id !== postItemId) {
                    continue;
                }

                if (this.postItems[i].type === "parcel" ) {
                  this.postItems[i].tooltip = null;
                  this.postItems[i].cta = null;
                  this.postItems[i].inboxStatus = "To be Forwarded";
                  break;
                }

                this.postItems[i].canBeDownloaded = true;
                this.postItems[i].cta = "View";
                this.postItems[i].inboxStatus = "Available";
                this.postItems[i].tooltip = null;
                break;
            }
        },
        handleError(errorMessage) {
            this.errorMessage = errorMessage;
        },
        handleCloseModal() {
            this.activePostItem = null;
        }
    }
};
</script>
