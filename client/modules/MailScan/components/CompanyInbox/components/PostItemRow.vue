<template>
    <tr>
        <td style="vertical-align: top;">
            {{ postItem.companyNumber }}
        </td>
        <td v-if="debugMode" style="vertical-align: top;">
            {{ String(postItem.id).slice(-8) }}
        </td>
        <td v-if="debugMode" style="vertical-align: top;">
            {{ postItem.isLegacy ? postItem.type + ' *' : postItem.type }}
        </td>
        <td style="vertical-align: top;">
            {{ postItem.companyName }}
        </td>
        <td style="vertical-align: top;">
            {{ postItem.sender }}
        </td>
        <td style="vertical-align: top;">
            {{ formattedDate }}
        </td>
        <td style="vertical-align: top;">
            <span>
                {{ postItem.inboxStatus }}
                <template v-if="postItem.tooltip">
                    <Tooltip
                        :content=postItem.tooltip
                    />
                </template>
            </span>
        </td>
        <td style="vertical-align: top;">
            <div style="display: block;">
                <a
                    v-if="postItem.cta"
                    class="btn btn-link p-0"
                    style="vertical-align: top;"
                    v-html="postItem.cta"
                    @click="handleCtaClick(postItem)"
                    data-bs-toggle="modal" :data-bs-target="`#${postItem.id}Modal`"
                ></a>
                <template v-if="loading">
                    <div class="spinner-border" style="vertical-align: top;" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                </template>
            </div>
        </td>
    </tr>
</template>

<script>
import Tooltip from "./Tooltip.vue";
import { getBlobUrl, downloadFile } from '../../../helpers/fileHelper.ts';

export default {
    name: "PostItemRow",
    components: { Tooltip },
    props: {
        postItem: { type: Object, required: true },
        debugMode: { type: Boolean, required: true },
    },
    data() {
        return {
            loading: false
        }
    },
    computed: {
        formattedDate() {
            return new Date(this.postItem.dtc).toLocaleString();
        }
    },
    methods: {
        handleCtaClick() {
          if (this.postItem.canBeDownloaded) {
              this.downloadItem();
              return;
          }

          if (this.postItem.requiresPayment) {
              this.openModal();
          }
        },
        openModal() {
            this.$emit("openModal", this.postItem);
        },
        getSanitizedFilename() {
            try {
                const sanitizedCompanyName = this.postItem.companyName.replace(/[/\\?%*:|"<>]/g, '-');
                const sanitizedDtc = this.postItem.dtc.replace(/[/\\?%*:|"<> :]/g, '-');
                return sanitizedCompanyName + "_" + sanitizedDtc + ".pdf";
            } catch (error) {
                console.error("Error sanitizing filename:", error);
                return "post_item.pdf";
            }

        },
        async downloadItem() {
            try {
                this.loading = true;
                const response = await fetch(`/api/v1/company-inbox/download-item/${this.postItem.companyId}/${this.postItem.id}/`);
                const data = await response.json();

                if (data.status === "error") {
                    throw new Error("Error downloading Post Item. Please contact support.");
                }

                const blobUrl = getBlobUrl(data.encodedFile);
                downloadFile(blobUrl, this.getSanitizedFilename());
            } catch (error) {
                console.error("Error fetching post items:", error);
                this.$emit("errorOccurred", error.message);
            } finally {
                this.loading = false;
            }
        }
    }
};
</script>
