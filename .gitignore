vendor/.git
vendor/*/*/.git
vendor/**/.git

!/project/temp
/project/temp/*
!/project/temp/upload
/project/temp/upload/*
!/project/temp/upload/ch_documents/
/project/temp/upload/ch_documents/*
!/project/temp/upload/ch_documents/rtfdocs
!/project/temp/upload/ch_documents/_modelArticles
www/sitemap.xml

temp/*
logs/*
www/project/upload/*
www/webtemp/*
temp/proxies/.
v8-compile-cache-0/

#netbeans ignore personal stuff
.DS_Store
._.DS_Store
/nbproject/
.idea/
.phpspec/

#Blog Config File
www/project/blog/
project/libs/CHSearch/trans/


project/libs/CHFiling/core/request/document/file/
support/
starting-a-business-blog/
wow/
ClickTale/Cache/
ClickTale/Logs/

build/
www/webtemp/*
!www/webtemp/index.html

project/config/app/config.*
!project/config/app/config.shared.*
!project/config/app/config.dev.*

console/console_dev.php*
www/index_*.php

desktop.ini

.solano*
node_modules
www/components/ui_server/.git
www/components/ui_server/.bower.json
npm-debug.log*
.env
vendor/made_simple/msg_docker/.env
www/dist/*.map
project/MoneyPennyNumbersModule/Config/prefixes.json
test
tests/errors.log
.phpunit.result.cache