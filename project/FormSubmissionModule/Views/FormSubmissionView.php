<?php

namespace FormSubmissionModule\Views;

use DateTime;
use Entities\CompanyHouse\FormSubmission;
use Libs\CHFiling\Core\Request\FormSubmission as OldFormSubmission;

class FormSubmissionView
{
    /**
     * @var OldFormSubmission
     */
    private $formSubmission;

    /**
     * @param OldFormSubmission $formSubmission
     */
    public function __construct(OldFormSubmission $formSubmission)
    {
        $this->formSubmission = $formSubmission;
    }

    /**
     * @return int
     */
    public function getId()
    {
        return $this->formSubmission->getFormSubmissionId();
    }

    /**
     * @return string
     */
    public function getFormIdentifier()
    {
        return $this->formSubmission->getFormIdentifier();
    }

    public function getCompaniesHouseSubmissionNumber()
    {
        return $this->formSubmission->getCompaniesHouseSubmissionNumber();
    }

    public function getFormName(): string
    {
        $names = [
            'FormSubmission' => 'Form Submission',
            'CompanyIncorporation' => 'Company Incorporation',
            'ChangeRegisteredOfficeAddress' => 'Change Registered Office Address',
            'OfficerResignation' => 'Officer Resignation',
            'OfficerAppointment' => 'Officer Appointment',
            'OfficerChangeDetails' => 'Officer Change Details',
            'ReturnOfAllotmentShares' => 'Return Of Allotment Shares',
            'ChangeOfName' => 'Change Of Name',
            'ChangeAccountingReferenceDate' => 'Change Accounting Reference Date',
            'AnnualReturn' => 'Confirmation Statement',
            'PSCNotification' => 'PSC Notification',
            'PSCChangeDetails' => 'PSC Change Details',
            'PSCCessation' => 'PSC Cessation',
            'PSCStatementNotification' => 'PSC Statement Notification',
            'PSCStatementWithdrawal' => 'PSC Statement Withdrawal'
        ];

        return $names[$this->getFormIdentifier()] ?? $this->getFormIdentifier();

    }

    /**
     * @return bool
     */
    public function hasResponse()
    {
        return (bool)$this->formSubmission->getSubmissionStatus();
    }

    /**
     * @return string
     */
    public function getResponse()
    {
        return $this->formSubmission->getSubmissionStatus();
    }

    /**
     * @return string
     */
    public function getResponseMessage()
    {
        if ($this->formSubmission->isInternalFailure()) {
            return FormSubmission::RESPONSE_PENDING;
        }

        if ($this->formSubmission->isWithhold()) {
            return 'WITHHELD';
        }

        return $this->getResponse();
    }

    /**
     * @return DateTime
     */
    public function getDateCancelled()
    {
        return $this->formSubmission->getDateCancelled();
    }

    /**
     * @return bool
     */
    public function isAccept()
    {
        return $this->formSubmission->isAccept();
    }

    /**
     * @return bool
     */
    public function isError()
    {
        return $this->formSubmission->isError();
    }

    public function isWithhold(): bool
    {
        return $this->formSubmission->isWithhold();
    }

    /**
     * @return bool
     */
    public function isPending()
    {
        return $this->formSubmission->isPending() || $this->formSubmission->isInternalFailure();
    }

    /**
     * @return bool
     */
    public function isInternalFailure()
    {
        return $this->formSubmission->isInternalFailure();
    }

    /**
     * @return bool
     */
    public function isCancelled()
    {
        return (bool)$this->formSubmission->getDateCancelled();
    }

    /**
     * @return bool
     */
    public function isCompanyIncorporationType()
    {
        return $this->formSubmission->isCompanyIncorporationType();
    }

    /**
     * @return bool
     */
    public function isAnnualReturnType()
    {
        return $this->formSubmission->isAnnualReturnType();
    }

    /**
     * @return int
     */
    public function getCompanyId()
    {
        return $this->formSubmission->getCompanyId();
    }

    /**
     * @return string
     */
    public function getLanguage()
    {
        return $this->formSubmission->getLanguage();
    }

    /**
     * @return string
     */
    public function getRejectReference()
    {
        return $this->formSubmission->getRejectReference();
    }

    /**
     * @return string
     */
    public function getExaminerTelephone()
    {
        return $this->formSubmission->getExaminerTelephone();
    }

    /**
     * @return string
     */
    public function getExaminerComment()
    {
        return $this->formSubmission->getExaminerComment();
    }

    /**
     * @return string
     */
    public function getFirstSubmissionDate()
    {
        return $this->formSubmission->getFirstSubmissionDate();
    }

    /**
     * @return string
     */
    public function getLastSubmissionDate()
    {
        return $this->formSubmission->getLastSubmissionDate();
    }

    /**
     * @return bool
     */
    public function isResubmittable()
    {
        return !$this->isCancelled() && !$this->isWithhold() && ($this->isPending() || $this->isError());
    }

    public function isRepollable()
    {
        return $this->hasResponse() && !$this->isAccept();
    }

    /**
     * @return bool
     */
    public function isViewableType()
    {
        return in_array(
            $this->getFormIdentifier(),
            [
                FormSubmission::TYPE_OFFICER_RESIGNATION,
                FormSubmission::TYPE_OFFICER_APPOINTMENT,
                FormSubmission::TYPE_CHANGE_REGISTERED_OFFICE_ADDRESS,
                FormSubmission::TYPE_COMPANY_INCORPORATION,
            ]
        );
    }

    /**
     * @return bool
     */
    public function isViewable()
    {
        return $this->isAccept() && $this->isViewableType();
    }

    /**
     * @return bool
     */
    public function isNull()
    {
        return $this->getResponseMessage() == NULL;
    }

    public function getSubmitter(): string
    {
        $submitter = $this->formSubmission->getSubmitter();
        if (empty($submitter)) {
            return '';
        } elseif ($submitter == 'cron') {
            return 'System';
        } elseif (is_numeric($submitter)) {
            return sprintf('Customer (%d)', $submitter);
        } else {
            return sprintf('Staff/Admin - %s', $submitter);
        }
    }
}
