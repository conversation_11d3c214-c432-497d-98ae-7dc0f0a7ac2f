<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <defaults public="true" />
        <service id="people_with_significant_control_module.providers.psc_choices_provider" class="PeopleWithSignificantControl\Providers\PscChoicesProvider">
            <argument>%people_with_significant_control_module.group_names.underscored%</argument>
        </service>

        <service id="people_with_significant_control_module.providers.psc_choices_provider.camelcased" class="PeopleWithSignificantControl\Providers\PscChoicesProvider">
            <argument>%people_with_significant_control_module.group_names.camel_cased%</argument>
        </service>


        <service class="PeopleWithSignificantControlModule\FormSubmissions\BaseTypes\Notifications\PersonTypes\PersonTypeFactory" id="people_with_significant_control_module.form_submissions.base_types.notifications.person_types.individual_factory">
            <argument type="service" id="companies_house_module.form_submissions.base_types.service_address_factory"/>
        </service>

        <service class="PeopleWithSignificantControlModule\Facades\PscNotificationFacade" id="people_with_significant_control_module.facades.psc_notification_facade">
            <argument type="service" id="form_submission_module.providers.old_form_submission_provider"/>
            <argument type="service" id="people_with_significant_control_module.form_submissions.base_types.notifications.person_types.individual_factory"/>
            <argument type="service" id="id_module.verification.id_validator"/>
            <argument type="service" id="id_module.deciders.company_validation_decider"/>
            <argument type="service" id="error.loggers.monolog"/>
        </service>
        <service class="PeopleWithSignificantControlModule\Facades\PscChangeDetailsFacade" id="people_with_significant_control_module.facades.psc_change_details_facade">
            <argument type="service" id="form_submission_module.providers.old_form_submission_provider"/>
            <argument type="service" id="people_with_significant_control_module.form_submissions.base_types.change_details.person_types.individual_factory"/>
            <argument type="service"
                      id="people_with_significant_control_module.form_submissions.base_types.change_details.person_types.corporate_factory"/>
            <argument type="service"
                      id="people_with_significant_control_module.form_submissions.base_types.change_details.person_types.legal_person_factory"/>
        </service>
        <service class="PeopleWithSignificantControlModule\Facades\PscCessationFacade" id="people_with_significant_control_module.facades.psc_cessation_facade">
            <argument type="service" id="form_submission_module.providers.old_form_submission_provider"/>
        </service>

        <service class="PeopleWithSignificantControlModule\Helpers\AuthCodeRedirectionHelper" id="people_with_significant_control_module.helpers.auth_code_redirection_helper">
            <argument id="session" type="service"/>
            <argument id="router_module.helpers.controller_helper" type="service"/>
        </service>

        <service class="PeopleWithSignificantControlModule\Controllers\PscNotificationController" id="people_with_significant_control_module.controllers.psc_notification_controller">
            <argument id="templating_module.front_renderer" type="service"/>
            <argument id="people_with_significant_control_module.providers.psc_choices_provider.camelcased" type="service"/>
            <argument id="people_with_significant_control_module.facades.psc_notification_facade" type="service"/>
            <argument id="router_module.helpers.controller_helper" type="service"/>
            <argument id="companies_house_module.helpers.prefill_helper" type="service"/>
            <argument id="people_with_significant_control_module.helpers.auth_code_redirection_helper" type="service"/>
            <argument type="service" id="companies_house_module.views.person_view_factory"/>
            <argument type="service" id="companies_house.repositories.countries_repository"/>
            <argument type="service" id="companies_house.repositories.nationalities_repository" />
            <argument id="id_module.repositories.id_info_repository" type="service"/>
        </service>

        <service class="PeopleWithSignificantControlModule\Controllers\PscChangeDetailsController" id="people_with_significant_control_module.controllers.psc_change_details_controller">
            <argument id="templating_module.front_renderer" type="service"/>
            <argument id="people_with_significant_control_module.providers.psc_choices_provider.camelcased" type="service"/>
            <argument id="people_with_significant_control_module.facades.psc_change_details_facade" type="service"/>
            <argument id="router_module.helpers.controller_helper" type="service"/>
            <argument id="people_with_significant_control_module.helpers.auth_code_redirection_helper" type="service"/>
            <argument id="companies_house.repositories.countries_repository" type="service" />
            <argument type="service" id="companies_house.repositories.nationalities_repository"/>
            <argument id="id_module.repositories.id_info_repository" type="service"/>
        </service>

        <service class="PeopleWithSignificantControlModule\Controllers\PscCessationController" id="people_with_significant_control_module.controllers.psc_cessation_controller">
            <argument id="templating_module.front_renderer" type="service"/>
            <argument id="people_with_significant_control_module.facades.psc_cessation_facade" type="service"/>
            <argument id="router_module.helpers.controller_helper" type="service"/>
            <argument id="people_with_significant_control_module.helpers.auth_code_redirection_helper" type="service"/>
        </service>


        <service class="PeopleWithSignificantControlModule\Controllers\PscViewDetailsController" id="people_with_significant_control_module.controllers.psc_view_details_controller">
            <argument id="templating_module.front_renderer" type="service"/>
            <argument type="service" id="url_generator"/>
        </service>

        <service class="PeopleWithSignificantControlModule\Facades\PscStatementNotificationFacade" id="people_with_significant_control_module.facades.psc_statement_notification_facade">
            <argument id="form_submission_module.providers.old_form_submission_provider" type="service" />
        </service>

        <service class="PeopleWithSignificantControlModule\Facades\PscStatementWithdrawalFacade" id="people_with_significant_control_module.facades.psc_statement_withdrawal_facade">
            <argument id="form_submission_module.providers.old_form_submission_provider" type="service"/>
        </service>

        <service class="PeopleWithSignificantControlModule\Controllers\PscStatementNotificationController" id="people_with_significant_control_module.controllers.psc_statement_notification_controller">
            <argument id="templating_module.front_renderer" type="service"/>
            <argument id="people_with_significant_control_module.facades.psc_statement_notification_facade" type="service"/>
            <argument id="router_module.helpers.controller_helper" type="service"/>
            <argument id="form_submission_module.services.form_submission_service" type="service"/>
        </service>

        <service class="PeopleWithSignificantControlModule\Controllers\PscStatementWithdrawController" id="people_with_significant_control_module.controllers.psc_statement_withdraw_controller">
            <argument id="templating_module.front_renderer" type="service"/>
            <argument id="people_with_significant_control_module.facades.psc_statement_withdrawal_facade" type="service"/>
            <argument id="router_module.helpers.controller_helper" type="service"/>
            <argument id="form_submission_module.services.form_submission_service" type="service"/>
        </service>

<!--        <service class="PeopleWithSignificantControlModule\Converters\OldPscMemberConverter" id="people_with_significant_control_module.converters.old_psc_member_converter">-->
<!--            <argument type="service" id="dibi_lpl"/>-->
<!--            <argument type="service" id="services.company_service"/>-->
<!--            <argument type="service" id="user_module.services.customer_availability"/>-->
<!--            <tag name="router.argument_converter" supports="old_psc_member"/>-->
<!--        </service>-->

        <service class="PeopleWithSignificantControlModule\Converters\Admin\OldPscMemberConverter" id="people_with_significant_control_module.converters.admin.old_psc_member_converter">
            <argument id="dibi" type="service"/>
            <argument id="services.company_service" type="service"/>
            <tag name="router.argument_converter" supports="admin.old_psc_member"/>
        </service>


        <service class="SerializingModule\Serializer" id="people_with_significant_control_module.serializer">
            <argument id="companies_house_module.serializer.builder" type="service"/>
            <call method="addMetadataDir">
                <argument>%people_with_significant_control_module.ch_serializer_config_dir%/FormSubmissions/BaseTypes</argument>
                <argument>CompaniesHouseModule\FormSubmissions\BaseTypes</argument>
            </call>
            <call method="addMetadataDir">
                <argument>%people_with_significant_control_module.serializer_config_dir%/FormSubmissions/Forms</argument>
                <argument>PeopleWithSignificantControlModule\FormSubmissions\Forms</argument>
            </call>
            <call method="addMetadataDir">
                <argument>%people_with_significant_control_module.serializer_config_dir%/FormSubmissions/BaseTypes</argument>
                <argument>PeopleWithSignificantControlModule\FormSubmissions\BaseTypes</argument>
            </call>
            <call method="addMetadataDir">
                <argument>%people_with_significant_control_module.serializer_config_dir%/FormSubmissions/BaseTypes/Notifications/PersonTypes</argument>
                <argument>PeopleWithSignificantControlModule\FormSubmissions\BaseTypes\Notifications\PersonTypes</argument>
            </call>
            <call method="addMetadataDir">
                <argument>%people_with_significant_control_module.serializer_config_dir%/FormSubmissions/BaseTypes/ChangeDetails</argument>
                <argument>PeopleWithSignificantControlModule\FormSubmissions\BaseTypes\ChangeDetails</argument>
            </call>
            <call method="addMetadataDir">
                <argument>%people_with_significant_control_module.serializer_config_dir%/FormSubmissions/BaseTypes/PSCStatementNotifications</argument>
                <argument>PeopleWithSignificantControlModule\FormSubmissions\BaseTypes\PSCStatementNotifications</argument>
            </call>
            <call method="addMetadataDir">
                <argument>%people_with_significant_control_module.serializer_config_dir%/FormSubmissions/BaseTypes/ChangeDetails/PersonTypes</argument>
                <argument>PeopleWithSignificantControlModule\FormSubmissions\BaseTypes\ChangeDetails\PersonTypes</argument>
            </call>
            <call method="addMetadataDir">
                <argument>%people_with_significant_control_module.serializer_config_dir%/FormSubmissions/BaseTypes/ChangeDetails/Changes</argument>
                <argument>PeopleWithSignificantControlModule\FormSubmissions\BaseTypes\ChangeDetails\Changes</argument>
            </call>
            <call method="addMetadataDir">
                <argument>%people_with_significant_control_module.serializer_config_dir%/FormSubmissions/BaseTypes/Cessations/PersonTypes</argument>
                <argument>PeopleWithSignificantControlModule\FormSubmissions\BaseTypes\Cessations\PersonTypes</argument>
            </call>
        </service>

        <service class="CompaniesHouseModule\Serializers\PartialXmlSerializer" id="people_with_significant_control_module.partial_xml_serializer">
            <argument id="people_with_significant_control_module.serializer" type="service"/>
        </service>

        <service class="PeopleWithSignificantControlModule\FormSubmissions\BaseTypes\ChangeDetails\PersonTypes\IndividualFactory" id="people_with_significant_control_module.form_submissions.base_types.change_details.person_types.individual_factory">
            <argument id="companies_house_module.form_submissions.base_types.service_address_factory" type="service"/>
        </service>
        <service class="PeopleWithSignificantControlModule\FormSubmissions\BaseTypes\ChangeDetails\PersonTypes\CorporateFactory" id="people_with_significant_control_module.form_submissions.base_types.change_details.person_types.corporate_factory">
            <argument id="companies_house_module.form_submissions.base_types.service_address_factory" type="service"/>
        </service>
        <service class="PeopleWithSignificantControlModule\FormSubmissions\BaseTypes\ChangeDetails\PersonTypes\LegalPersonFactory" id="people_with_significant_control_module.form_submissions.base_types.change_details.person_types.legal_person_factory">
            <argument id="companies_house_module.form_submissions.base_types.service_address_factory" type="service"/>
        </service>

        <service class="PeopleWithSignificantControl\Forms\Old\PscFormFactory" id="people_with_significant_control.forms.old.psc_form_factory">
            <argument id="people_with_significant_control_module.providers.psc_choices_provider" type="service"/>
            <argument id="symfony.router" type="service"/>
            <argument id="company_module.providers.our_service_address_provider" type="service"/>
            <argument id="companies_house.repositories.countries_repository" type="service"/>
            <argument id="companies_house.repositories.nationalities_repository" type="service"/>
        </service>

        <service class="PeopleWithSignificantControlModule\Controllers\Admin\PscNotificationController" id="people_with_significant_control_module.controllers.admin.psc_notification_controller">
            <argument type="service" id="templating_module.renderers.admin_renderer" />
            <argument type="service" id="router_module.helpers.controller_helper" />
            <argument type="service" id="companies_house_module.helpers.prefill_helper"/>
            <argument type="service" id="people_with_significant_control_module.providers.psc_choices_provider.camelcased" />
            <argument type="service" id="people_with_significant_control_module.facades.psc_notification_facade"/>
            <argument type="service" id="companies_house_module.views.person_view_factory"/>
            <argument type="service" id="companies_house.repositories.countries_repository" />
            <argument type="service" id="companies_house.repositories.nationalities_repository" />
        </service>

        <service class="PeopleWithSignificantControlModule\Controllers\Admin\PscChangeDetailsController" id="people_with_significant_control_module.controllers.admin.psc_change_details_controller">
            <argument id="templating_module.renderers.admin_renderer" type="service"/>
            <argument id="router_module.helpers.controller_helper" type="service"/>
            <argument id="people_with_significant_control_module.providers.psc_choices_provider.camelcased" type="service"/>
            <argument id="people_with_significant_control_module.facades.psc_change_details_facade" type="service"/>
            <argument id="companies_house.repositories.countries_repository" type="service" />
            <argument id="companies_house.repositories.nationalities_repository" type="service"/>
            <argument id="id_module.repositories.id_info_repository" type="service"/>
        </service>

        <service class="PeopleWithSignificantControlModule\Controllers\Admin\PscCessationController" id="people_with_significant_control_module.controllers.admin.psc_cessation_controller">
            <argument id="templating_module.renderers.admin_renderer" type="service"/>
            <argument id="router_module.helpers.controller_helper" type="service"/>
            <argument id="people_with_significant_control_module.facades.psc_cessation_facade" type="service"/>
        </service>

        <service class="PeopleWithSignificantControlModule\Controllers\Admin\PscViewController" id="people_with_significant_control_module.controllers.admin.psc_view_controller">
            <argument id="templating_module.renderers.admin_renderer" type="service"/>
            <argument id="router_module.helpers.controller_helper" type="service"/>
            <argument id="people_with_significant_control_module.facades.psc_cessation_facade" type="service"/>
        </service>

        <service class="PeopleWithSignificantControlModule\Controllers\Admin\PscStatementNotificationController" id="people_with_significant_control_module.controllers.admin.psc_statement_notification_controller">
            <argument id="templating_module.renderers.admin_renderer" type="service"/>
            <argument id="router_module.helpers.controller_helper" type="service"/>
            <argument id="people_with_significant_control_module.facades.psc_statement_notification_facade" type="service"/>
        </service>

        <service class="PeopleWithSignificantControlModule\Controllers\Admin\PscStatementWithdrawController" id="people_with_significant_control_module.controllers.admin.psc_statement_withdraw_controller">
            <argument id="templating_module.renderers.admin_renderer" type="service"/>
            <argument id="router_module.helpers.controller_helper" type="service"/>
            <argument id="people_with_significant_control_module.facades.psc_statement_withdrawal_facade" type="service"/>
        </service>

        <service class="PeopleWithSignificantControlModule\Validator\Constraints\NatureOfControlsValidator" id="people_with_significant_control_module.symfony_forms.constraints.nature_of_controls_validator">
            <argument type="service" id="people_with_significant_control_module.providers.psc_choices_provider"/>
            <tag name="validator.constraint_validator" alias="people_with_significant_control_module.forms.validators.constraints.nature_of_controls_validator" />
        </service>

        <service class="PeopleWithSignificantControlModule\Providers\EmailProvider" id="people_with_significant_control_module.providers.email_provider">
            <argument id="email_module.loaders.front_matter_template_loader" type="service"/>
            <argument id="user_module.creators.one_time_password_auth_token_creator" type="service"/>
            <argument id="content_module.repositories.node_repository" type="service"/>
            <argument>%people_with_significant_control_module.sweeper_email.template_path%</argument>
        </service>

        <service class="PeopleWithSignificantControlModule\Commands\PscSweeperCommand" id="people_with_significant_control_module.commands.psc_sweeper_command">
            <argument type="service" id="company_module.repositories.company_repository"/>
            <argument type="service" id="people_with_significant_control_module.providers.email_provider"/>
            <argument type="service" id="marketing_module.emailers.company_emailer"/>
            <argument type="service" id="cron.loggers.default_logger" />
            <argument type="service" id="email_module.repositories.predefined_email_repository"/>
            <tag name="cron.command" command-name="psc:sweeper" action="execute"/>
        </service>

        <service class="PeopleWithSignificantControlModule\Providers\NatureOfControlTextProvider" id="people_with_significant_control_module.providers.nature_of_control_text_provider"/>
    </services>
</container>
