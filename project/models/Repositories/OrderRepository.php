<?php

declare(strict_types=1);

namespace Repositories;

use Doctrine\DBAL\Exception;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Internal\Hydration\IterableResult;
use Doctrine\ORM\Query\ResultSetMappingBuilder;
use Doctrine\ORM\QueryBuilder;
use Entities\Customer;
use Entities\Order;
use Entities\OrderItem;
use Entities\Transaction;
use Exceptions\Business\ItemNotFound;

class OrderRepository extends BaseRepository_Abstract
{
    /**
     * @param \DateTime $dtc
     *
     * @return IterableResult
     */
    public function getLaterThan(\DateTime $dtc)
    {
        $qb = $this->_em->createQueryBuilder();
        $qb->select('i')->from('Entities\Order', 'i');
        $qb->where('i.dtc > ?1')->setParameter(1, $dtc, Types::DATETIME_MUTABLE);

        return $qb->getQuery()->iterate();
    }

    /**
     * @return QueryBuilder
     */
    public function getListBuilder()
    {
        $qb = $this->_em->createQueryBuilder();
        $qb->select('o')->from(Order::class, 'o')
            ->leftJoin('o.transactions', 't')
            ->orderBy('o.orderId', 'DESC');

        return $qb;
    }

    public function getCustomerOrder(Customer $customer, int $orderId): Order
    {
        $order = $this->findOneBy(['customer' => $customer, 'orderId' => $orderId]);
        if (!$order) {
            throw new ItemNotFound(sprintf('Customer `%d` does not have order ID `%d`', $customer->getId(), $orderId));
        }

        return $order;
    }

    public function getOrdersFromCustomer(Customer $customer): QueryBuilder
    {
        return $this->createSimpleBuilder()
            ->select('DISTINCT o.orderId, o.dtc, o.vat, o.subTotal, o.total, o.paymentMediumId, o.description, c.companyName, c.companyId, t.transactionId, t.orderCode, t.typeId')
            ->from(Order::class, 'o')
            ->leftJoin('o.transactions', 't')
            ->leftJoin('o.items', 'oi')
            ->leftJoin('oi.company', 'c')
            ->where('o.customer = :customer')
            ->setParameter('customer', $customer)
            ->orderBy('o.dtc', 'DESC');
    }

    public function getCountOrdersFromCustomer(Customer $customer): int
    {
        return $this->_em->createQueryBuilder()
            ->select('count(o.orderId)')
            ->from(Order::class, 'o')
            ->where('o.customer = :customer')
            ->setParameter('customer', $customer)
            ->getQuery()
            ->getSingleScalarResult();
    }

    public function companyOrderHasItems(int $companyId, array $items): bool
    {
        $qb = $this->_em->createQueryBuilder();
        $qb->select('i')->from(OrderItem::class, 'i')
            ->where('i.company = :companyId')
            ->andWhere('i.productId IN (:items)')
            ->setParameter('companyId', $companyId)
            ->setParameter('items', $items);

        return (bool) $qb->getQuery()->getResult();
    }

    /**
     * @throws Exception
     */
    public function getDirectDebitOrdersWithOnlyFailedTransactions(): array
    {
        $sql = <<<SQL
            SELECT o.*
            FROM cms2_orders o
            WHERE NOT EXISTS (
                SELECT 1
                FROM cms2_transactions t
                WHERE t.orderId = o.orderId
                  AND t.statusId != :failedStatusId
            )
            AND EXISTS (
                SELECT 1
                FROM cms2_transactions t
                WHERE t.orderId = o.orderId
                AND t.typeId = :directDebitTypeId
                AND t.statusId = :failedStatusId
                AND t.retryAt IS NOT NULL
                AND t.retryAt <= CURDATE()
            )
            ORDER BY o.orderId DESC;
        SQL;

        $entityManager = $this->getEntityManager();
        $rsm = new ResultSetMappingBuilder($entityManager);
        $rsm->addRootEntityFromClassMetadata(Order::class, 'o');

        $query = $entityManager->createNativeQuery($sql, $rsm);
        $query->setParameter('failedStatusId', Transaction::STATUS_FAILED);
        $query->setParameter('directDebitTypeId', Transaction::TYPE_OMNIPAY_DIRECT_DEBIT);

        return $query->getResult();
    }
}
