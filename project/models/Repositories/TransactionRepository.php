<?php

namespace Repositories;

use BusinessServicesModule\Entities\Offer;
use DateTime;
use Doctrine\ORM\AbstractQuery;
use Doctrine\ORM\Internal\Hydration\IterableResult;
use DataGrid\src\DataGrid\Doctrine\DoctrineDataSource;
use Doctrine\ORM\Query\ResultSetMappingBuilder;
use DoctrineModule\SelfClearingIterator;
use Entities\Customer;
use Entities\Transaction;
use Entities\Order;
use Iterator;
use OmnipayModule\Services\DirectDebitService;
use OrmModule\Iterators\DoctrineIterator;
use Exception;
use Repositories\BaseRepository_Abstract;

class TransactionRepository extends BaseRepository_Abstract
{

    /**
     * @param \DateTime $dtc
     * @return IterableResult
     */
    public function getLaterThan(\DateTime $dtc)
    {
        $qb = $this->_em->createQueryBuilder();
        $qb->select('i')->from('Entities\Transaction', 'i');
        $qb->where('i.dtc > ?1')->setParameter(1, $dtc, \Doctrine\DBAL\Types\Types::DATETIME_MUTABLE);
        $iterator = $qb->getQuery()->iterate();
        return $iterator;
    }

    /**
     * @param Customer $customer
     * @return array
     */
    public function getTransactions(Customer $customer)
    {
        $qb = $this->_em->createQueryBuilder()
            ->select('i')->from('Entities\Transaction', 'i')
            ->where('i.customer = :customer')
            ->setParameter('customer', $customer);
        $rows = $qb->getQuery()->getResult();
        return $rows;
    }

    /**
     * @param Customer $customer
     * @param string $cardNumber
     * @return SelfClearingIterator|Transaction[]
     */
    public function getSuccessTransactionsForCard(Customer $customer, $cardNumber)
    {
        $qb = $this->createQueryBuilder('t');
        $qb->where('t.customer = :customer')
            ->andWhere('t.statusId = :status')
            ->andWhere('t.cardNumber = :cardNumber')
            ->andWhere('t.typeId = :type')
            ->orderBy('t.transactionId', 'DESC')
            ->setParameter('status', Transaction::STATUS_SUCCEEDED)
            ->setParameter('cardNumber', $cardNumber)
            ->setParameter('type', Transaction::TYPE_SAGEPAY)
            ->setParameter('customer', $customer);
        return new SelfClearingIterator($qb);
    }

    public function getLastSuccessTransactionForCard(Customer $customer, string $cardNumber): ?Transaction
    {
        return $this->createQueryBuilder('t')
            ->where('t.customer = :customer')
            ->andWhere('t.statusId = :status')
            ->andWhere('t.cardNumber = :cardNumber')
            ->andWhere('t.typeId = :type')
            ->orderBy('t.transactionId', 'DESC')
            ->setMaxResults(1)
            ->setParameters(
                [
                    'status' => Transaction::STATUS_SUCCEEDED,
                    'cardNumber' => $cardNumber,
                    'type' => Transaction::TYPE_SAGEPAY,
                    'customer' => $customer

                ]
            )
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * @return Transaction[]
     */
    public function getTransactionsForCustomerInLastHour(Customer $customer): iterable
    {
        $qb = $this->createQueryBuilder('t');
        $qb->where('t.customer = :customer')
            ->andWhere('t.dtc >= :date')
            ->orderBy('t.transactionId', 'DESC')
            ->setParameter('customer', $customer)
            ->setParameter('date', (new DateTime())->modify('-1 hour'));

        return new DoctrineIterator($qb);
    }

    /**
     * @return DoctrineDataSource
     */
    public function getTransactionDataSource()
    {
        $qb = $this->createQueryBuilder('t')
            ->leftJoin('t.customer', 'c')
            ->addSelect('c')
            ->orderBy('t.transactionId', 'DESC');
        return new DoctrineDataSource($qb);
    }

    public function getNonRefundedTransactionByOrder(int $orderId)
    {
        $qb = $this->_em->createQueryBuilder()
            ->select('i')->from('Entities\Transaction', 'i')
            ->innerJoin(Order::class, 'o')
            ->where('i.order = o.orderId')
            ->andWhere('i.order = :orderId')
            ->andWhere('o.isRefunded is null')
            ->setParameter('orderId', $orderId);
        return $qb->getQuery()->getOneOrNullResult();
    }

    public function getTransactionByOrderId(int $orderId): ?Transaction
    {
        $qb = $this->_em->createQueryBuilder()
            ->select('i')->from('Entities\Transaction', 'i')
            ->where('i.order = :orderId')
            ->orderBy('i.dtc', 'desc')
            ->setMaxResults(1)
            ->setParameter('orderId', $orderId);
        return $qb->getQuery()->getOneOrNullResult();
    }

    public function getTransactionByOrderCode(string $orderCode): ?Transaction
    {
        $qb = $this->_em->createQueryBuilder()
            ->select('i')->from('Entities\Transaction', 'i')
            ->where('i.orderCode = :orderCode')
            ->setParameter('orderCode', $orderCode);
        return $qb->getQuery()->getOneOrNullResult();
    }

    public function findOneByOrderCode(string $orderCode): ?Transaction
    {
        $qb = $this->createQueryBuilder('o')
            ->where('o.orderCode = :orderCode')
            ->setParameter('orderCode', $orderCode)
            ->setMaxResults(1);

        return $qb->getQuery()->getOneOrNullResult();
    }

    public function getFailedDirectDebitTransactionsWithExhaustedAttempts(): array
    {
        $sql = <<<SQL
            SELECT t.* FROM cms2_transactions t
            LEFT JOIN cms2_orders o ON o.orderId = t.orderId
            LEFT JOIN cms2_events e ON e.objectId = t.orderId
            WHERE e.eventKey = :notifyAdminEventKey
             AND NOT EXISTS (
                SELECT 1
                FROM cms2_transactions t
                WHERE t.orderId = o.orderId
                AND t.statusId != :failedStatusId
            )
            GROUP BY t.orderId
            ORDER BY t.dtc DESC
        SQL;

        $entityManager = $this->getEntityManager();
        $rsm = new ResultSetMappingBuilder($entityManager);
        $rsm->addRootEntityFromClassMetadata(Transaction::class, 't');

        $query = $entityManager->createNativeQuery($sql, $rsm);
        $query->setParameter('notifyAdminEventKey', DirectDebitService::FAILED_DIRECT_DEBIT_NOTIFY_ADMIN_EVENT);
        $query->setParameter('failedStatusId', Transaction::STATUS_FAILED);

        return $query->getResult();
    }
}
