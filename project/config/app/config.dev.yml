imports:
  - {resource: "%root%/project/DataGeneration/Config/config.yml"}
  - {resource: "%root%/project/DevelopmentModule/Config/config.yml"}
  - {resource: "%root%/project/SmokeTestsModule/Config/config.yml"}

services:
  - "%root%/tests/dummy.xml"
  - "%root%/tests/dev.xml"
  - "%root%/tests/tests.xml"

database:
  username: cms_dev
  database: cms_dev

cms_mirror_database:
  username: cms_dev
  database: cms_dev

vo_mirror_database:
  user: vo_dev
  dbname: vo_dev

debug_panel:
  show_queries: true
  show_cache: true

cron:
  lock_context: "cron_dev_lock"

# CHFiling
chfiling:
  packageReference: '0012'
  senderId: '66662231000'
  emailAddress: <EMAIL>
  gatewayTest: 1
  doc_path: '%root%/project/temp/upload/ch_documents'
#  doc_path: cms://cms-dev/project/temp/upload/ch_documents

# sagepay
sage:
  server: TEST
  system: TEST
  rest_api:
    base_url: https://pi-test.sagepay.com
    repeat_transaction_endpoint: /api/v1/transactions

# paypal
paypal:
  apiUsername: diviak_1225732123_biz_api1.gmail.com
  apiSignature: AFcWxV21C7fd0v3bYYYRCpSSRl31Ayz-Eu1HLHcxMBoaUd3QIGjfzkDn
  environment: sandbox

# tax assist lead
taxassist:
  host: localhost
  username: cms_dev

# notifier
notifier:
  url:  "http://test.csms/webservices/v3/notifier/"
  defaultParameters:

google_services:
  service_account_name: <EMAIL>
  service_scopes:
    - https://www.googleapis.com/auth/drive
  key_file_path: "%root%/storage/keys/google_services_key.p12"
  service_name: msg_google_service

host: cms

barclays:
  token_url: https://login.salesforce.com/services/oauth2/token
  test_token_url: https://test.salesforce.com/services/oauth2/token
  submission_path: /services/apexrest/BBALead
  gateway_test: 1
  test:
    consumer_key: "3MVG9X0_oZyBSzHqRCtOI2D_4IH4Ppy3I4Db9x8BzWfpJW3kjeuhn0jg8crq2.lJliC2q5WYzVmAgSNgetfdh"
    consumer_secret: "2450229902414040928"
    username: "<EMAIL>.bukb2buat"
    password: "RuHeHtz*k4v=M7lnRZmRLpEwdJ8,xw"

worldpay_lead_loading_api:
  username:
  password:
  host: http://wpleadsg-test.worldpay.com
  ta_code: 2235

takepayments:
  email_address: <EMAIL>
  export_dir: temp/ #"gs://takepayments-msg/leads"

# for docker
trusted_proxies:
  - 0.0.0.0/0

smarty:
  force_compile: true

cache:
  enabled: true
  memory:
    type: array

symfony_forms:
  cache:
    type: array

template_variables:
  cache:
    type: array

doctrine:
  cache:
    type: array

notifications:
  cache:
    type: array

id3global:
  wsdl: https://pilot.Id3Global.com/ID3gWS/Id3Global.svc?wsdl
  profile:
    UK_COMBINED:
      id: fa2db688-c938-477b-9439-607b97148f74
      version: 1.1

efiling:
  api:
    base_url: http://31fefc.stage.web.efiling.co.uk
    version: 2

hide_external_js: true

id_documents:
  upload_dir: "%root%/temp/document-storage"

tide:
  auth:
    api_key: 2a4gKL2Gs9&1-GlN
  api:
    base_url: https://api-staging.tide.co

mailgun:
  domains:
    cms: mg.msg.cool
    marketing: mg.msg.cool
    tsb_business: mg.msg.cool
    cool: mg.msg.cool

vo_api:
  base_url: "http://dev.vo/api/"
  basicAuth:
    username: stan
    password: 123456

session:
  save_handler: files
  save_path: "%root%/temp/sessions"

blog_feed:
    reader:
      filepath: http://%host%/webtemp/cms_blog.xml

google_optimize:
  code: GTM-54HBLK5

toolkit_offers:
  namesco:
    google_drive_directory_id: '1SyqYYJG1xLEXNnuUpVc5n8z8mJh0rUi6'

business_services:
  cache:
    type: array

tsb_banking:
  zip:
    password: SuppaDuppaPassword

business_data:
  enabled_from: '2021-02-24'
  enabled_to: '2030-01-01'
  api:
    base_url: https://stage-api.bdgroup.co.uk
    credentials:
      client_id: <EMAIL>
      client_secret: c0bd89ee251c9df20d0a9cd039e373b7

get_address:
  api:
    key: gMhlRwQNUEGprdiuiFUBpg24252

mettle:
  api:
    url: 'https://api.eevee.stg-mettle.co.uk/'
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************************************************************************************************************.Wg-bqq5v3ksqGQ-b2zKtUaOuWh9j7vuLRPOz-5Elki4'

mantle:
  api:
    url: 'https://europe-west1-madesimplegroup-151616.cloudfunctions.net/'
    token: 'bWFudGxlLXJlbG9hZGVkOjExM2I3MWY1YmZkMjU3NzQ3MWE1MDQzYmU1MjBlZmFk'

mailroom:
  api:
    url: 'https://mailroom-staging-yscx6r3plq-ew.a.run.app/api/'
    token: '==hjqvwZ5k=8u=)5=QWYAF@x($orYK3@__^yRi9x!09(1MTY3M7$v85ST5$E'

big_query_credentials:
  projectId: madesimplegroup-151616
  environment: test
  keyFilePath: "%root%/storage/keys/test/bigquery.json"
  retries: 2

money_penny:
  api:
    url: https://test-api.moneypenny.com
    headers:
      auth: 37b61e78d68c4d5c9bb11d53745a87e0
  charge_api:
    stripe:
      account: acct_1LFFahGMUy8IDa5X

firebase:
  auth:
    project_id: msg-dev-debug-customer-auth
    api_key: AIzaSyAo0aaX66DcOCtXzwIl9azbsq2wXRSwbu0
    emulator_url: 'http://firebase:2095/identitytoolkit.googleapis.com/v1/accounts:'

omnipay:
  username: api-key
  password: 58e3f5dd9180a612721bab3e75d..0b469
  token_password: cuHwuw-6wyrky-gavfih
  url: https://dev-omnipay.companiesmadesimple.com/
  #url: 'http://host.docker.internal:3000/'
  #url: 'http://host.docker.internal:3000/'
  local_component_url: #only use this if you are running omnipay locally
  stripe_public_key: pk_test_51JxEzjJXCdOXyGnofuHN22x1AJelReNPVyfRyY5iAbpVswQuoHEVDLny0utpX7m2jJMhSoUggiMzBhaX9w1Vfn2K00jel7nUgp
  stripe_direct_debit_enabled: true

env:
  environment: staging

ghost:
  url: https://cms.companiesmadesimple.com/ghost/api/
  key: 622d13a6d448f2f8d207885949

credas:
  url: https://portal.credasdemo.com/api/
  api_key: MWQyNGE5NWQtNzgyNC00NzM0LTllMDQtMjNiNjZkYTVkNzM0
  wholesale_api_key: MWQyNGE5NWQtNzgyNC00NzM0LTllMDQtMjNiNjZkYTVkNzM0
  user_group_id: null
  wholesale_user_group_id: null
  journeys:
    CREDAS_HIGH_RISK:
      journeyId: '9d3ffca0-b096-4653-a8ad-3a07955cb2d1'
      actorId: 380
    CREDAS_BASIC_NON_UK:
      journeyId: '9d3ffca0-b096-4653-a8ad-3a07955cb2d1'
      actorId: 380
    CREDAS_BASIC_UK:
      journeyId: '36f46d2d-8e51-4645-8970-efe2aaf61ca6'
      actorId: 324
    CREDAS_CORPORATE_ENTITY:
      journeyId: '93a8e7bc-87b4-4b23-b31a-94a305230102'
      actorId: 435
  wholesaleJourneys:
    CREDAS_HIGH_RISK:
      journeyId: '9d3ffca0-b096-4653-a8ad-3a07955cb2d1'
      actorId: 380
    CREDAS_BASIC_NON_UK:
      journeyId: '9d3ffca0-b096-4653-a8ad-3a07955cb2d1'
      actorId: 380
    CREDAS_BASIC_UK:
      journeyId: '36f46d2d-8e51-4645-8970-efe2aaf61ca6'
      actorId: 324
    CREDAS_CORPORATE_ENTITY:
      journeyId: '93a8e7bc-87b4-4b23-b31a-94a305230102'
      actorId: 435

show_clarity: false
show_sentry: false

google_tag_manager:
  code: 'GTM-MMJQC7FT'

sentry:
  enabled: false

google_analytics:
  api:
    secret: 'l3MD2CHvQr6iXtM96qcVTw'
    measurement_id: 'G-HLTC5W11JW'
    url: 'https://www.google-analytics.com/mp/collect?'

pubsub:
  enabled: false
  secret: '123456'
  projectId: 'dev-companiesmadesimple'
  keyFilePath: '%root%/storage/keys/pubsub.json'
  topics:
    webhook: 'projects/dev-companiesmadesimple/topics/cms-webhook-events'
    pull: 'projects/dev-companiesmadesimple/topics/cms-pull-events'
  subscriptions:
    webhook: 'projects/dev-companiesmadesimple/subscriptions/cms-webhook-events'
    pull: 'projects/dev-companiesmadesimple/subscriptions/cms-pull-events'
  failedMessages:
    table: 'pubsub.failed_messages'

cashplus:
  productName: CashplusBusPremier

strapi:
  cache:
    business_services:
      type: array