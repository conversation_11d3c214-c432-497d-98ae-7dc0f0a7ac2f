styles:
    common:
        - dist/legacy-common.css

    xdna:
        - dist/css/xdna.css

    numbers:
        - dist/css/numbers.css

    international_packages_page:
        - dist/css/international_packages_page.css

    responsive:
        - dist/legacy-responsive.css

    transition:
        - dist/legacy-transition.css

    formation_sic_step: #must be here as it overrides datatables style from the responsive section
        - dist/legacy-formation.css

    formation_common_steps:
        - dist/legacy-formation.css

    formation_toolkit_step:
        - dist/legacy-formation.css

    formation:
        - dist/css/formation.css

    modal:
        - dist/css/modal.css

    secondary:
        - dist/css/secondary.css

    main:
        - dist/css/main.css

scripts:
    common:
        files:
            - dist/legacy-common.js
        remote:
            - https://apis.google.com/js/plusone.js

    orderSummary:
        files:
            - front/js/OrderSummaryForm.js

    responsive:
        files:
           - dist/legacy-responsive.js

    formation_sic_step:
        files:
            - front/js/backwards_compatibility/old-tooltip.js

    modal:
        files: []

    formation_toolkit_step:
        files:
            - front/js/backwards_compatibility/old-tooltip.js

    secondary:
        files:
            - dist/secondary.bundle.js

    tertiary:
        files:
            - dist/tertiary.bundle.js
            - components/ui_server/js/payment.js

    main:
        files:
            - dist/1.bundle.js
            - dist/main.bundle.js

