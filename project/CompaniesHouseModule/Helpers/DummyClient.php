<?php

namespace CompaniesHouseModule\Helpers;

use Libs\CHFiling\Core\Exceptions\AuthenticationCodeException;
use CompaniesHouseModule\Repositories\MemberRepository;
use CompanyFormationModule\Entities\IMember;
use CompanyFormationModule\Repositories\MemberRepository as IncorporationMemberRepository;
use Entities\Company;
use Repositories\CompanyRepository;
use SimpleXMLElement;
use Symfony\Component\HttpFoundation\Request;
use TemplateModule\ITemplateEngine;
use TemplateModule\PathContent;
use Utils\File;
use function random_int;
use function simplexml_load_string;

final class DummyClient
{
    /**
     * @var Request
     */
    private $request;

    /**
     * @var CompanyRepository
     */
    private $companyRepository;

    /**
     * @var ITemplateEngine
     */
    private $engine;

    /**
     * @var MemberRepository
     */
    private $memberRepository;

    /**
     * @var IncorporationMemberRepository
     */
    private $incorporationMemberRepository;

    /**
     * @var string
     */
    private $companyNumber;

    public function __construct(
        Request $request,
        CompanyRepository $companyRepository,
        ITemplateEngine $engine,
        MemberRepository $memberRepository,
        IncorporationMemberRepository $incorporationMemberRepository
    ) {
        $this->request = $request;
        $this->companyRepository = $companyRepository;
        $this->engine = $engine;
        $this->memberRepository = $memberRepository;
        $this->incorporationMemberRepository = $incorporationMemberRepository;
    }

    public function send(string $request): string
    {
        $envelope = simplexml_load_string($request);
        if (!isset($envelope->Body->FormSubmission->Form)) {
            $xml = $envelope->Body->children();
        } else {
            $xml = $envelope->Body->FormSubmission->Form->children();
        }
        switch ($xml->getName()) {
            case 'GetSubmissionStatus':
                return $this->handleGetSubmissionStatus($xml->GetSubmissionStatus);
            case 'OfficerAppointment':
            case 'CompanyIncorporation':
                return $this->handleIncorporation($xml->CompanyIncorporation);
            case 'GetDocument':
                return $this->handleGetDocument($xml->GetDocument);
            case 'CompanyDataRequest':
                return $this->handleSync($xml->CompanyDataRequest);
        }
        return '';
    }

    private function handleIncorporation(SimpleXMLElement $element)
    {
        if ($this->request->query->has('failIncorporationRequest')) {
            return $this->responseByType(
                $this->request->query->get('failIncorporationRequest'),
                'Test incorporation. Error: 503'
            );
        }
        return $this->load('CompanyIncorporation.xml');
    }

    private function handleSync(SimpleXMLElement $element)
    {
        if ($this->request->query->has('failSyncRequest')) {
            return $this->responseByType(
                $this->request->query->get('failSyncRequest'),
                'Test sync. Error: 503'
            );
        }


        $companyNumber = (string)$element->CompanyNumber;
        /** @var Company $company */
        $company = $this->companyRepository->optionalOneBy(['companyNumber' => $companyNumber]);

        if (!$company) {
            return $this->generateImport($companyNumber);
        }

        if (!$company->hasAuthenticationCode()) {
            throw new AuthenticationCodeException('Dummy authentication code not provided');
        }

        $template = 'default.xml';
        if ($this->request->query->has('template')) {
            $template = $this->request->query->get('template');
        }
        return $this->generateSync($company, $template);
    }

    private function handleGetDocument(SimpleXMLElement $element)
    {
        if ($this->request->query->has('failDocumentRequest')) {
            return $this->responseByType(
                $this->request->query->get('failDocumentRequest'),
                'Test document. Error: 503'
            );
        }
        return $this->load(
            'Document.xml',
            [
                '{COMPANY_NUMBER}' => $this->companyNumber,
                '{DOCUMENT_DATE}' => date('Y-m-d')
            ]
        );
    }

    private function handleGetSubmissionStatus(SimpleXMLElement $element): string
    {
        if ($this->request->query->has('failSubmissionRequest')) {
            return $this->responseByType(
                $this->request->query->get('failSubmissionRequest'),
                'Test get submission. Error: 503'
            );
        }
        $submissionData = ['{SUBMISSION_NUMBER}' => (string)$element->SubmissionNumber];
        if ($this->request->query->has('pending')) {
            return $this->load(
                'GetSubmissionStatus/Pending.xml',
                $submissionData
            );
        } else if ($this->request->query->has('reject')) {
            return $this->load(
                'GetSubmissionStatus/Reject.xml',
                $submissionData
            );
        } else {
            $this->companyNumber = $this->getNewCompanyNumber();
            return $this->load(
                'GetSubmissionStatus/Accept.xml',
                $submissionData +
                [
                    '{COMPANY_NUMBER}' => $this->companyNumber,
                    '{AUTHENTICATION_CODE}' => '123456',
                    '{INCORPORATION_DATE}' => date('Y-m-d')
                ]
            );
        }
    }

    private function getNewCompanyNumber(): string
    {
        return random_int(10000000, 99999999);
    }

    private function getFile(string $file): File
    {
        return File::fromExistingPath(__DIR__ . '/../Config/fixtures/responses/' . $file);
    }

    private function load(string $file, array $placeHolders = []): string
    {
        $filePath = $this->getFile($file);
        return str_replace(array_keys($placeHolders), array_values($placeHolders), $filePath->getContent());
    }

    private function generateSync(Company $company, string $template): string
    {
        $file = sprintf('CompanyDataRequest/%s/%s', $company->getCompanyCategory() ?? 'BYSHR', $template);
        $placeholders = $this->createPlaceholders($company);
        return $this->engine->render(PathContent::fromFile($this->getFile($file)), $placeholders);
    }

    private function createPlaceholders(Company $company): array
    {
        $members = $company->getIncorporationFormSubmission()
            ? $this->incorporationMemberRepository->getCompanyMembers($company)
            : $members = $this->memberRepository->getCompanyMembers($company);

        $personDirectors = $personPscs = $personSecretaries = $personShareholders = $corporateDirectors = $corporateSecretaries = $corporatePscs = $corporateShareholders = [];

        /** @var IMember $member * */
        foreach ($members as $member) {
            if ($member->isCorporate()) {
                switch ($member->getType()) {
                    case IMember::TYPE_DIRECTOR:
                        $corporateDirectors[] = $member;
                        break;
                    case IMember::TYPE_PSC:
                        $corporatePscs[] = $member;
                        break;
                    case IMember::TYPE_SECRETARY:
                        $corporateSecretaries[] = $member;
                        break;
                    case IMember::TYPE_SUBSCRIBER:
                        $corporateShareholders[] = $member;
                        break;
                }
            } else {
                switch ($member->getType()) {
                    case IMember::TYPE_DIRECTOR:
                        $personDirectors[] = $member;
                        break;
                    case IMember::TYPE_PSC:
                        $personPscs[] = $member;
                        break;
                    case IMember::TYPE_SECRETARY:
                        $personSecretaries[] = $member;
                        break;
                    case IMember::TYPE_SUBSCRIBER:
                        $personShareholders[] = $member;
                        break;
                }
            }
        }


        $registeredOfficeAddress = $company->getIncorporationFormSubmission()
            ? $company->getIncorporationFormSubmission()->getRegisteredOfficeAddress()
            : $company->getRegisteredOffice();

        return [
            'company' => $company,
            'registeredOfficeAddress' => $registeredOfficeAddress,
            'personDirectors' => $personDirectors,
            'personPscs' => $personPscs,
            'personSecretaries' => $personSecretaries,
            'personShareholders' => $personShareholders,
            'corporateDirectors' => $corporateDirectors,
            'corporateSecretaries' => $corporateSecretaries,
            'corporatePscs' => $corporatePscs,
            'corporateShareholders' => $corporateShareholders,
            'hasFormSubmission' => (bool)$company->getIncorporationFormSubmission()
        ];
    }

    private function responseByType(string $type, string $text): string
    {
        if ($type === 'text') {
            return 'Test incorporation. Error: 503';
        } elseif ($type === 'empty') {
            return '';
        } else {
            return '<html>Test incorporation. Error: 503</html>';
        }
    }

    private function generateImport(string $companyNumber): string
    {
        $file = sprintf('CompanyDataRequest/%s/%s', 'BYSHR', 'default.xml');

        $company = new DummyCompany($companyNumber);

        $placeholders = [
            'company' => $company,
            'registeredOfficeAddress' => $company->getRegisteredOfficeAddress(),
            'formSubmission' => $company,
            'personDirectors' => $company->getDirectors(),
            'personPscs' => $company->getPscs(),
            'personShareholders' => $company->getShareholders(),
            'personSecretaries' => [],
            'corporateDirectors' => [],
            'corporateSecretaries' => [],
            'corporatePscs' => [],
            'corporateShareholders' => []
        ];

        return $this->engine->render(PathContent::fromFile($this->getFile($file)), $placeholders);
    }
}