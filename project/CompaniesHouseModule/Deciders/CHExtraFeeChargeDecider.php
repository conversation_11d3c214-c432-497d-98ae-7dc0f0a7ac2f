<?php

declare(strict_types=1);

namespace CompaniesHouseModule\Deciders;

use Entities\Company;
use Entities\Service;
use Models\OldModels\AnnualReturnServiceModel;
use Models\Products\Product;
use ProductModule\Repositories\ProductRepository;
use Services\CompanyService;
use Utils\Date;

class CHExtraFeeChargeDecider
{
    public const LIMIT_DATE = '2024-05-01';

    /**
     * @var CompanyService
     */
    private $companyService;

    /**
     * @var ProductRepository
     */
    private $productRepository;

    /**
     * @var \DateTime
     */
    private $limitDate;

    public function __construct(
        CompanyService $companyService,
        ProductRepository $productRepository,
    ) {
        $this->companyService = $companyService;
        $this->productRepository = $productRepository;
        $this->limitDate = new \DateTime(self::LIMIT_DATE);
    }

    public function isIncorporationExtraFeeChargeApplicableForCompanyId(int $companyId): bool
    {
        try {
            return $this->isIncorporationExtraFeeChargeApplicable(
                $this->companyService->getCompanyById($companyId)
            );
        } catch (\Exception $e) {
            return false;
        }
    }

    public function isIncorporationExtraFeeChargeApplicable(Company $company): bool
    {
        if ($company->isIncorporated()) {
            return false;
        }

        if ($this->limitDate > new \DateTime()) {
            return false;
        }

        if ($company->getDtc() > $this->limitDate) {
            return false;
        }

        return !$this->hasBoughtProduct(
            $company,
            Product::PRODUCT_CH_EXTRA_FEE_FOR_COMPANY_INCORPORATION
        );
    }

    public function isConfirmationStatementExtraFeeChargeApplicable(Company $company): bool
    {
        $now = new Date();

        // if today < May 1st, Fee is not applicable
        if ($now < $this->limitDate) {
            return false;
        }

        // if Company is not yet incorporated, Fee is not applicable
        if (!$company->isIncorporated()) {
            return false;
        }

        // If company has bought the product CH_EXTRA_FEE_FOR_CONFIRMATION_STATEMENT, Fee is not applicable
        if ($this->hasBoughtProduct(
            $company,
            Product::PRODUCT_CH_EXTRA_FEE_FOR_CONFIRMATION_STATEMENT
        )) {
            return false;
        }

        // Get all CS and CS related services bought by company
        $csServicesArray = [];
        foreach (Service::CONFIRMATION_STATEMENT_RELATED_SERVICE_TYPES as $serviceType) {
            $service = $company->getLastPackageServiceForTypes([$serviceType]);
            if (!empty($service)) {
                $csServicesArray[] = $service;
            }
        }

        $services = $company->getServicesByType(Service::TYPE_CONFIRMATION_STATEMEMT);
        if (!is_array($services)) {
            $services = $services->toArray();
        }

        $csServicesArray = array_merge(
            $csServicesArray,
            array_values($services)
        );

        if (empty($csServicesArray)) {
            return false;
        }

        // Get the latest CS service
        usort($csServicesArray, function (Service $first, Service $second) {
            return $first->getDtc() <=> $second->getDtc();
        });

        /** @var Service $csService */
        $csService = $csServicesArray[0];

        // If the latest CS service was bought after May 1st, Fee is not applicable
        if ($csService->getDtc() >= $this->limitDate) {
            return false;
        }

        // if the latest CS service is expired, Fee is not applicable
        if ($csService->getDtExpires() < $now) {
            return false;
        }

        // If the service is part of a Package (has a parent), Fee is not applicable
        if ($csService->hasParent()) {
            return false;
        }

        // Try to fetch the latest entry in cms2_annual_return_service table for this company
        try {
            $annualReturnServiceModel = AnnualReturnServiceModel::getByCompanyId($company->getCompanyId());
            // If there is an entry in cms2_annual_return_service table and its status is pending, Fee is not applicable
            if ($annualReturnServiceModel->statusId == AnnualReturnServiceModel::STATUS_PENDING) {
                return false;
            }
        } catch (\Exception $e) {
            // If there is no entry in cms2_annual_return_service table, draft has not been created yet,
            // or CS has been submitted to CH and accepted.
            // Fee is not applicable
            return false;
        }

        return true;
    }

    private function hasBoughtProduct(Company $company, string $product): bool
    {
        try {
            $product = $this->productRepository->getProductByName($product);

            return $this->companyService->hasBoughtProduct($company, $product);
        } catch (\Exception $e) {
            return false;
        }
    }
}
