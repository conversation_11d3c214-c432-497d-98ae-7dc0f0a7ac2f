<?php

namespace CompaniesHouseModule\Commands;

use CompaniesHouseModule\Entities\IdSubmissionQueue;
use CompaniesHouseModule\Repositories\IdSubmissionQueueRepository;
use CompaniesHouseModule\Services\SubmissionHandler;
use Doctrine\ORM\EntityNotFoundException;
use Exception;
use Exceptions\Technical\RequestException;
use InvalidStateException;
use Psr\Log\LoggerInterface;

/**
 * @description Sends the form submission for companies pending on ID check in submission queue
 */
class ProcessPendingIdCheckOnSubmissionQueue
{
    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @var IdSubmissionQueueRepository
     */
    private $idSubmissionQueueRepository;

    /**
     * @var SubmissionHandler
     */
    private $submissionHandler;

    public function __construct(
        LoggerInterface $logger,
        IdSubmissionQueueRepository $idSubmissionQueueRepository,
        SubmissionHandler $submissionHandler,
    ) {
        $this->logger = $logger;
        $this->idSubmissionQueueRepository = $idSubmissionQueueRepository;
        $this->submissionHandler = $submissionHandler;
    }

    public function processReadyFormSubmissions(bool $dryRun = true): void
    {
        $this->logger->debug(sprintf('Processing Form Submissions pending on ID check in %s mode.', $dryRun ? 'DRY RUN' : 'LIVE'));

        if (!$submissionQueues = $this->idSubmissionQueueRepository->getAll()) {
            $this->logger->info('No companies in the queue to process.');
            return;
        }

        $processed = $pending = $errorsCount = 0;

        /** @var IdSubmissionQueue $queue */
        foreach ($submissionQueues as $queue) {
            try {
                $company = $queue->getCompany();

                $this->logger->debug(sprintf('Processing Company %d.', $company->getId()));

                if ($this->submissionHandler->canSubmitCompany($company)) {
                    $this->logger->info(sprintf('ID check for company %d is valid - submitting the company to CH.', $company->getId()));

                    if (!$dryRun) {
                        $this->submissionHandler->submit($queue);
                    }

                    $this->logger->info(sprintf('Company %d submitted to CH.', $company->getId()));
                    $processed++;
                } else {

                    $this->submissionHandler->withHoldSubmission($company, $queue->getFormSubmissionId());

                    if (!$this->submissionHandler->isCompanyIdValid($company))
                        $this->logger->info(sprintf('ID for Company %d not yet validated.', $company->getId()));

                    if ($this->submissionHandler->isExtraFeeChargeApplicable($company))
                        $this->logger->info(sprintf('Company %d did not pay for CH Extra Fee.', $company->getId()));

                    $pending++;
                }
            } catch (RequestException $e) {
                $this->logError($queue, $e);
                $errorsCount++;
            }  catch (EntityNotFoundException $e) {
                $this->logCompanyNotFoundError($queue, $e);
                $errorsCount++;
            } catch (Exception $e) {
                $this->logError($queue, $e, true);
                $errorsCount++;
            }
        }

        $this->logger->info(
            sprintf(
                'Summary: %d submissions sent, %d submissions pending, %d errors',
                $processed,
                $pending,
                $errorsCount
            )
        );
    }

    private function logError(IdSubmissionQueue $idSubmissionQueue, Exception $exception, bool $removeEntity = false): void
    {
        $this->logger->warning(
            sprintf('Failed to process form submission for company %d - %s', $idSubmissionQueue->getCompany()->getId(), $exception->getMessage()),
            [
                'e' => $exception,
                'customer' => $idSubmissionQueue->getCustomer(),
                'company' => $idSubmissionQueue->getCompany(),
            ]
        );

        if ($removeEntity) {
            $this->idSubmissionQueueRepository->removeEntity($idSubmissionQueue);
        }
    }


    private function logCompanyNotFoundError(IdSubmissionQueue $idSubmissionQueue, Exception $exception): void
    {
        $this->logger->warning(
            sprintf(
                'Company associated with Submission Queue Id %d cannot be found. It will be removed from queue - %s',
                $idSubmissionQueue->getId(),
                $exception->getMessage()
            ),
            ['e' => $exception]
        );

         $this->idSubmissionQueueRepository->removeEntity($idSubmissionQueue);
    }
}
