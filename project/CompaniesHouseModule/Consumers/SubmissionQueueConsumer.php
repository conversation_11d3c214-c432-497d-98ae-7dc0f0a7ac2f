<?php

declare(strict_types=1);

namespace CompaniesHouseModule\Consumers;

use CompaniesHouseModule\Entities\IdSubmissionQueue;
use CompaniesHouseModule\Repositories\IdSubmissionQueueRepository;
use CompaniesHouseModule\Services\SubmissionHandler;
use IdModule\Messages\CustomerIdValidatedMessage;

readonly class SubmissionQueueConsumer
{
    public function __construct(
        private IdSubmissionQueueRepository $queueRepository,
        private SubmissionHandler $submissionHandler
    ) {}

    public function validateAndSubmitIdQueues(CustomerIdValidatedMessage $message): void
    {
        /** @var IdSubmissionQueue $submission */
        foreach ($this->queueRepository->getIdSubmissionsByCustomerId($message->getCustomerId()) as $submission) {
            if (!$this->submissionHandler->canSubmitCompany($submission->getCompany())) {
                $this->submissionHandler->withHoldSubmission($submission->getCompany(), $submission->getFormSubmissionId());
                continue;
            }

            try {
                $this->submissionHandler->submit($submission);
            } catch (\Exception $e) {
                // do nothing
            }
        }
    }
}