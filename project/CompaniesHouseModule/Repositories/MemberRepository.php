<?php

namespace CompaniesHouseModule\Repositories;

use CompaniesHouseModule\Entities\FormSubmission;
use CompaniesHouseModule\Entities\FormSubmission\OfficerAppointment;
use CompaniesHouseModule\Entities\DirectorPerson;
use CompaniesHouseModule\Entities\Member;
use CompaniesHouseModule\Entities\PscPerson;
use CompaniesHouseModule\Entities\SecretaryPerson;
use CompaniesHouseModule\Entities\ShareholderPerson;
use CompanyFormationModule\Entities\IMember;
use CompanyModule\ICompany;
use CompanyModule\Repositories\IOfficerRepository;
use Doctrine\ORM\Query\Expr\Join;
use Entities\Company;
use IdModule\Dto\PersonEntity;
use IdModule\Factories\IdQueryBuilder;
use IdModule\Repositories\Member\IIdMemberRepository;
use OrmModule\Repositories\DoctrineRepository_Abstract;

class MemberRepository extends DoctrineRepository_Abstract implements IOfficerRepository, IIdMemberRepository
{
    /**
     * @param int $memberId
     *
     * @return Member|object|null
     */
    public function getMemberById(int $memberId): ?Member
    {
        return $this->find($memberId);
    }

    /**
     * can return both non incorporated and incorporated members.
     */
    public function getIdCompanyMembers(ICompany $company): iterable
    {
        $builder = new IdQueryBuilder($this->createSimpleBuilder());

        return $builder->getMembers($company)
            ->thatBelongsToCompany($company)
            ->iterate();
    }

    public function getCompanyMembersByEntityName(ICompany $company, string $entityName): iterable
    {
        $qb = $this->createSimpleBuilder()
            ->select('m')
            ->from($entityName, 'm')
            ->andWhere('m.company = :company')
            ->setParameter('company', $company);

        return $qb->getQuery()->getResult();
    }

    public function hasIdCompanyMembersUsingMsgServiceAddress(ICompany $company): bool
    {
        $builder = new IdQueryBuilder($this->createSimpleBuilder());
        $builder->getMembersCount($company)
            ->thatBelongsToCompany($company)
            ->withRegisteredOffice(null);

        return $builder->count() > 0;
    }

    public function hasMembers(ICompany $company): bool
    {
        return (bool) $this->optionalOneBy(['company' => $company]);
    }

    public function getMembersByPersonEntity(PersonEntity $personEntity, string $entityClass): ?array
    {
        $qb = $this->getEntityManager()->createQueryBuilder();

        $qb->select('m')
            ->from($entityClass, 'm', null)
            ->where('m.forename = :forename')
            ->andWhere('m.surname = :surname')
            ->andWhere('m.dob = :dob')
            ->setParameter('forename', $personEntity->getForename())
            ->setParameter('surname', $personEntity->getSurname())
            ->setParameter('dob', $personEntity->getDob());

        return $qb->getQuery()->getResult();
    }

    public function removeCompanyMembers(Company $company)
    {
        return $this->createSimpleBuilder()
            ->delete(Member::class, 'm')
            ->where('m.company = :company')
            ->setParameter('company', $company)
            ->getQuery()->execute();
    }

    /**
     * @return Member[]
     */
    public function getCompanyMembersByIds(ICompany $company, array $ids): array
    {
        return $this->createSimpleBuilder()
            ->select('m')
            ->from(Member::class, 'm')
            ->where('m.company = :company')
            ->andWhere('m.id IN (:ids)')
            ->setParameter('company', $company)
            ->setParameter('ids', $ids)
            ->getQuery()->getResult();
    }

    public function getCompanyMembersByType(ICompany $company, string $type): array
    {
        $qb = $this->createSimpleBuilder()
            ->select('m')
            ->from(Member::class, 'm')
            ->andWhere('m.company = :company')
            ->andWhere('m.type = :type')
            ->setParameters(
                [
                    'company' => $company,
                    'type' => $type,
                ]
            );

        return $qb->getQuery()->getResult();
    }

    /**
     * @return Member[]
     */
    public function getCompanyDirectors(ICompany $company): array
    {
        return $this->getCompanyMembersByType($company, Member::TYPE_DIRECTOR);
    }

    public function getCompanyMembers(ICompany $company): array
    {
        return $this->findBy(['company' => $company]);
    }

    public function getCompanyMembersShares(string $memberTypeClass, string $shareClass): array
    {
        $qb = $this->createSimpleBuilder()
            ->select('m')
            ->from($memberTypeClass, 'm')
            ->innerJoin(Company::class, 'c', Join::WITH, '(m.company = c.companyId AND c.productId IS NOT NULL)')
            ->where('m.shareClass = :shareClass')
            ->andWhere('c.companyStatus is not null')
            ->andWhere('c.companyStatus <> :dissolved')
            ->setParameter('shareClass', $shareClass)
            ->setParameter('dissolved', Company::COMPANY_STATUS_DISSOLVED);

        return $qb->getQuery()->getResult();
    }

    public function getPscMembersByCompanyId(int $companyId): array
    {
        $qb = $this->createSimpleBuilder()
            ->select('m')
            ->from(Member::class, 'm')
            ->where('m.type = :type')
            ->andWhere('m.company = :companyId')
            ->setParameters(
                [
                    'type' => IMember::TYPE_PSC,
                    'companyId' => $companyId,
                ]
            );

        return $qb->getQuery()->getResult();
    }

    public function deleteOfficerAppointment(int $formSubmissionId)
    {
        $this->createSimpleBuilder()
            ->delete(OfficerAppointment::class, 'm')
            ->where('m.formSubmissionId = :formSubmissionId')
            ->setParameter('formSubmissionId', $formSubmissionId)
            ->getQuery()->execute();

        return $this->createSimpleBuilder()
            ->delete(FormSubmission::class, 'm')
            ->where('m.formSubmissionId = :formSubmissionId')
            ->setParameter('formSubmissionId', $formSubmissionId)
            ->getQuery()->execute();
    }

    public function getCompanyMemberByName(Company $company, string $forename, string $surname): array
    {
        return array_merge(
            $this->getByNameForClass($company, DirectorPerson::class, $surname, $forename),
            $this->getByNameForClass($company, ShareholderPerson::class, $surname, $forename),
            $this->getByNameForClass($company, SecretaryPerson::class, $surname, $forename),
            $this->getByNameForClass($company, PscPerson::class, $surname, $forename)
        );
    }

    private function getByNameForClass(Company $company, string $class, string $surname, string $forename): array
    {
        return $this->_em->createQueryBuilder()
            ->select('m')
            ->from($class, 'm')
            ->where('m.company = :company')
            ->andWhere('LOWER(m.surname) LIKE LOWER(:surname)')
            ->andWhere('LOWER(m.forename) LIKE LOWER(:forename)')
            ->setParameters([
                'company' => $company,
                'surname' => \sprintf('%%%s%%', $surname),
                'forename' => \sprintf('%%%s%%', $forename),
            ])
            ->getQuery()->getResult();
    }
}
