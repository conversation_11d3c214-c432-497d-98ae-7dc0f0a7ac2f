<?php

declare(strict_types=1);

namespace MailScanModule\Commands;

use EmailModule\IEmailLog;
use Entities\Service;
use Exceptions\Technical\NodeException;
use MailScanModule\Emailers\MailboxEmailer;
use Models\Products\Product;
use Monolog\Logger;
use Services\EventService;
use Services\NodeService;
use Services\ServiceService;

class MailboxTrialEndingEmailCommand
{
    private const DAYS_BEFORE = [7, 6, 5, 4, 3, 2, 1];
    private const EVENT_KEY = 'mailbox.trial_ending_email';
    private const MAILBOX_TRIAL_PRODUCT_NAMES = [
        Product::PRODUCT_MAILBOX_STANDARD_INITIAL_3_MONTHS,
        Product::PRODUCT_MAILBOX_STANDARD_PRIVACY_INITIAL_1_MONTH,
    ];
    private const DURATION_MAPPING = [
        Service::DURATION_ONE_MONTH => '1-month',
        Service::DURATION_ONE_MONTH_ONE_DAY => '1-month',
        Service::DURATION_THREE_MONTHS => '3-month',
        SERVICE::DURATION_ONE_YEAR => '12-month',
    ];

    public function __construct(
        readonly ServiceService $serviceService,
        readonly NodeService $nodeService,
        readonly Logger $logger,
        readonly MailboxEmailer $mailboxEmailer,
        readonly EventService $eventService,
    ) {
    }

    /**
     * @throws NodeException
     * @throws \Exception
     */
    public function sendTrialEndingEmails(
        array $daysBefore = self::DAYS_BEFORE,
        bool $dryRun = true,
        bool $debug = true,
        ?int $companyId = null,
    ): void {
        $this->logDebug($debug, 'Starting trial ending email process', [
            'daysBefore' => $daysBefore,
            'dryRun' => $dryRun,
            'debug' => $debug,
        ]);

        $productIds = array_map(
            fn ($productName) => $this->nodeService->requiredProductByName($productName)->getId(),
            self::MAILBOX_TRIAL_PRODUCT_NAMES
        );

        $this->logDebug($debug, 'Retrieved product IDs', [
            'productIds' => $productIds,
            'productNames' => self::MAILBOX_TRIAL_PRODUCT_NAMES,
        ]);

        $services = $this->getExpiringServices($daysBefore, $productIds, $debug);

        if (!is_null($companyId)) {
            $this->logDebug($debug, 'Filtering services by company ID', [
                'companyId' => $companyId,
            ]);

            $services = array_filter(
                $services,
                fn (Service $service) => $service->getCompany()->getId() == $companyId
            );
        }

        $this->logDebug($debug, 'Found expiring services', [
            'count' => count($services),
            'serviceIds' => array_map(fn ($s) => $s->getId(), $services),
        ]);

        $this->processServices($services, $debug, $dryRun);
    }

    /**
     * @throws \Exception
     */
    private function getExpiringServices(array $daysBefore, array $productIds, bool $debug): array
    {
        try {
            $services = $this->serviceService->getServicesExpiringWithinDays(
                $daysBefore,
                $productIds,
                self::EVENT_KEY
            );

            $this->logDebug($debug, 'Services query executed', [
                'daysBefore' => $daysBefore,
                'productIds' => $productIds,
                'eventKey' => self::EVENT_KEY,
                'resultCount' => count($services),
            ]);

            return $services;
        } catch (\Exception $e) {
            $this->logger->error('Failed to retrieve expiring services', [
                'daysBefore' => $daysBefore,
                'productIds' => $productIds,
                'error' => $e->getMessage(),
                'exception' => $e,
            ]);

            throw $e;
        }
    }

    private function processServices(array $services, bool $debug, bool $dryRun): void
    {
        $successCount = $errorCount = 0;

        foreach ($services as $service) {
            try {
                $this->logDebug($debug, 'Processing service', [
                    'serviceId' => $service->getId(),
                    'customerId' => $service->getCustomer()->getId(),
                    'expiresAt' => $service->getDtExpires()->format('Y-m-d H:i:s'),
                ]);

                $emailData = $this->prepareEmailData($service, $debug);

                $emailLog = !$dryRun ? $this->sendEmail($emailData, $debug) : null;

                if (!$dryRun) {
                    $this->logDebug($debug, 'Email sent successfully', [
                        'serviceId' => $service->getId(),
                        'emailLogId' => $emailLog->getId(),
                    ]);

                    $this->recordEvent($service->getId(), $debug);
                }

                ++$successCount;
            } catch (\Throwable $e) {
                ++$errorCount;
                $this->logger->error('Failed to process service', [
                    'serviceId' => $service->getId(),
                    'error' => $e->getMessage(),
                    'exception' => $e,
                ]);
            }
        }

        $this->logProcessingSummary($successCount, $errorCount, $debug);
    }

    /**
     * @throws \Exception
     * @throws \Throwable
     */
    private function prepareEmailData(Service $service, bool $debug): array
    {
        try {
            $customer = $service->getCustomer();
            $emailData = [
                'customer' => $customer,
                'customerEmail' => $customer->getEmail(),
                'firstName' => $customer->getFirstName(),
                'companyName' => $service->getCompany()->getCompanyName(),
                'mailboxProductName' => Service::$types[$service->getProduct()->getServiceTypeId()],
                'trialPeriod' => self::DURATION_MAPPING[$service->getInitialDuration()],
                'endDate' => $service->getDtExpires()->format('d F Y'),
                'renewalPrice' => $service->getRenewalProduct()->getPrice(),
                'packageName' => $this->getPackageName($service),
            ];

            $this->logDebug($debug, 'Email data prepared', [
                'serviceId' => $service->getId(),
                'customerId' => $customer->getId(),
                'customerEmail' => $customer->getEmail(),
                'firstName' => $emailData['firstName'],
                'mailboxProductName' => $emailData['mailboxProductName'],
                'trialPeriod' => $emailData['trialPeriod'],
                'endDate' => $emailData['endDate'],
                'renewalPrice' => $emailData['renewalPrice'],
                'packageName' => $emailData['packageName'],
            ]);

            return $emailData;
        } catch (\Throwable $e) {
            $this->logger->error('Failed to prepare email data', [
                'serviceId' => $service->getId(),
                'error' => $e->getMessage(),
                'exception' => $e,
            ]);

            throw $e;
        }
    }

    private function sendEmail(array $emailData, bool $debug): IEmailLog
    {
        try {
            $emailLog = $this->mailboxEmailer->sendTrialEndingEmail($emailData);

            $this->logDebug($debug, 'Email sending completed', [
                'recipientEmail' => $emailData['customer']->getEmail(),
                'emailLogId' => $emailLog->getId(),
            ]);

            return $emailLog;
        } catch (\Exception $e) {
            $this->logger->error('Email sending failed', [
                'recipientEmail' => $emailData['customer']->getEmail(),
                'error' => $e->getMessage(),
                'exception' => $e,
            ]);

            throw $e;
        }
    }

    private function recordEvent(int $serviceId, bool $debug): void
    {
        try {
            $this->eventService->notify(self::EVENT_KEY, $serviceId);

            $this->logDebug($debug, 'Event recorded successfully', [
                'serviceId' => $serviceId,
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Failed to record event', [
                'serviceId' => $serviceId,
                'eventKey' => self::EVENT_KEY,
                'error' => $e->getMessage(),
                'exception' => $e,
            ]);

            throw $e;
        }
    }

    private function logProcessingSummary(int $successCount, int $errorCount, bool $debug): void
    {
        $totalProcessed = $successCount + $errorCount;

        $summaryData = [
            'totalProcessed' => $totalProcessed,
            'successful' => $successCount,
            'errors' => $errorCount,
            'successRate' => $totalProcessed > 0 ? round(($successCount / $totalProcessed) * 100, 2) : 0,
        ];

        $errorCount > 0
            ? $this->logger->warning('Trial ending email processing completed with issues', $summaryData)
            : $this->logger->info('Trial ending email processing completed', $summaryData);
    }

    private function logDebug(bool $debug, string $message, array $context = []): void
    {
        if (!$debug) {
            return;
        }

        $this->logger->debug($message, $context);
    }

    /**
     * @throws \Throwable
     */
    private function getPackageName(Service $service): string
    {
        try {
            $currentServices = $service->getCompany()->getEnabledParentServicesWithType(Service::TYPE_REGISTERED_OFFICE)->toArray();
            usort($currentServices, function (Service $a, Service $b) {
                return $b->getDtExpires() <=> $a->getDtExpires();
            });

            return $currentServices[0]->getServiceName();
        } catch (\Throwable $e) {
            $this->logger->error(sprintf(
                'Error getting package name for company %d: %s',
                $service->getCompany()->getId(),
                $e->getMessage()
            ));

            throw $e;
        }
    }
}
