<?php

declare(strict_types=1);

namespace MailScanModule\UseCase\CompanyInbox\Api\MarkAsPurchased;

use Entities\Company;
use MailScanModule\ApiClient\MailroomApiClient;

readonly class Factory
{
    public function __construct(
        private MailroomApiClient $mailroomApiClient,
    ) {}

    /**
     * @throws \Exception
     */
    public function makeRequest(Company $company, array $payload): Request
    {
        return new Request(
            $company,
            $this->mailroomApiClient->getPostItemById($payload['postItemId']),
            (int) $payload['orderId']
        );
    }

    public function makeResponse(bool $success): Response
    {
        return new Response($success);
    }
}
