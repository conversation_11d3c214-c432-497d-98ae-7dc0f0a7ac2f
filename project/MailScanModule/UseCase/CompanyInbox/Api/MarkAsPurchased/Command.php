<?php

declare(strict_types=1);

namespace MailScanModule\UseCase\CompanyInbox\Api\MarkAsPurchased;

use MailScanModule\ApiClient\MailroomApiClient;
use MailScanModule\Enums\PostItemTypeEnum;
use MailScanModule\Enums\StatusEnum;
use Repositories\TransactionRepository;

readonly class Command
{
    public function __construct(
        private Factory $factory,
        private MailroomApiClient $mailroomApiClient,
        private TransactionRepository $transactionRepository,
    ) {}

    public function execute(Request $request): Response
    {
        try {
            $transaction = $this->transactionRepository->getTransactionByOrderId($request->orderId);

            if ($transaction->getCustomer()->getId() !== $request->company->getCustomer()->getId()) {
                throw new \Exception('Order belongs to another customer');
            }

            $this->mailroomApiClient->purchaseItem([
                'post_item_id' => $request->postItem->getId(),
                'desired_status' => StatusEnum::fromTypeForInboxPurchase(
                    PostItemTypeEnum::fromType($request->postItem->getType())
                )->value,
                'transaction_id' => $transaction->getId(),
            ]);

            if ($request->postItem->isParcel()) {
                $this->mailroomApiClient->setPostItemStatus(
                    [$request->postItem],
                    StatusEnum::STATUS_TO_BE_FORWARDED->value
                );
            }

            $success = true;
        } catch (\Exception $e) {
            $success = false;
        }

        return $this->factory->makeResponse($success);
    }
}
