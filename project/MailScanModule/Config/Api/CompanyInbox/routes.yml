company_inbox.api.mark_as_purchased:
  path: /api/v1/company-inbox/mark-as-purchased/{company}/
  methods: [POST]
  defaults:
    controller: mail_scan_module.controllers.api.company_inbox_api_controller
    action: markAsPurchased
    requirements:
      company:
        converter: doctrine
  requirements:
    company: '\d+'
  options:
    excludeFromSitemap: true

company_inbox.api.download_item:
  path: /api/v1/company-inbox/download-item/{company}/{item}/
  methods: [GET]
  defaults:
    controller: mail_scan_module.controllers.api.company_inbox_api_controller
    action: downloadItem
    requirements:
      company:
        converter: doctrine
  requirements:
    company: '\d+'
  options:
    excludeFromSitemap: true

company_inbox.api.list_items:
  path: /api/v1/company-inbox/list-items/{customer}/
  methods: [POST]
  defaults:
    controller: mail_scan_module.controllers.api.company_inbox_api_controller
    action: listItems
    requirements:
      customer:
        converter: doctrine
  requirements:
    customer: '\d+'
  options:
    excludeFromSitemap: true

company_inbox.api.generate_inline_payment_block:
  path: /api/v1/company-inbox/generate-inline-payment-block/{company}/
  methods: [ POST ]
  defaults:
    controller: mail_scan_module.controllers.api.company_inbox_api_controller
    action: generateInlinePaymentBlock
    requirements:
      company:
        converter: doctrine
  requirements:
    company: '\d+'
  options:
    excludeFromSitemap: true