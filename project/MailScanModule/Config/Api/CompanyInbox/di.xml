<?xml version="1.0" ?>
<container xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xmlns="http://symfony.com/schema/dic/services"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <defaults public="true" />

        <service class="MailScanModule\Controllers\Api\CompanyInboxApiController" id="mail_scan_module.controllers.api.company_inbox_api_controller">
            <argument type="service" id="mail_scan_module.use_case.company_inbox.download_item.command"/>
            <argument type="service" id="mail_scan_module.use_case.company_inbox.download_item.factory"/>
            <argument type="service" id="mail_scan_module.use_case.company_inbox.generate_inline_payment_block.command"/>
            <argument type="service" id="mail_scan_module.use_case.company_inbox.generate_inline_payment_block.factory"/>
            <argument type="service" id="mail_scan_module.use_case.company_inbox.list_items.command"/>
            <argument type="service" id="mail_scan_module.use_case.company_inbox.list_items.factory"/>
            <argument type="service" id="mail_scan_module.use_case.company_inbox.mark_as_purchased.command"/>
            <argument type="service" id="mail_scan_module.use_case.company_inbox.mark_as_purchased.factory"/>
        </service>

        <service class="MailScanModule\UseCase\CompanyInbox\Api\MarkAsPurchased\Command"
                 id="mail_scan_module.use_case.company_inbox.mark_as_purchased.command">
            <argument type="service" id="mail_scan_module.use_case.company_inbox.mark_as_purchased.factory"/>
            <argument type="service" id="mail_scan_module.api_client.mailroom_api_client"/>
            <argument type="service" id="repositories.transaction_repository"/>
        </service>

        <service class="MailScanModule\UseCase\CompanyInbox\Api\MarkAsPurchased\Factory"
                 id="mail_scan_module.use_case.company_inbox.mark_as_purchased.factory">
            <argument type="service" id="mail_scan_module.api_client.mailroom_api_client"/>
        </service>

        <service class="MailScanModule\UseCase\CompanyInbox\Api\DownloadItem\Command"
                 id="mail_scan_module.use_case.company_inbox.download_item.command">
            <argument type="service" id="mail_scan_module.use_case.company_inbox.download_item.factory"/>
            <argument type="service" id="mail_scan_module.helpers.encoder_helper"/>
            <argument type="service" id="mail_scan_module.services.google_cloud_storage_service"/>
        </service>

        <service class="MailScanModule\UseCase\CompanyInbox\Api\DownloadItem\Factory"
                 id="mail_scan_module.use_case.company_inbox.download_item.factory">
            <argument type="service" id="mail_scan_module.services.post_item_service"/>
        </service>

        <service class="MailScanModule\UseCase\CompanyInbox\Api\ListItems\Command"
                 id="mail_scan_module.use_case.company_inbox.list_items.command">
            <argument type="service" id="mail_scan_module.use_case.company_inbox.list_items.factory"/>
            <argument type="service" id="mail_scan_module.api_client.mailroom_api_client"/>
            <argument type="service" id="mail_scan_module.repositories.post_item_data_table_repository"/>
            <argument type="service" id="mail_scan_module.factories.mailroom_post_item_data_factory"/>
            <argument type="service" id="mail_scan_module.services.post_item_service"/>
        </service>

        <service class="MailScanModule\UseCase\CompanyInbox\Api\ListItems\Factory"
                 id="mail_scan_module.use_case.company_inbox.list_items.factory">
        </service>

        <service class="MailScanModule\UseCase\CompanyInbox\Api\GenerateInlinePaymentBlock\Command"
                 id="mail_scan_module.use_case.company_inbox.generate_inline_payment_block.command">
            <argument type="service" id="mail_scan_module.use_case.company_inbox.generate_inline_payment_block.factory"/>
            <argument type="service" id="product_module.repositories.product_repository"/>
            <argument type="service" id="payment_module.factories.inline_payment_factory"/>
            <argument type="service" id="mail_scan_module.services.post_item_service"/>
        </service>

        <service class="MailScanModule\UseCase\CompanyInbox\Api\GenerateInlinePaymentBlock\Factory"
                 id="mail_scan_module.use_case.company_inbox.generate_inline_payment_block.factory">
            <argument type="service" id="mail_scan_module.api_client.mailroom_api_client"/>
        </service>
    </services>
</container>
