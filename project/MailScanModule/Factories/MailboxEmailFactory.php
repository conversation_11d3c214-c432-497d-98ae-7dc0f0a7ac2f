<?php

declare(strict_types=1);

namespace MailScanModule\Factories;

use CompanyModule\Facades\PostItemHandlingFacade;
use Entities\Company;
use Entities\Service;
use MailScanModule\ApiClient\MailroomApiClient;
use MailScanModule\Deciders\ForwardingAddressDecider;
use MailScanModule\Deciders\MailboxTierDecider;
use MailScanModule\Deciders\ReleaseItemDecider;
use MailScanModule\Dto\MailboxEmailData;
use MailScanModule\Dto\MailroomPostItemData;
use MailScanModule\Enums\PostItemTypeEnum;
use MailScanModule\Exceptions\NoMailboxServiceException;
use MailScanModule\Facades\MailForwardingAddressFacade;
use MailScanModule\Helpers\MailboxProductPropertyHelper;
use MailScanModule\Services\PostItemService;
use Psr\Log\LoggerInterface;

class MailboxEmailFactory
{
    public const MOCKED_FORWARDING_ADDRESS = '123 Main St, London, SW1 1WS, UK';
    public const MOCKED_MAX_QUOTAS = 10;
    public const MOCKED_PRICE = 5;

    public function __construct(
        private MailboxTierDecider $mailboxTierDecider,
        private PostItemHandlingFacade $postItemHandlingFacade,
        private MailForwardingAddressFacade $mailForwardingAddressFacade,
        private PostItemService $postItemService,
        private readonly ReleaseItemDecider $releaseItemDecider,
        private readonly ForwardingAddressDecider $forwardingAddressDecider,
        private readonly LoggerInterface $logger,
    ) {
    }

    /**
     * @throws \Exception
     * @throws \Throwable
     */
    public function createFromMailroomPostItemData(Company $company, MailroomPostItemData $item): MailboxEmailData
    {
        $isReleased = $item->isDownloadableStatus();
        $hasFailedToCharge = ($item->hasUnpaidQuotaCharges() && !$isReleased);
        $lastEmailSent = $item->getDetail(MailroomApiClient::LAST_EMAIL_SENT_DETAIL_NAME);
        $hasIdCheck = $this->releaseItemDecider->isIdCheckCompleted($company);
        $needsForwardingAddress = $this->forwardingAddressDecider->companyNeedsMailForwardingAddressByFormat($company, $item->getFormat());

        $mailboxService = $company->getActiveOrLatestMailboxService();
        if (!is_null($mailboxService) && $mailboxService->isActive()) {
            $mailboxTier = $this->mailboxTierDecider->determineMailboxTier($mailboxService);
            $packageName = $this->getPackageName($company);
            $mailboxInitialProduct = $this->postItemService->getProductByTier($mailboxTier, $company);
            $handlingSettingValue = $this->postItemHandlingFacade->getHandlingSettingByType(
                $company,
                $item,
                $mailboxInitialProduct,
                $mailboxTier
            );
            $forwardingAddress = $this->mailForwardingAddressFacade->getMailForwardingAddressSetting($company)->getFullAddress();
            $isOverdue = $mailboxService->isOverdueAndExpired();
            $maximumQuota = max(
                MailboxProductPropertyHelper::getMaximumQuotaByTypeAndProcessingMethod(
                    $mailboxInitialProduct,
                    $item->getType(),
                    $handlingSettingValue
                ),
                MailboxProductPropertyHelper::getMaximumGenericQuotaByTypeAndProcessingMethod(
                    $mailboxInitialProduct,
                    MailboxProductPropertyHelper::getProcessingMethodAsString($item->getType(), $handlingSettingValue),
                )
            );
            $quotaPrice = MailboxProductPropertyHelper::getExtraQuotaFeeByFormat(
                $mailboxInitialProduct,
                $item->getFormat()
            );
            $releasePrice = $this->postItemService->getReleasePrice($item, $company);
            try {
                $withinQuota = $handlingSettingValue !== 0 ? (bool) $this->postItemService->isWithinQuotaMaximum(
                    $company,
                    $mailboxInitialProduct,
                    $item,
                    $handlingSettingValue
                ) : null;
            } catch (NoMailboxServiceException $e) {
                $withinQuota = null;
            }
        }

        return new MailboxEmailData(
            $company->getCompanyName(),
            $company->getId(),
            $company->getCustomer()->getFirstName() ?? '', // @phpstan-ignore-line
            $company->getCustomer()->getFullName() ?? '', // @phpstan-ignore-line
            $item->getType(),
            $isReleased,
            $item->getStatus(),
            $item->getDtc(),
            $this->getParsedMailSenderHtml($item),
            $item->getDetails(),
            $hasFailedToCharge,
            $withinQuota ?? null,
            $lastEmailSent,
            $handlingSettingValue ?? null,
            $mailboxTier ?? null,
            $packageName ?? null,
            $forwardingAddress ?? null,
            $maximumQuota ?? null,
            isset($quotaPrice) ? $this->getPriceLabelFromFloat($quotaPrice) : null,
            isset($releasePrice) ? $this->getPriceLabelFromFloat($releasePrice) : null,
            $isOverdue ?? null,
            $hasIdCheck,
            $needsForwardingAddress,
        );
    }

    /**
     * @throws \Exception
     */
    public function createFromAdminPayload(Company $company, MailroomPostItemData $item, array $payload): MailboxEmailData
    {
        return new MailboxEmailData(
            companyName: $company->getCompanyName(),
            companyId: $company->getId(),
            customerFirstName: $company->getCustomer()->getFirstName(),
            customerFullName: $company->getCustomer()->getFullName(),
            postItemType: $item->getType(),
            isReleased: (bool) $payload['released'],
            itemStatus: $item->getStatus(),
            itemReceivedDate: $item->getDtc(),
            mailTypeHtml: $this->getParsedMailSenderHtml($item),
            itemDetails: $item->getDetails(),
            hasFailedToCharge: (bool) $payload['unable_to_charge'],
            withinQuota: !(bool) $payload['out_of_quotas'],
            lastEmailSent: $item->getDetail(MailroomApiClient::LAST_EMAIL_SENT_DETAIL_NAME),
            handlingSetting: MailboxProductPropertyHelper::getProcessingMethodAsInt($item->getFormat(), $payload['handling_setting']),
            mailboxTier: (int) $payload['mailbox_tier'] === 0 ? null : (int) $payload['mailbox_tier'],
            packageName: $payload['core_package'],
            forwardingAddress: ((bool) $payload['missing_address']) ? null : self::MOCKED_FORWARDING_ADDRESS,
            maxQuotas: self::MOCKED_MAX_QUOTAS,
            quotaPriceLabel: $this->getPriceLabelFromFloat(self::MOCKED_PRICE),
            releasePriceLabel: $this->getPriceLabelFromFloat(self::MOCKED_PRICE),
            isServiceOverdue: (bool) $payload['service_overdue'],
            hasIdCheck: !(bool) $payload['no_id_check'],
            needsForwardingAddress: (bool) $payload['missing_address'] && $payload['handling_setting'] === MailboxProductPropertyHelper::PROCESSING_METHOD_POST,
        );
    }

    private function getPriceLabelFromFloat(float $price): string
    {
        return sprintf(
            '£%s',
            number_format($price, 2)
        );
    }

    private function getParsedMailSenderHtml(MailroomPostItemData $postItem): string
    {
        if ($postItem->isCourtLetter()) {
            return 'court document';
        }

        if ($postItem->getType() === PostItemTypeEnum::TYPE_STATUTORY->value) {
            return sprintf(
                '<a href="https://support.companiesmadesimple.com/hc/en-us/articles/360002313778-What-is-Government-Mail">%s</a> mail',
                $postItem->getSender()
            );
        }

        if ($postItem->getType() === PostItemTypeEnum::TYPE_PARCEL->value) {
            return sprintf(
                '<b>%s</b>',
                $postItem->getSender()
            );
        }

        return sprintf(
            '<a href="https://support.companiesmadesimple.com/hc/en-us/articles/35038298824849-What-is-Business-Mail">%s</a> mail',
            $postItem->getSender()
        );
    }

    /**
     * @throws \Throwable
     */
    private function getPackageName(Company $company): string
    {
        try {
            $currentServices = $company->getEnabledParentServicesWithType(Service::TYPE_REGISTERED_OFFICE)->toArray();
            usort($currentServices, function (Service $a, Service $b) {
                return $b->getDtExpires() <=> $a->getDtExpires();
            });

            return $currentServices[0]->getServiceName();
        } catch (\Throwable $e) {
            $this->logger->error(sprintf(
                'Error getting package name for company %d: %s',
                $company->getId(),
                $e->getMessage()
            ));

            throw $e;
        }
    }
}
