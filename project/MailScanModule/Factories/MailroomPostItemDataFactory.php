<?php

declare(strict_types=1);

namespace MailScanModule\Factories;

use Entities\Company;
use MailScanModule\ApiClient\MailroomApiClient;
use MailScanModule\Dto\MailroomPostItemData;
use MailScanModule\Enums\PostItemTypeEnum;
use MailScanModule\Enums\StatusEnum;
use MailScanModule\Helpers\MailboxProductPropertyHelper;
use MailScanModule\Providers\MailboxEmailNameProvider;
use Utils\Helpers\ArrayHelper;

class MailroomPostItemDataFactory
{
    public const GENERIC_STATUTORY_SENDER_NAME = 'Government';
    public const GENERIC_NONSTATUTORY_SENDER_NAME = 'Business';
    public const GENERIC_PARCEL_SENDER_NAME = 'Parcel';
    public const COMPANIES_HOUSE_SENDER_NAME = 'Companies House';
    public const HMRC_SENDER_NAME = 'HMRC';
    public const COURT_LETTER_SENDER_NAME = 'Court Letter';

    public const MAILROOM_STATUTORY_SENDER_MAP = [
        self::SENDER_COMPANIES_HOUSE => self::COMPANIES_HOUSE_SENDER_NAME,
        self::SENDER_HMRC => self::HMRC_SENDER_NAME,
        self::SENDER_COURT_LETTER => self::COURT_LETTER_SENDER_NAME,
        self::SENDER_OTHER => self::GENERIC_STATUTORY_SENDER_NAME,
    ];

    public const MAILROOM_NONSTATUTORY_SENDER_MAP = [
        'OTHER' => self::GENERIC_NONSTATUTORY_SENDER_NAME,
    ];

    public const MAILROOM_PARCEL_SENDER_MAP = [
        'OTHER' => self::GENERIC_PARCEL_SENDER_NAME,
    ];

    public const KEY_POST_ITEM_ID = 'post_item_id';
    public const KEY_COMPANY_NAME = 'company_name';
    public const KEY_COMPANY_NUMBER = 'company_number';
    public const KEY_TYPE = 'type';
    public const KEY_SENDER = 'sender';
    public const KEY_FILE_NAME = 'file_name';
    public const KEY_BATCH_NUMBER = 'batch_number';
    public const KEY_OPERATOR = 'operator';
    public const KEY_DETAILS = 'details';
    public const KEY_EVENTS = 'events';
    public const KEY_STATUS = 'status';
    public const KEY_TRANSACTION_ID = 'transaction_id';

    private const SENDER_COMPANIES_HOUSE = 'COMPANIES_HOUSE';
    private const SENDER_HMRC = 'HMRC';
    private const SENDER_COURT_LETTER = 'COURT_LETTER';
    private const SENDER_OTHER = 'OTHER';

    public static function createFromMailroomApiResponseItem(array $mailroomApiResponseItem): MailroomPostItemData
    {
        return new MailroomPostItemData(
            ArrayHelper::get($mailroomApiResponseItem, self::KEY_POST_ITEM_ID),
            ArrayHelper::get($mailroomApiResponseItem, self::KEY_COMPANY_NAME),
            ArrayHelper::get($mailroomApiResponseItem, self::KEY_COMPANY_NUMBER),
            ArrayHelper::get($mailroomApiResponseItem, self::KEY_TYPE),
            self::parseSender(
                ArrayHelper::get($mailroomApiResponseItem, self::KEY_SENDER),
                ArrayHelper::get($mailroomApiResponseItem, self::KEY_TYPE)
            ),
            ArrayHelper::get($mailroomApiResponseItem, self::KEY_FILE_NAME),
            ArrayHelper::get($mailroomApiResponseItem, self::KEY_BATCH_NUMBER),
            ArrayHelper::get($mailroomApiResponseItem, self::KEY_OPERATOR),
            ArrayHelper::get($mailroomApiResponseItem, self::KEY_DETAILS),
            ArrayHelper::get($mailroomApiResponseItem, self::KEY_EVENTS),
            new \DateTime(ArrayHelper::get($mailroomApiResponseItem, ['dtc', 'date']), new \DateTimeZone('UTC')),
            ArrayHelper::get($mailroomApiResponseItem, self::KEY_STATUS),
            ArrayHelper::get($mailroomApiResponseItem, self::KEY_TRANSACTION_ID, null),
        );
    }

    public static function MockFromArray(array $mailroomApiResponseItem): MailroomPostItemData
    {
        try {
            $dtc = new \DateTime(ArrayHelper::get($mailroomApiResponseItem, ['dtc', 'date']), new \DateTimeZone('UTC'));
        } catch (\Exception $e) {
            $dtc = new \DateTime();
        }

        return new MailroomPostItemData(
            ArrayHelper::get($mailroomApiResponseItem, self::KEY_POST_ITEM_ID, '1'),
            ArrayHelper::get($mailroomApiResponseItem, self::KEY_COMPANY_NAME, 'Test Company'),
            ArrayHelper::get($mailroomApiResponseItem, self::KEY_COMPANY_NUMBER, '1'),
            ArrayHelper::get($mailroomApiResponseItem, self::KEY_TYPE, 'non-statutory'),
            self::parseSender(
                ArrayHelper::get($mailroomApiResponseItem, self::KEY_SENDER, 'OTHER'),
                ArrayHelper::get($mailroomApiResponseItem, self::KEY_TYPE)
            ),
            ArrayHelper::get($mailroomApiResponseItem, self::KEY_FILE_NAME, 'test.pdf'),
            ArrayHelper::get($mailroomApiResponseItem, self::KEY_BATCH_NUMBER, '12345678'),
            ArrayHelper::get($mailroomApiResponseItem, self::KEY_OPERATOR, 'abcdefghijk'),
            ArrayHelper::get($mailroomApiResponseItem, self::KEY_DETAILS, []),
            ArrayHelper::get($mailroomApiResponseItem, self::KEY_EVENTS, []),
            $dtc,
            ArrayHelper::get($mailroomApiResponseItem, self::KEY_STATUS, null),
            ArrayHelper::get($mailroomApiResponseItem, self::KEY_TRANSACTION_ID, null),
        );
    }

    public static function createFromKofaxArray(?array $postItem): MailroomPostItemData
    {
        return new MailroomPostItemData(
            id: $postItem['postItemId'],
            companyName: $postItem['companyName'],
            companyNumber: $postItem['companyNumber'],
            type: PostItemTypeEnum::TYPE_STATUTORY->value,
            sender: self::parseSender($postItem['typeId'], PostItemTypeEnum::TYPE_STATUTORY->value),
            fileName: null,
            batch: $postItem['batchName'],
            operator: $postItem['agent'],
            details: [],
            events: [],
            dtc: new \DateTime($postItem['dtc']),
            status: $postItem['statusId'] === 'RELEASED' ? StatusEnum::STATUS_SCAN_ONLY->value : StatusEnum::STATUS_ADDED->value,
            isLegacy: true,
        );
    }

    /**
     * @throws \Exception
     */
    public static function MockFromAdminPayload(Company $company, array $payload): MailroomPostItemData
    {
        $lastEmailSent = self::getLastEmailSentFromPayload($payload);
        $mockItem = new MailroomPostItemData(
            id: 1,
            companyName: $company->getCompanyName(),
            companyNumber: $company->getCompanyNumber(),
            type: PostItemTypeEnum::fromParsedSender($payload['post_item_type'])->value,
            sender: $payload['post_item_type'],
            fileName: 'test.pdf',
            batch: '12345678',
            operator: 'CMS_email_test',
            details: self::mockDetails($payload),
            events: [],
            dtc: ($payload['reminder_email'] ?? false) ?
                new \DateTime(sprintf('-%s days', MailboxEmailNameProvider::DAYS_TO_SEND_REMINDER[$lastEmailSent])) :
                new \DateTime(),
            status: self::getStatusFromPayload($payload),
        );

        return is_null($lastEmailSent) ? $mockItem : self::setLastEmailSent($mockItem, $lastEmailSent);
    }

    private static function parseSender(string $sender, string $type): string
    {
        if ($type === PostItemTypeEnum::TYPE_STATUTORY->value) {
            return self::MAILROOM_STATUTORY_SENDER_MAP[$sender] ?? self::GENERIC_STATUTORY_SENDER_NAME;
        }
        if ($type === PostItemTypeEnum::TYPE_NON_STATUTORY->value) {
            return self::MAILROOM_NONSTATUTORY_SENDER_MAP[$sender] ?? self::GENERIC_NONSTATUTORY_SENDER_NAME;
        }
        if ($type === PostItemTypeEnum::TYPE_PARCEL->value) {
            return self::MAILROOM_PARCEL_SENDER_MAP[$sender] ?? self::GENERIC_PARCEL_SENDER_NAME;
        }

        return 'Other';
    }

    private static function setLastEmailSent(MailroomPostItemData $mockItem, string $lastEmailSent): MailroomPostItemData
    {
        return $mockItem->setEvent(
            sprintf('email_sent:_%s_with_log_id_12345678', $lastEmailSent),
            'CMS_email_test',
            $mockItem->getDtc()
        )->setDetail(
            MailroomApiClient::LAST_EMAIL_SENT_DETAIL_NAME,
            $lastEmailSent
        );
    }

    private static function getStatusFromPayload(array $payload): string
    {
        return match (true) {
            $payload['handling_setting'] === MailboxProductPropertyHelper::PROCESSING_METHOD_SCANNED => StatusEnum::STATUS_SCAN_ONLY->value,
            $payload['handling_setting'] === MailboxProductPropertyHelper::PROCESSING_METHOD_COLLECT => StatusEnum::STATUS_TO_BE_COLLECTED->value,
            $payload['handling_setting'] === MailboxProductPropertyHelper::PROCESSING_METHOD_POST => StatusEnum::STATUS_TO_BE_FORWARDED->value,
            $payload['handling_setting'] === MailboxProductPropertyHelper::PROCESSING_METHOD_REJECT => StatusEnum::STATUS_TO_BE_RTS->value,
            default => StatusEnum::STATUS_ADDED->value,
        };
    }

    /**
     * @throws \Exception
     */
    private static function getLastEmailSentFromPayload(array $payload): ?string
    {
        return match (true) {
            !(bool) $payload['reminder_email'] => null,
            (int) $payload['mailbox_tier'] === 1 && $payload['post_item_type'] === self::GENERIC_NONSTATUTORY_SENDER_NAME => MailboxEmailNameProvider::MAIL_WAITING_PAYMENT_SCAN,
            $payload['post_item_type'] === self::GENERIC_PARCEL_SENDER_NAME && $payload['handling_setting'] === MailboxProductPropertyHelper::PROCESSING_METHOD_COLLECT => MailboxEmailNameProvider::PARCEL_RELEASED_COLLECT,
            PostItemTypeEnum::fromParsedSender($payload['post_item_type']) !== PostItemTypeEnum::TYPE_PARCEL && $payload['handling_setting'] === MailboxProductPropertyHelper::PROCESSING_METHOD_COLLECT => MailboxEmailNameProvider::MAIL_RELEASED_COLLECT,
            $payload['post_item_type'] === self::GENERIC_PARCEL_SENDER_NAME && $payload['handling_setting'] === MailboxProductPropertyHelper::PROCESSING_METHOD_POST => MailboxEmailNameProvider::PARCEL_WAITING_PAYMENT_POST,
            default => throw new \Exception('This scenario does not have a reminder email'),
        };
    }

    /**
     * @throws \Exception
     */
    private static function mockDetails(array $payload)
    {
        return match (PostItemTypeEnum::fromParsedSender($payload['post_item_type'])) {
            PostItemTypeEnum::TYPE_PARCEL => ["description" => 'Test description', "total_weight" => '100', "dimensions" => '10x10x10', "custom_price" => "12.34"],
            default => []
        };
    }
}
