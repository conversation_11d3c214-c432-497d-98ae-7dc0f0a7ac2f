<?php

declare(strict_types=1);

namespace MailScanModule\Services;

use CompaniesHouseModule\Deciders\CountryDecider;
use CompanyModule\Facades\MailForwardingFacade;
use Doctrine\ORM\NonUniqueResultException;
use Entities\Company;
use Exceptions\Technical\NodeException;
use FrontModule\controlers\ManagePaymentControler;
use FrontModule\controlers\MyServicesControler;
use MailScanModule\ApiClient\MailroomApiClient;
use MailScanModule\Deciders\MailboxTierDecider;
use MailScanModule\Deciders\ReleaseItemDecider;
use MailScanModule\Dto\MailroomPostItemData;
use MailScanModule\Entities\PostItem;
use MailScanModule\Enums\InboxStatusEnum;
use MailScanModule\Enums\StatusEnum;
use MailScanModule\Enums\PostItemTypeEnum;
use MailScanModule\Exceptions\MissingCountryException;
use MailScanModule\Exceptions\NoMailboxServiceException;
use MailScanModule\Exceptions\NotAMailboxProductException;
use MailScanModule\Facades\MailForwardingAddressFacade;
use MailScanModule\Helpers\MailboxProductPropertyHelper;
use MailScanModule\Providers\MailboxProductProvider;
use MailScanModule\Repositories\PostItemRepository;
use MailScanModule\Transformers\MailroomPostItemTransformer;
use Models\Products\BasketProduct;
use Models\Products\Product;
use Monolog\Logger;
use RouterModule\Generators\IUrlGenerator;
use RouterModule\Generators\UrlGenerator;
use Services\CompanyService;
use Services\NodeService;

class PostItemService
{
    public const WITHIN_TYPE_QUOTA = 'withinTypeQuota';
    public const WITHIN_GENERIC_QUOTA = 'withinGenericQuota';
    public const DEFAULT_DAYS_TO_REPROCESS = 7;

    public function __construct(
        private readonly MailroomApiClient $mailroomApiClient,
        private readonly PostItemRepository $postItemRepository,
        private readonly MailroomPostItemTransformer $transformer,
        private readonly CompanyService $companyService,
        private readonly ReleaseItemDecider $releaseItemDecider,
        private readonly MailForwardingFacade $mailForwardingFacade,
        private readonly MailForwardingAddressFacade $mailForwardingAddressFacade,
        private readonly NodeService $nodeService,
        private readonly UrlGenerator $urlGenerator,
        private readonly MailboxProductProvider $mailboxProductProvider,
    ) {
    }

    /**
     * @throws \Exception
     */
    public function get(int|string $postItemId, Company $company): ?MailroomPostItemData
    {
        try {
            $postItem = $this->mailroomApiClient->getPostItemById((string) $postItemId);

            if (!empty($postItem)) {
                $this->validate($postItem, $company);

                return $postItem;
            }
        } catch (\Exception $e) {
            // do nothing
        }

        $postItem = $this->postItemRepository->find($postItemId);

        if (empty($postItem)) {
            throw new \InvalidArgumentException('Invalid Post Item - not found!');
        }

        $this->validate($postItem, $company);

        return $this->transformer->makeDTOFromEntity($postItem);
    }

    /**
     * @throws NonUniqueResultException
     * @throws \Exception
     */
    public function enhancePostItems(array $postItems): array
    {
        /** @var MailroomPostItemData $postItem */
        foreach ($postItems as $postItem) {
            $company = $this->companyService->getCompanyByCompanyNumber($postItem->getCompanyNumber());
            $postItem->setCompanyId($company->getId())
                ->removePdfFileInfo();

            if ($postItem->isStatusAdded()) {
                $this->handleAddedPostItem($postItem, $company);
                continue;
            }

            if ($postItem->isStatusWaitingPayment()) {
                $this->handleWaitingPaymentPostItem($postItem, $company);
                continue;
            }

            $this->setAvailabilityParameters($postItem);
        }

        return $postItems;
    }

    /**
     * @throws NoMailboxServiceException
     */
    public function isWithinQuotaMaximum(
        Company $company,
        BasketProduct $product,
        MailroomPostItemData $item,
        int $processingMethod,
    ): string|bool {
        if ($this->hasUnlimitedQuotas($product, $item, $processingMethod)) {
            return true;
        }

        $processingMethodAsString = MailboxProductPropertyHelper::getProcessingMethodAsString($item->getType(), $processingMethod);

        $withinMaximumQuotaByType = MailboxProductPropertyHelper::getMaximumQuotaByTypeAndProcessingMethod(
            $product,
            $item->getType(),
            $processingMethod
        ) > $this->mailForwardingFacade->getQuotasByType(
            $company,
            $processingMethodAsString,
            PostItemTypeEnum::fromType($item->getType()),
        );

        $withinMaximumGenericQuota = MailboxProductPropertyHelper::getMaximumGenericQuotaByTypeAndProcessingMethod(
            $product,
            $processingMethodAsString,
        ) > $this->mailForwardingFacade->getQuotasByType(
            $company,
            $processingMethodAsString,
        );

        return match (true) {
            $withinMaximumQuotaByType => self::WITHIN_TYPE_QUOTA,
            $withinMaximumGenericQuota => self::WITHIN_GENERIC_QUOTA,
            default => false,
        };
    }

    public function hasUnlimitedQuotas(
        BasketProduct $product,
        MailroomPostItemData $item,
        int $processingMethod,
    ): bool {
        return MailboxProductPropertyHelper::hasUnlimitedQuotasByTypeAndProcessingMethod(
            $product,
            $item->getType(),
            $processingMethod
        );
    }

    /**
     * @throws NodeException
     * @throws \Exception
     */
    public function getProductByTier(int $tier, Company $company): BasketProduct
    {
        return match ($tier) {
            MailboxTierDecider::TIER_1 => $this->nodeService->requiredProductByName((string) Product::PRODUCT_REGISTERED_OFFICE),
            MailboxTierDecider::TIER_2 => $this->nodeService->requiredProductByName(Product::PRODUCT_MAILBOX_STANDARD_INITIAL),
            MailboxTierDecider::TIER_3 => $this->nodeService->requiredProductByName(Product::PRODUCT_MAILBOX_PREMIUM_INITIAL),
            MailboxTierDecider::TIER_4 => $this->nodeService->requiredProductByName(Product::PRODUCT_MAILBOX_BUSINESS_ADDRESS_INITIAL),
            default => throw new \Exception(sprintf('Mailbox service should have already been checked at this point. Please check. Company: %s - %s', $company->getCompanyNumber(), $company->getCompanyName())),
        };
    }

    /**
     * @throws NotAMailboxProductException
     * @throws NodeException
     * @throws \Exception
     */
    public function getReleasePrice(MailroomPostItemData $postItem, Company $company): float
    {
        if ($postItem->isParcel()) {
            return $this->getParcelReleasePrice($postItem, $company);
        }

        if (!is_null($payToReleasePrice = $postItem->getDetail(MailroomApiClient::PAY_TO_RELEASE_FEE_DETAIL_NAME))) {
            return floatval($payToReleasePrice);
        }

        $mailboxInitialProduct = $this->mailboxProductProvider->getInitialProductFromCompany($company);

        return MailboxProductPropertyHelper::getPayToReleaseFeeByFormat(
            $mailboxInitialProduct,
            $postItem->getFormat()
        );
    }

    /**
     * @throws \Exception
     */
    public function getParcelReleasePrice(MailroomPostItemData $postItem, Company $company): float
    {
        if (!empty($customPrice = $postItem->getDetail(MailroomApiClient::CUSTOM_PRICE_DETAIL_NAME))) {
            return floatval($customPrice);
        }

        $country = $this->mailForwardingAddressFacade->getMailForwardingAddressSetting($company)->getCountry();

        if (empty($country)) {
            throw new MissingCountryException();
        }

        $region = match (true) {
            CountryDecider::isUkCountry($country)  => MailroomApiClient::REGION_UK,
            CountryDecider::isEEACountry($country) => MailroomApiClient::REGION_EUROPE,
            default                                => MailroomApiClient::REGION_WORLD,
        };

        return $this->mailroomApiClient->getParcelTransitFee($postItem, $region);
    }

    /**
     * @throws \Throwable
     */
    public function setPostItemsToReprocess(Company $company, int $days = self::DEFAULT_DAYS_TO_REPROCESS): void
    {
        try {
            $companyPostItems = $this->mailroomApiClient->getPostItemsForCompany($company->getCompanyNumber())['postItems']->getAllPostItems();
            if (empty($companyPostItems)) {
                return;
            }

            $postItemsToReprocess = array_filter(
                $companyPostItems,
                fn (MailroomPostItemData $postItem) => $postItem->getDtc()->diff(new \DateTime())->days <= $days
                    && $postItem->isReprocessableStatus()
            );

            if (empty($postItemsToReprocess)) {
                return;
            }

            $this->mailroomApiClient->setPostItemStatus(
                $postItemsToReprocess,
                StatusEnum::STATUS_ADDED->value
            );

            return;
        } catch (\Throwable $e) {
            throw new \RuntimeException('Failed to set post items to reprocess: %s');
        }
    }

    private function validate(MailroomPostItemData|PostItem $item, Company $company): void
    {
        if ($item->getCompanyNumber() !== $company->getCompanyNumber()) {
            throw new \InvalidArgumentException('Invalid Post Item - not related to the given company!');
        }
    }

    private function setIdCheckParameters(MailroomPostItemData $postItem, Company $company): void
    {
        $postItem->setTooltip('In order to access this item of mail you need to complete the proof of ID process for this company.')
            ->setInboxStatus(InboxStatusEnum::POST_STATUS_NOT_RELEASED)
            ->setCta(sprintf(
                '<a href="%s" target="_blank">Perform ID Check</a>',
                $this->urlGenerator->url('id_entity_checks', ['company' => $company->getId()])
            ));
    }

    private function setChargingIssuesParameters(MailroomPostItemData $postItem): void
    {
        $postItem->setTooltip('We attempted to charge an extra quota for this product but failed. Please update your payment methods or contact support.')
            ->setInboxStatus(InboxStatusEnum::POST_STATUS_NOT_RELEASED)
            ->setCta(sprintf(
                '<a href="%s" target="_blank">Review Payment Method</a>',
                $this->urlGenerator->url((string) ManagePaymentControler::MANAGE_PAYMENT_PAGE, [], IUrlGenerator::OLD_LINK | IUrlGenerator::ABSOLUTE)
            ));
    }

    /**
     * @throws \Exception
     */
    private function setAvailabilityParameters(MailroomPostItemData $postItem): void
    {
        $postItem->setInboxStatusByStatus();

        if (!$postItem->isParcel()) {
            $postItem->setCanBeDownloaded(true)
                ->setCta('View');
        }
    }

    private function handleAddedPostItem(MailroomPostItemData $postItem, ?Company $company): void
    {
        match (true) {
            $company->isDissolved() => $this->setDissolvedCompanyParameters($company, $postItem),
            !$this->releaseItemDecider->isIdCheckCompleted($company) => $this->setIdCheckParameters($postItem, $company),
            !$company->hasActiveMailboxService() => $this->handleServiceIssues($company, $postItem),
            $postItem->hasUnpaidQuotaCharges() => $this->setChargingIssuesParameters($postItem),
            default => $this->setProcessingParameters($postItem),
        };
    }

    private function handleServiceIssues(Company $company, MailroomPostItemData $postItem): void
    {
        $service = $company->getActiveOrLatestMailboxService();

        if (empty($service)) {
            $this->setNoServiceParameters($postItem);

            return;
        }

        if ($service->isOverdueAndExpired()) {
            $this->setInactiveServiceParameters($postItem);

            return;
        }

        $this->setNoServiceParameters($postItem);
    }

    private function setProcessingParameters(MailroomPostItemData $postItem): void
    {
        $postItem->setTooltip('This item is currently being processed, please check back in a few hours.')
            ->setInboxStatus(InboxStatusEnum::POST_STATUS_PROCESSING);
    }

    private function setNoServiceParameters(MailroomPostItemData $postItem): void
    {
        $postItem->setTooltip('In order to access this item of mail you need to have an active mail forwarding service.')
            ->setInboxStatus(InboxStatusEnum::POST_STATUS_NOT_RELEASED)
            ->setCta(sprintf(
                '<a href="%s" target="_blank">Purchase Service</a>',
                '/services/registered-office-address#mail-forwarding'
            ));
    }

    private function setInactiveServiceParameters(MailroomPostItemData $postItem): void
    {
        $postItem->setTooltip('In order to access this item of mail you need to have an active mail forwarding service.')
            ->setInboxStatus(InboxStatusEnum::POST_STATUS_NOT_RELEASED)
            ->setCta(sprintf(
                '<a href="%s" target="_blank">Activate Service</a>',
                $this->urlGenerator->url((string) MyServicesControler::PAGE_SERVICES, [], IUrlGenerator::OLD_LINK | IUrlGenerator::ABSOLUTE)
            ));
    }

    /**
     * @throws \Exception
     */
    private function handleWaitingPaymentPostItem(MailroomPostItemData $postItem, Company $company): void
    {
        match (true) {
            $company->isDissolved() => $this->setDissolvedCompanyParameters($company, $postItem),
            !$this->releaseItemDecider->isIdCheckCompleted($company) => $this->setIdCheckParameters($postItem, $company),
            !$company->hasActiveMailboxService() => $this->handleServiceIssues($company, $postItem),
            $postItem->isParcel() => $this->handleParcelWaitingPayment($company, $postItem),
            default => $this->setPayToViewParameters($postItem, $company),
        };
    }

    /**
     * @throws \Exception
     */
    private function handleParcelWaitingPayment(Company $company, MailroomPostItemData $postItem): void
    {
        $postItem->setCanBeDownloaded(false);
        $this->setPayToForwardParameters($postItem, $company);
    }

    /**
     * @throws \Exception
     */
    private function setPayToForwardParameters(MailroomPostItemData $postItem, Company $company): void
    {
        $postItem->setInboxStatusByStatus();
        try {
            $postItem->setTooltip('') // @TODO: TBA
            ->setCta('Pay to Forward')
                ->setRequiresPayment(true)
                ->setInboxPrice(
                    $this->getParcelReleasePrice($postItem, $company)
                );
        } catch (MissingCountryException) {
            $postItem->setCta(
                '<a href="/company-inbox/settings/">Review Forwarding Address</a>'
            )->setRequiresPayment(false)
                ->setTooltip("We're unable to calculate your parcel forwarding fee. Please review your forwarding address.");
        } catch (\Exception) {
            $postItem->setCta(
                '<a href="https://support.companiesmadesimple.com/hc/en-us" target="_blank">Contact Support</a>'
            )->setRequiresPayment(false)
                ->setTooltip("We're unable to calculate your parcel forwarding fee. Please contact support.");
        }
    }

    /**
     * @throws NodeException
     * @throws NotAMailboxProductException
     * @throws \Exception
     */
    private function setPayToViewParameters(MailroomPostItemData $postItem, Company $company): void
    {
        $postItem->setTooltip('') // @TODO: TBA
            ->setInboxStatusByStatus()
            ->setCta('Pay to View')
            ->setRequiresPayment(true)
            ->setInboxPrice($this->getReleasePrice($postItem, $company));
    }

    private function setDissolvedCompanyParameters(Company $company, MailroomPostItemData $postItem): void
    {
        $postItem->setInboxStatus(InboxStatusEnum::POST_STATUS_NOT_RELEASED)
            ->setTooltip('You cannot access this item because your company is dissolved.');
    }
}
