<?php

declare(strict_types=1);

namespace MailScanModule\Controllers\Api;

use Entities\Company;
use Entities\Customer;
use MailScanModule\UseCase\CompanyInbox\Api;
use RouterModule\ApiController;
use Symfony\Component\HttpFoundation\JsonResponse;

class CompanyInboxApiController extends ApiController
{
    public function __construct(
        private readonly Api\DownloadItem\Command $downloadItemCommand,
        private readonly Api\DownloadItem\Factory $downloadItemFactory,
        private readonly Api\GenerateInlinePaymentBlock\Command $generateInlinePaymentBlockCommand,
        private readonly Api\GenerateInlinePaymentBlock\Factory $generateInlinePaymentBlockFactory,
        private readonly Api\ListItems\Command $listItemsCommand,
        private readonly Api\ListItems\Factory $listItemsFactory,
        private readonly Api\MarkAsPurchased\Command $markAsPurchasedCommand,
        private readonly Api\MarkAsPurchased\Factory $markAsPurchasedFactory,
    ) {
        $this->setApikeyRequiredFalse();
    }

    public function markAsPurchased(Company $company): JsonResponse
    {
        try {
            return $this->successResponse(
                $this->markAsPurchasedCommand->execute(
                    $this->markAsPurchasedFactory->makeRequest(
                        $company,
                        $this->getSymfonyRequest()->getPayload()->all()
                    )
                )
            );
        } catch (\Throwable $e) {
            return $this->apiErrorResponse($e->getMessage(), [], $e->getCode());
        }
    }

    public function downloadItem(Company $company, string $item): JsonResponse
    {
        try {
            return $this->successResponse(
                $this->downloadItemCommand->execute(
                    $this->downloadItemFactory->makeRequest(
                        $company,
                        $item
                    )
                )
            );
        } catch (\Throwable $e) {
            return $this->apiExceptionResponse($e);
        }
    }

    public function listItems(Customer $customer): JsonResponse
    {
        try {
            return $this->successResponse(
                $this->listItemsCommand->execute(
                    $this->listItemsFactory->makeRequest(
                        $customer,
                        $this->getSymfonyRequest()->getPayload()->all()
                    )
                )
            );
        } catch (\Throwable $e) {
            return $this->apiErrorResponse($e->getMessage(), [], $e->getCode());
        }
    }

    public function generateInlinePaymentBlock(Company $company): JsonResponse
    {
        try {
            return $this->successResponse(
                $this->generateInlinePaymentBlockCommand->execute(
                    $this->generateInlinePaymentBlockFactory->makeRequest(
                        $company,
                        $this->getSymfonyRequest()->getPayload()->all()
                    )
                )
            );
        } catch (\Throwable $e) {
            return $this->apiExceptionResponse($e);
        }
    }
}
