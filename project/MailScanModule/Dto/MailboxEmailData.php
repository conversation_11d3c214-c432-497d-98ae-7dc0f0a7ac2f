<?php

declare(strict_types=1);

namespace MailScanModule\Dto;

class MailboxEmailData
{
    public function __construct(
        public string $companyName,
        public int $companyId,
        public string $customerFirstName,
        public string $customerFullName,
        public string $postItemType,
        public bool $isReleased,
        public string $itemStatus,
        public \DateTime $itemReceivedDate,
        public string $mailTypeHtml,
        public array $itemDetails = [],
        public bool $hasFailedToCharge = false,
        public ?bool $withinQuota = null,
        public ?string $lastEmailSent = null,
        public ?int $handlingSetting = null,
        public ?int $mailboxTier = null,
        public ?string $packageName = null,
        public ?string $forwardingAddress = null,
        public ?int $maxQuotas = null,
        public ?string $quotaPriceLabel = null,
        public ?string $releasePriceLabel = null,
        public ?bool $isServiceOverdue = null,
        public bool $hasIdCheck = true,
        public bool $needsForwardingAddress = false,
    ) {
    }
}
