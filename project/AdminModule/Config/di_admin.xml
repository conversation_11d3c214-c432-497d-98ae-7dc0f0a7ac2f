<?xml version="1.0" ?>

<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <defaults public="true" />
        <service class="AdminModule\Factories\CompaniesAdminControlerViewFactory" id="admin_module.factories.companies_admin_controler_view_factory">
            <argument id="form_submission_module.factories.form_submissions_view_factory" type="service"/>
            <argument id="company_formation_module.repositories.sic_codes_repository" type="service"/>
        </service>

        <service class="AdminModule\controlers\Emails\IdEmailTemplateController" id="admin_module.controlers.emails.id_email_template_controller">
            <argument id="id_module.repositories.id_info_repository" type="service"/>
            <argument id="id_module.repositories.id_info_customer_repository" type="service"/>
            <argument id="id_module.emailers.id_emailer" type="service"/>
        </service>

        <service class="AdminModule\Deciders\CompanyTransferDecider" id="admin_module.deciders.company_transfer_decider">
            <argument id="id_module.verification.validation_checker" type="service"/>
        </service>

        <service class="AdminModule\controlers\Emails\GeneralEmailTemplateController" id="admin_module.controlers.emails.general_email_template_controller">
            <argument id="email_module.loaders.front_matter_template_loader" type="service"/>
        </service>

        <service class="AdminModule\Factories\CustomerCompaniesAdminDataGridFactory" id="admin_module.factories.customer_companies_admin_datagrid_factory">
            <argument id="repositories.company_repository" type="service"/>
            <argument id="url_generator" type="service"/>
        </service>

        <service class="AdminModule\Factories\CustomerOrdersAdminDataGridFactory" id="admin_module.factories.customer_orders_admin_datagrid_factory">
            <argument id="repositories.order_repository" type="service"/>
            <argument id="url_generator" type="service"/>
        </service>

        <service class="AdminModule\Facades\MantleFacade" id="admin_module.facades.mantle_facade" />

        <service class="AdminModule\Authenticators\UniversalAdminAuth" id="admin_module.authenticators.universal_admin_auth">
            <argument id="user_module.repositories.admin_user_repository" type="service"/>
            <argument id="error.loggers.monolog" type="service" />
        </service>

        <service class="AdminModule\Providers\PurchaseLinkEmailProvider" id="admin_module.providers.purchase_link_email_provider">
            <argument id="email_module.loaders.front_matter_template_loader" type="service"/>
        </service>

        <service class="AdminModule\Controllers\Admin\CommandController" id="admin_module.controllers.admin.command_controller">
            <argument id="mail_scan_module.commands.mailbox_post_item_processor" type="service" />
            <argument id="router_module.helpers.controller_helper" type="service" />
            <argument>%env.environment%</argument>
        </service>
    </services>
</container>
