id_status_email:
  path: /admin/email-templates/id/company/{company}/{email}/
  defaults:
    feature: role_admin
    controller: admin_module.controlers.emails.id_email_template_controller
    action: showStatusEmail
    requirements:
      company:
        converter: doctrine

id_summary_email:
  path: /admin/email-templates/id/customer/{customer}/{email}/
  defaults:
    feature: role_admin
    controller: admin_module.controlers.emails.id_email_template_controller
    action: showSummaryEmail
    requirements:
      customer:
        converter: doctrine

transferwise_email:
  path: /admin/email-templates/transferwise/customer/{customer}/
  defaults:
    feature: role_admin
    controller: admin_module.controlers.emails.general_email_template_controller
    action: showTransferWise
    requirements:
      customer:
        converter: doctrine

cancel_company_transfer:
  path: /admin/company-transfer/{companyId}/cancel/
  defaults:
    feature: role_admin
    controller: admin_module.controlers.company_transfer_controller
    action: cancelTransfer

needs_warning_message:
  path: /admin/company-transfer/need-warning/{fromCustomer}/{toCustomer}/
  defaults:
    feature: role_admin
    controller: admin_module.controlers.company_transfer_controller
    action: needsWarningMessage
    requirements:
      fromCustomer:
        converter: doctrine
      toCustomer:
        converter: doctrine

manually_download_certificate:
  path: /admin/manually-download-certificate/{companyId}/
  defaults:
    feature: role_admin
    controller: company_module.controllers.admin.company_controller
    action: manuallyDownloadCertificate
    title: Companies

admin_run_process_post_items_command:
  path: /admin/run-process-items-command/
  defaults:
    feature: role_admin
    controller: admin_module.controllers.admin.command_controller
    action: runProcessPostItemsCommand