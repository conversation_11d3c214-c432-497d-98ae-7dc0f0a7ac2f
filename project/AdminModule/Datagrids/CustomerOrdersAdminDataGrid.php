<?php

declare(strict_types=1);

namespace AdminModule\Datagrids;

use AdminModule\Controlers\OrdersAdminControler;
use DataGrid\src\DataGrid\DataGrid;
use DataGrid\src\DataGrid\DataGridTextColumn;
use <PERSON>bi\IDataSource;
use Models\OldModels\Transaction;
use PaymentModule\Contracts\IPaymentResponse;
use RouterModule\Generators\IUrlGenerator;

class CustomerOrdersAdminDataGrid extends DataGrid
{
    public const PRIMARY_KEY = 'orderId';

    public function __construct(
        private readonly IUrlGenerator $urlGenerator,
        IDataSource $datasource,
    ) {
        parent::__construct($datasource, self::PRIMARY_KEY);
    }

    public function renderDate(DataGridTextColumn $column, $text, array $order)
    {
        return $order['dtc']->format('Y-m-d');
    }

    public function renderAction(DataGridTextColumn $column, $text, array $order)
    {
        $url = $this->urlGenerator->url(
            \sprintf('%s view', OrdersAdminControler::ORDERS_PAGE),
            ['order_id' => $order['orderId']],
            IUrlGenerator::OLD_LINK | IUrlGenerator::ADMIN_LINK
        );

        return \sprintf(
            '<a href="%s">view</a>',
            $url
        );
    }

    protected function init(): void
    {
        $this->getPaginator()->setItemsPerPage(50);

        /* --- Columns --- */
        $this->addTextColumn('orderId', 'Order ID', 10);
        $this->addFormatColumn('dtc', 'Date', 10, '', [$this, 'renderDate']);
        $this->addTextColumn('companyId', 'Company Id', 10);
        $this->addTextColumn('companyName', 'Company name', 30);
        $this->addTextColumn('vat', 'VAT', 10);
        $this->addTextColumn('subTotal', 'Subtotal', 10);
        $this->addTextColumn('total', 'Total', 10);
        $this->addFormatColumn('actions', 'Actions', 10, 'center', [$this, 'renderAction']);

        /* --- filters --- */
        $this->addTextFilter('orderId', 'Order Id', 'o');
        $this->addTextFilter('dtc', 'Date', 'o');
        $this->addTextFilter('total', 'Total', 'o');
        $this->addTextFilter('companyId', 'Company ID', 'c');
        $this->addTextFilter('companyName', 'Company Name', 'c');
        $this->addTextFilter('description', 'Description', 'o');
        $this->addTextFilter('orderCode', 'Payment ID', 't');
        $this->addSelectFilter(
            'paymentMediumId',
            'Payment Medium',
            [
                IPaymentResponse::MEDIUM_ON_SITE => 'On site',
                IPaymentResponse::MEDIUM_PHONE => 'Phone',
                IPaymentResponse::MEDIUM_RENEWAL => 'Auto renewal',
                IPaymentResponse::MEDIUM_COMPLIMENTARY => 'Complimentary',
                IPaymentResponse::MEDIUM_NON_STANDARD => 'Non standard',
            ],
            'o'
        );
		$this->addSelectFilter('typeId', 'Payment Type', [
			Transaction::TYPE_PAYPAL => 'Paypal',
			Transaction::TYPE_CREDIT => 'Credit',
			Transaction::TYPE_OMNIPAY => 'Omnipay',
			Transaction::TYPE_OMNIPAY_REFUND => 'Omnipay Refund',
			Transaction::TYPE_CREDIT_EXPIRED => 'Credit Expired',
			Transaction::TYPE_OMNIPAY_CREDIT => 'On Account Omnipay',
			Transaction::TYPE_OMNIPAY_DIRECT_DEBIT => 'Omnipay Direct Debit',
			Transaction::TYPE_OMNIPAY_DIRECT_DEBIT_REFUND => 'Omnipay Direct Debit Refund'
		], 't');
    }
}
