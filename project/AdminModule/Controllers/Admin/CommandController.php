<?php

declare(strict_types=1);

namespace AdminModule\Controllers\Admin;

use BootstrapModule\ApplicationLoader_Abstract;
use Doctrine\ORM\NonUniqueResultException;
use Exceptions\Business\BasketException;
use Exceptions\Technical\NodeException;
use Framework\FApplication;
use MailScanModule\Commands\MailboxPostItemProcessor;
use RouterModule\Helpers\IControllerHelper;
use Symfony\Component\HttpFoundation\RedirectResponse;

readonly class CommandController
{
    private bool $isProduction;

    public function __construct(
        private MailboxPostItemProcessor $mailboxPostItemProcessor,
        private IControllerHelper $controllerHelper,
        private string $environment, /** @phpstan-ignore-line */
    ) {
        $this->isProduction = $environment === ApplicationLoader_Abstract::PRODUCTION;
    }

    /**
     * @throws NodeException
     * @throws NonUniqueResultException
     * @throws BasketException
     * @throws \Exception
     */
    public function runProcessPostItemsCommand(): RedirectResponse
    {
        if (!FApplication::isAdmin()) {
            throw new \Exception('Action not allowed.');
        }

        if ($this->isProduction) {
            throw new \Exception('Cannot run this command in production.');
        }

        $this->mailboxPostItemProcessor->process(dryRun: false);
        return $this->controllerHelper->redirectionTo('homepage');
    }
}