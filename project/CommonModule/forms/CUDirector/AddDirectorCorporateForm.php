<?php

declare(strict_types=1);

namespace CommonModule\forms\CUDirector;

use AdminModule\forms\CHForm;
use CompaniesHouse\Repositories\CountriesRepository;
use CompaniesHouseModule\Services\SubmissionHandler;
use Entities\Company as CompanyEntity;
use Framework\FControler;
use Framework\Forms\Controls\DatePickerNew;
use Framework\Forms\Controls\SingleCheckbox;
use Framework\Forms\FForm;
use Id3GlobalApiClient\Entities\Address;
use IdModule\Domain\CompanyDetails;
use IdModule\Domain\IdEntity;
use IdModule\Repositories\IIdEntityInfoRepository;
use Libs\CHFiling\Core\Company;
use Libs\CHFiling\Core\UtilityClass\Address as CorporateAddress;
use Libs\CHFiling\Core\UtilityClass\Corporate;
use Libs\CHFiling\Core\UtilityClass\Identification;
use Libs\Forms\Helpers\Prefiller;
use Libs\Forms\Validators\CHValidator;
use Utils\Date;

class AddDirectorCorporateForm extends CHForm
{
    /**
     * @var FControler
     */
    public $controller;

    /**
     * @var Company
     */
    public $company;

    /**
     * @var CompanyEntity
     */
    public $companyEntity;

    /**
     * @var array
     */
    public $callback;

    /**
     * @var Prefiller
     */
    public $prefiller;

    /**
     * @var CountriesRepository
     */
    private $chCountriesRepository;

    /**
     * @var IIdEntityInfoRepository
     */
    private $idEntityInfoRepository;

    /**
     * @var SubmissionHandler
     */
    private $submissionHandler;

    /**
     * @param FControler          $controller
     * @param array               $callback
     * @param Company             $company
     * @param CompanyEntity       $companyEntity
     * @param Prefiller           $prefiller
     * @param CountriesRepository $chCountriesRepository
     */
    public function startup(
        FControler $controller,
        array $callback,
        Company $company,
        CompanyEntity $companyEntity,
        Prefiller $prefiller,
        CountriesRepository $chCountriesRepository,
    ) {
        $this->controller = $controller;
        $this->company = $company;
        $this->companyEntity = $companyEntity;
        $this->prefiller = $prefiller;
        $this->callback = $callback;
        $this->chCountriesRepository = $chCountriesRepository;
        $this->init();
    }

    public function startupWithEntityRepository(
        FControler $controller,
        array $callback,
        Company $company,
        CompanyEntity $companyEntity,
        Prefiller $prefiller,
        CountriesRepository $chCountriesRepository,
        IIdEntityInfoRepository $idEntityInfoRepository,
        SubmissionHandler $submitHandler,
    ) {
        $this->controller = $controller;
        $this->company = $company;
        $this->companyEntity = $companyEntity;
        $this->prefiller = $prefiller;
        $this->callback = $callback;
        $this->chCountriesRepository = $chCountriesRepository;
        $this->idEntityInfoRepository = $idEntityInfoRepository;
        $this->submissionHandler = $submitHandler;
        $this->init();
    }

    /**
     * @throws \Exception
     */
    public function process(): void
    {
        try {
            $data = $this->getValues();
            $directorData = $this->setupCorporateDirector($data);

            $this->company->sendAppointmentOfCorporateDirector($directorData['directorCorporate']);
            $this->clean();
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * @throws \Exception
     */
    public function processWithEntity(): ?string
    {
        try {
            $directorData = $this->setupCorporateDirector($this->getValues());
            $directorCorporate = $directorData['directorCorporate'];
            $address = $directorData['address'];

            $entity = $this->buildEntity($directorCorporate->getCorporate(), $address);
            $idInfo = $this->idEntityInfoRepository->getIdInfoForEntity($entity);

            $formSubmissionId = $this->company->sendAppointmentOfCorporateDirector($directorCorporate, $idInfo->isValid());
            $this->clean();

            if (!$idInfo->isValid()) {
                $this->submissionHandler->withHoldSubmission($this->companyEntity, $formSubmissionId);

                return $idInfo->getId();
            }

            return null;
        } catch (\Exception $e) {
            throw $e;
        }
    }

    private function init()
    {
        $prefillAddress = $this->prefiller->getPrefillAdresses();
        $prefillOfficers = $this->prefiller->getPrefillOfficers();

        // prefill
        $this->addFieldset('Prefill');
        $this->addSelect('prefillOfficers', 'Prefill Officers', $prefillOfficers['select'])
            ->class('form-control')
            ->setFirstOption('--- Select --');

        // person
        $this->addFieldset('Corporate');
        $this->addText('corporate_name', 'Company name *')
            ->class('form-control')
            ->addRule(FForm::Required, 'Please provide company name');

        $this->addText('forename', 'First name *')
            ->class('form-control')
            ->addRule(FForm::Required, 'Please provide first name')
            ->addRule(FForm::MAX_LENGTH, "First name can't be more than 50 characters", 50);
        $this->addText('surname', 'Last name *')
            ->class('form-control')
            ->addRule(FForm::Required, 'Please provide last name')
            ->addRule(FForm::MAX_LENGTH, "Last name can't be more than 160 characters", 160);

        // prefill
        $this->addFieldset('Prefill');
        $this->addSelect('prefillAddress', 'Prefill Address', $prefillAddress['select'])
            ->class('form-control')
            ->setFirstOption('--- Select ---');

        $incorporationDate = $this->companyEntity->getIncorporationDate();
        // apointment date
        $this->addFieldset('Appointment Date');
        $datePicker = $this->add(DatePickerNew::class, 'appointment_date', 'Date of Appointment *')
            ->addRule(FForm::Required, 'Please select New Date')
            ->addRule('DateFormat', 'Please enter date format as DD-MM-YYYY', 'd-m-Y')
            ->addRule('DateMin', 'Appointment date cannot be before the company\'s incorporation date %s', [$incorporationDate, 'd-m-Y'])
            ->addRule('DateMax', 'Date cannot be in the future', [new Date(), 'd-m-Y'])
            ->setDescription('Date cannot be in the future');
        $datePicker->class('date form-control')->setValue(date('d-m-Y'));

        // address
        $this->addFieldset('Address');
        $this->addText('premise', 'Building name/number *')
            ->class('form-control')
            ->addRule(FForm::Required, 'Please provide Building name/number')
            ->addRule(FForm::MAX_LENGTH, "Building name/number can't be more than 50 characters", 50);
        $this->addText('street', 'Street *')
            ->class('form-control')
            ->addRule(FForm::Required, 'Please provide Street')
            ->addRule(FForm::MAX_LENGTH, "Street can't be more than 50 characters", 50);
        $this->addText('thoroughfare', 'Address 3')
            ->class('form-control')
            ->addRule(FForm::MAX_LENGTH, "Address 3 can't be more than 50 characters", 50);
        $this->addText('post_town', 'Town *')
            ->class('form-control')
            ->addRule(FForm::Required, 'Please provide Town')
            ->addRule(FForm::MAX_LENGTH, "Town can't be more than 50 characters", 50);
        $this->addText('county', 'County')
            ->class('form-control')
            ->addRule(FForm::MAX_LENGTH, "County can't be more than 50 characters", 50);
        $this->addText('postcode', 'Postcode *')
            ->class('form-control')
            ->addRule(FForm::Required, 'Please provide Postcode')
            ->addRule(FForm::MAX_LENGTH, "Postcode can't be more than 15 characters", 15);
        $this->addSelect('country', 'Country *', $this->chCountriesRepository->getServiceAddressCountries())
            ->class('form-control')
            ->addRule(FForm::Required, 'Please provide Country');

        $this->addFieldset('UK/ Non UK');
        $this->addRadio('type', 'Type *', [1 => 'UK', 2 => 'Non UK'])
            ->addRule(FForm::Required, 'Please provide UK type!');
        $this->addSelect('place_registered', 'Country Registered *', array_merge(['' => 'Choose an option'], $this->chCountriesRepository->getPersonCountriesOfResidence()))
            ->class('form-control')
            ->addRule([CHValidator::class, 'Validator_ukRequired'], 'Please provide Country registered!');
        $this->addText('registration_number', 'Registration number *')
            ->class('form-control')
            ->addRule([CHValidator::class, 'Validator_ukRequired'], 'Please provide Registration number!');
        $this->addText('law_governed', 'Governing law *')
            ->class('form-control')
            ->addRule([CHValidator::class, 'Validator_ukRequired'], 'Please provide Governing law!');
        $this->addText('legal_form', 'Legal From *')
            ->class('form-control')
            ->addRule([CHValidator::class, 'Validator_ukRequired'], 'Please provide Legal form!');

        // security
        if ($this->company->getType() == 'LLP') {
            $this->addFieldset('Designation');
            $this->addRadio('designated_ind', 'DesignatedInd *', [0 => 'No', 1 => 'Yes'])
                ->addRule(FForm::Required, 'Please provide DesignatedInd');
        }

        $this->addFieldset('Consent to act');
        if ($this->company->getType() == 'LLP') {
            $consentLabel = 'The Limited Liability Partnership (LLP) confirms that the corporate body named has consented to act as a designated member';
        } else {
            $consentLabel = 'The company confirms that the corporate body named has consented to act as a director';
        }

        $this->add(SingleCheckbox::class, 'consentToAct', $consentLabel)
            ->addRule(FForm::Required, 'Consent to act is required')
            ->{'data-label-designated_ind-0'}('The Limited Liability Partnership (LLP) confirms that the corporate body named has consented to act as a non-designated member')
            ->{'data-label-designated_ind-1'}('The Limited Liability Partnership (LLP) confirms that the corporate body named has consented to act as a designated member');

        $this->addFieldset('Action');
        $this->addSubmit('continue', 'Continue >')->class('btn btn-primary');

        $this->onValid = $this->callback;
        $this->start();
    }

    private function setupCorporateDirector(array $data): array
    {
        $directorCorporate = $this->company->getNewCorporate('dir');
        $corporate = $directorCorporate->getCorporate();
        $corporate->setFields($data);
        $corporate->setAppointmentDate(
            !empty($data['appointment_date']) ? Date::changeFormat($data['appointment_date'], 'd-m-Y', 'Y-m-d') : null
        );
        $directorCorporate->setCorporate($corporate);

        $address = $directorCorporate->getAddress();
        $address->setFields($data);
        $directorCorporate->setAddress($address);

        if ($this->company->getType() == 'LLP') {
            $directorCorporate->setDesignatedInd($data['designated_ind']);
        }

        $directorCorporate->setConsentToAct($data['consentToAct']);

        $identification = $directorCorporate->getIdentification(
            $data['type'] == 1 ? Identification::UK : Identification::NonUK
        );

        $identification->setFields($data);
        $directorCorporate->setIdentification($identification);

        return [
            'directorCorporate' => $directorCorporate,
            'address' => $address,
        ];
    }

    private function buildEntity(Corporate $corporate, CorporateAddress $address): IdEntity
    {
        $companyDetails = CompanyDetails::fromArr([
            'companyName' => $corporate->getCorporateName(),
            [],
        ]);

        $entityAddress = new Address(
            $address->getPremise(),
            $address->getStreet(),
            $address->getThoroughfare(),
            $address->getPostTown(),
            $address->getCounty(),
            $address->getPostcode(),
            $address->getCountry(),
        );

        $entity = IdEntity::fromCompany($this->companyEntity, $companyDetails, $entityAddress);
        $entity->addRoles(['DIR']);
        $entity->getId();

        return $entity;
    }
}
