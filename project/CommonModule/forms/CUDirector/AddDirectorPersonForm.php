<?php

namespace CommonModule\forms\CUDirector;

use AdminModule\forms\CHForm;
use CompaniesHouse\Repositories\CountriesRepository;
use CompaniesHouse\Repositories\NationalitiesRepository;
use CompaniesHouseModule\Services\SubmissionHandler;
use Framework\Forms\Controls\DatePickerNew;
use Framework\Forms\Controls\DateSelect;
use Framework\Forms\Controls\SingleCheckbox;
use Id3GlobalApiClient\Entities\Address;
use Id3GlobalApiClient\Entities\PersonalDetails;
use IdModule\Domain\IdEntity;
use IdModule\Repositories\IIdCompanyInfoRepository;
use IdModule\Repositories\IIdEntityInfoRepository;
use Libs\CHFiling\Core\Company as OldCompany;
use Entities\Company;
use Framework\Forms\FForm;
use Libs\CHFiling\Core\UtilityClass\Person;
use Libs\Forms\Helpers\Prefiller;
use Libs\Forms\Validators\CHValidator;
use Symfony\Component\Routing\RouterInterface;
use Utils\Date;
use Libs\CHFiling\Core\UtilityClass\Address as DirectorAddress;

class AddDirectorPersonForm extends CHForm
{
    /**
     * @var array
     */
    private $callback;

    /**
     * @var OldCompany
     */
    private $oldCompany;

    /**
     * @var Company
     */
    private $company;

    /**
     * @var Prefiller
     */
    private $prefiller;

    /**
     * @var RouterInterface
     */
    private $router;

    /**
     * @var CountriesRepository
     */
    private $chCountriesRepository;

    /**
     * @var NationalitiesRepository
     */
    private $nationalitiesRepository;

    /**
     * @var IIdEntityInfoRepository
     */
    private $idEntityInfoRepository;

    /**
     * @var SubmissionHandler
     */
    private $submissionHandler;

    public function startup(
        callable $callback,
        OldCompany $oldCompany,
        Company $company,
        Prefiller $prefiller,
        RouterInterface $router,
        CountriesRepository $chCountriesRepository,
        NationalitiesRepository $nationalitiesRepository
    )
    {
        $this->callback = $callback;
        $this->oldCompany = $oldCompany;
        $this->company = $company;
        $this->prefiller = $prefiller;
        $this->router = $router;
        $this->chCountriesRepository = $chCountriesRepository;
        $this->nationalitiesRepository = $nationalitiesRepository;
        $this->init();
    }

    public function startupWithEntityRepository(
        callable $callback,
        OldCompany $oldCompany,
        Company $company,
        Prefiller $prefiller,
        RouterInterface $router,
        CountriesRepository $chCountriesRepository,
        NationalitiesRepository $nationalitiesRepository,
        IIdEntityInfoRepository $idEntityInfoRepository,
        SubmissionHandler  $submitHandler,
    )
    {
        $this->callback = $callback;
        $this->oldCompany = $oldCompany;
        $this->company = $company;
        $this->prefiller = $prefiller;
        $this->router = $router;
        $this->chCountriesRepository = $chCountriesRepository;
        $this->nationalitiesRepository = $nationalitiesRepository;
        $this->idEntityInfoRepository = $idEntityInfoRepository;
        $this->submissionHandler = $submitHandler;
        $this->init();
    }

    private function init()
    {
        $prefillAddress = $this->prefiller->getPrefillAdresses();
        $prefillOfficers = $this->prefiller->getPrefillOfficers();

        // prefill
        $this->addFieldset('Prefill');
        $this->addSelect('prefillOfficers', 'Prefill Officers', $prefillOfficers['select'])
            ->setFirstOption('--- Select --')
            ->class('form-control');

        $incorporationDate = $this->company->getIncorporationDate();
        // apointment date
        $this->addFieldset('Appointment Date');
        $datePicker = $this->add(DatePickerNew::class, 'appointment_date', 'Date of Appointment *')
            ->addRule(FForm::Required, 'Please select New Date')
            ->addRule('DateFormat', 'Please enter date format as DD-MM-YYYY', 'd-m-Y')
            ->addRule('DateMin', 'Appointment date cannot be before the company\'s incorporation date %s', array($incorporationDate, 'd-m-Y'))
            ->addRule('DateMax', 'Date cannot be in the future', array(new Date(), 'd-m-Y'))
            ->setDescription('Date cannot be in the future');
        $datePicker->class('date form-control')->setValue(date('d-m-Y'));

        // person
        $this->addFieldset('Person');
        $this->addSelect('title', 'Title *', Person::$titles)
            ->setFirstOption('--- Select ---')
            ->addRule(FForm::Required, 'Please provide title')
            ->class('form-control');
        $this->addText('forename', 'First name *')
            ->addRule(FForm::Required, 'Please provide first name')
            ->addRule(FForm::MAX_LENGTH, "First name can't be more than 50 characters", 50)
            ->class('form-control');
        $this->addText('middle_name', 'Middle name')
            ->addRule(FForm::MAX_LENGTH, "Middle name can't be more than 50 characters", 50)
            ->class('form-control');
        $this->addText('surname', 'Last name *')
            ->addRule(FForm::Required, 'Please provide last name')
            ->addRule(FForm::MAX_LENGTH, "Last name can't be more than 160 characters", 160)
            ->class('form-control');

        $this->add(DateSelect::class, 'dob', 'Date Of Birth *')
            ->addRule(FForm::Required, 'Please provide date of birth')
            ->addRule(
                [CHValidator::class, 'Validator_personDOB'],
                ['DOB is not a valid date.', 'Director has to be older than %d years.', 'Sorry, all directors must be under %d years (this is a Companies House requirement)'],
                [16, 100]
            )
            ->setStartYear(1900)
            ->setEndYear(date('Y') - 1)
            ->setValue(date('Y-m-d', strtotime(date("Y-m-d", strtotime(date("Y-m-d"))) . " -15 year")))
            ->class('form-control');

        if ($this->oldCompany->getType() != 'LLP') {
            $this->addSelect('nationality', 'Nationality *', $this->nationalitiesRepository->getNationalities())
                ->addRule(FForm::Required, 'Please provide nationality')
                ->setFirstOption('--- Select ---')
                ->class('searchable-select')
                ->class('form-control');
            $this->addText('occupation', 'Occupation *')
                ->addRule(FForm::Required, 'Please provide occupation')
                ->class('form-control');
            //->setDescription('This is your current business occupation. If you do not have one, please enter Company Director');
        }
        $this->addSelect(
            'country_of_residence',
            'Country Of Residence *',
            $this->chCountriesRepository->getPersonCountriesOfResidence()
        )
            ->setFirstOption('--- Select a country ---')
            ->addAtrib('rv-on-change', 'countryOfResidenceUpdated')
            ->addAtrib('rv-value', "personAddress.countryOfResidence.value")
            ->addRule(FForm::Required, 'Please provide Country of Residence')
            ->class('form-control');//->addRule(FForm::REGEXP, "Only letters are allowed", '#^[A-Z]+$#i')

        // prefill
        $this->addFieldset('Prefill');
        $this->addSelect('prefillAddress', 'Prefill Address', $prefillAddress['select'])
            ->class('form-control')
            ->setFirstOption('--- Select ---');

        // adddress
        $this->addFieldset('Address');
        $this->addText('premise', 'Building name/number *')
            ->class('form-control')
            ->addRule(FForm::Required, 'Please provide Building name/number')
            ->addRule(FForm::MAX_LENGTH, "Building name/number can't be more than 50 characters", 50);
        $this->addText('street', 'Street *')
            ->class('form-control')
            ->addRule(FForm::Required, 'Please provide Street')
            ->addRule(FForm::MAX_LENGTH, "Street can't be more than 50 characters", 50);
        $this->addText('thoroughfare', 'Address 3')
            ->class('form-control')
            ->addRule(FForm::MAX_LENGTH, "Address 3 can't be more than 50 characters", 50);
        $this->addText('post_town', 'Town *')
            ->class('form-control')
            ->addRule(FForm::Required, 'Please provide Town')
            ->addRule(FForm::MAX_LENGTH, "Town can't be more than 50 characters", 50);
        $this->addText('county', 'County')
            ->class('form-control')
            ->addRule(FForm::MAX_LENGTH, "County can't be more than 50 characters", 50);
        $this->addText('postcode', 'Postcode *')
            ->class('form-control')
            ->addRule(
                [CHValidator::class, 'Validator_serviceAddressPostCode'],
                sprintf(
                    'You cannot use our postcode for this address without first purchasing the <a href="%s">Registered Office Service</a>.',
                    $this->router->generate('product_module.registered_office')
                ),
                $this->company
            )
            ->addRule(FForm::Required, 'Please provide Postcode')
            ->addRule(FForm::MAX_LENGTH, "Postcode can't be more than 15 characters", 15);
        $this->addSelect(
            'country',
            'Country *',
            $this->chCountriesRepository->getServiceAddressCountries()
        )
            ->class('form-control')
            ->addRule(FForm::Required, 'Please provide Country');

        // residential address
        $this->addFieldset('Residential Address');

        $this->addCheckbox('residentialAddress', 'Different address: ', 1)
            ->addAtrib('rv-checked', 'residentialAddress.different');

        $this->addText('residential_premise', 'Building name/number *')
            ->class('form-control')
            ->addAtrib('rv-enabled', 'residentialAddress.isEnabled < different isCompulsory')
            ->addAtrib('data-pca-form', 'premise')
            ->addRule(array(CHValidator::class, 'Validator_requiredResidentialAddress'), 'Please provide Building name/number', 'add')
            ->addRule(FForm::MAX_LENGTH, "Building name/number can't be more than 50 characters", 50);
        $this->addText('residential_street', 'Street *')
            ->class('form-control')
            ->addAtrib('rv-enabled', 'residentialAddress.isEnabled < different isCompulsory')
            ->addAtrib('data-pca-form', 'street')
            ->addRule(array(CHValidator::class, 'Validator_requiredResidentialAddress'), 'Please provide Street', 'add')
            ->addRule(FForm::MAX_LENGTH, "Street can't be more than 50 characters", 50);
        $this->addText('residential_thoroughfare', 'Address 3')
            ->class('form-control')
            ->addAtrib('data-pca-form', 'thoroughfare')
            ->addAtrib('rv-enabled', 'residentialAddress.isEnabled < different isCompulsory')
            ->addRule(FForm::MAX_LENGTH, "Address 3 can't be more than 50 characters", 50);
        $this->addText('residential_post_town', 'Town *')
            ->class('form-control')
            ->addAtrib('data-pca-form', 'city')
            ->addAtrib('rv-enabled', 'residentialAddress.isEnabled < different isCompulsory')
            ->addRule(array(CHValidator::class, 'Validator_requiredResidentialAddress'), 'Please provide Town', 'add')
            ->addRule(FForm::MAX_LENGTH, "Town can't be more than 50 characters", 50);
        $this->addText('residential_county', 'County')
            ->class('form-control')
            ->addAtrib('rv-enabled', 'residentialAddress.isEnabled < different isCompulsory')
            ->addRule(FForm::MAX_LENGTH, "County can't be more than 50 characters", 50);
        $this->addText('residential_postcode', 'Postcode *')
            ->class('form-control')
            ->addAtrib('data-pca-form', 'postcode')
            ->addAtrib('rv-enabled', 'residentialAddress.isEnabled < different isCompulsory')
            ->addRule(array(CHValidator::class, 'Validator_requiredResidentialAddress'), 'Please provide Postcode', 'add')
            ->addRule(FForm::MAX_LENGTH, "Postcode can't be more than 15 characters", 15)
            ->addRule(
                [CHValidator::class, 'Validator_residentialAddressPostCode'],
                'You cannot use our postcode for this address'
            );
        $this->addSelect(
            'residential_country',
            'Country *',
            $this->chCountriesRepository->getResidentialAddressCountries()
        )
            ->class('form-control')
            ->addAtrib('rv-hide', 'residentialAddress.isCountryHidden')
            ->addAtrib('rv-value', "residentialAddress.country.value");
//            ->addRule(array(CHValidator::class, 'Validator_requiredResidentialAddress'), 'Please provide Country', 'add')
//            ->addRule(FForm::MAX_LENGTH, "Country can't be more than 50 characters", 50)
        ;

        $this->addFieldset('Section 243 Exemption');
        $this->addCheckbox('residentialSecureAddressInd', '243 Exemption: ', 1)
            ->setDescription('Only tick the box below if you are in the process of applying for, or have been granted, exemption by the Registrar from disclosing you usual residential address to credit reference agencies under section 243 of the Companies Act 2006.');


        if ($this->oldCompany->getType() == 'LLP') {
            $this->addFieldset('Designation');

            $this->addRadio('designated_ind', 'Designated member *', array(0 => 'No', 1 => 'Yes'))
                ->addRule(FForm::Required, 'Please select an option');
        }

        // security
        $this->addFieldset('Consent to act');
        if ($this->oldCompany->getType() == 'LLP') {
            $consentLabel = 'The Limited Liability Partnership (LLP) confirms that the person named has consented to act as a designated member';
        } else {
            $consentLabel = 'The company confirms that the person named has consented to act as a director';
        }

        $this->add(SingleCheckbox::class, 'consentToAct', $consentLabel)
            ->addRule(FForm::Required, 'Consent to act is required')
            ->{'data-label-designated_ind-0'}('The Limited Liability Partnership (LLP) confirms that the person named has consented to act as a non-designated member')
            ->{'data-label-designated_ind-1'}('The Limited Liability Partnership (LLP) confirms that the person named has consented to act as a designated member');

        $this->addFieldset('Action');
        // diff callback function
        $this->addSubmit('continue', 'Continue >')->class('btn btn-primary');

        $this->onValid = $this->callback;
        $this->start();
    }

    public function process(): void
    {
        $data = $this->getValues();
        $directorData = $this->setupDirectorPerson($data);
        $director = $directorData['director'];

        $this->oldCompany->sendAppointmentOfNaturalDirector($director);
        $this->clean();
    }

    public function processWithEntity(): ?string
    {
        $data = $this->getValues();
        $directorData = $this->setupDirectorPerson($data);
        $director = $directorData['director'];
        $person = $directorData['person'];
        $address = $directorData['address'];

        $entity = $this->buildEntity($person, $address);
        $idInfo = $this->idEntityInfoRepository->getIdInfoForEntity($entity);

        $formSubmissionId = $this->oldCompany->sendAppointmentOfNaturalDirector($director, $idInfo->isValid());
        $this->clean();

        if (!$idInfo->isValid()) {
            $this->submissionHandler->withHoldSubmission($this->company, $formSubmissionId);
            return $idInfo->getId();
        }
        return null;
    }

    private function setupDirectorPerson(array $data): array
    {
        $data['residential_secure_address_ind'] = $data['residentialSecureAddressInd'];

        $director = $this->oldCompany->getNewPerson('dir');

        $person = $director->getPerson();
        $person->setTitle($data['title']);
        $person->setForename($data['forename']);
        $person->setMiddleName($data['middle_name']);
        $person->setSurname($data['surname']);

        //don't required for llp
        if ($this->oldCompany->getType() != 'LLP') {
            $person->setNationality($data['nationality']);
            $person->setOccupation($data['occupation']);
        }

        $person->setDob($data['dob']);
        $person->setCountryOfResidence($data['country_of_residence']);
        // set appointment Date
        $person->setAppointmentDate(
            !empty($data['appointment_date']) ? Date::changeFormat($data['appointment_date'], 'd-m-Y', 'Y-m-d') : NULL
        );
        $director->setPerson($person);

        // set address
        $address = $director->getAddress();
        $address->setFields($data);
        $director->setAddress($address);

        if ($this->oldCompany->getType() == 'LLP') {
            $director->setDesignatedInd($data['designated_ind']);
        }

        // set residential address
        if ($data['residentialAddress'] == 1) {
            $address = $director->getResidentialAddress();
            $address->setFields($data, 'residential_');
            $director->setResidentialAddress($address);
        } else {
            $address->setSecureAddressInd($data['residential_secure_address_ind']);
            $director->setResidentialAddress($address);
        }

        $director->setConsentToAct($data['consentToAct']);
        return [
            'director' => $director,
            'person' => $person,
            'address' => $address,
        ];
    }

    private function buildEntity(Person $person, DirectorAddress $address): IdEntity
    {
        $personalDetails = new PersonalDetails(
            $person->getForename(),
            $person->getSurname(),
            new Date($person->getDob()),
            null,
            $person->getMiddleName()
        );

        $entityAddress = new Address(
            $address->getPremise(),
            $address->getStreet(),
            $address->getThoroughfare(),
            $address->getPostTown(),
            $address->getCounty(),
            $address->getPostcode(),
            $address->getCountry(),
        );

        $entity = IdEntity::fromPerson($this->company, $personalDetails, $entityAddress);
        $entity->addRoles(['DIR']);
        $entity->getId();

        return $entity;
    }
}
