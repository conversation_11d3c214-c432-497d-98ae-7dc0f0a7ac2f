<?php

namespace FrontModule\Controlers;

use CompaniesHouseModule\Repositories\MemberRepository;
use Entities\Company;
use RouterModule\Generators\IUrlGenerator;
use RouterModule\Helpers\IControllerHelper;

class WithdrawAppointmentController
{
    public function __construct(
        private readonly MemberRepository $memberRepository,
        private readonly IControllerHelper $controllerHelper,
    ){}

    public function withdrawAppointment(Company $company, int $formSubmissionId)
    {
        $this->memberRepository->deleteOfficerAppointment($formSubmissionId);
        /** @phpstan-ignore-next-line */
        return $this->controllerHelper->redirectionTo(CUSummaryControler::SUMMARY_PAGE, ['company_id' => $company->getId()], IUrlGenerator::OLD_LINK);
    }

}