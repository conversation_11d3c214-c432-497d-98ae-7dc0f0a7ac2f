<?php
namespace FrontModule\controlers;

use BankingModule\Views\CompanyBankingViewFactory;
use Exception;
use Framework\Forms\FForm;
use FrontModule\Forms\CUResign\CUResignForm;
use IdModule\Repositories\EntityProvider;
use Libs\CHFiling\Core\Exceptions\InvalidCompanyAuthenticationCodeException;
use CompaniesHouseModule\Repositories\MemberRepository;
use CompanyUpdateModule\Factories\CUSummaryControlerViewFactory;
use DataTablesModule\Dto\SearchData;
use Doctrine\ORM\NonUniqueResultException;
use Exceptions\Technical\NodeException;
use Factories\Front\CompanyViewFactory;
use FeatureModule\Feature;
use FrontModule\Processors\ServiceWidgetProcessor;
use IdModule\Repositories\IIdCompanyInfoRepository;
use IdModule\Verification\IIdValidationChecker;
use MailScanModule\Repositories\PostItemDataTableRepository;
use Models\RTFDocuments\RTFDocuments;
use PeopleWithSignificantControl\Views\CompanySummaryView;
use VoServiceModule\Facades\VoServiceFacade;
use CompaniesHouseModule\Services\SubmissionHandler;
use Models\OldModels\CUSummaryModel;
use Config\Constants\DiLocator;
use Models\OldModels\Order;
use Models\Products\Package;
use Models\OldModels\CompanyOtherDocument;

class CUSummaryControler extends CUControler
{
    const SUMMARY_PAGE = 205;
    const BARCLAYS_PAGE = 343;
    const RESIGNATION_PAGE = 1229;

    /**
     * @var array
     */
    public $possibleActions = [
        self::SUMMARY_PAGE => 'summary',
        self::BARCLAYS_PAGE => 'barclays',
        self::RESIGNATION_PAGE => 'resign'
    ];

    /**
     * @var string
     */
    static public $handleObject = CUSummaryModel::class;

    /**
     * @var CUSummaryModel
     */
    public $node;

    /**
     * @var CompanyViewFactory
     */
    private $companyViewFactory;

    /**
     * @var CUSummaryControlerViewFactory
     */
    private $controllerViewFactory;

    /**
     * @var PostItemDataTableRepository
     */
    private $postItemDataTableRepository;

    /**
     * @var IIdValidationChecker
     */
    private $idChecker;

    /**
     * @var MemberRepository
     */
    private $memberRepository;

    /**
     * @var CompanyBankingViewFactory
     */
    private $companyBankingViewFactory;

    /**
     * @var ServiceWidgetProcessor
     */
    private $serviceWidgetProcessor;

    /**
     * @var VoServiceFacade
     */
    private $voServiceFacade;

    /**
     * @var IIdCompanyInfoRepository
     */
    private $idCompanyInfoRepository;

    /**
     * @var SubmissionHandler
     */
    private $submissionHandler;

    /**
     * @var EntityProvider
     */
    private $entityProvider;

    public function startup()
    {
        // fix for IE
        if (isset($this->get['document_name'])) {
            $this->httpsRequired = FALSE;
        }
        parent::startup();

        $this->companyViewFactory = $this->getService(DiLocator::FACTORY_FRONT_COMPANY_VIEW);
        $this->controllerViewFactory = $this->getService('company_update_module.factories.cusummary_controler_view_factory');
        $this->postItemDataTableRepository = $this->getService('mail_scan_module.repositories.post_item_data_table_repository');
        $this->idChecker = $this->getService('id_module.verification.validation_checker');
        $this->memberRepository = $this->getService('companies_house_module.repositories.member_repository');
        $this->companyBankingViewFactory = $this->getService('banking_module.views.company_banking_view_factory');
        $this->serviceWidgetProcessor = $this->getService('front_module.processors.service_widget_processor');
        $this->voServiceFacade = $this->getService('vo_service_module.facades.vo_service_facade');
        $this->idCompanyInfoRepository = $this->getService('id_module.repositories.id_info_repository');
        $this->submissionHandler = $this->getService('companies_house_module.services.submission_handler');
        $this->entityProvider = $this->getService('id_module.repositories.entity_provider');
    }

    public function beforePrepare()
    {
        if ($this->action == $this->possibleActions[self::RESIGNATION_PAGE]
            || $this->action == $this->possibleActions[self::SUMMARY_PAGE] && isset($this->get['sync'])
        ) {
            $this->authCodeRequired = TRUE;
        }

        parent::beforePrepare();
        $this->node->startup($this->company, $this->customer);
    }

    /*     * ********************************** summary *********************************** */

    /**
     * Handle with:
     * - display document
     * - sync company
     * - display subscribe certificate
     *
     * @return void
     */
    public function handleSummary()
    {
        // --- show document ---
        if (isset($this->get['document_name'])) {
            try {
                $this->node->outputDocument($this->get['document_name']);
            } catch (Exception $e) {
                $this->flashMessage($e->getMessage());
                $this->redirect(NULL, 'document_name=');
            }
        }
        // --- show rtf document ---
        if (isset($this->get['formid'])) {
            if (RTFDocuments::getFormSubmisionStatus($this->get['formid'])) {
                try {
                    $Rtf = new RTFDocuments($this->get['company_id'], $this->get['formid']);
                    $Rtf->output();
                } catch (Exception $e) {
                    $this->flashMessage($e->getMessage());
                    $this->redirect(NULL);
                }
            } else {
                $this->flashMessage('Your form id is not correct');
                $this->redirect(self::SUMMARY_PAGE, "company_id={$this->company->getCompanyId()}");
            }
        }
        // --- user clicked sync button ---
        if (isset($this->get['sync'])) {
            try {
                $this->node->syncCompany();
                $this->flashMessage('Your company has been synchronised with data from Companies House.');
                $this->redirect(NULL, 'sync=');
            } catch (InvalidCompanyAuthenticationCodeException $e) {
                $this->flashMessage('The company sync was unsuccessful because the authentication code we have is invalid - see <a href="https://support.companiesmadesimple.com/hc/en-us/articles/360019465217-Where-can-I-find-my-Authentication-Code-#invalid" target="_blank">here</a> for information on getting a new one.', 'error', FALSE);
                $this->redirect(NULL, 'sync=');
            } catch (Exception $e) {
                $this->flashMessage($e->getMessage(), 'error');
                $this->redirect(NULL, 'sync=');
            }
        }

        // --- show subscriber certificate ---
        if (isset($this->get['subscriberCertificate'])) {
            try {
                $this->node->outputSubscriberCertificate($this->get['subscriberCertificate']);
            } catch (Exception $e) {
                $this->flashMessage($e->getMessage(), 'error');
                $this->redirect(NULL, 'subscriberCertificate=');
            }
        }
        // --- show company summary ---
        if (isset($this->get['summary'])) {
            try {
                $this->node->outputCompanySummary();
            } catch (Exception $e) {
                $this->flashMessage($e->getMessage(), 'error');
                $this->redirect(NULL, 'summary=');
            }
        }
    }

    protected function prepareSummary()
    {
        // --- change view for locked company ---
        if ($this->company->isLocked()) {
            $this->changeView('summaryLocked');
        }
    }

    /**
     * @throws NonUniqueResultException
     * @throws NodeException
     */
    protected function renderSummary()
    {
        // --- check if package is bronze = don't show subsribers link ---
        if ($this->node->hasAllowedSubscriberCertificates() === FALSE) {
            $this->template->doNotShowSubsLink = 1;
        }

        // --- display barclays link if haven't submitted
        if ($this->node->displayBarclaysBox()) {
            $this->template->barclays = 1;
        }

        $this->template->postItems = iterator_to_array($this->postItemDataTableRepository->searchManyBy(
            SearchData::fromQuery(['customer' => $this->customerEntity, 'companyNumber' => $this->companyEntity->getCompanyNumber()], 5)
        )->getResults());

        if ($voCustomer = $this->voServiceFacade->getCustomerWithActiveServicesByEmail($this->customerEntity->getEmail())) {
            $this->template->hasActiveServiceVo = $voCustomer['serviceId'] && $voCustomer['statusId'] == 'ACTIVE';
            $this->template->hasAccountVo = $voCustomer['email'];
            $this->template->lpNumber = $voCustomer['lpNumber'];
            $this->template->voCustomerId = $voCustomer['customerId'];
        } else {
            $this->template->hasActiveServiceVo = false;
            $this->template->hasAccountVo = null;
            $this->template->lpNumber = null;
            $this->template->voCustomerId = null;
        }

        $this->template->entitiesWithIdInvalid = $this->idCompanyInfoRepository->getEntitiesWithInvalidId($this->companyEntity);
        $this->template->officerAppointmentEntities = $this->entityProvider->getOfficerAppointmentsEntities($this->companyEntity);
        $this->template->canSubmitCompany = $this->submissionHandler->canSubmitCompany($this->companyEntity);
        $this->template->view = $this->controllerViewFactory->create($this->companyEntity);
        $this->template->companyView = $this->companyViewFactory->create($this->companyEntity);
        $this->template->summaryView = new CompanySummaryView($this->company);
        $this->template->bronze = Order::hasItem($this->company->getOrderId(), Package::PACKAGE_BRONZE);
        $this->template->title = $this->company->getCompanyName();
        $this->template->data = $this->company->getData();
        $this->template->documents = $this->company->getDocumentsList();
        $this->template->otherdocuments = CompanyOtherDocument::getAllObjects(NULL, NULL, ['companyId' => $this->company->getCompanyId()]);
        $this->template->hasBronzeProduct = Order::hasItem($this->company->getOrderId(), Package::PACKAGE_BRONZE);
        $this->template->customerEntity = $this->customerEntity;
        $this->template->isCompanyIdValid = $this->idChecker->isCompanyValid($this->companyEntity);
        $this->template->hasNoOfficers = !$this->memberRepository->hasMembers($this->companyEntity);
        $this->template->companyBankingView = $this->companyBankingViewFactory->create($this->companyEntity);
        $this->template->isChDown = Feature::isEnabled('companies_house_system_down');
        $this->template->servicesWidget = $this->serviceWidgetProcessor->createServiceWidgetsForCompany($this->companyEntity);
        $this->template->companyEntity = $this->companyEntity;
        $this->template->companyId = $this->companyEntity->getCompanyId();
        $this->template->hasConfirmationStatementServiceActive = $this->companyEntity->hasConfirmationStatementServiceActive();
    }

    /*     * ********************************** barclays *********************************** */

    public function prepareBarclays()
    {
        try {
            if ($this->customer->countryId != 223) {
                throw new Exception('We are unable to process your Barclays application at this time. Barclays can only open bank accounts for UK customers and your current contact address is international. If you reside in the UK, please update your <a href="' . $this->newRouter->generate('my_details') . '">My Details</a> and retry.');
            }
            $this->node->checkBarclaysPage();
        } catch (Exception $e) {
            $this->flashMessage($e->getMessage(), 'error');
            $this->redirect(self::SUMMARY_PAGE, "company_id={$this->company->getCompanyId()}");
        }
    }

    public function renderBarclays()
    {
        $this->template->form = $this->node->getComponentBarclaysForm([$this, 'Form_barclaysFormSubmitted']);
    }

    /**
     * @param FForm $form
     */
    public function Form_barclaysFormSubmitted(FForm $form)
    {
        try {
            $this->node->processBarclaysForm($form);
            $this->flashMessage('Request has been sent');
            $this->redirect(self::SUMMARY_PAGE, "company_id={$this->company->getCompanyId()}");
        } catch (Exception $e) {
            $this->flashMessage($e->getMessage(), 'error');
            $this->redirect();
        }
    }

    public function renderResign()
    {
        if (isset($this->get['resign']) && isset($this->get['officer_id'])) {
            try {
                $officer = $this->company->getOfficer($this->get['officer_id']);
            } catch (Exception $e) {
                $this->flashMessage('Officer record not exist');
                $this->reloadOpener();
            }
            $resignForm = new CUResignForm($this->node->getId() . '_resign');
            $resignForm->startup($this, $this->company, $officer, [$this, 'Form_ResignFormSubmitted']);
            $this->template->form = $resignForm;
            $this->template->companyType = $this->company->getType();
        } else {
            $this->flashMessage('Officer record not exist');
            $this->redirect(self::SUMMARY_PAGE, "company_id={$this->company->getCompanyId()}");
        }
    }

    /**
     * @param CUResignForm $form
     */
    public function Form_ResignFormSubmitted(CUResignForm $form)
    {
        try {
            $form->process();
            $this->flashMessage('Application has been sent.');
            $this->reloadOpener();
        } catch (Exception $e) {
            $this->flashMessage($e->getMessage(), 'error');
            $this->redirect(self::SUMMARY_PAGE, "company_id={$this->company->getCompanyId()}");
        }
    }

}
