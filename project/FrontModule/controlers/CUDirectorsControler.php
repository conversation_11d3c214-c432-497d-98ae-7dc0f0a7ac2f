<?php
namespace FrontModule\controlers;

use CommonModule\forms\CUDirector\AddDirectorCorporateForm;
use CommonModule\forms\CUDirector\AddDirectorPersonForm;
use CommonModule\forms\CUDirector\EditDirectorCorporateForm;
use CommonModule\forms\CUDirector\EditDirectorPersonForm;
use CompaniesHouse\Repositories\CountriesRepository;
use CompaniesHouseModule\Deciders\CountryDecider;
use CompaniesHouse\Repositories\NationalitiesRepository;
use CompaniesHouseModule\Services\SubmissionHandler;
use CompaniesHouseModule\Views\PersonViewFactory;
use CompanyUpdateModule\Dto\DirectorPersonData;
use CompanyUpdateModule\Forms\DirectorPersonForm;
use Exception;
use FormModule\Gender;
use IdModule\Deciders\CompanyValidationDecider;
use IdModule\Dto\IdRequest;
use IdModule\Domain\IdEntity;
use IdModule\Dto\IdCheckPersonData;
use IdModule\Dto\PersonEntity;
use IdModule\Entities\IdStatus;
use IdModule\Forms\IdCheckPersonForm;
use IdModule\Entities\IdHashCompany;
use IdModule\Entities\IdHashPerson;
use IdModule\Providers\IdStatusProvider;
use IdModule\Repositories\IIdCompanyInfoRepository;
use IdModule\Repositories\IIdEntityInfoRepository;
use Libs\CHFiling\Core\UtilityClass\Person;
use Libs\Forms\Helpers\Prefiller;
use RouterModule\Helpers\ControllerHelper;
use Services\Registry;

class CUDirectorsControler extends CUControler
{
    const MIN_MIDDLENAME_LENGTH = 1;
    const ADD_DIRECTOR_PERSON_PAGE = 207;
    const ADD_DIRECTOR_CORPORATE_PAGE = 211;
    const EDIT_DIRECTOR_PERSON_PAGE = 212;
    const EDIT_DIRECTOR_CORPORATE_PAGE = 213;
    const SHOW_DIRECTOR_PERSON_PAGE = 220;
    const SHOW_DIRECTOR_CORPORATE_PAGE = 221;

    /** @var array */
    public $possibleActions = array(
        self::ADD_DIRECTOR_PERSON_PAGE => 'addDirectorPerson',
        self::ADD_DIRECTOR_CORPORATE_PAGE => 'addDirectorCorporate',
        self::EDIT_DIRECTOR_PERSON_PAGE => 'editDirectorPerson',
        self::EDIT_DIRECTOR_CORPORATE_PAGE => 'editDirectorCorporate',
        self::SHOW_DIRECTOR_PERSON_PAGE => 'showDirectorPerson',
        self::SHOW_DIRECTOR_CORPORATE_PAGE => 'showDirectorCorporate',
    );

    /**
     * @var Person
     */
    private $director;

    /**
     * @var CountriesRepository
     */
    private $chCountriesRepository;

    /**
     * @var NationalitiesRepository
     */
    private $nationalitiesRepository;

    /**
     * @var IIdEntityInfoRepository
     */
    private $idEntityInfoRepository;

    /**
     * @var SubmissionHandler
     */
    private $submissionHandler;

    public function beforePrepare()
    {
        $authCodeRequiredActions = array(
            $this->possibleActions[self::ADD_DIRECTOR_PERSON_PAGE],
            $this->possibleActions[self::EDIT_DIRECTOR_CORPORATE_PAGE],
            $this->possibleActions[self::EDIT_DIRECTOR_PERSON_PAGE],
        );

        if (in_array($this->action, $authCodeRequiredActions)) {
            $this->authCodeRequired = TRUE;
        }

        $this->chCountriesRepository = $this->getService('companies_house.repositories.countries_repository');
        $this->nationalitiesRepository = $this->getService('companies_house.repositories.nationalities_repository');
        $this->idEntityInfoRepository = $this->getService('id_module.repositories.id_info_repository');
        $this->submissionHandler = $this->getService('companies_house_module.services.submission_handler');

        parent::beforePrepare();
    }

    /*     * ******************************************* add director person ************************************************** */

    protected function renderAddDirectorPerson()
    {
        /** @var ControllerHelper $controllerHelper */
        $controllerHelper = $this->getService('router_module.helpers.controller_helper');

        /** @var IdStatusProvider $idStatusProvider */
        $idStatusProvider = $this->getService('id_module.providers.id_status_provider');

        $form = new AddDirectorPersonForm($this->node->getId() . '_addDirectorPerson' . $this->companyEntity->getId());
        $form->setStorable(FALSE);

        $idStatus = new IdStatus(0);

        $isCountryUK = FALSE;
        if (isset($_POST['country_of_residence'])) {
            $isCountryUK = CountryDecider::isUkCountry($_POST['country_of_residence']);
        }

        $directorPersonData = DirectorPersonData::default();
        $directorPersonForm = $controllerHelper->buildForm(
            DirectorPersonForm::class,
            $directorPersonData,
            [
                'csrf_protection' => FALSE,
                'genders' => Gender::getNames(),
                'documentTypes' => array_flip(IdRequest::$documentTypes),
                'isCountryUK' => $isCountryUK,
                'isCompanyValidationEligible' => $idStatus->isEligible()
            ]
        );

        $form->startupWithEntityRepository(
            [$this, 'Form_addDirectorPersonFormSubmitted'],
//            function (AddDirectorPersonForm $form) use ($directorPersonForm) {
//                if ($directorPersonForm->isValid()) {
//                    dump($directorPersonForm->getData());
//                    dump($form->getValues());
//                    exit;
//                }
//            },
            $this->company,
            $this->companyEntity,
            $this->prefiller,
            $this->newRouter,
            $this->chCountriesRepository,
            $this->nationalitiesRepository,
            $this->idEntityInfoRepository,
            $this->submissionHandler,
        );
        $this->template->form = $form;

        $prefillAddress = $this->prefiller->getPrefillAdresses();
        $prefillOfficers = $this->prefiller->getPrefillOfficers();

        $this->template->jsPrefillAdresses = $prefillAddress['js'];
        $this->template->jsPrefillOfficers = $prefillOfficers['js'];

        $this->template->directorPersonForm = $directorPersonForm->createView();
        $this->template->isLLP = $this->company->getType() == 'LLP';
        $this->template->invalidEntityId = $this->get['invalidEntityId'] ?? null;

        /** @var PersonViewFactory $viewFactory */
        $viewFactory = $this->getService('companies_house_module.views.person_view_factory');
        $this->template->view = $viewFactory->fromDirectorPersonData($form, $directorPersonForm, $directorPersonData, $idStatus);
    }

    public function Form_addDirectorPersonFormSubmitted(AddDirectorPersonForm $form)
    {
        try {
            $invalidEntityId = $form->processWithEntity();

            $middleName = $form->getValues()['middle_name'];
            if (!empty($middleName) && strlen($middleName) === self::MIN_MIDDLENAME_LENGTH) {
                $this->flashMessageMiddleNameValidation();
            } else {
                $this->flashMessage('Your Director appointment has been sent to Companies House and will be processed in approximately 3 working hours.');
            }

            if ($this->company->getType() == 'LLP') {
                $this->flashMessage('Your Member appointment has been sent to Companies House and will be processed in approximately 3 working  hours.');
            }

            if (!is_null($invalidEntityId)) {
                $this->redirect(null, ['invalidEntityId' => $invalidEntityId]);
            }

        } catch (Exception $e) {
            $this->flashMessage($e->getMessage(), 'error');
            $this->redirect();
        }
        $this->redirect(CUSummaryControler::SUMMARY_PAGE, 'company_id=' . $this->company->getCompanyId());
    }

    /*     * ****************************************** show director person ************************************************** */

    protected function handleShowDirectorPerson()
    {
        // remove
        if (isset($this->get['director_id']) && isset($this->get['resign'])) {
            try {
                $this->company->sendTerminationOfDirector($this->get['director_id']);
                $this->flashMessage('Your Director resignation has been sent to Companies House and will be processed in approximately 3 Working hours.');
                if ($this->company->getType() == 'LLP') {
                    $this->flashMessage('Your Member resignation has been sent to Companies House and will be processed in approximately 3 Working hours.');
                }
            } catch (Exception $e) {
                $this->flashMessage($e->getMessage());
            }
            $this->redirect(CUSummaryControler::SUMMARY_PAGE, array('company_id=' . $this->company->getCompanyId(), 'director_id' => NULL, 'resign' => NULL));
        }
    }

    protected function renderShowDirectorPerson()
    {
        // check get
        if (!isset($this->get['director_id'])) {
            $this->redirect(CUSummaryControler::SUMMARY_PAGE, array('company_id=' . $this->company->getCompanyId()));
        }

        // check existing director
        try {
            $person = $this->company->getPerson($this->get['director_id'], 'dir');
            $fields = $person->getFields();
            if ($this->company->getType() == 'LLP') {
                $fields['designated_ind'] = $person->getDesignatedInd();
            }

            $this->template->director = $fields;
            $this->template->company = $this->company;
        } catch (Exception $e) {
            $this->flashMessage($e->getMessage());
            $this->redirect(CUSummaryControler::SUMMARY_PAGE, array('company_id=' . $this->company->getCompanyId(), 'director_id' => NULL));
        }
    }

    /*     * ****************************************** edit director person ************************************************** */

    protected function prepareEditDirectorPerson()
    {
        // check get
        if (!isset($this->get['director_id'])) {
            $this->redirect(CUSummaryControler::SUMMARY_PAGE, array('company_id=' . $this->company->getCompanyId()));
        }

        // check existing director
        try {
            $this->director = $this->company->getPerson($this->get['director_id'], 'dir');
        } catch (Exception $e) {
            $this->flashMessage($e->getMessage());
            $this->redirect(CUSummaryControler::SUMMARY_PAGE, array('company_id=' . $this->company->getCompanyId(), 'director_id' => NULL));
        }
    }

    protected function renderEditDirectorPerson()
    {
        $form = new EditDirectorPersonForm($this->node->getId() . '_editDirectorPerson');
        $form->startup(
            [$this, 'Form_editDirectorPersonFormSubmitted'],
            $this->company,
            $this->companyEntity,
            $this->director,
            $this->prefiller,
            $this->newRouter,
            $this->chCountriesRepository,
            $this->nationalitiesRepository
        );
        $this->template->form = $form;

        $isOfficerEntityValid = NULL;
        $customer = $this->companyEntity->getCustomer();

        /** @var IIdEntityInfoRepository $idInfoRepository */
        $idInfoRepository = Registry::getService('id_module.repositories.id_info_repository');

        $directorData = $this->director->getFields();
        $idHash = IdHashPerson::fromBasicData(
            $directorData['forename'] ?? NULL,
            $directorData['surname'] ?? NULL,
            $directorData['dob'] ?? NULL
        );

        $idEntityInfo = $idInfoRepository->optionalIdInfoForEntityById($this->companyEntity, $idHash->getIdHash());
        $isOfficerEntityValid = $idEntityInfo && $idEntityInfo->isValid();

        $this->template->isOfficerEntityValid = $isOfficerEntityValid;
        $this->template->isLLP = $this->company->getType() == 'LLP';

        $prefillAddress = $this->prefiller->getPrefillAdresses();
        $this->template->jsPrefillAdresses = $prefillAddress['js'];
    }

    public function Form_editDirectorPersonFormSubmitted(EditDirectorPersonForm $form)
    {
        try {
            $form->process();

            $middleName = $form->getValues()['middle_name'];
            if (!empty($middleName) && strlen($middleName) === self::MIN_MIDDLENAME_LENGTH) {
                $this->flashMessageMiddleNameValidation();
            } else {
                $this->flashMessage('Your Director change has been sent to Companies House and will be processed in approximately 3 Working hours.');
            }

            if ($this->company->getType() == 'LLP') {
                $this->flashMessage('Your Member change has been sent to Companies House and will be processed in approximately 3 Working hours.');
            }
        } catch (Exception $e) {
            $this->flashMessage($e->getMessage(), 'error');
        }
        $this->redirect(CUSummaryControler::SUMMARY_PAGE, 'company_id=' . $this->company->getCompanyId());
    }

    /*     * ****************************************** add director corporate ************************************************** */

    protected function renderAddDirectorCorporate()
    {
        $form = new AddDirectorCorporateForm($this->node->getId() . '_addDirectorCorporate' . $this->companyEntity->getId());
        $form->startupWithEntityRepository(
            $this,
            [$this, 'Form_addDirectorCorporateFormSubmitted'],
            $this->company,
            $this->companyEntity,
            $this->prefiller,
            $this->chCountriesRepository,
            $this->idEntityInfoRepository,
            $this->submissionHandler,
        );
        $this->template->form = $form;

        // prefill
        $prefillAddress = $this->prefiller->getPrefillAdresses();
        $prefillOfficers = $this->prefiller->getPrefillOfficers(Prefiller::TYPE_CORPORATE);

        $this->template->jsPrefillAdresses = $prefillAddress['js'];
        $this->template->jsPrefillOfficers = $prefillOfficers['js'];
        $this->template->invalidEntityId = $this->get['invalidEntityId'] ?? null;
    }

    public function Form_addDirectorCorporateFormSubmitted(AddDirectorCorporateForm $form)
    {
        try {
            $invalidEntityId = $form->processWithEntity();

            $this->flashMessage("Corporate Director appointment has been submitted to Companies House and should be approved within 3 Working hours.");
            if ($this->company->getType() == 'LLP') {
                $this->flashMessage('Corporate Member appointment has been submitted to Companies House and should be approved within 3 Working hours.');
            }

            if (!is_null($invalidEntityId)) {
                $this->redirect(null, ['invalidEntityId' => $invalidEntityId]);
            }

        } catch (Exception $e) {
            $this->flashMessage($e->getMessage(), 'error');
            $this->redirect();
        }

        $this->redirect(CUSummaryControler::SUMMARY_PAGE, 'company_id=' . $this->company->getCompanyId());
    }

    /*     * ****************************************** show director corporate ************************************************** */

    protected function handleShowDirectorCorporate()
    {
        // remove
        if (isset($this->get['director_id']) && isset($this->get['resign'])) {
            try {
                $this->company->sendTerminationOfDirector($this->get['director_id']);
                $this->flashMessage('Corporate Director resignation has been sent to Companies House and will be processed in approximately 3 Working hours.');
                if ($this->company->getType() == 'LLP') {
                    $this->flashMessage('Corporate Member resignation has been sent to Companies House and will be processed in approximately 3 Working hours.');
                }
            } catch (Exception $e) {
                $this->flashMessage($e->getMessage());
            }
            $this->redirect(CUSummaryControler::SUMMARY_PAGE, array('company_id=' . $this->company->getCompanyId(), 'director_id' => NULL, 'resign' => NULL));
        }
    }

    protected function renderShowDirectorCorporate()
    {
        // check get
        if (!isset($this->get['director_id'])) {
            $this->redirect(CUSummaryControler::SUMMARY_PAGE, array('company_id=' . $this->company->getCompanyId()));
        }

        // check existing director
        try {
            $director = $this->company->getCorporate($this->get['director_id'], 'dir');
            $fields = $director->getFields();
            if ($this->company->getType() == 'LLP') {
                $fields['designated_ind'] = $director->getDesignatedInd();
            }

            $this->template->director = $fields;
        } catch (Exception $e) {
            $this->flashMessage($e->getMessage());
            $this->redirect(CUSummaryControler::SUMMARY_PAGE, array('company_id=' . $this->company->getCompanyId(), 'director_id' => NULL));
        }
    }

    /*     * ****************************************** edit director corporate ************************************************** */

    protected function prepareEditDirectorCorporate()
    {
        // check get
        if (!isset($this->get['director_id'])) {
            $this->redirect(CUSummaryControler::SUMMARY_PAGE, 'company_id=' . $this->company->getCompanyId());
        }

        // check existing director
        try {
            $this->director = $this->company->getCorporate($this->get['director_id'], 'dir');
        } catch (Exception $e) {
            $this->flashMessage($e->getMessage());
            $this->redirect(CUSummaryControler::SUMMARY_PAGE, array('company_id=' . $this->company->getCompanyId(), 'director_id' => NULL));
        }
    }

    protected function renderEditDirectorCorporate()
    {
        $form = new EditDirectorCorporateForm($this->node->getId() . '_editDirectorCorporate');
        $form->startup(
            $this,
            [$this, 'Form_editDirectorCorporateFormSubmitted'],
            $this->company,
            $this->director,
            $this->prefiller,
            $this->chCountriesRepository
        );
        $this->template->form = $form;

        $isOfficerEntityValid = NULL;

        /** @var IIdEntityInfoRepository $idInfoRepository */
        $idInfoRepository = Registry::getService('id_module.repositories.id_info_repository');

        $directorData = $this->director->getFields();
        $idHash = IdHashCompany::fromBasicData(
            $directorData['corporate_name'] ?? NULL,
            $directorData['postcode'] ?? NULL
        );

        $idEntityInfo = $idInfoRepository->optionalIdInfoForEntityById($this->companyEntity, $idHash->getIdHash());
        $isOfficerEntityValid = $idEntityInfo && $idEntityInfo->isValid();

        $this->template->isOfficerEntityValid = $isOfficerEntityValid;

        // prefill
        $prefillAddress = $this->prefiller->getPrefillAdresses();
        $this->template->jsPrefillAdresses = $prefillAddress['js'];
        $this->template->isLLP = $this->company->getType() == 'LLP';
    }

    public function Form_editDirectorCorporateFormSubmitted(EditDirectorCorporateForm $form)
    {
        try {
            $form->process();
            $this->flashMessage('Corporate Director change has been sent to Companies House and will be processed in approximately 3 Working hours.');
            if ($this->company->getType() == 'LLP') {
                $this->flashMessage('Corporate Member change has been sent to Companies House and will be processed in approximately 3 Working hours.');
            }
        } catch (Exception $e) {
            $this->flashMessage($e->getMessage(), 'error');
        }
        $this->redirect(CUSummaryControler::SUMMARY_PAGE, 'company_id=' . $this->company->getCompanyId());
    }

    public function flashMessageMiddleNameValidation()
    {
        $this->flashMessage('Companies House does not accept initials as part of a name, middle name or last 
            name, please enter in your FULL NAME. If the initial is as it shows on your ID documents then please 
            email us a <NAME_EMAIL>', 'warning');
    }
}
