{assign var="hide" value="1"}

{include file="@header.tpl"}
{styles file='webloader_front.neon' section='modal'}

<div class="row">
    <div class="col-md-8 col-xs-12">
        <div class="row">
            <div class="col-xs-12">
                <p style="margin: 0 0 15px 0; font-size: 11px;"><a href="{$this->router->link("FrontModule\controlers\CUSummaryControler::SUMMARY_PAGE", "company_id=$companyId")}">{$companyName}</a> &gt; Add Person{if $companyType == 'LLP'} Member{else} Director{/if}</p>
            </div>
            <div class="col-xs-12">
                <h1>Add Person{if $companyType == 'LLP'} Member{else} Director{/if}</h1>
            </div>
            <div class="col-xs-12">
                <p style="text-align: right;">To make a corporate appointment <a href="{$this->router->link("FrontModule\controlers\CUDirectorsControler::ADD_DIRECTOR_CORPORATE_PAGE", "company_id=$companyId")}">click here</a></p>
            </div>
        </div>

        <div class="row">
            <div id="add-director-person" class="col-xs-12" style="background-color: #f2f3f5; padding-top: 10px; padding-bottom:10px;">

                {$form->getBegin() nofilter}
                <div class="row">
                    <div class="col-xs-12 has-error">
                        <div class="help-block">
                            {if $view->hasErrors()}
                                Form has <b> {$view->getErrorsCount()}</b> error(s). See below for more details:
                            {/if}
                        </div>
                    </div>
                </div>

                <div class="row" id="fieldset_1">
                    <div class="col-xs-12">
                        <h3>Appointment Date</h3>
                    </div>
                    <div class="col-xs-12">
                        <div class="row">
                            <div class="col-xs-4">
                                {$form->getLabel('appointment_date') nofilter}
                            </div>
                            <div class="col-xs-8">
                                {$form->getControl('appointment_date') nofilter}
                            </div>
                            <div class="col-xs-offset-4 col-xs-8 has-error">
                                <div class="help-block">
                                    {$form->getError('appointment_date') nofilter}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {include 'CompanyModule/Templates/oldBlocks/officersPrefill.tpl'}
                {include 'CompanyModule/Templates/oldBlocks/director/person.tpl'}

                <div class="row" rv-show="isIdStatusEligible < personAddress.countryOfResidence.value">
                    <div class="col-xs-12" rv-show="idDataChecker.progress">
                        <div style="width: 25%; margin: 0 auto;">
                            <div style="display: block; float: left;"><i class="fa fa-spinner fa-spin fa-3x"></i></div>
                            <div style="display: block; float: left; margin-left: 10px; padding-top: 10px;">Please wait...</div>
                        </div>
                    </div>
                </div>

                <div class="row" rv-hide="idDataChecker.progress">
                    <div class="col-xs-12" rv-show="personAddress.hasCountrySelected < countryOfResidence.value">
                        <div class="alert alert-info" rv-show="idDataChecker.isIdValid < idStatus">
                            <div class="checkbox">
                                <label>
                                    {*{$form->getControl('entityChangeEnabled') nofilter}*}
                                    Enable update of address information
                                </label>
                                <div class="small">This person has already been ID checked. By updating any address information you will need to provide new proof of ID/address documentation.</div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-xs-12">
                                {include 'id_module/src/Templates/idCheck.tpl' idCheckForm=$directorPersonForm['idCheck']}
                            </div>
                            <div class="col-xs-12">
                                {include 'CompanyModule/Templates/oldBlocks/serviceAddress.tpl'}
                            </div>
                            <div class="col-xs-12">
                                {include 'CompanyModule/Templates/oldBlocks/residentialAddress.tpl' addressSuffix='residential_address' hasEmptyUkResidentialAddress=$view->hasEmptyUkResidentialAddress()}
                            </div>
                            <div class="col-xs-12" rv-show="idDataChecker.isIdValid < idStatus">
                                <div class="disabled-content" rv-hide="idDataChecker.updateEnabled"></div>
                            </div>
                        </div>

                        <div class="row" id="fieldset_6">
                            <div class="col-xs-12">
                                <h3>Section 243 Exemption</h3>
                            </div>
                            <div class="col-xs-12">
                                <div class="row">
                                    <div class="col-xs-4">
                                        {$form->getLabel('residentialSecureAddressInd') nofilter}
                                    </div>
                                    <div class="col-xs-8">
                                        {$form->getControl('residentialSecureAddressInd') nofilter}
                                    </div>
                                    <div class="col-xs-offset-4 col-xs-8">
                                        <div class="help-block">
                                            {$form['residentialSecureAddressInd']->getDescription()}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {if $isLLP}
                            <div class="row">
                                <div class="col-xs-12">
                                    <h3>Designation</h3>
                                </div>
                                <div class="col-xs-12">
                                    <div class="row">
                                        <div class="col-xs-4">
                                            {$form->getLabel('designated_ind') nofilter}
                                        </div>
                                        <div class="col-xs-8">
                                            {$form->getControl('designated_ind') nofilter}
                                        </div>
                                        <div class="col-xs-offset-4 col-xs-8 has-error">
                                            <div class="help-block">
                                                {$form->getError('designated_ind') nofilter}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {/if}

                        <div class="row">
                            <div class="col-xs-12">
                                <h3>Consent to Act</h3>
                            </div>
                            <div class="col-xs-12">
                                <div class="row">
                                    <div class="col-xs-12">
                                        {$form->getControl('consentToAct') nofilter}
                                    </div>
                                    <div class="col-xs-offset-4 col-xs-8 has-error">
                                        <div class="help-block">
                                            {$form->getError('consentToAct') nofilter}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-xs-12">
                                <h3>Action</h3>
                            </div>
                            <div class="col-xs-12">
                                {$form->getControl('continue') nofilter}
                            </div>
                        </div>
                    </div>
                </div>

                {$form->getEnd() nofilter}

            </div>
        </div>
    </div>
</div>

{if $invalidEntityId}
<div
        class="modal fade"
        id="idCheckModal"
        data-bs-backdrop="static"
        data-bs-keyboard="false"
        tabindex="-1"
        aria-hidden="true"
        style="z-index: 100000;"
>
    <div class="modal-dialog modal-dialog-centered custom-modal-width">
        <div class="modal-content p-4" style="border-radius: 0 !important;">
            <div class="modal-header">
                <h5 class="modal-title fw-semibold nip-text-color">Officer ID Check required</h5>
                <button type="button" class="btn-close nip-close-modal-icon mr-4" data-dismiss="modal" aria-label="Close"></button>
            </div>

            <div class="modal-body">
                <p>
                    It looks like the ID Check for this officer hasn’t been done yet.
                    To comply with Companies House regulations, all company officers must complete an ID Check.
                </p>
            </div>

            <div class="modal-footer">
                <div class="d-flex justify-content-between align-items-center w-100">
                    <div>
                        <a class="nip-link-light fw-semibold" data-dismiss="modal">Close</a>
                    </div>
                    <div class="d-flex flex-align-end">
                        <div class="col-7 mr-1">
                            <a
                                    href="{url route="id_entity_validation" company=$companyId entityId=$invalidEntityId checkName='ID_VERIFICATION'}"
                                    class="btn btn-orange btn-view-formation">
                                Complete ID Check now
                                <i data-v-4aac1e54="" class="fa fa-long-arrow-right mx-3"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{/if}

<script type="text/javascript">
    $(function () {
        $('[name="designated_ind"]').change(function (){
            setConsentLabel();
        }).trigger('change');
        function setConsentLabel() {
            var value = $('[name="designated_ind"]:checked').val();
            var label = $('#consentToAct').data('label-designated_ind-' + value);
            $('label[for="consentToAct"]').text(label);
        }

        const invalidEntityId = '{$invalidEntityId}';
        if (invalidEntityId) {
            $('#idCheckModal').modal('show')
        }
    });

    CompaniesHouse.Controllers.PersonControllerFactory.createLegacy({
            rivets: {
                selector: '#add-director-person',
            },
            prefiller: {
                selectors: {
                    officers: '#prefillOfficers',
                    addresses: '#prefillAddress',
                },
                data: {
                    officers: {$jsPrefillOfficers nofilter},
                    addresses: {$jsPrefillAdresses nofilter},
                },
            },
            tabs: {
                selector: '#id-check-tabs'
            }
        },
        {
            personAddress: {
                countryOfResidence: '{$view->getCountryOfResidence()}',
            },
            serviceAddress: {
                isMsgAddress: {$view->hasOurServiceAddress()|booleanToString}
            },
            residentialAddress: {
                isCompulsory: {$view->hasOurServiceAddress()|booleanToString},
                different: {$view->hasDifferentResidentialAddress()|booleanToString},
                pca: {
                    key: 'GD81-KE87-YR36-MY93',
                    data: {$view->getResidentialAddressPcaData() nofilter},
                }
            },
            idCheck: {
                documentType: '{$view->getDocumentType()}',
                passportCountry: '{$view->getPassportCountry()}',
                idStatus: {$view->getIdStatus()->getValue()},
                residentialAddressData: {$view->getPcaData() nofilter},
                type: 'UPD-ADD-DIR'
            }
        });
</script>

{include file="@footer.tpl"}
