{include file="@header.tpl"}
{styles file='webloader_front.neon' section='modal'}
{* JS FOR PREFILL *}
<script type="text/javascript">
/* <![CDATA[ */
var addresses = {$jsPrefillAdresses nofilter};
var officers = {$jsPrefillOfficers nofilter};
/* ]]> */
</script>

{literal}
<script>
$(document).ready(function () {

	// prefill address
	$("#prefillAddress").change(function () {
		var value = $(this).val();
		address = addresses[value];
		for (var name in address) {
			$('#'+name).val(address[name]);
		}
	});

	// prefill officers
	$("#prefillOfficers").change(function () {
		var value = $(this).val();
		address = officers[value];
		for (var name in address) {
			$('#'+name).val(address[name]);
		}
	});

	// UK
	toogleUK();

	$("input[name='type']").click(function (){
		toogleUK();
	});

	function toogleUK() {
		// UK
		if ($("#type1").is(":checked")) {
			$("#place_registered").val("United Kingdom");
			$("#place_registered").attr("readonly", true).attr("disabled", false);
			$("#registration_number").attr("readonly", false).attr("disabled", false);
			$("#law_governed").attr("readonly", true).attr("disabled", true);
			$("#legal_form").attr("readonly", true).attr("disabled", true);
		// Non UK
		} else if ($("#type2").is(":checked")) {
			$("#place_registered").attr("readonly", false).attr("disabled", false);
			$("#registration_number").attr("readonly", false).attr("disabled", false);
			$("#law_governed").attr("readonly", false).attr("disabled", false);
			$("#legal_form").attr("readonly", false).attr("disabled", false);
		} else {
			$("#place_registered").attr("readonly", true).attr("disabled", true);
			$("#registration_number").attr("readonly", true).attr("disabled", true);
			$("#law_governed").attr("readonly", true).attr("disabled", true);
			$("#legal_form").attr("readonly", true).attr("disabled", true);
		}

	}

	$('[name="designated_ind"]').change(function (){
		setConsentLabel();
	}).trigger('change');

	function setConsentLabel() {
		var value = $('[name="designated_ind"]:checked').val();
		var label = $('#consentToAct').data('label-designated_ind-' + value);
		$('label[for="consentToAct"]').text(label);
	}

	const invalidEntityId = '{$invalidEntityId}';
	if (invalidEntityId) {
		$('#idCheckModal').modal('show')
	}
});
</script>
{/literal}
<div class="row">
	<div class="col-md-6 col-xs-12">
		<div class="row">
			<div class="col-xs-12">
				<p style="margin: 0 0 15px 0; font-size: 11px;"><a href="{$this->router->link("FrontModule\controlers\CUSummaryControler::SUMMARY_PAGE", "company_id=$companyId")}">{$companyName}</a> &gt; Add Corporate{if $companyType == 'LLP'} Member{else} Director{/if}</p>
			</div>
		</div>
		<div class="row">
			<div class="col-xs-12">
				{if $visibleTitle}
					<h1>Add Corporate{if $companyType == 'LLP'} Member{else} Director{/if}</h1>
				{/if}
			</div>
		</div>
		<div class="row">
			<div class="col-xs-12 text-right">
				<p style="text-align: right;">To make a human appointment <a href="{$this->router->link("FrontModule\controlers\CUDirectorsControler::ADD_DIRECTOR_PERSON_PAGE", "company_id=$companyId")}">click here</a></p>
			</div>
		</div>

		<div class="row">
			<div class="col-xs-12" style="background-color: #f2f3f5; padding-top: 10px; padding-bottom:10px;">
				{$form nofilter}
			</div>
		</div>
	</div>
</div>

{if $invalidEntityId}
	<div
			class="modal fade"
			id="idCheckModal"
			data-bs-backdrop="static"
			data-bs-keyboard="false"
			tabindex="-1"
			aria-hidden="true"
			style="z-index: 100000;"
	>
		<div class="modal-dialog modal-dialog-centered custom-modal-width">
			<div class="modal-content p-4" style="border-radius: 0 !important;">
				<div class="modal-header">
					<h5 class="modal-title fw-semibold nip-text-color">Officer ID Check required</h5>
					<button type="button" class="btn-close nip-close-modal-icon mr-4" data-dismiss="modal" aria-label="Close"></button>
				</div>

				<div class="modal-body">
					<p>
						It looks like the ID Check for this officer hasn’t been done yet.
						To comply with Companies House regulations, all company officers must complete an ID Check.
					</p>
				</div>

				<div class="modal-footer">
					<div class="d-flex justify-content-between align-items-center w-100">
						<div>
							<a class="nip-link-light fw-semibold" data-dismiss="modal">Close</a>
						</div>
						<div class="d-flex flex-align-end">
							<div class="col-7 mr-1">
								<a
										href="{url route="id_entity_validation" company=$companyId entityId=$invalidEntityId checkName='SUPPORTING_DOCUMENTS'}"
										class="btn btn-orange btn-view-formation">
									Complete ID Check now
									<i data-v-4aac1e54="" class="fa fa-long-arrow-right mx-3"></i>
								</a>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
{/if}
{include file="@footer.tpl"}
