<h2>
    {if $data.company_details.company_category != 'LLP'}
        Directors
        {feature type='companies_house_service'}
            <span style="font-size: 12px; font-weight: 400;">
                        <a class="auth-code-required" href="{$this->router->link("FrontModule\controlers\CUDirectorsControler::ADD_DIRECTOR_PERSON_PAGE", "company_id=$companyId")}">Appoint New Director</a>
                    </span>
        {/feature}
    {else}
        Members
        {feature type='companies_house_service'}
            <span style="font-size: 12px; font-weight: 400;">
                        <a class="auth-code-required" href="{$this->router->link("FrontModule\controlers\CUDirectorsControler::ADD_DIRECTOR_PERSON_PAGE", "company_id=$companyId")}">Appoint New Member</a>
                    </span>
        {/feature}
    {/if}
</h2>
<div class="table-responsive table-borderless" style="max-width: calc(100vw - 30px)">
    <table class="grid2 table" style="margin-bottom: 20px;">
    <col width="600">
    <col width="60">
    <col width="60">
    <col width="60">
    <tr>
        <th>Name</th>
        <th colspan="3" class="center">Action</th>
    </tr>
    {if !empty($data.directors)}
        {foreach from=$data.directors key="id" item="director"}
            <tr>
                <td>
                    {if $director.corporate}
                        {$director.corporate_name}
                    {else}
                        {$director.title} {$director.forename} {$director.middle_name} {$director.surname}
                    {/if}
                </td>
                {if $director.corporate}
                    <td class="center"><a href="{$this->router->link("FrontModule\controlers\CUDirectorsControler::SHOW_DIRECTOR_CORPORATE_PAGE", "company_id=$companyId", "director_id=$id")}" >View</a></td>
                    <td class="center">
                        {feature type='companies_house_service'}
                            <a class="auth-code-required" href="{$this->router->link("FrontModule\controlers\CUDirectorsControler::EDIT_DIRECTOR_CORPORATE_PAGE", "company_id=$companyId", "director_id=$id")}" >Edit</a>
                        {/feature}
                    </td>
                    <td class="center">
                        {feature type='companies_house_service'}
                            <a class="auth-code-required-popup" href="{$this->router->link("FrontModule\controlers\CUSummaryControler::RESIGNATION_PAGE", "company_id=$companyId", "officer_id=$id" , "resign=1")}">Resign</a>
                        {/feature}
                    </td>
                {else}
                    <td class="center"><a href="{$this->router->link("FrontModule\controlers\CUDirectorsControler::SHOW_DIRECTOR_PERSON_PAGE", "company_id=$companyId", "director_id=$id")}" >View</a></td>
                    <td class="center">
                        {feature type='companies_house_service'}
                            <a class="auth-code-required" href="{$this->router->link("FrontModule\controlers\CUDirectorsControler::EDIT_DIRECTOR_PERSON_PAGE", "company_id=$companyId", "director_id=$id")}" >Edit</a>
                        {/feature}
                    </td>
                    <td class="center">
                        {feature type='companies_house_service'}
                            <a class="auth-code-required-popup" href="{$this->router->link("FrontModule\controlers\CUSummaryControler::RESIGNATION_PAGE", "company_id=$companyId", "officer_id=$id" , "resign=1")}">Resign</a>
                        {/feature}
                    </td>

                {/if}
            </tr>
        {/foreach}
    {else}
        <tr>
            <td colspan="4">No Directors</td>
        </tr>
    {/if}
</table>

    {if !empty($officerAppointmentEntities)}
        <h2>
        {if $data.company_details.company_category != 'LLP'}
            Directors pending ID check

        {else}
            Members pending ID check
        {/if}
        </h2>

        <div class="table-responsive table-borderless" style="max-width: calc(100vw - 30px)">
            <table class="grid2 table" style="margin-bottom: 20px;">
                <col width="600">
                <col width="60">
                <col width="60">
                <col width="60">
                <tr>
                    <th>Name</th>
                    <th class="center">Action</th>
                </tr>
                {foreach from=$officerAppointmentEntities key="id" item="director"}
                    <tr>
                        <td>
                            {if $director->getTypeId() === IdModule\Domain\IdEntity::COMPANY}
                                {$director->getOriginalData()->getCompanyDetails()->getCompanyName()}
                            {else}
                                {$director->getOriginalData()->getPersonalDetails()->getFirstName()}
                                {$director->getOriginalData()->getPersonalDetails()->getLastName()}
                            {/if}

                        </td>
                        <td><a href="{url route="front_module.withdraw_appointment" company=$company->getId() formSubmissionId=$director->getFirstMemberId()}"
                               onclick="return confirm('This will delete this appointment. Are you sure?')">withdraw</a></td>
                    </tr>
                {/foreach}
            </table>
        </div>
    {/if}
</div>
