<?xml version="1.0" ?>
<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <defaults public="true" />
        <service class="FrontModule\Deciders\ServiceWidgetDecider" id="front_module.deciders.service_widget_decider">
            <argument id="customer_module.providers.customer_setting_provider" type="service"/>
            <argument id="front_module.deciders.service_widget_condition_decider" type="service"/>
        </service>

        <service class="FrontModule\Deciders\ServiceWidgetStatusDecider" id="front_module.deciders.service_widget_status_decider">
            <argument id="id_module.repositories.id_info_repository" type="service"/>
            <argument id="mail_scan_module.repositories.vo_search_repository" type="service"/>
            <argument id="vo_service_module.repositories.vo_service_queue_repository" type="service"/>
            <argument id="factories.front.service_view_factory" type="service"/>
            <argument id="banking_module.repositories.banking_repository" type="service"/>
            <argument id="business_services_module.providers.choices_provider" type="service"/>
            <argument id="business_services_module.facades.select_offers_facade" type="service"/>
            <argument id="business_services_module.providers.offer_provider" type="service"/>
        </service>

        <service class="FrontModule\Processors\ServiceWidgetProcessor" id="front_module.processors.service_widget_processor">
            <argument id="front_module.deciders.service_widget_decider" type="service" />
            <argument id="front_module.deciders.service_widget_status_decider" type="service"/>
            <argument id="front_module.factories.service_widget_factory" type="service"/>
            <argument>%widgets%</argument>
        </service>

        <service class="FrontModule\Deciders\ServiceWidgetConditionDecider" id="front_module.deciders.service_widget_condition_decider">
            <argument id="business_services_module.facades.select_offers_facade" type="service"/>
            <argument id="front_module.facades.mantle_data_facade" type="service"/>
        </service>

        <service class="FrontModule\Facades\MantleDataFacade" id="front_module.facades.mantle_data_facade">
            <argument id="mantle_module.api_client.mantle_client" type="service"/>
            <argument type="service" id="error.loggers.monolog"/>
            <argument>%environment%</argument>
        </service>

        <service class="FrontModule\Dto\ServiceWidgetCustomData" id="front_module.dto.service_widget_custom_data">
            <argument id="front_module.facades.mantle_data_facade" type="service"/>
            <argument id="csms_module.repositories.csms_report_repository" type="service"/>
            <argument id="repositories.company_repository" type="service"/>
            <argument id="csms_module.deciders.csms_company_decider" type="service"/>
        </service>

        <service class="FrontModule\Factories\ServiceWidgetFactory" id="front_module.factories.service_widget_factory">
            <argument id="front_module.dto.service_widget_custom_data" type="service"/>
            <argument id="services.package_service" type="service"/>
        </service>

        <service class="FrontModule\Controlers\WithdrawAppointmentController" id="front_module.controllers.withdraw_appointment_controller">
            <argument id="companies_house_module.repositories.member_repository" type="service"/>
            <argument id="router_module.helpers.controller_helper" type="service"/>
        </service>

    </services>
</container>
