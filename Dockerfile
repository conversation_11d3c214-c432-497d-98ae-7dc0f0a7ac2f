ARG registry
ARG shared_tag="latest"
FROM ${registry}msg-dev/httpd-php82:${shared_tag}

# content
#COPY --chown=apache:apache . /var/www/project

# avoid ?
ARG hosts_with_ssl="localhost"
ARG virtual_host_type="production"
ARG REVIEW_MACHINE_NAME="non-existing"
ARG VALID_IPS="************/24"

#RUN runuser apache - mkdir -p /var/www/temp/cache/ui\
#    && chmod -R 777 /var/www/project/www/webtemp /var/www/project/temp\
#    && setup
RUN setup

#VOLUME /var/www/project
WORKDIR /var/www/project

