<?php

namespace Features\Bootstrap;

use Behat\Behat\Context\Context;
use Behat\Behat\Context\SnippetAcceptingContext;
use Behat\MinkExtension\Context\MinkContext;
use CompanyModule\Domain\Company\CompanyName;

class RetailPurchaseFlowContext extends MinkContext implements Context, SnippetAcceptingContext
{
    /**
     * @param Basket $basket
     */
    private function resetBasketSession(Basket $basket)
    {
        $basket->clear(TRUE);
    }

    /**
     * @Given /^basket is empty$/
     */
    public function basketIsEmpty()
    {
        $this->resetBasketSession(new Basket());
    }

    /**
     * @Given /^I didn't search for a name yet$/
     */
    public function iDidnTSearchForANameYet()
    {
        $this->resetBasketSession(new Basket());
    }

    /**
     * @Given /^I searched for "([^"]*)"$/
     * @param $companyName
     */
    public function iSearchedFor($companyName)
    {
        $basket = new Basket();
        $basket->setIncorporationCompanyName(CompanyName::uppercased($companyName));
    }
}