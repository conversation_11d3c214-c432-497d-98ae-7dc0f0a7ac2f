<?php

namespace Features\Bootstrap;

use Behat\Behat\Context\Context;
use Behat\Behat\Context\SnippetAcceptingContext;
use Behat\Behat\Hook\Scope\AfterStepScope;
use Behat\Mink\Driver\Selenium2Driver;
use Behat\Mink\Element\NodeElement;
use Behat\Mink\Exception\ElementNotFoundException;
use Behat\Mink\Exception\ResponseTextException;
use Behat\MinkExtension\Context\MinkContext;
use BootstrapModule\Singletons\StaticContainer;
use Entities\Payment\Token;
use Entities\Customer;
use Entities\Company;
use tests\helpers\EntityHelper;
use tests\helpers\ObjectHelper;
use Utils\Date;

/**
 * Defines application features from the specific context.
 */
class WebContext extends \TestModule\Behat\WebContext
{
    /**
     * @var Customer
     */
    protected $customer;

    /**
     * @var Company
     */
    protected $company;

    /**
     * @var Token
     */
    protected $token;

    /**
     * @Transform /^(is|is not)$/
     */
    public function transformToBool(string $value)
    {
        return $value === 'is';
    }

    /** @BeforeScenario */
    public function before($event)
    {
        //$this->getSession()->getDriver()->setTimeouts(['page load' => 10000]);
        EntityHelper::emptyTables(
            array_merge(
                [TBL_CUSTOMERS, TBL_COMPANY, TBL_TOKENS, TBL_TRANSACTIONS, TBL_ORDERS, TBL_ORDERS_ITEMS, TBL_VOUCHERS],
                EntityHelper::$tables
            )
        );
    }

    /**
     * @Given I am :customerEmail
     */
    public function iAm($customerEmail)
    {
        $this->customer = ObjectHelper::createCustomer($customerEmail, 'password');
        $this->customer->setStatusId(Customer::STATUS_VALIDATED);
        EntityHelper::save([$this->customer]);
        $this->iHaveCompany('2');
    }

    /**
     * @Given I have company :companyNumber
     */
    public function iHaveCompany($companyNumber)
    {
        $this->company = ObjectHelper::createCompany($this->customer, 'Test Company', $companyNumber);
        $this->company->setCompanyStatus(Company::COMPANY_STATUS_ACTIVE);
        EntityHelper::save([$this->company]);
    }

    /**
     * @Given I am :customerEmail with credit :credit
     */
    public function iAmWithCredit($customerEmail, $credit)
    {
        $this->iAm($customerEmail);
        $this->customer->setCredit($credit);
        EntityHelper::save([$this->customer]);
    }

    /**
     * @Given I log in
     */
    public function iLogIn()
    {
        $this->visit('/login.html');
        $this->fillField('login_form_email', $this->customer->getEmail());
        $this->fillField('login_form_password', 'password');
        $this->pressButton('login_form_submit');
        $this->waitForPageToLoad();
    }

    /**
     * @Given At checkout I log in as :customerEmail
     */
    public function atCheckoutILogInAs($customerEmail)
    {
        $this->fillField('login_form_email', $this->customer->getEmail());
        $this->fillField('login_form_password', 'password');
        $this->pressButton('login_form_submit');
        $this->waitForPageToLoad();
    }

    /**
     * @Given I have a token
     */
    public function iHaveAToken()
    {
        $this->token = ObjectHelper::createToken($this->customer, TRUE, new Date('+1 year'));
        EntityHelper::save([$this->token]);
    }

    /**
     * @Given I am logged in customer
     */
    public function iAmLoggedInCustomer()
    {
        $this->iAm('<EMAIL>');
        $this->iLogIn();
    }

    /**
     * @Given Product :productId has :key :value
     */
    public function modifyProduct($productId, $key, $value)
    {
        /** @var Product $product */
        $product = FNode::getProductById($productId);
        $product->$key = $value;
        $product->save(TRUE);
    }

    /**
     * @Given I search for new company name :companyName
     */
    public function searchForCompanyName($companyName)
    {
        $this->visit('/');
        $this->fillField('company_name_search_form_q', $companyName);
        $this->pressButton('Search');
    }

    /**
     * @Given confirm alert
     */
    public function iConfirmThePopup()
    {
        $driver = $this->getSession()->getDriver();
        if (!$driver instanceof Selenium2Driver) {
            return;
        }
        $driver->getWebDriverSession()->accept_alert();
    }

}
