<?php

namespace Features\Bootstrap\WorkflowEngine;

use BankingModule\Entities\BarclaysLead;
use BankingModule\Entities\TsbLead;
use Behat\Behat\Context\Context;
use Behat\Behat\Context\SnippetAcceptingContext;
use Behat\Behat\Hook\Scope\BeforeScenarioScope;
use Behat\Gherkin\Node\TableNode;
use DateTime;
use EmailModule\IPlaceholderProvider;
use Entities\Company;
use Entities\Customer;
use Iterator;
use MailgunModule\Entities\MailgunEvent;
use PHPUnit\Framework\Assert as test;
use TestModule\Behat\ObjectInjectorContext;
use TestModule\Helpers\DatabaseHelper;
use WorkflowEngineModule\Contexts\CustomerContext;
use WorkflowEngineModule\PlaceholderProviders\ICustomerProvider;

class BankLeadsContext extends ObjectInjectorContext implements Context, SnippetAcceptingContext
{
    /**
     * @var DatabaseHelper
     */
    private $databaseHelper;

    /**
     * @var Iterator
     */
    private $customers;

    /**
     * @Transform /^([^ ]+@.+)$/
     */
    public function toArray($emails)
    {
        return array_map('trim', explode(',', $emails));
    }

    /**
     * @param DatabaseHelper $databaseHelper
     */
    public function __construct(DatabaseHelper $databaseHelper)
    {
        $this->databaseHelper = $databaseHelper;
    }

    /**
     * @BeforeScenario
     */
    public function prepare(BeforeScenarioScope $scope)
    {
        $this->databaseHelper->emptyTables([TBL_CUSTOMERS, TBL_COMPANIES, TBL_TSB_LEADS, TBL_BARCLAYS, TBL_MAILGUN_EVENTS]);
        $this->customers = $scope->getEnvironment()->getContext(CustomerContext::class)->getCustomerCollection();
    }

    /**
     * @Given bank lead data:
     */
    public function bankLeadData(TableNode $table)
    {
        foreach ($table as $row) {
            $customer = new Customer($row['email'], 'test');
            $this->databaseHelper->saveEntity($customer);
            if (!empty($row['companyName'])) {
                $company = new Company($customer, $row['companyName']);
                $this->databaseHelper->saveEntity($company);
            }
            if (!empty($row['dateSent'])) {
                $mailgunEvent = MailgunEvent::fromRecipient('sent', $customer->getEmail(), $row['emailTemplateId'], 'test', [], 0);
                $mailgunEvent->setCustomer($customer);
                $mailgunEvent->setCreatedAt(new DateTime($row['dateSent']));
                $this->databaseHelper->saveEntity($mailgunEvent);
            }
            if (!empty($row['leadType'])) {
                if ($row['leadType'] === 'tsb') {
                    $lead = new TsbLead($company);
                    $lead->setDtc(new DateTime($row['leadDate']));
                    $this->databaseHelper->saveEntity($lead);
                } elseif ($row['leadType'] === 'barclays') {
                    $lead = new BarclaysLead($company);
                    $lead->setDtc(new DateTime($row['leadDate']));
                    $lead->setTest(FALSE);
                    $lead->setSuccess($row['success'] === 'true');
                    $lead->setResponseCode(200);
                }
                $this->databaseHelper->saveEntity($lead);
            }
        }

    }

    /**
     * @Then /^emails should be sent to (.+)$/
     * @param array $emails
     */
    public function checkEmailsSentTo(array $emails)
    {
        $providers = iterator_to_array($this->customers);
        $sendEmails = array_map(function(ICustomerProvider $customerProvider) {
            return $customerProvider->getEmail();
        }, $providers);
        test::assertEquals($emails, $sendEmails);
    }
}
