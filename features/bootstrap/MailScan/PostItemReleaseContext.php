<?php

namespace Features\Bootstrap\MailScan;

use CompanyModule\Contracts\IService;
use CompanyModule\Domain\Company\CompanyName;
use <PERSON>ron\Loggers\IEventNotifier;
use Entities\Company;
use Entities\Customer;
use MailScanModule\Checkers\IObjectChecker;
use MailScanModule\Commands\PostItemUpdater;
use MailScanModule\Deciders\ReleaseItemDecider;
use MailScanModule\Dto\PostItemData;
use MailScanModule\Dto\VoService;
use MailScanModule\Emailers\PostDataEmailer;
use MailScanModule\Entities\PostItem;
use MailScanModule\Factories\PostItemDataFactory;
use MailScanModule\Repositories\PostItemRepository;
use MailScanModule\Repositories\ScanRepository;
use Prophecy\Argument;
use Psr\Log\LoggerInterface;
use Repositories\ServiceSettingsRepository;
use ServiceModule\Deciders\LatePaymentFeeDecider;
use TestModule\Behat\ObjectInjectorContext;
use PHPUnit\Framework\Assert as test;

class PostItemReleaseContext extends ObjectInjectorContext
{
    /**
     * @var PostItem|null
     */
    private $postItem;

    /**
     * @var PostItemUpdater
     */
    private $command;

    /**
     * @var ScanRepository
     */
    public $scanRepository;

    /**
     * @var PostDataEmailer
     */
    public $emailer;

    /**
     * @var PostItemRepository
     */
    public $repository;

    /**
     * @var IObjectChecker
     */
    public $objectChecker;

    /**
     * @var Customer|null
     */
    private $customer;

    /**
     * @var Customer|null
     */
    private $voCustomer;

    /**
     * @var ReleaseItemDecider
     */
    public $decider;

    /**
     * @var Company|null
     */
    private $company;

    /**
     * @var IService
     */
    public $cmsService;

    public function setupWithMocks(
        PostDataEmailer $emailer,
        PostItemRepository $repository,
        LoggerInterface $logger,
        IEventNotifier $eventNotifier,
        IObjectChecker $objectChecker,
        ScanRepository $scanRepository,
        ReleaseItemDecider $decider,
        ServiceSettingsRepository $serviceSettingsRepository,
        LatePaymentFeeDecider $latePaymentFeeDecider,
        IService $cmsService
    )
    {
        $factory = new PostItemDataFactory($scanRepository, $decider);
        $this->command = new PostItemUpdater($emailer, $repository, $factory, $logger, $eventNotifier, $objectChecker);
    }

    public function setUpDepsAfterScenario()
    {
    }

    /**
     * @Given post item status is :status and type is :type
     */
    public function createPostItem(string $status, string $type)
    {
        $statusId = $status === "Released" ? PostItem::STATUS_RELEASED : PostItem::STATUS_WAITING;
        $typeId = $type === 'CH' ? PostItem::TYPE_COMPANIES_HOUSE : PostItem::TYPE_HMRC;
        $this->postItem = $this->getPostItem($statusId, $typeId);
        $this->scanRepository->getCompanyByNumber($this->postItem->getCompanyNumber())->willReturn(NULL);
        $this->repository->getWaitingPostItems()->willReturn([$this->postItem]);
        $this->objectChecker->exists(Argument::any())->willReturn(TRUE);
        $this->repository->markItemAsReleased($this->postItem)->will(function (array $args) {
            $args[0]->markReleased();
        });

    }

    /**
     * @Given email :email for this post item has not been sent
     */
    public function emailHasBeenSent(string $email, string $site = 'cms')
    {
    }

    /**
     * @Then email :email is sent to the :site customer
     */
    public function checkEmail(string $email, string $site = 'cms')
    {
        $this->emailer->sendEmail(Argument::that($this->getMatching($site, $email)))->shouldBeCalled();
    }

    /**
     * @Then email :email is not sent to the :site customer
     */
    public function checkEmailIsNotSent(string $email, string $site = 'cms')
    {
        $this->emailer->sendEmail(Argument::that($this->getMatching($site, $email)))->shouldNotBeCalled();
    }

    /**
     * @Given customer's proof of id is :status
     */
    public function customerProofOfId(string $status = NULL)
    {
        $this->decider->isIdCheckCompleted($this->company)->willReturn($status === 'Valid');
    }

    /**
     * @Given customer has a company on cms
     */
    public function hasCompany(string $status = NULL)
    {
        $this->company = $this->getCompany();
        $this->customer = $this->company->getCustomer();
        $this->scanRepository->getCompanyByNumber($this->postItem->getCompanyNumber())->willReturn($this->company);
        $this->scanRepository->getCmsMailService($this->company)->willReturn(NULL);

    }

    /**
     * @Given customer does not have a company on cms
     */
    public function noCmsCompany()
    {
        $this->scanRepository->getCompanyByNumber($this->postItem)->willReturn(NULL);
    }

    /**
     * @Given company has cms service with status :status
     */
    public function createMailService(string $status)
    {
        if ($status === 'Active') {
            $this->cmsService->isActive()->willReturn(TRUE);
            $this->cmsService->isOverdue()->willReturn(FALSE);
            $this->cmsService->isSuspended()->willReturn(FALSE);
        } elseif ($status === 'Overdue') {
            $this->cmsService->isActive()->willReturn(TRUE);
            $this->cmsService->isOverdue()->willReturn(TRUE);
            $this->cmsService->isSuspended()->willReturn(FALSE);
        } elseif ($status === 'Suspended') {
            $this->cmsService->isActive()->willReturn(FALSE);
            $this->cmsService->isSuspended()->willReturn(TRUE);
            $this->cmsService->isOverdue()->willReturn(FALSE);
        } else {
            $this->cmsService->isActive()->willReturn(FALSE);
            $this->cmsService->isSuspended()->willReturn(FALSE);
            $this->cmsService->isOverdue()->willReturn(FALSE);
        }

        $this->cmsService->getCompanyId()->willReturn($this->company->getId());
        $this->cmsService->getCustomer()->willReturn($this->customer);
        $this->service = $this->cmsService;
        $this->scanRepository->getCmsMailService($this->company)->willReturn($this->service);
    }

    /**
     * @Given company does not have cms service with status :status
     */
    public function noMailService(string $status)
    {
        $this->scanRepository->getCmsMailService($this->company)->willReturn(NULL);
    }

    /**
     * @Then post item status becomes :status
     */
    public function checkPostItemStatus(string $status)
    {
        $statusId = $status === "Released" ? PostItem::STATUS_RELEASED : PostItem::STATUS_WAITING;
        test::assertEquals($statusId, $this->postItem->getStatusId());
        $this->getProphet()->checkPredictions();
    }

    /**
     * @When mailroom process runs
     */

    public function runUpdater()
    {
        $this->repository->fixMissingCompanyNumbers(Argument::type(LoggerInterface::class))->willReturn(1);
        $this->result = $this->command->updatePostItems(FALSE);
    }

    /**
     * @Given company is :status
     */
    public function companyStatus(string $status)
    {
        $this->company = $this->getCompany();
        $this->company->setLocked(TRUE);
        $this->scanRepository->getCompanyByNumber($this->postItem->getCompanyNumber())->willReturn($this->company);
    }

    /**
     * @Given customer has vo service with status :status
     */
    public function createVoService(string $status)
    {
        $statusId = strtoupper($status);
        $this->voCustomer = new Customer('test1', '4');
        $voService = new VoService($this->voCustomer, 'Test', strtoupper($status));
        $this->scanRepository->getVoMailService($this->postItem->getLpNumber())->willReturn($voService);
        $this->scanRepository->getActiveVoMailService(Argument::any())->willReturn($statusId === VoService::STATUS_ACTIVE ? $voService : NULL);
    }

    /**
     * @Given customer does not have vo service with status :status
     */
    public function noVoService(string $status)
    {
        $this->scanRepository->getActiveVoMailService(Argument::any())->willReturn(NULL);
        $this->scanRepository->getVoMailService(Argument::any())->willReturn(NULL);
    }

    /**
     * @param string $statusId
     * @param string $typeId
     * @return PostItem
     */
    private function getPostItem($statusId = PostItem::STATUS_WAITING, $typeId = PostItem::TYPE_HMRC)
    {
        $postItem = new PostItem($typeId, $statusId, CompanyName::uppercased('test'), '123', 'any');
        return $postItem;
    }

    /**
     * @param bool $idCheckPassed
     * @return Company
     */
    private function getCompany()
    {
        $customer = Customer::temporary('test');
        $company = new Company($customer, 'name');
        $company->setCompanyNumber('123');
        $company->setCustomer($customer);
        return $company;
    }

    private function getMatching(string $site, string $email)
    {
        return function (PostItemData $itemData) use ($site, $email) {

            if ($email === 'any') {
                $emailsMatch = (bool) $itemData->getEmailId();
            } else {
                $emailId = $this->mapEmail($email);
                if (!$emailId) {
                    return true;
                }
                $emailsMatch = $emailId === $itemData->getEmailId();
            }
            if ($site === 'cms') {
                return $emailsMatch && $this->customer === $itemData->getCustomer();
            } elseif ($site === 'vo') {
                return $emailsMatch && $this->voCustomer === $itemData->getCustomer();
            } else {
                return $emailsMatch;
            }
        };
    }

    private function mapEmail(string $email): int
    {
        switch ($email) {
            case 'A':
                return PostItemData::SERVICE_ACTIVE_WITH_ID_CHECK;
            case 'B':
                return PostItemData::SERVICE_OVERDUE_WITH_ID_CHECK;
            case 'C':
                return PostItemData::SERVICE_ACTIVE_NO_ID_CHECK;
            case 'D':
                return PostItemData::SERVICE_OVERDUE_NO_ID_CHECK;
            case 'E':
                return PostItemData::SERVICE_SUSPENDED;
            case 'F':
                return PostItemData::SERVICE_DOES_NOT_EXIST;
            default:
                return 0;
        };
    }
}