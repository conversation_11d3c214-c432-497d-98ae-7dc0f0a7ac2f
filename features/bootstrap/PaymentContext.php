<?php

namespace Features\Bootstrap;

use Behat\Mink\Exception\ElementNotFoundException;
use BootstrapModule\Singletons\StaticContainer;
use PaymentModule\Contracts\IPaymentData;
use PaymentModule\Dto\PaymentDetailsFactory;
use PaymentModule\Forms\PurchaseConfirmationForm;
use PaymentModule\PaymentTypes\SagePayment;
use PaymentModule\Services\SageTransactionFinder;
use Repositories\Payment\TokenRepository;
use SagePayToken\Token\Exception\FailedResponse;
use Services\Payment\TokenService;
use tests\helpers\EntityHelper;
use tests\helpers\ObjectHelper;
use tests\helpers\VoucherHelper;

/**
 * Defines application features from the specific context.
 */
class PaymentContext extends WebContext
{
    /**
     * @var SagePayment
     */
    private $sagePayment;

    /**
     * @var TokenRepository
     */
    private $tokenRepository;

    /**
     * @var SageTransactionFinder
     */
    private $transactionFinder;

    public function __construct(
        SagePayment $sagePayment,
        TokenRepository $tokenRepository,
        SageTransactionFinder $transactionFinder
    )
    {
        $this->sagePayment = $sagePayment;
        $this->tokenRepository = $tokenRepository;
        $this->transactionFinder = $transactionFinder;
    }

    /**
     * @Given I add product :item to the basket
     */
    public function iAddProductToTheBasket($item)
    {
        $map = [
            443 => '/mail-forwarding-service.html',
            165 => '/company-services-registered-office.html',
        ];

        if (!isset($map[$item])) {
            throw new InvalidArgumentException(sprintf('Item not found in mapping: "%s"', $item));
        }

        $this->visit($map[$item]);
        $this->pressButton('Buy Now');
        $this->waitForPageToLoad();
        $this->clickLink('desktop-checkout-button');
        $this->waitForPageToLoad();
    }

    /**
     * @Given I am on payment page for renewal :productId
     */
    public function iAmRenewingProduct($productId)
    {
        $router = StaticContainer::get('url_generator');
        $this->visit($router->url('renew_show_payment', ['company' => $this->company->getId(), 'productId' => $productId]));
        $this->waitForPageToLoad();
    }

    /**
     * @Given I apply existing voucher :code with :discount percent discount
     */
    public function iApplyExistingVoucher($code, $discount)
    {
        $voucher = VoucherHelper::createDiscount($code, $discount);
        $voucher->save();
        $this->fillField('voucherCode', $voucher->voucherCode);
    }

    /**
     * @Given I use existing voucher :code with :discount percent discount
     */
    public function iUseExistingVoucher($code, $discount)
    {
        $voucher = VoucherHelper::createDiscount($code, $discount);
        $voucher->save();
        // we wait for the page to load first
        $this->wait(1000);
        $this->visit($this->getSession()->getCurrentUrl() . '&redeem=1');
        // selenium looks for first link and if its not visible throws exception
        $this->getSession()->wait(3000, '$("#mobile-voucher-block").remove().length == 1');
        $this->clickLink('I have a voucher');
        $this->wait(1000);
        $this->fillField('VoucherForm[voucherCode]', $voucher->voucherCode);
        $this->pressButton('Apply');
        $this->wait(1000);
    }

    /**
     * @Given I select my company
     */
    public function iSelectMyCompany()
    {
        $this->selectOption('company-number-select_0', $this->company->getId());
        $this->getSession()->wait(8000, '$("#payment:disabled").length === 0');
    }

    /**
     * @Given I am on payment page with products :item
     */
    public function iAmOnPaymentPageWithProducts($item)
    {
        $this->iAddProductToTheBasket($item);
        if ($this->company) {
            $this->iSelectMyCompany();
        }
        $this->pressButton('Checkout');
        //$this->assertPageAddress('/payment/');
    }

    /**
     * @TODO adapt to packages
     * @Given I am on payment page with package :item and companyName :companyName
     */
    public function iAmOnPaymentPageWithPackage($item, $companyName)
    {
        $this->visit('/search-for-company-name.html');
        $this->fillField('company_name_search_form_q', $companyName);
        $this->pressButton('send');
        $this->wait(3000);
        $this->visit('/page' . $item . 'en.html');
        $this->pressButton('Buy Now');
        $this->clickLink('desktop-checkout-button');

        $this->assertPageAddress('/payment/');
    }

    /**
     * completeType one of: [3d secure v1, 3d failure, address]
     * @Given I complete sage form with :completeType
     */
    public function iCompleteSageForm(string $completeType)
    {
        $this->fillField('Cardholder name', $completeType === '3d secure v1' ? 'STATUS201DS' : 'challenge');
        $this->selectOption('Card type', 'VISA');
        $this->fillField('Card number', '*************');
        $this->selectOption('payment_form[cardDetails][expiryDate][month]', '3');
        $this->selectOption('payment_form[cardDetails][expiryDate][year]', '2022');
        $this->fillField('Security code', '123');

        if ($completeType === 'address') {
            $this->fillField('Address Line 1', '123');
            $this->fillField('Town', '123');
            $this->fillField('Postcode', '123');
            $this->selectOption('Country', 'GB');
        }
        $this->checkOption('By making this purchase I agree');
        $this->pressButton('payment-submit-button');
        $this->iComplete3dAuthenticationForm($completeType);
    }

    /**
     * completeType on of: [3d failure]
     * @Given I complete 3d authentication form
     */
    public function iComplete3dAuthenticationForm(string $completeType = null)
    {
        if ($completeType === '3d failure') {
            $this->fail3dAuth();
        } else {
            $this->submit3dAuthForm();
        }
    }



    /**
     * @Given I fail 3d authentication form
     */
    public function fail3dAuth()
    {
        foreach (range(1, 3) as $index) {
            $stop = $this->submit3dAuthForm(false);
            if ($stop) {
                break;
            }
        }
    }

    public function submitButton()
    {
        $submit = $this->getSession()->getPage()->find('css', 'input[type=submit]');
        if (!$submit) {
            return new ElementNotFoundException($this->getSession(), 'input[type=submit]', 'css');
        }
        $submit->click();
    }

    /**
     * @Given I use email :email
     */
    public function iUseEmail($email)
    {
        $this->fillField('Email', $email);
        $this->pressButton("Create account or log in");
        $this->getSession()->wait(2000, '$("button:contains(\"Create account or log in\"):disabled")');
    }

    /**
     * @Given Customer :customerKey with company :companyNumber exists
     */
    public function customerWithCompanyExists($customerKey, $companyNumber)
    {
        $this->customer = ObjectHelper::createCustomer(TEST_EMAIL1);
        $this->customer->setPassword('password');
        $this->customer->setStatusId(Customer::STATUS_VALIDATED);
        $this->customer->setCredit(1900);
        $this->company = ObjectHelper::createCompany($this->customer, 'Test Company');
        $this->company->setCompanyNumber($companyNumber);
        EntityHelper::save([$this->customer, $this->company]);
    }

    /**
     * @Given I buy :productId with credit
     */
    public function iBuyWithCredit($productId)
    {
        $this->customer->setCredit(99999);
        EntityHelper::save([$this->customer]);

        $this->iAmOnPaymentPageWithProducts($productId);
        $this->completePaymentOnAccount();
    }

    public function completePaymentOnAccount()
    {
        $this->checkOption('Use credit to make this payment');
        $this->checkOption('By making this purchase I agree');
        $this->pressButton('payment-submit-button');
    }

    /**
     * @Given I should not see auto-renewal checkbox
     */
    public function iShouldNotSeeAutoRenewalCheckbox()
    {
        $this->assertElementNotOnPage(PurchaseConfirmationForm::NAME . '[enableAutoRenewal]');
    }

    /**
     * @When I buy :productId with new card
     */
    public function iBuyWithNewCard($productId)
    {
        $this->iAmOnPaymentPageWithProducts($productId);
        $this->iCompleteSageForm('address');
    }

    /**
     * @When I buy :productId with one-off card
     */
    public function iBuyWithOneOffCard($productId)
    {
        $this->iAmOnPaymentPageWithProducts($productId);
        $this->fillField('payment_form[paymentType]', IPaymentData::TYPE_SAGE_NO_TOKEN);
        $this->iCompleteSageForm('address');
    }

    /**
     * @Given I don't have any payment method saved
     */
    public function iDontHaveAnyPaymentMethodSaved()
    {
        /** @var TokenService $tokenService */
        $tokenService = Registry::$container->get(DiLocator::SERVICE_TOKEN);
        $tokens = $this->customer->getTokens();
        foreach ($tokens as $token) {
            $tokenService->removeEntity($token);
        }
    }

    /**
     * @Then I should see checked auto-renewal checkbox
     */
    public function iShouldSeeCheckedAutoRenewalCheckbox()
    {
        $this->assertCheckboxChecked(PurchaseConfirmationForm::NAME . '[enableAutoRenewal]');
    }

    /**
     * @Then I should see unchecked auto-renewal checkbox
     */
    public function iShouldSeeUncheckedAutoRenewalCheckbox()
    {
        $this->assertCheckboxNotChecked(PurchaseConfirmationForm::NAME . '[enableAutoRenewal]');
    }

    /**
     * @When I complete payment method form with card :cardType
     */
    public function iCompletePaymentMethodFormWith($cardType)
    {

        if ($cardType === '0006') {
            $this->fillField('cardHolder', 'challenge');
            $this->selectOption('cardType', 'VISA');
            $this->fillField('cardNumber', '*************');
        } elseif ($cardType === '0043') {
            $this->fillField('cardHolder', 'Mr Tester');
            $this->selectOption('cardType', 'MC');
            $this->fillField('cardNumber', '5404 0000 0000 0043');
        }
        $this->selectOption('expiryDate[m]', '3');
        $this->selectOption('expiryDate[y]', '2022');
        $this->fillField('CV2', '123');

        $this->fillField('address1', '123');
        $this->fillField('city', '123');
        $this->fillField('postcode', '123');
        $this->selectOption('country', 'GB');
//        $this->checkOption('By making this purchase I agree');
        $this->pressButton('submit');
    }

    /**
     * @Given I have a token with card :cardNumber
     */
    public function iHaveATokenWithCard($cardNumber)
    {
        $this->visit('/wallet');
        $this->clickLink('Add New Card');
        $this->iCompletePaymentMethodFormWith($cardNumber);
        $this->wait(5000);
    }

    /**
     * @Given Token :cardNumber can be used for future payments
     */
    public function tokenCanBeUseForFuturePayments(string $cardNumber)
    {
        $token = $this->tokenRepository->requiredOneBy(['customer' => $this->customer, 'cardNumber' => $cardNumber]);
        $response = $this->sagePayment->makeTokenPayment(
            PaymentDetailsFactory::createFromToken($token, 10, 'test payment')
        );
        Assert::assertInstanceOf(\PaymentModule\Responses\PaymentResponse::class, $response);
    }

    /**
     * @Given Token :cardNumber can not be used for future payments
     */
    public function tokenCanNotBeUseForFuturePayments($cardNumber)
    {
        $token = $this->tokenRepository->requiredOneBy(['customer' => $this->customer, 'cardNumber' => $cardNumber]);
        try {
            $this->sagePayment->makeTokenPayment(
                PaymentDetailsFactory::createFromToken($token, 10, 'test payment')
            );
        } catch (FailedResponse $e) {
            return;
        }
        Assert::fail('Make payment should have throw an exception');
    }

    /**
     * @Given Transaction for card :card can be repeated
     */
    public function transactionCanBeRepeated($card)
    {
        $transaction = $this->transactionFinder->requireTransactionToRepeat($this->customer, $card);
        $response = $this->sagePayment->repeatTransaction(10.00, $transaction, 'test repeat');
        Assert::assertInstanceOf(\PaymentModule\Responses\PaymentResponse::class, $response);
    }

    /**
     * @Given Transaction for card :card can not be repeated
     */
    public function transactionCanNotBeRepeated($card)
    {
        try {
            $this->transactionCanBeRepeated($card);
        } catch (FailedResponse $e) {
            return;
        }
        Assert::fail('Repeat payment should have throw an exception');
    }

    private function submit3dAuthForm(bool $success = true)
    {
        try {
            $this->findElement('input[name=password]');
            $this->fillField('password', $success ? 'password' : 'wrong');
            $stop = true;
        } catch (ElementNotFoundException $e) {
            $this->fillField('cd', $success ? 'challenge' : 'wrong');
            $stop = false;
        }
        $this->submitButton();
        return $stop;
    }

}
