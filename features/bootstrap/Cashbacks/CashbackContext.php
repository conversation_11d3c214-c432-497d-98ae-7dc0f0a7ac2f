<?php

namespace Features\Bootstrap\Cashbacks;

use Behat\Behat\Context\Context;
use CashBackModule\Commands\CashbackStatusToggler;
use DateTime;
use Entities\Cashback;
use Entities\Company;
use Entities\Customer;
use IdModule\Dto\IdConversion;
use IdModule\Repositories\IIdCompanyInfoRepository;
use IdModule\Verification\IIdValidator;
use PHPUnit\Framework\Assert as test;
use Models\Products\RegisterOffice;
use TestModule\Helpers\DatabaseHelper;
use tests\helpers\ObjectHelper;
use Utils\Date;

class CashbackContext implements Context
{
    /**
     * @var CashbackStatusToggler
     */
    private $toggleCashbackStatus;

    /**
     * @var DatabaseHelper
     */
    private $databaseHelper;

    /**
     * @var IIdCompanyInfoRepository
     */
    private $companyInfoRepository;

    /**
     * @var IIdValidator
     */
    private $idValidator;

    public function __construct(
        CashbackStatusToggler $toggleCashbackStatus,
        DatabaseHelper $databaseHelper,
        IIdCompanyInfoRepository $companyInfoRepository,
        IIdValidator $idValidator
    )
    {
        $this->toggleCashbackStatus = $toggleCashbackStatus;
        $this->databaseHelper = $databaseHelper;
        $this->companyInfoRepository = $companyInfoRepository;
        $this->idValidator = $idValidator;
    }

    /** @BeforeScenario */
    public function before()
    {
        $this->databaseHelper->emptyTables([TBL_CUSTOMERS, TBL_COMPANY, TBL_CASHBACKS, TBL_CUSTOMER_LOGS, TBL_ID_VALIDATIONS]);
    }

    /**
     * @Given wholesale customer exists
     */
    public function wholesaleCustomer()
    {
        $customer = $this->customer = Customer::temporary('test1');
        $customer->setRoleId(Customer::ROLE_WHOLESALE);
        $customer->setCashbackType(Customer::CASHBACK_ON_BANK_ACCOUNT);
        $company = $this->company = ObjectHelper::createCompany(
            $customer, 'test1', '1', new Date('-1 year'), Company::COMPANY_STATUS_ACTIVE, RegisterOffice::POSTCODE
        );
        $this->databaseHelper->saveEntities([$customer, $company]);
    }

    /**
     * @Given customer id check status is :idStatus
     */
    public function customerIdCheckStatusIs(string $idStatus)
    {
        if ($idStatus === 'valid') {
            $this->manuallyValidate($this->company);
            $this->databaseHelper->saveEntity($this->customer);
        }
    }

    public function manuallyValidate(Company $company)
    {
        $entities = $this->companyInfoRepository->getIdInfoForCompanyIterator($company);
        foreach ($entities as $entity) {
            $manual = new IdConversion(true, $entity->getCheckNames());
            $this->idValidator->validateManually($entity->getEntity(), $manual, 'phpunit');
        }
    }

    /**
     * @Given customer has set claim preferences on :dateString
     */
    public function customerHasSetPreferenceOn(string $dateString)
    {
        $log = ObjectHelper::createCustomerLog($this->customer, new Date($dateString));
        $this->databaseHelper->saveEntities([$log]);
    }

    /**
     * @Given customer has applied for cashback on :dateString :status
     */
    public function customerHasAppliedForCashbackDaysAgo(string $dateString, string $status)
    {
        $this->cashback = $cashBack = ObjectHelper::createCashback(
            $this->company,
            $status,
            Cashback::PACKAGE_TYPE_WHOLESALE,
            new Date($dateString)
        );
        $this->databaseHelper->saveEntities([$cashBack]);
    }

    /**
     * @Given cashback status changes to :status
     */
    public function changeCashbackStatus(string $status)
    {
        $this->databaseHelper->saveEntity($this->cashback);
    }

    /**
     * @Then cashback status becomes :status
     */
    public function cashbackStatusIs(string $status)
    {
        test::assertEquals($status, $this->cashback->getStatusId());
    }

    /**
     * @When cashback id toggler is triggered
     */
    public function cashbackIdTogglerIsTriggered()
    {
        $this->toggleCashbackStatus->toggle(FALSE);
    }
}
