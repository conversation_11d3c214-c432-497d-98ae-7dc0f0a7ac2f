<?php

namespace Features\Bootstrap\SmokeTests;

use Entities\Order;
use Entities\Payment\Token;
use Entities\Service;
use Framework\FUser;
use Goutte\Client;
use HttpClient\Requests\RequestInterface;
use SmokeTestsModule\Exceptions\FailedResponse;
use SmokeTestsModule\Exceptions\InvalidContext;
use SmokeTestsModule\Exceptions\ParamDoesntExist;
use SmokeTestsModule\Providers\ConfigProvider;
use SmokeTestsModule\Views\SmokeTestUrlView;
use TestModule\Behat\WebContext;
use Entities\Customer;
use Entities\Company;
use TestModule\Helpers\DatabaseHelper;

class SmokeTestsContext extends WebContext
{
    public const ADMIN_MAIN_PAGE = '/admin/en/';

    /**
     * @var Company
     */
    private $company;

    /**
     * @var ConfigProvider
     */
    private $configProvider;

    /**
     * @var string
     */
    private $context;

    /**
     * @var Customer
     */
    private $customer;

    /**
     * @var DatabaseHelper
     */
    private $databaseHelper;

    /**
     * @var int
     */
    private $expectedStatusCode = 0;

    /**
     * @var Order
     */
    private $order;

    /**
     * @var int
     */
    private $responseStatusCode = 0;

    /**
     * @var Service
     */
    private $service;

    /**
     * @var Token
     */
    private $token;

    public function __construct(ConfigProvider $configProvider, DatabaseHelper $databaseHelper)
    {
        $this->configProvider = $configProvider;
        $this->databaseHelper = $databaseHelper;
    }

    /** @BeforeScenario */
    public function beforeScenario(): void
    {
        $this->getSession()->reset();
    }

    /** @AfterScenario */
    public function afterScenario(): void
    {
        switch ($this->context) {
            case SmokeTestUrlView::CONTEXT_ADMIN:
                $this->iLogOutAsAdmin();
                break;
            case SmokeTestUrlView::CONTEXT_CUSTOMER:
                $this->iLogOutAsCustomer();
                break;
            case SmokeTestUrlView::CONTEXT_PUBLIC:
                break;
            default:
                throw new InvalidContext(sprintf('Context %s does not exist!', $this->context));
        }
    }

    /**
     * @Given /^I'm a logged in admin$/
     */
    public function iLogInAsAdmin(): void
    {
        if (!FUser::isAdminSignedIn()) {
            FUser::signIn(1);
        }
        $this->getSession()->visit($this->locatePath(self::ADMIN_MAIN_PAGE));
        $this->waitForPageToLoad();
    }

    /**
     * @Given /^I'm a logged out admin$/
     */
    public function iLogOutAsAdmin(): void
    {
        if (FUser::isAdminSignedIn()) {
            FUser::signOut();
        }
    }

    /**
     * @Given /^I'm a logged in customer$/
     */
    public function iLogInAsCustomer()
    {
        $this->visit($this->locatePath('/login.html'));
        $this->fillField('login_form_email', TEST_EMAIL1);
        $this->fillField('login_form_password', 'n/a');
        $this->pressButton('login_form_submit');
        $this->waitForPageToLoad();
    }

    /**
     * @Given /^I'm a logged out customer$/
     */
    public function iLogOutAsCustomer()
    {
        $this->visit($this->locatePath('/logout/'));
        $this->waitForPageToLoad();
    }

    /**
     * @Given /^a required context: (.*)$/
     */
    public function givenRequiredContext(string $context): void
    {
        $this->context = $context;
        $this->loadEntities();

        switch ($this->context) {
            case SmokeTestUrlView::CONTEXT_ADMIN:
                $this->iLogInAsAdmin();
                break;
            case SmokeTestUrlView::CONTEXT_CUSTOMER:
                $this->iLogInAsCustomer();
                break;
            case SmokeTestUrlView::CONTEXT_PUBLIC:
                break;
            default:
                throw new InvalidContext(sprintf('Context %s does not exist!', $this->context));
        }
    }

    /**
     * @Given /^a expected status code: (.*)$/
     */
    public function givenExpectedStatusCode(string $code): void
    {
        $this->expectedStatusCode = intval($code);
    }

    /**
     * @Given /^a request is made to url: (.*) with method: (.*) and params: (.*)$/
     */
    public function givenRequestIsMadeToUrlWithMethodAndParams(string $url, string $method, string $params): void
    {
        $paramsData = [];
        $params = explode(',', $params);

        foreach ($params as $param) {
            $paramName = str_replace(['{', '}'], ['', ''], $param);

            if (empty($param)) {
                continue;
            } elseif (in_array($paramName, ['customer', 'customerId', 'customer_id'])) {
                $paramsData[$paramName] = $this->customer->getId() ?? 1;
            } elseif (in_array($paramName, ['company', 'companyId', 'company_id'])) {
                $paramsData[$paramName] = $this->company->getId() ?? 1;
            } elseif (empty($this->configProvider->getParam($param))) {
                throw new ParamDoesntExist(sprintf('The param `%s` doesn\'t exist in the params.yml', $param));
            } else {
                $paramsData[$paramName] = $this->configProvider->getParam($param);
            }
        }

        if ($method === RequestInterface::GET) {
            $url = str_replace(array_values($params), array_values($paramsData), $url);
            $paramsData = [];
        }

        /** @var Client $client */
        $client = $this->getSession()->getDriver()->getClient();

        // If the expected status code is a redirection (condition is true) then set max redirects to 1 (true) otherwise set to 0.
        $maxRedirects = (int)($this->expectedStatusCode >= 300 && $this->expectedStatusCode < 400);
        $client->setMaxRedirects($maxRedirects);

        $client->request($method, $this->locatePath($url), $paramsData, [], []);

        $this->responseStatusCode = $client->getInternalResponse()->getStatusCode();
    }

    /**
     * @Then /^the response status code should be equal to the expected status code$/
     */
    public function thenResponseStatusCodeShouldBe(): bool
    {
        if ($this->responseStatusCode !== $this->expectedStatusCode) {
            throw new FailedResponse(sprintf("Wrong response status code. Returned: %d but expected %d", $this->responseStatusCode, $this->expectedStatusCode));
        }
        return true;
    }

    private function loadEntities(): void
    {
        $this->company = $this->databaseHelper->find(Company::class, 1);
        $this->customer = $this->databaseHelper->find(Customer::class, 1);
        $this->order = $this->databaseHelper->find(Order::class, 1);
        $this->service = $this->databaseHelper->find(Service::class, 1);
        $this->token = $this->databaseHelper->find(Token::class, 1);
    }
}