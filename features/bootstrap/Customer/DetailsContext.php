<?php

namespace Features\Bootstrap\Customer;

use Libs\CHFiling\Core\UtilityClass\Address;
use CustomerModule\Entities\BusinessInformation;
use Entities\Company;
use Entities\Customer;
use FormModule\Helpers\Country;
use IdModule\Domain\IdEntity;
use IdModule\Dto\IdConversion;
use IdModule\Verification\IIdValidator;
use Models\Products\RegisterOffice;
use RouterModule\Generators\IUrlGenerator;
use TestModule\Behat\WebContext;
use TestModule\Helpers\DatabaseHelper;
use Utils\Date;

class DetailsContext extends WebContext
{
    /**
     * @var Customer
     */
    private $customer;

    /**
     * @var Company
     */
    private $company;

    /**
     * @var DatabaseHelper
     */
    private $databaseHelper;

    /**
     * @var IIdValidator
     */
    private $idValidator;

    /**
     * @var IUrlGenerator
     */
    private $generator;

    public function __construct(DatabaseHelper $databaseHelper, IIdValidator $idValidator)
    {
        $this->databaseHelper = $databaseHelper;
        $this->idValidator = $idValidator;
    }

    /**
     * @Given /^I am (an existing|a new) customer with id (required|not required|valid|invalid)$/
     */
    public function createCustomer(string $customerType, string $idType)
    {
        $this->databaseHelper->emptyTables([
            TBL_CUSTOMERS, TBL_COMPANIES, TBL_ID_VALIDATIONS,
            TBL_ID_EVENTS, TBL_ID_VALIDATION_EVENTS, TBL_CUSTOMER_BUSINESS_INFO, TBL_ID_VERIFICATIONS,
            TBL_CUSTOMER_ADDRESS
        ]);
        $entities[] = $this->customer = new Customer('<EMAIL>', 'password');
        // assumption customer is from UK
        $this->customer->setCountryIso(Country::UNITED_KINGDOM);

        // assumption company is incorporated
        $entities[] = $this->company = Company::incorporated($this->customer, 'test', '1', new Date('-1 year'));
        if ($idType !== 'not required') {
            $this->company->setCompanyStatus(Company::COMPANY_STATUS_ACTIVE);
            $this->company->setPostcode(RegisterOffice::POSTCODE);
            $this->company->setCountry('GBR');
        }
        $this->customer->setStatusId(Customer::STATUS_VALIDATED);
        if ($customerType === 'an existing') {
            $this->customer->setFirstName('Ted');
            $this->customer->setLastName('Reed');
            $this->customer->setDateOfBirth(new Date('1990-01-01'));
            $this->customer->setAddress1('test 1');
            $this->customer->setAddress2('test');
            $this->customer->setCity('London');
            $this->customer->setCountryIso('GB');
            $this->customer->setPostcode('ec23 8db');
            $this->customer->setPhone('834234242342');
            $entities[] = $businessInformation = new BusinessInformation($this->customer, BusinessInformation::NOT_PROFESSIONAL_COMPANY_FORMATION);
            $this->customer->setBusinessInformation($businessInformation);
            $this->customer->setRoleId(Customer::ROLE_NORMAL);
        }

        $this->databaseHelper->saveEntities($entities);

        $entity = IdEntity::fromCustomer($this->company, $this->customer);
        if ($idType === 'valid') {
            $this->idValidator->validateManually($entity, new IdConversion(true, ['PERSON_NAME_ADDRESS', 'PHOTO_ID', 'SANCTIONS', 'PEP', 'MORTALITY']), 'phpunit');
        } elseif ($idType === 'invalid') {
            $this->idValidator->validateManually($entity, new IdConversion(false, ['PHOTO_ID']), 'phpunit');
        }
        $this->iLogInAs($this->customer->getEmail());
    }
}