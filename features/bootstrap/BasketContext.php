<?php

namespace Features\Bootstrap;

use RouterModule\Generators\IUrlGenerator;

class BasketContext extends \TestModule\Behat\WebContext
{
    /**
     * @var IUrlGenerator
     */
    private $generator;

    public function __construct(IUrlGenerator $generator)
    {
        $this->generator = $generator;
    }

    /**
     * @Given :productName package is added to the basket
     * @Given :productName package is in the basket
     */
    public function addProduct(string $productName)
    {
        if ($this->getSession()->getCurrentUrl() !== $this->generator->url('package_module.matrix')) {
            $this->formationPage();
        }
        $this->pressButton(sprintf('Add %s', $productName));
    }

    /**
     * @When :productName package is removed
     */
    public function removeProduct(string $productName)
    {
        if ($this->getSession()->getCurrentUrl() !== $this->generator->url('basket_module_package_basket_upgrade')) {
            $this->basketPage();
        }
        $this->pressButton(sprintf('Remove %s - Ltd Company Formation Package', $productName));
    }

    /**
     * @Given Formation page is loaded
     */
    public function formationPage()
    {
        $this->visitAndWait($this->generator->url('package_module.matrix'), 'Select the perfect package for you');
    }

    /**
     * @Given Basket page is loaded
     */
    public function basketPage()
    {
        $this->visitAndWait($this->generator->url('basket_module_package_basket_upgrade'), 'My Basket');
    }
}