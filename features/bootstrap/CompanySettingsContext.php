<?php

namespace Features\Bootstrap;

use Behat\Behat\Context\Context;
use Behat\Behat\Context\SnippetAcceptingContext;
use Entities\Company;
use Entities\Customer;
use Entities\Payment\Token;
use InvalidArgumentException;
use PHPUnit\Framework\Assert as test;
use Services\CompanyService;
use TestModule\Annotations\Inject;
use TestModule\Behat\ObjectInjectorContext;
use TestModule\Helpers\DatabaseHelper;
use tests\helpers\ObjectHelper;
use Utils\Date;

class CompanySettingsContext extends ObjectInjectorContext implements Context, SnippetAcceptingContext
{
    /**
     * @var Customer
     */
    private $customer;

    /**
     * @var CompanyService
     */
    private $companyService;

    /**
     * @var DatabaseHelper
     */
    private $databaseHelper;

    /**
     * @var Company
     */
    private $company;

    /**
     * @var Token
     */
    private $token;

    /**
     * @Transform :bool
     */
    public function transformBool($string)
    {
        switch ($string) {
            case 'enabled':
            case 'enable':
                return TRUE;
            case 'disabled':
            case 'disable':
                return FALSE;
        }
        throw new InvalidArgumentException(sprintf('Can not convert to bool %s', $string));
    }

    /**
     * @Inject({"services.company_service", "test_module.database_helper"})
     * @param CompanyService $companyService
     * @param DatabaseHelper $databaseHelper
     */
    public function setupWithContainer(CompanyService $companyService, DatabaseHelper $databaseHelper)
    {
        $this->databaseHelper = $databaseHelper;
        $this->companyService = $companyService;
        $this->databaseHelper->emptyTables([TBL_CUSTOMERS, TBL_COMPANIES, TBL_COMPANY_SETTINGS]);
    }

    /**
     * @Given I am customer :id
     */
    public function iAmCustomer($id)
    {
        $this->customer = new Customer($id, 'test');
        $this->databaseHelper->saveEntity($this->customer);
    }

    /**
     * @Given I have company :id
     */
    public function iHaveCompany($id)
    {
        $this->company = new Company($this->customer, $id);
        $this->databaseHelper->saveEntity($this->company);
    }

    /**
     * @Given I have token
     */
    public function iHaveToken()
    {
        $this->token = ObjectHelper::createToken($this->customer, TRUE, new Date('+1 year'));
        $this->databaseHelper->saveEntity($this->token);
    }

    /**
     * @Then Company dismissed bank offer setting should be :bool
     */
    public function myCompanyReportTrackingSettingShouldBe($bool)
    {
        test::assertEquals($bool, $this->company->getSettings()->getBankingOfferDismissed()->isDismissed());
    }

    /**
     * @When I dismiss bank offer for company
     */
    public function iDismissBankOffer()
    {
        $this->companyService->dismissBankOffer($this->company);
    }

    /**
     * @When I set Registered Office Upsell type to :productId
     */
    public function iSetRegisteredOfficeTypeTo($productId)
    {
        $this->companyService->setRegisteredOfficeUpsellType($this->company, $productId, $this->token);
    }

    /**
     * @Then Company Registered Office Upsell type should be :productId
     */
    public function companyRegisteredOfficeUpsellTypeShouldBe($productId)
    {
        test::assertEquals($productId, $this->company->getSettings()->getRegisteredOfficeUpsell()->getType());
        test::assertEquals($this->token->getId(), $this->company->getSettings()->getRegisteredOfficeUpsell()->getTokenId());
    }

    /**
     * @Then Company Registered Office Upsell type should not be set
     */
    public function companyRegisteredOfficeUpsellTypeShouldNotBeSet()
    {
        test::assertNull($this->company->getSettings()->getRegisteredOfficeUpsell()->getType());
    }
}
