<?php

namespace Features\Bootstrap\BusinessData;

use BusinessDataModule\Entities\FollowUp;
use BusinessDataModule\Entities\Lead as BdgLead;
use BusinessServicesModule\Entities\Lead;
use BusinessServicesModule\Entities\Offer;
use DataGeneration\CompanyGenerator;
use DateTime;
use Entities\Company;
use Entities\CompanyHouse\FormSubmission\CompanyIncorporation;
use Entities\CompanyHouse\Helper\Address;
use Entities\Customer;
use Models\Products\Package;
use RouterModule\Helpers\ControllerHelper;
use Services\SubmissionService;
use TestModule\Behat\WebContext;
use TestModule\Helpers\DatabaseHelper;
use tests\helpers\EntityHelper;
use tests\helpers\ObjectHelper;
use PHPUnit\Framework\Assert as test;

class BdgContext extends WebContext
{
    const CUSTOMER_PASSWORD = 'password';
    const REFERRAL = 'referral_user';

    /**
     * @var DatabaseHelper
     */
    protected $databaseHelper;

    /**
     * @var CompanyGenerator
     */
    private $companyGenerator;

    /**
     * @var ControllerHelper
     */
    private $controllerHelper;

    /**
     * @var Customer
     */
    private $customer;

    /**
     * @var Company
     */
    private $company;

    /**
     * @var SubmissionService
     */
    private $submissionService;

    public function __construct(
        DatabaseHelper $databaseHelper,
        CompanyGenerator $companyGenerator,
        ControllerHelper $controllerHelper,
        SubmissionService $submissionService
    ) {
        $this->databaseHelper = $databaseHelper;
        $this->companyGenerator = $companyGenerator;
        $this->controllerHelper = $controllerHelper;
        $this->submissionService = $submissionService;
    }

    /**
     * @Transform /^(eligible|not eligible)$/
     */
    public function transformEligibleToBool(string $value)
    {
        return $value === 'eligible';
    }

    /**
     * @Transform /^(incorporated|not incorporated)$/
     */
    public function transformIncorporatedToBool(string $value)
    {
        return $value === 'incorporated';
    }

    /** @BeforeScenario */
    public function before($event)
    {
        $this->databaseHelper->emptyTables(EntityHelper::$tables);
    }

    /**
     * @Given I am :customerEmail
     */
    public function iAm($customerEmail)
    {
        $this->customer = ObjectHelper::createCustomer($customerEmail, self::CUSTOMER_PASSWORD);
        $this->customer->setStatusId(Customer::STATUS_VALIDATED);
        $this->databaseHelper->saveEntity($this->customer);
    }

    /**
     * @Given I have an incomplete company
     */
    public function setupIncompleteCompany()
    {
        $this->company = ObjectHelper::createCompany($this->customer);
        $this->company->setProductId(Package::PACKAGE_BASIC);
        $formSubmission = new CompanyIncorporation($this->company);
        $formSubmission->setRegisteredOfficeAddress(new Address('1', 'Num', 'a', 'E99 123', 'GB-ENG'));
        $formSubmission->setType(Company::COMPANY_CATEGORY_BYSHR);
        $formSubmission->setCountryOfIncorporation('EW');
        $formSubmission->setSicCodes(['01110']);
        $formSubmission->setArticles('BYSHRMODEL');
        $this->databaseHelper->saveEntities([$this->company, $formSubmission]);
    }

    /**
     * DummyApiGateway returns no adverts for company name "NO BANK ADVERTS COMPANY LTD"
     *
     * @Given /^I have BDG (eligible|not eligible) (incorporated|not incorporated) company "(.*)"$/
     */
    public function setupReadyToBeIncorporatedCompany(bool $eligible, bool $incorporated, string $companyName)
    {
        $productId = $eligible ? Package::PACKAGE_BASIC : Package::PACKAGE_PRIVACY;
        $result = $this->companyGenerator->companyReadyToBeIncorporated(
            $this->customer,
            'BYSHR',
            $productId,
            $companyName
        );
        $this->company = $result->getEntityByKey('company');

        if ($incorporated) {
            $this->submissionService->sendIncorporation($this->company);
        }

        $this->company->setDtc(new DateTime('01-01-2020'));
        $this->databaseHelper->saveEntity($this->company);
    }

    /**
     * @Given /^Company is a BDG lead$/
     */
    public function setBdgLead()
    {
        $bsLead = new Lead($this->company, Offer::BDG_OFFER_ID, true);
        $bdgLead = new BdgLead(
            $bsLead,
            'advert',
            'advertiser',
            new FollowUp('type', 'value'),
            false,
            false
        );

        $this->databaseHelper->saveEntities([$bsLead, $bdgLead]);
    }

    /**
     * @Given I log in
     */
    public function iLogIn()
    {
        $this->visit('/login.html');
        $this->fillField('login_form_email', $this->customer->getEmail());
        $this->fillField('login_form_password', self::CUSTOMER_PASSWORD);
        $this->pressButton('login_form_submit');
        $this->waitForPageToLoad();
    }

    /**
     * @Given I should be on the route :route
     */
    public function route(string $route)
    {
        $this->assertPageAddress(
            $this->controllerHelper->getUrl($route, ['company' => $this->company->getId()])
        );
    }

    /**
     * @Given I should be on the generated link :nodeId
     */
    public function assertGeneratedLink(int $nodeId)
    {
        $this->assertPageAddress(
            $this->controllerHelper->getLink($nodeId, ['company_id' => $this->company->getId()])
        );
    }

    /**
     * @Given I am on the generated link :nodeId
     */
    public function visitGeneratedLink(int $nodeId)
    {
        $this->visit(
            $this->controllerHelper->getLink($nodeId, ['company_id' => $this->company->getId()])
        );
    }

    /**
     * @When I am on formation banking page
     */
    public function iAmOnFormationBankingPage()
    {
        $this->visit(
            $this->controllerHelper->getUrl('business_data_select_banks', ['company' => $this->company->getId()])
        );
    }

    /**
     * @When /^I am on company banking page\s?(with referral)?$/
     */
    public function iAmOnCompanyBankingPage()
    {
        $this->visit(
            $this->controllerHelper->getUrl(
                'business_data_select_banks_post_incorporation',
                ['company' => $this->company->getId(), 'r' => self::REFERRAL]
            )
        );
    }

    /**
     * @Given Referral should be stored
     */
    public function referralShouldBeStored()
    {
        /** @var BdgLead $bdgLead */
        $bdgLead = $this->databaseHelper->findOneBy(BdgLead::class, ['lead' => 1]);
        test::assertEquals($bdgLead->getReferral(), self::REFERRAL);
    }
}
