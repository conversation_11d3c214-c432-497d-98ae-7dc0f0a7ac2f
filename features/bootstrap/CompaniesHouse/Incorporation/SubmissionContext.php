<?php

namespace Features\Bootstrap\CompaniesHouse\Incorporation;

use DataGeneration\CompanyGenerator;
use DateTime;
use Entities\Company;
use Entities\CompanyHouse\FormSubmission;
use Entities\Customer;
use Features\Bootstrap\WebContext;
use FormModule\Helpers\Country;
use PHPUnit\Framework\Assert as test;
use TestModule\Helpers\DatabaseHelper;
use tests\helpers\EntityHelper;

class SubmissionContext extends WebContext
{
    /**
     * @var DatabaseHelper
     */
    protected $databaseHelper;

    /**
     * @var CompanyGenerator
     */
    private $companyGenerator;

    public function __construct(DatabaseHelper $databaseHelper, CompanyGenerator $companyGenerator)
    {
        $this->databaseHelper = $databaseHelper;
        $this->companyGenerator = $companyGenerator;
    }

    /**
     * @Given Company :category with incorporation data
     */
    public function setUpByShares(string $category)
    {
        $this->databaseHelper->emptyTables(array_merge(EntityHelper::$tables, [TBL_CUSTOMER_BUSINESS_INFO]));
        $this->customer = $customer = new Customer('<EMAIL>', 'password');
        $customer->setStatusId(Customer::STATUS_VALIDATED);
        $customer->setFirstName('Testing');
        $customer->setPostcode('1L 2XL');
        $customer->setPhone('+**************');
        $customer->setDtc(new DateTime('2020-01-05'));

        $result = $this->companyGenerator->companyReadyToBeIncorporated($customer, $category);
        $this->company = $result->getEntityByKey('company');

        $this->databaseHelper->clearEntities();
    }

    /**
     * @Given I am on incorporation page with query :query
     */
    public function goToIncorporationPage(string $query = NULL)
    {
        $this->iLogIn();
        $this->visit('/formation/' . $this->company->getId() . '/summary/' . ($query ? '?' . $query : ''));
    }

    /**
     * @Given /^Company (is|is not) incorporated$/
     */
    public function checkIncorporation(bool $incorporated)
    {
        $company = $this->databaseHelper->find(Company::class, $this->company->getId());
        test::assertEquals($incorporated, $company->isIncorporated());
    }

    /**
     * @Given /^Company (is|is not) synced$/
     */
    public function checkCompanySynced(bool $synced)
    {
        /** @var Company $company */
        $company = $this->databaseHelper->find(Company::class, $this->company->getId());
        test::assertEquals($synced, (bool)$company->getAcceptedDate());
        test::assertEquals($company->getIncorporationFormSubmission()->getRegisteredOfficeAddress()->getPostcode(), $company->getRegisteredOffice()->getPostcode());
        test::assertNotEmpty($company->getIncorporationFormSubmission()->getRegisteredOfficeAddress()->getCountry());
        test::assertEquals($company->getIncorporationFormSubmission()->getRegisteredOfficeAddress()->getCountry(), $company->getRegisteredOffice()->getCountry());
    }

    /**
     * @Given Incorporation is :response
     */
    public function checkIncorporationStatus(string $response)
    {
        $formSubmission = $this->databaseHelper->findOneBy(FormSubmission::class, ['company' => $this->company->getId()]);
        test::assertEquals($response, $formSubmission->getResponse());
    }
}