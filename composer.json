{"repositories": [{"type": "git", "url": "**************:madesimplegroup/msg-framework.git"}, {"type": "git", "url": "**************:madesimplegroup/msg-features.git"}, {"type": "git", "url": "**************:madesimplegroup/msg-docker.git"}, {"type": "git", "url": "**************:madesimplegroup/msg-emails.git"}, {"type": "git", "url": "**************:madesimplegroup/knapsack.git"}, {"type": "git", "url": "**************:madesimplegroup/nette-reflection.git"}, {"type": "package", "package": {"name": "kdyby/doctrine", "version": "dev-dev", "type": "git", "source": {"url": "**************:madesimplegroup/kdyby-doctrine.git", "type": "git", "reference": "dev"}}}], "require": {"php": "~8.2.0", "ext-apcu": "*", "ext-bcmath": "*", "ext-ctype": "*", "ext-curl": "*", "ext-date": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-filter": "*", "ext-ftp": "*", "ext-gd": "*", "ext-gettext": "*", "ext-hash": "*", "ext-http": "*", "ext-iconv": "*", "ext-intl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-memcache": "*", "ext-mysqli": "*", "ext-mysqlnd": "*", "ext-openssl": "*", "ext-pcre": "*", "ext-PDO": "*", "ext-pdo_dblib": "*", "ext-pdo_mysql": "*", "ext-posix": "*", "ext-readline": "*", "ext-redis": "*", "ext-Reflection": "*", "ext-session": "*", "ext-SimpleXML": "*", "ext-soap": "*", "ext-sockets": "*", "ext-SPL": "*", "ext-sqlite3": "*", "ext-tokenizer": "*", "ext-xml": "*", "ext-zip": "*", "ext-zlib": "*", "ext-igbinary": "*", "kdyby/doctrine": "dev-dev", "made_simple/msg-framework": "dev-cms", "made_simple/msg-features": "~0.0.0", "made_simple/msg-emails": "dev-php-upgrade-v2", "ccampbell/chromephp": "~4.1.0", "knplabs/gaufrette": "~0.1.0", "phpoffice/phpspreadsheet": "~2.1.0", "iio/libmergepdf": "~4.0.0", "firebase/php-jwt": "~5.2.0", "google/cloud-core": "~1.49.0", "google/cloud-bigquery": "~1.23.4", "http-interop/http-factory-guzzle": "~1.2.0", "symfony/framework-bundle": "~6.4.0", "symfony/http-kernel": "~6.4.0", "symfony/http-foundation": "~6.4.0", "symfony/process": "~6.4.0", "symfony/console": "~6.4.0", "symfony/event-dispatcher": "~6.4.0", "symfony/dependency-injection": "~6.4.0", "symfony/filesystem": "~6.4.0", "symfony/config": "~6.4.0", "symfony/yaml": "~6.4.0", "symfony/form": "~6.4.0", "symfony/templating": "~6.4.0", "symfony/doctrine-bridge": "~6.4.0", "symfony/twig-bridge": "~7.0.7", "symfony/options-resolver": "~6.4.0", "symfony/intl": "~6.4.0", "symfony/property-access": "~6.4.0", "symfony/security-core": "~6.4.0", "symfony/security-csrf": "~6.4.0", "symfony/stopwatch": "~6.4.0", "symfony/class-loader": "~3.4.0", "symfony/css-selector": "~6.4.0", "symfony/dom-crawler": "~6.4.0", "symfony/browser-kit": "~6.4.0", "symfony/finder": "~6.4.0", "symfony/routing": "~6.4.0", "symfony/translation": "~6.4.0", "symfony/validator": "~6.4.0", "symfony/expression-language": "~6.4.0", "symfony/proxy-manager-bridge": "~6.4.0", "symfony/serializer": "~6.4.0", "symfony/http-client": "~6.4.0", "twig/twig": "~3.6.0", "dibi/dibi": "~5.0.0", "tracy/tracy": "~2.9.5", "nette/utils": "^3.2", "nette/http": "~3.1.2", "nette/reflection": "dev-php-upgrade", "leafo/lessphp": "~0.5.0", "leafo/scssphp": "~0.6.5", "doctrine/cache": "~1.12.0", "doctrine/persistence": "~3.1.0", "doctrine/common": "~3.3.0", "doctrine/annotations": "~1.14.0", "doctrine/orm": "~2.15.0", "gedmo/doctrine-extensions": "^3.6", "beberlei/doctrineextensions": "~1.0.0", "league/commonmark": "~2.1.2", "guzzlehttp/guzzle": "~7.5.0", "fakerphp/faker": "^1.10.0", "nelmio/alice": "~3.10.0", "monolog/monolog": "~1.17.0", "isometriks/spam-bundle": "^1.1.0", "excelwebzone/recaptcha-bundle": "~1.5.0", "defuse/php-encryption": "~2.0.0", "garoevans/php-enum": "~1.2.0", "latte/latte": "~2.11.0", "natxet/cssmin": "~3.0.0", "robmorgan/phinx": "~0.12.0", "mailgun/mailgun-php": "~3.3.0", "nyholm/psr7": "~1.8.0", "google/cloud-storage": "~1.30.0", "google/apiclient": "~2.11.0", "google/cloud-datastore": "~1.13.4", "google/cloud-logging": "~1.24.10", "psr/log": "~1.1.0", "league/iso3166": "~4.0.0", "mustache/mustache": "~2.12.0", "jms/serializer": "~3.19.0", "ocramius/proxy-manager": "~2.1.0", "ramsey/uuid": "~3.9.2", "ramsey/uuid-doctrine": "~1.6.0", "widmogrod/php-functional": "^6.0", "dusank/knapsack": "~11.0", "picqer/php-barcode-generator": "~2.4.0", "egulias/email-validator": "~3.2.0", "sentry/sentry": "~4.9.0", "guzzlehttp/promises": "^1.5", "smarty/smarty": "^5.1", "machy8/webloader": "^2.0", "vwo/vwo-php-sdk": "1.66.0", "symfony/lock": "^7.1", "google/cloud-pubsub": "^1.44"}, "require-dev": {"made_simple/msg_docker": "~7.3.3", "phpunit/phpunit": "~9.5.0", "phpspec/phpspec": "~7.5.0", "behat/behat": "~3.9.0", "behat/mink": "~1.10.0", "fabpot/goutte": "~4.0.0", "behat/mink-goutte-driver": "~2.0.0", "behat/mink-selenium2-driver": "1.5.0", "friends-of-behat/mink-extension": "~2.7.0", "mikey179/vfsstream": "^2.0.x-dev", "squizlabs/php_codesniffer": "~3.6.0", "slevomat/coding-standard": "~7.0.0", "phpspec/prophecy-phpunit": "*", "behat/mink-browserkit-driver": "^2.1", "friendsofphp/php-cs-fixer": "^3.64", "phpstan/phpstan": "^2.0"}, "autoload": {"psr-4": {"AdminModule\\Controlers\\": "project/AdminModule/controlers/", "AdminModule\\Forms\\ServiceAddress\\": "project/AdminModule/forms/ServiceAddress/", "Config\\Constants\\": "project/config/constants/", "Console\\Commands\\Cashbacks\\": "project/libs/Commands/Nikolai/Cashbacks/", "Console\\Commands\\OrderItems\\": "project/libs/Commands/Nikolai/OrderItems/", "Console\\Commands\\Submissions\\": "project/libs/Commands/Nikolai/Submissions/", "Console\\Commands\\VoService\\": "project/libs/Commands/Nikolai/VoService/", "Console\\Commands\\Services\\": "project/libs/Commands/Nikolai/Services/", "Dispatcher\\Events\\": "project/Services/Dispatcher/Events/", "Dispatcher\\Listeners\\": "project/Services/Dispatcher/Listeners/", "Entities\\": "project/models/Entities/", "EmailModule\\": "project/EmailModule/", "Exceptions\\": "project/libs/Exceptions/", "Factories\\Factories\\": "project/factories/Factories/", "Factories\\": "project/factories/", "Features\\Bootstrap\\": "features/bootstrap/", "Framework\\": "libs/Framework/", "FrontModule\\Controlers\\": "project/FrontModule/controlers/", "FrontModule\\Forms\\": "project/FrontModule/forms/", "Kdyby\\Doctrine\\": "vendor/kdyby/doctrine/src/Kdyby/Doctrine/", "Kdyby\\Persistence\\": "vendor/kdyby/doctrine/src/Kdyby/Persistence/", "Legacy\\": "libs/3rdParty/", "Libs\\": "project/libs/", "Libs\\CHFiling\\Core\\": "project/libs/CHFiling/core/", "Libs\\CHFiling\\Core\\Banking\\": "project/libs/CHFiling/core/banking/", "Libs\\CHFiling\\Core\\Request\\": "project/libs/CHFiling/core/request/", "Libs\\CHFiling\\Core\\Request\\Document\\": "project/libs/CHFiling/core/request/document/", "Libs\\CHFiling\\Core\\Request\\Form\\": "project/libs/CHFiling/core/request/form/", "Libs\\CHFiling\\Core\\Request\\Form\\AnnualReturn\\": "project/libs/CHFiling/core/request/form/annualReturn/", "Libs\\CHFiling\\Core\\Request\\Form\\Incorporation\\": "project/libs/CHFiling/core/request/form/incorporation/", "Libs\\CHFiling\\Core\\UtilityClass\\": "project/libs/CHFiling/core/utilityClass/", "Models\\Basket\\": "project/models/Basket/", "Models\\Company\\Service\\": "project/models/Company/Service/", "Models\\Export\\Order\\": "project/models/Export/Order/", "Models\\Products\\": "project/models/Products/", "Models\\Repositories\\": "project/models/Repositories/", "Models\\Order\\": "project/models/Order/", "Models\\OldModels\\": "project/models/OldModels/", "Models\\ValueObject\\": "project/models/ValueObject/", "Models\\View\\": "project/models/View/", "Models\\": "project/models/", "PeopleWithSignificantControl\\": "project/PeopleWithSignificantControlModule/", "Repositories\\": "project/models/Repositories/", "Services\\Emailers\\": "project/Services/Emailers/", "tests\\helpers\\": "tests/helpers/", "tests\\project\\": "tests/project/", "": ["project/"]}, "files": ["project/Services/Email/EmailerFactory.php", "project/Services/Registry.php", "project/models/OldModels/IIdentifier.php", "project/models/OldModels/Customer.php", "project/models/Entities/Customer.php", "project/bootstrap/Ext/ChFilingExt.php", "project/CompanyModule/Services/Transfer/functions.php", "project/CompaniesHouseModule/Factories/MemberFactory/functions.php", "project/CompaniesHouseModule/Helpers/Api/functions.php", "project/config/constants/DiLocator.php", "libs/3rdParty/Logger/ILogger.php", "libs/3rdParty/Nette/Web/Uri.php", "tests/helpers/ServiceSettingsHelpers.php"]}, "autoload-dev": {"psr-4": {"tests\\": "tests/", "TestModule\\": "vendor/made_simple/msg-framework/test_module/src/", "IdModule\\": ["vendor/made_simple/msg-framework/id_module/behat/", "vendor/made_simple/msg-framework/id_module/tests/"], "SagePay\\": ["vendor/made_simple/msg-framework/sagepaytoken/tests"]}}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "php-http/discovery": true}, "process-timeout": 1800}}