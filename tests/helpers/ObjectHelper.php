<?php

namespace tests\helpers;

use AdminModule\Controlers\EmailAdminControler;
use AdminModule\Controlers\PageAdminControler;
use CompaniesHouseModule\Entities\Address;
use CompanyModule\Domain\Company\CompanyName;
use Config\Constants\DiLocator;
use Entities\Cashback;
use Entities\Company;
use Entities\CompanyDummy;
use Entities\CompanyHouse\FormSubmission;
use Entities\Customer;
use Entities\CustomerLog;
use Entities\Order;
use Entities\OrderItem;
use Entities\Payment\Token;
use Entities\Service;
use Exceptions\Technical\NodeException;
use Faker\Factory as FakerFactory;
use Framework\FEmail;
use Framework\FUser;
use Framework\FUserRole;
use IdModule\Domain\Check;
use IdModule\Domain\DiligenceLevel;
use IdModule\Domain\IdEntity;
use IdModule\Entities\IdValidation;
use IdModule\Views\IdCompanyInfo;
use IdModule\Views\IdCustomerInfo;
use IdModule\Views\IdEntityInfo;
use MailScanModule\Entities\PostItem;
use Models\OldModels\CustomerActionLogger;
use Models\Products\Package;
use Models\Products\Product;
use Nette\Utils\Strings;
use SagePayToken\Tests\Token\PaymentHelper;
use SagePayToken\Token\Request\Details;
use SagePayToken\Token\Request\Registration;
use ServiceRemindersModule\Entities\ReminderService;
use Services\NodeService;
use Services\Registry;
use Utils\Date;
use VoServiceModule\Entities\VoServiceQueue;

class ObjectHelper
{
    /**
     * @var int
     */
    private static $serviceId = 1;

    /**
     * @param mixed  $entity
     * @param string $field
     *
     * @return int|string
     */
    public static function increment($entity, $field)
    {
        $property = new \ReflectionProperty($entity, $field);
        $property->setAccessible(true);
        $value = $field === 'formSubmissionId' ? mb_str_pad(self::$serviceId++, 6, '0', STR_PAD_LEFT) : self::$serviceId++;
        $property->setValue($entity, $value);

        return $value;
    }

    /**
     * @param Company    $company
     * @param Date       $dtStart
     * @param Date       $dtExpires
     * @param bool       $product
     * @param string     $typeId
     * @param mixed|null $order
     * @param mixed|null $productId
     *
     * @return Service
     */
    public static function createService(
        ?Company $company = null,
        ?Date $dtStart = null,
        ?Date $dtExpires = null,
        $product = true,
        $typeId = Service::TYPE_REGISTERED_OFFICE,
        $order = null,
        $productId = null,
    ) {
        $customer = new Customer('test', 'test');

        if (!$order) {
            $order = new Order($customer);
        }

        if (!$company) {
            $company = new Company($customer, 'test');
        }
        if ($productId) {
            $product = new Product($productId);
        } else {
            $product = $product ? new Product() : new Package();
            $product->setCompanyId(1);
            $product->serviceTypeId = $typeId;
            $product->renewalProductId = 165;
            $product->setDuration('+12 months');
        }
        $orderItem = new OrderItem($order);

        $service = new Service($typeId, $product, $company, $orderItem);
        $serviceIdProperty = new \ReflectionProperty($service, 'serviceId');
        $serviceIdProperty->setAccessible(true);
        $serviceIdProperty->setValue($service, self::$serviceId++);

        $company->addService($service);
        if ($dtStart) {
            $service->setDtStart($dtStart);
        }
        if ($dtExpires) {
            $service->setDtExpires($dtExpires);
        }
        $service->setRenewalProduct(new Product());

        return $service;
    }

    /**
     * @return Details
     */
    public static function createDetails()
    {
        return PaymentHelper::createSageDetails();
    }

    /**
     * @return Registration
     */
    public static function createRegistration()
    {
        return PaymentHelper::createSageRegistration();
    }

    /**
     * @param Customer  $customer
     * @param bool      $isActive
     * @param Date|null $cardExpiryDate
     * @param bool      $produceId
     *
     * @return Token
     */
    public static function createToken(
        Customer $customer,
        $isActive = true,
        ?Date $cardExpiryDate = null,
        $produceId = false,
    ) {
        $cardExpiryDate = $cardExpiryDate ? $cardExpiryDate : new Date();
        $tokenEntity = new Token(
            $customer,
            '{E76C0C13-79F7-E3F1-99B4-02FA7F779B7D}',
            'VISA',
            'lucky luke',
            '0001',
            $cardExpiryDate,
            '123456',
            '123456',
            '123456'
        );
        $tokenEntity->setIsCurrent($isActive);
        $details = self::createDetails();
        $tokenService = EntityHelper::getService(DiLocator::SERVICE_TOKEN);
        $tokenService->mapRequestToToken($details, $tokenEntity);
        if ($produceId) {
            static $tokenId = 1;
            $tokenIdReflection = new \ReflectionProperty($tokenEntity, 'tokenId');
            $tokenIdReflection->setAccessible(true);
            $tokenIdReflection->setValue($tokenEntity, $tokenId++);
        }

        return $tokenEntity;
    }

    /**
     * @param string|null         $email
     * @param string              $password
     * @param \DateTime|bool|null $id
     *
     * @throws \Exception
     *
     * @return Customer
     */
    public static function createCustomer($email = null, $password = 'n/a')
    {
        $customer = new Customer($email ? $email : TEST_EMAIL1, $password);
        $customer->setFirstName('Johny');
        $customer->setLastName('Tester');
        $customer->setAddress1('145 Road');
        $customer->setAddress2('Faringdon');
        $customer->setPostcode('EC1V 4PQ');
        $customer->setCity('London');
        $customer->setCountryId(1);
        $customer->setPhone('12345678');
        $customer->setDateOfBirth(new Date('2000-01-01'));

        return $customer;
    }

    public static function createIdCustomerInfo()
    {
        $customer = self::createCustomer();
        $idCompanyInfo = [
            self::createIdCompanyInfo('made simple'),
            self::createIdCompanyInfo('Linus Media Group'),
        ];

        return new IdCustomerInfo($customer, $idCompanyInfo);
    }

    /**
     * @param Customer  $customer
     * @param string    $companyName
     * @param null      $companyNumber
     * @param Date|null $incorporationDate
     *
     * @return Company
     */
    public static function createCompany(
        Customer $customer,
        $companyName = 'TEST COMPANY NAME',
        $companyNumber = null,
        ?Date $incorporationDate = null,
        ?string $status = null,
        ?string $postcode = null,
    ) {
        $company = new Company($customer, $companyName);

        if (!empty($companyNumber)) {
            $company->setCompanyNumber($companyNumber);
            $company->setIncorporationDate($incorporationDate ?? new Date('-10 days'));
            $company->setCompanyCategory(Company::COMPANY_CATEGORY_BYSHR);
        }

        if ($status) {
            $company->setCompanyStatus($status);
        }

        if ($postcode) {
            $company->setPostcode($postcode);
        }

        $company->setSicCode1(86101);

        return $company;
    }

    public static function createCompanyDummy(
        Customer $customer,
        $companyName = 'TEST COMPANY NAME',
        $companyNumber = null,
        ?Date $incorporationDate = null,
        ?string $status = null,
        ?string $postcode = null,
    ) {
        $company = new CompanyDummy($customer, $companyName);

        if ($companyNumber !== null) {
            $company->setId(rand(1, 10));
            $company->setCompanyNumber($companyNumber);
            $company->setIncorporationDate($incorporationDate ?? new Date('-10 days'));
            $company->setCompanyCategory(Company::COMPANY_CATEGORY_BYSHR);
        }

        if ($status) {
            $company->setCompanyStatus($status);
        }
        if ($postcode) {
            $company->setPostcode($postcode);
        }

        return $company;
    }

    /**
     * @param string $status
     *
     * @return Cashback
     */
    public static function createRetailCashback($status, ?Customer $customer = null)
    {
        $faker = FakerFactory::create();
        $customer = $customer ?? self::createCustomer($faker->companyEmail);

        return self::createRetailCashbackForCustomer($customer, $status);
    }

    /**
     * @param Customer $customer
     * @param string   $status
     *
     * @return Cashback
     */
    public static function createRetailCashbackForCustomer(Customer $customer, $status, ?Date $dateAccountOpened = null)
    {
        $faker = FakerFactory::create();

        $cashback = new Cashback(
            self::createCompany(
                $customer,
                $faker->company
            ),
            Cashback::BANK_TYPE_BARCLAYS,
            50,
            new Date($faker->date()),
            $dateAccountOpened ?? new Date($faker->date())
        );
        $cashback->setPackageTypeId(Cashback::PACKAGE_TYPE_RETAIL);
        $cashback->setStatusId($status);

        return $cashback;
    }

    public static function createCashback(
        Company $company,
        string $status,
        string $packageType = Cashback::PACKAGE_TYPE_RETAIL,
        ?Date $creationDate = null,
    ) {
        $faker = FakerFactory::create();

        $cashback = new Cashback(
            $company,
            Cashback::BANK_TYPE_BARCLAYS,
            50,
            new Date($faker->date()),
            $creationDate ?: new Date($faker->date())
        );
        $cashback->setPackageTypeId($packageType);
        $cashback->setStatusId($status);

        return $cashback;
    }

    /**
     * @param FUserRole $role
     * @param string    $email
     * @param string    $login
     *
     * @return FUser
     */
    public static function createUser(FUserRole $role, $email = null, $login = null)
    {
        $faker = FakerFactory::create();

        $user = FUser::create();
        $user->email = $email ? $email : TEST_EMAIL1;
        $user->statusId = FUser::STATUS_ACTIVE;
        $user->login = $login ? $login : $faker->userName;
        $user->firstName = $faker->firstName;
        $user->lastName = $faker->lastName;
        $user->roleId = $role->getId();
        $user->save();

        return $user;
    }

    /**
     * @param string $key
     * @param string $title
     *
     * @return FUserRole
     */
    public static function createUserRole($key = 'admin', $title = 'admin')
    {
        $role = new FUserRole();
        $role->key = $key;
        $role->title = $title;
        $role->save();

        return $role;
    }

    /**
     * @param Customer  $customer
     * @param int       $numberOfItems
     * @param \DateTime $date
     *
     * @return Order
     */
    public static function createOrder($customer, $numberOfItems = 2, ?\DateTime $date = null)
    {
        $order = new Order($customer);
        $order->setRealSubTotal($numberOfItems * 2 * 10);
        $order->setSubTotal($numberOfItems * 2 * 10);
        $order->setVat($order->getSubTotal() / 5);
        $order->setTotal($order->getSubTotal() + $order->getVat());
        $order->setCustomerName($customer->getFirstName());
        $order->setDescription('Test order');
        $order->setDtc($date ?: new \DateTime());
        $order->setDtm($date ?: new \DateTime());
        $i = 0;
        while ($numberOfItems > $i) {
            $orderItem = self::createOrderItem($order);
            $order->addItem($orderItem);
            ++$i;
        }

        return $order;
    }

    public static function createOrderItem($order, $price = 20, $qty = 2, $productId = null, $company = null, $title = 'Test item'): OrderItem
    {
        $orderItem = new OrderItem($order);
        $orderItem->setPrice($price);
        $orderItem->setQty($qty);
        $orderItem->setSubTotal($price);
        $orderItem->setVat($price * 0.2);
        $orderItem->setTotalPrice($orderItem->getSubTotal() + $orderItem->getVat());
        $orderItem->setIsFee(false);
        $orderItem->setProductTitle($title);
        $orderItem->setNotApplyVat(false);
        $orderItem->setNonVatableValue(0);
        $orderItem->setDtc(new \DateTime());

        if ($company) {
            $orderItem->setCompany($company);
        }

        if ($productId) {
            $orderItem->setProductId($productId);
        }

        return $orderItem;
    }

    /**
     * @param int       $serviceTypeId
     * @param int       $productId
     * @param Company   $company
     * @param OrderItem $orderItem
     * @param \DateTime $dtStart
     * @param \DateTime $dtExpires
     *
     * @throws NodeException
     *
     * @return Service
     */
    public static function createServiceFromType(
        $serviceTypeId,
        $productId,
        Company $company,
        OrderItem $orderItem,
        ?\DateTime $dtStart = null,
        ?\DateTime $dtExpires = null,
    ) {
        /** @var NodeService $nodeService */
        $nodeService = Registry::$container->get(DiLocator::SERVICE_NODE);
        $product = $nodeService->getProductById($productId);
        $typeId = $product->getServiceTypeId();
        $renewalProduct = $product->getRenewalProduct();

        $service = new Service($serviceTypeId, $product, $company, $orderItem);
        $service->setRenewalProduct($renewalProduct);
        if ($dtStart) {
            $service->setDtStart($dtStart);
        }
        if ($dtExpires) {
            $service->setDtExpires($dtExpires);
        }

        $company->addService($service);

        return $service;
    }

    public static function createServiceFromProductName(
        Company $company,
        OrderItem $orderItem,
        string $productName,
        ?\DateTime $dtStart = null,
        ?\DateTime $dtExpires = null,
    ): Service {
        /** @var NodeService $nodeService */
        $nodeService = Registry::$container->get(DiLocator::SERVICE_NODE);
        $product = $nodeService->requiredProductByName($productName);
        $serviceTypeId = $product->getServiceTypeId();

        $service = new Service($serviceTypeId, $product, $company, $orderItem);
        $service->setRenewalProduct($product->getRenewalProduct());
        if ($dtStart) {
            $service->setDtStart($dtStart);
        }
        if ($dtExpires) {
            $service->setDtExpires($dtExpires);
        }

        $company->addService($service);

        return $service;
    }

    /**
     * @param int       $serviceTypeId
     * @param int       $packageId
     * @param Company   $company
     * @param OrderItem $orderItem
     * @param \DateTime $dtStart
     * @param \DateTime $dtExpires
     *
     * @throws NodeException
     *
     * @return Service
     */
    public static function createPackageServiceFromType(
        $serviceTypeId,
        $packageId,
        Company $company,
        OrderItem $orderItem,
        ?\DateTime $dtStart = null,
        ?\DateTime $dtExpires = null,
    ) {
        /** @var NodeService $nodeService */
        $nodeService = Registry::$container->get(DiLocator::SERVICE_NODE);
        /** @var Package $package */
        $package = $nodeService->getProductById($packageId);
        $renewalPackage = $package->getRenewalProduct();

        $service = new Service($serviceTypeId, $package, $company, $orderItem);
        $service->setRenewalProduct($renewalPackage);
        if ($dtStart) {
            $service->setDtStart($dtStart);
        }
        if ($dtExpires) {
            $service->setDtExpires($dtExpires);
        }

        /** @var Product[] $products */
        $products = $package->getPackageProducts('includedProducts');
        foreach ($products as $product) {
            if ($product->hasServiceType()) {
                $childService = self::createServiceFromType(
                    $product->serviceTypeId,
                    $product->getId(),
                    $company,
                    $orderItem,
                    $dtStart,
                    $dtExpires
                );
                $service->addChild($childService);
            }
        }

        $company->addService($service);

        return $service;
    }

    /**
     * @param Customer  $customer
     * @param Company   $company
     * @param OrderItem $orderItem
     * @param int       $productId
     * @param string    $productName
     * @param int       $durationInMonths
     *
     * @return VoServiceQueue
     */
    public static function createVoServiceQueue(
        ?Customer $customer = null,
        ?Company $company = null,
        ?OrderItem $orderItem = null,
        $productId = 1,
        $productName = 'Product Name',
        $durationInMonths = 12,
    ) {
        $customer = $customer ?: self::createCustomer();
        $company = $company ?: self::createCompany($customer);
        $orderItem = $orderItem ?: self::createOrderItem(self::createOrder($customer));

        // return new VoServiceQueue($email, $orderId, $productId, $productName, $durationInMonths);
        return new VoServiceQueue($customer, $company, $orderItem, $productId, $productName, $durationInMonths);
    }

    public static function createNode(array $options = []): FEmail
    {
        $node = new FEmail();
        $node->setStatusId(FEmail::STATUS_PUBLISHED);
        $node->setParentId(1);
        $node->adminControler = PageAdminControler::class;

        foreach ($options as $property => $value) {
            $methodName = sprintf('set%s', Strings::firstUpper($property));
            $node->$methodName($options[$property] ?? null);
        }

        $node->save();

        return $node;
    }

    public static function createEmail(array $options = []): FEmail
    {
        $email = new FEmail();
        $email->setStatusId(FEmail::STATUS_PUBLISHED);
        $email->setParentId(1);
        $email->adminControler = EmailAdminControler::class;

        foreach ($options as $property => $value) {
            if ($property === 'body') {
                $email->page->text = $value;
            } elseif ($property === 'from') {
                $email->setFrom($value, $options['fromName'] ?? null);
            } elseif ($property === 'fromName') {
                continue;
            } else {
                $methodName = sprintf('set%s', Strings::firstUpper($property));
                $email->$methodName($value ?? null);
            }
        }

        $email->save();

        return $email;
    }

    public static function createCustomerLog(Customer $customer, \DateTimeInterface $dateTime): CustomerLog
    {
        $log = CustomerLog::cashbackPreferenceChanged(CustomerActionLogger::CASHBACK_PREF_CREATED);
        $log->setDtc($dateTime);
        $log->setDtm($dateTime);
        $log->setCustomer($customer);

        return $log;
    }

    public static function createPostItem(
        string $companyName,
        ?string $companyNumber = null,
        ?string $lpNumber = null,
    ): PostItem {
        $postItem = new PostItem(
            PostItem::TYPE_COMPANIES_HOUSE,
            PostItem::STATUS_WAITING,
            CompanyName::uppercased($companyName),
            $companyNumber,
            $lpNumber
        );
        $postItem->setDtc(new \DateTime('now'));

        return $postItem;
    }

    public static function createIdCompanyInfo($companyName = 'test made simple')
    {
        $company = self::createCompanyDummy(self::createCustomer(), $companyName, rand(1, 1000));
        $checks = [
            Check::fromArr(Check::NAME_PHOTO_ID, ['type' => Check::MANUAL, 'responsible_verifier' => 'b']),
        ];
        $diligenceLevel = new DiligenceLevel('tests', $checks);
        $idValidation = new IdValidation('4', true, [], 'test');
        $idEntity = IdEntity::fromCustomerCompany($company);
        $idEntityInfo = new IdEntityInfo($idEntity, $diligenceLevel, [$idValidation]);

        return new IdCompanyInfo($company, [$idEntityInfo]);
    }

    public static function createReminderService()
    {
        $customer = self::createCustomer();
        $company = self::createCompanyDummy($customer);
        $service = self::createService(
            $company,
            new Date(),
            new Date('+30 days'),
            true,
            Service::TYPE_REGISTERED_OFFICE,
            null,
            1315
        );

        $service->setRenewalProduct(Product::getProductById(1353));

        $reminderService = ReminderService::fromService($service);

        return [$reminderService];
    }

    public static function createNewAddress(): Address
    {
        return new Address(
            'premise example',
            'some street',
            'London',
            'TR19 7AA',
            'GB-ENG'
        );
    }

    public static function createBusinessServicesOfferData()
    {
        return [
            'id' => 1,
            'enable_conditions' => [
                'products' => [1],
                'includedCountries' => ['UK'],
            ],
            'mediatorId' => 3,
            'externalId' => 999,
            'partnerId' => 20,
            'categoryId' => 5,
            'name' => 'test',
            'logo' => 'test',
            'partnerName' => 'Test 1',
            'features' => ['abc', 'def'],
            'introduction' => 'introduction',
            'description' => 'description',
            'ctaLabel' => 'cta label',
            'order' => 1,
            'cashbackAmount' => [
                'default' => 10,
                '1315' => 55,
            ],
        ];
    }

    public static function createListOfOrderItems(int $productId): array
    {
        $customer = self::createCustomer();

        return [
            self::createOrderItem(self::createOrder($customer, 1), 20, 2, $productId, self::createCompany($customer, 'TEST COMPANY NAME', 123456)),
            self::createOrderItem(self::createOrder($customer, 1), 20, 2, $productId, self::createCompany($customer, 'TEST COMPANY NAME 2', 123457)),
            self::createOrderItem(self::createOrder($customer, 1), 20, 2, $productId, self::createCompany($customer, 'TEST COMPANY NAME 3', 123458)),
        ];
    }
}
