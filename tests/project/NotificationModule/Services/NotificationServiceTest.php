<?php

namespace tests\project\NotificationModule\Services;

use BankingModule\Views\CompanyBankingViewFactory;
use Entities\Company;
use Entities\Customer;
use Http\Discovery\Exception;
use NotificationModule\Deciders\NotificationDecider;
use NotificationModule\Helpers\NotificationHelper;
use NotificationModule\Mappers\NotificationTemplateMapper;
use NotificationModule\Notifications\SyncNotificationFactory;
use NotificationModule\Providers\BankingNotificationProvider;
use NotificationModule\Providers\CsmsReportNotificationProvider;
use NotificationModule\Providers\INotificationProvider;
use NotificationModule\Providers\SyncNotificationProvider;
use NotificationModule\Services\NotificationService;
use NotificationModule\Views\NotificationView;
use RouterModule\Helpers\ControllerHelper;
use Services\CompanyService;
use TestModule\Annotations\Inject;
use TestModule\Helpers\DatabaseHelper;
use TestModule\PhpUnit\TestCase;
use tests\helpers\EntityHelper;
use Utils\Date;

class NotificationServiceTest extends TestCase
{
    /**
     * @var INotificationProvider[]
     */
    private $providers = [];

    /**
     * @var DatabaseHelper
     */
    private $databaseHelper;

    /**
     * @var Customer
     */
    private $customer;

    /**
     * @var Company
     */
    private $company;

    /**
     * @var NotificationService
     */
    private $notificationService;

    /**
     * @var ControllerHelper
     */
    private $controllerHelper;

    /**
     * @var NotificationDecider
     */
    private $notificationDecider;

    /**
     * @var NotificationTemplateMapper
     */
    private $notificationTemplateMapper;

    /**
     * @var CompanyService
     */
    private $companyService;

    /**
     * @var SyncNotificationFactory
     */
    private $syncNotificationFactory;

    /**
     * @var CompanyBankingViewFactory
     */
    private $companyBankingViewFactory;

    /**
     * @Inject({
     *     "controllerHelper"="router_module.helpers.controller_helper",
     *     "notificationDecider"="notification_module.deciders.notification_decider",
     *     "notificationService"="notification_module.notification_service",
     *     "notificationTemplateMapper"="notification_module.mappers.notification_template_mapper",
     *     "companyService"="services.company_service",
     *     "syncNotificationFactory"="notification_module.notifications.sync_notification_factory",
     *     "companyBankingViewFactory"="banking_module.views.company_banking_view_factory",
     *     "databaseHelper"="test_module.helpers.database_helper"
     * })
     */
    public function setupDependencies(
        ControllerHelper $controllerHelper,
        NotificationDecider $notificationDecider,
        NotificationService $notificationService,
        NotificationTemplateMapper $notificationTemplateMapper,
        CompanyService $companyService,
        SyncNotificationFactory $syncNotificationFactory,
        CompanyBankingViewFactory $companyBankingViewFactory,
        DatabaseHelper $databaseHelper
    ): void
    {
        $this->controllerHelper = $controllerHelper;
        $this->notificationDecider = $notificationDecider;
        $this->notificationService = $notificationService;
        $this->notificationTemplateMapper = $notificationTemplateMapper;
        $this->companyService = $companyService;
        $this->syncNotificationFactory = $syncNotificationFactory;
        $this->companyBankingViewFactory = $companyBankingViewFactory;

        $this->databaseHelper = $databaseHelper;

        $this->clearTables();
        $this->init();
    }

    public function testShouldReturnNotificationForCustomer(): void
    {
        $this->expectNotToPerformAssertions();
        /** @var INotificationProvider $provider */
        foreach ($this->generateProviders() as $provider) {
            $this->notificationService->addProvider($provider);

            $notificationData = [
                'customer' => $this->customer,
                'company' => $this->company,
                'tags' => [NotificationHelper::getProviderTag($provider)]
            ];

            $notificationProviderData = $provider->getNotifications($notificationData);
            $notification = array_shift($notificationProviderData);

            if ($notification) {
                $notificationView = new NotificationView($notification, $this->notificationTemplateMapper->getTemplatePathForNotificationProvider($notification));
                $notificationProviderData = $this->notificationService->getViews($notificationData);
                $this->assertEquals($notificationView, $notificationProviderData[0]);
            }
        }
    }

    public function testShouldReturnNoNotificationForCustomer(): void
    {
        /** @var INotificationProvider $provider */
        foreach ($this->generateProviders() as $provider) {
            EntityHelper::createEvent(NotificationHelper::getProviderNotificationKey($provider), $this->customer->getId());
            $this->notificationService->addProvider($provider);
            $this->notificationDecider->clearCache();

            $notificationData = [
                'customer' => $this->customer,
                'company' => $this->company,
                'tags' => [NotificationHelper::getProviderTag($provider)]
            ];

            $notificationProviderData = $this->notificationService->getNotifications($notificationData);
            $this->assertEmpty($notificationProviderData);
        }
    }

    private function generateProviders(): array
    {
        return [
            new BankingNotificationProvider($this->controllerHelper, $this->companyBankingViewFactory),
            new SyncNotificationProvider($this->companyService, $this->syncNotificationFactory),
        ];
    }

    private function init(): void
    {
        $this->customer = EntityHelper::createCustomer('<EMAIL>');
        $this->company = EntityHelper::createCompany($this->customer);
        $this->company->setCompanyNumber('*********');
        $this->company->setIncorporationDate(new Date('-1 month'));

        $this->databaseHelper->saveEntity($this->company);
        $this->databaseHelper->clearEntities();
    }

    public function tearDown(): void
    {
        $this->clearTables();
    }

    private function clearTables(): void
    {
        EntityHelper::emptyTables(EntityHelper::$tables);
    }
}