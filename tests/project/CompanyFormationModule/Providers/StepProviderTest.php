<?php

namespace tests\project\CompanyFormationModule\Providers;

use CompanyFormationModule\Dto\Step;
use CompanyFormationModule\Factories\StepFactory;
use CompanyFormationModule\Providers\StepProvider;
use CompanyIncorporationModule\Deciders\IncorporationDecider;
use CompanyIncorporationModule\Services\IncorporationService;
use Entities\Company;
use Entities\CompanyHouse\FormSubmission\CompanyIncorporation;
use Entities\Customer;
use PHPUnit\Framework\MockObject\MockObject;
use Symfony\Component\HttpFoundation\Request;
use TestModule\PhpUnit\TestCase;
use UserModule\Services\ICustomerAvailability;

class StepProviderTest extends TestCase
{
    /**
     * @var StepProvider
     */
    private $provider;

    /**
     * @var StepFactory
     */
    private $stepFactory;

    /**
     * @var Request
     *
     */
    private $request;

    /**
     * @var IncorporationService|MockObject
     */
    private $incorporationService;

    /**
     * @var IncorporationDecider|MockObject
     */
    private $incorporationDecider;

    /**
     * @var ICustomerAvailability|MockObject
     */
    private $customerAvailability;

    public function setUp(): void
    {
        $this->stepFactory = $this->getMockBuilder(StepFactory::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->incorporationService = $this->getMockBuilder((IncorporationService::class))
            ->disableOriginalConstructor()
            ->getMock();

        $this->request = $this->getMockBuilder(Request::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->incorporationDecider = $this->getMockBuilder(IncorporationDecider::class)
            ->disableOriginalConstructor()
            ->getMock();

        $this->steps = [
            'office' => [
                'title' => 'Registered',
                'route' => '153',
                'company_param' => 'company_id',
                'blocked' => [
                    'COMPANY_PENDING'
                ],
                'conditions' => [
                    'REGISTERED_OFFICE_FILLED'
                ]
            ],
            'sic' => [
                'title' => 'SIC',
                'route' => 'company_formation_module.sic',
                'company_param' => 'company',
                'requirements' => [
                    'NOT_LLP',
                    'NOT_ANNA_PACKAGE'
                ],
                'blocked' => [
                    'COMPANY_PENDING'
                ],
                'conditions' => [
                    'SIC_CODES_FILLED'
                ]
            ],
            'directors' => [
                'title' => [
                    'LLP' => 'Members',
                    'default' => 'Directors'
                ],
                'route' => 'company_formation_module.directors_overview',
                'company_param' => 'company',
                'blocked' => [
                    'COMPANY_PENDING'
                ],
                'conditions' => [
                    'PLC' => ['MINIMUM_2_DIRECTORS'],
                    'LLP' => ['MINIMUM_2_DIRECTORS'],
                    'default' => ['MINIMUM_1_PERSON_DIRECTOR'],
                ]
            ],
        ];

        $this->customerAvailability = $this->getMockBuilder(ICustomerAvailability::class)
            ->disableOriginalConstructor()
            ->getMock();

        $incorporationDecider = new IncorporationDecider(
            $this->incorporationService,
            $this->customerAvailability,
            [],
            $this->steps,
        );

        $this->provider = new StepProvider(
            $this->stepFactory,
            $incorporationDecider,
            $this->request
        );
    }

    public function test_company_incorporated(): void
    {
        $company = $this->getMockBuilder(Company::class)
            ->disableOriginalConstructor()
            ->getMock();

        $mockCustomer = $this->getMockBuilder(Customer::class)
            ->disableOriginalConstructor()
            ->getMock();

        $mockCustomer->method('isOnNewIncorporationProcess')->willReturn(false);

        $company->method('isIncorporated')->willReturn(false);
        $company->method('getIncorporationFormSubmission')->willReturn(null);
        $company->method('getCustomer')->willReturn($mockCustomer);

        $this->assertEquals([], $this->provider->getAllSteps($company));

        $this->assertEquals(
            [
                'previous' => null,
                'current' => null,
                'next' => null
            ],
            $this->provider->getNavigationSteps($company, 'office')
        );

        $this->assertNull(
            $this->provider->getCurrentStep($company)
        );

        $this->assertFalse(
            $this->provider->isStepAccessible($company, 'office')
        );
    }

    public function test_all_steps(): void
    {
        [$company, $incorporation, $stepOffice, $stepSic, $stepDirectors, $stepFormationPage ] = $this->buildSteps();

        $this->stepFactory->method('getStep')->withConsecutive(
            ['office', $this->steps['office'], $company, $incorporation, true],
            ['sic', $this->steps['sic'], $company, $incorporation, true],
            ['directors', $this->steps['directors'], $company, $incorporation, false],
        )->willReturnOnConsecutiveCalls(
            $stepOffice,
            $stepSic,
            $stepDirectors,
            $stepFormationPage
        );

        $this->assertEquals(
            ['office' => $stepOffice, 'sic' => $stepSic, 'directors' => $stepDirectors],
            $this->provider->getAllSteps($company)
        );
    }

    public function test_navigation(): void
    {
        [$company, $incorporation, $stepOffice, $stepSic, $stepDirectors, $stepFormationPage] = $this->buildSteps();

        $this->stepFactory->method('getStep')->withConsecutive(
            ['office', $this->steps['office'], $company, $incorporation],
            ['sic', $this->steps['sic'], $company, $incorporation],
            ['directors', $this->steps['directors'], $company, $incorporation],
        )->willReturnOnConsecutiveCalls(
            $stepOffice,
            $stepSic,
            $stepDirectors,
            $stepFormationPage
        );

        $this->assertEquals(
            [
                'previous' => $stepOffice,
                'current' => $stepSic,
                'next' => $stepDirectors
            ],
            $this->provider->getNavigationSteps($company, 'sic')
        );
    }

    public function test_navigation_not_existent_step(): void
    {
        [$company, $incorporation, $stepOffice, $stepSic, $stepDirectors, $stepFormationPage] = $this->buildSteps();

        $this->stepFactory->method('getStep')->withConsecutive(
            ['office', $this->steps['office'], $company, $incorporation],
            ['sic', $this->steps['sic'], $company, $incorporation],
            ['directors', $this->steps['directors'], $company, $incorporation],
        )->willReturnOnConsecutiveCalls(
            $stepOffice,
            $stepSic,
            $stepDirectors,
            $stepFormationPage
        );

        $this->assertEquals(
            [
                'previous' => null,
                'current' => null,
                'next' => null
            ],
            $this->provider->getNavigationSteps($company, 'pscs')
        );
    }

    public function test_current_step(): void
    {
        [$company, $incorporation, $stepOffice, $stepSic, $stepDirectors, $stepFormationPage] = $this->buildSteps();

        $this->stepFactory->method('getStep')->withConsecutive(
            ['office', $this->steps['office'], $company, $incorporation, true],
            ['sic', $this->steps['sic'], $company, $incorporation, true],
            ['directors', $this->steps['directors'], $company, $incorporation, false],
        )->willReturnOnConsecutiveCalls(
            $stepOffice,
            $stepSic,
            $stepDirectors,
            $stepFormationPage
        );

        $this->assertEquals(
            $stepSic,
            $this->provider->getCurrentStep($company)
        );
    }

    /**
     * @dataProvider accessibleProvider
     */
    public function test_step_accessible(string $stepKey, bool $result): void
    {
        [$company, $incorporation, $stepOffice, $stepSic, $stepDirectors, $stepFormationPage] = $this->buildSteps();

        $this->stepFactory->method('getStep')->withConsecutive(
            ['office', $this->steps['office'], $company, $incorporation, true],
            ['sic', $this->steps['sic'], $company, $incorporation, true],
            ['directors', $this->steps['directors'], $company, $incorporation, false],
        )->willReturnOnConsecutiveCalls(
            $stepOffice,
            $stepSic,
            $stepDirectors,
            $stepFormationPage
        );

        $this->assertEquals($result, $this->provider->isStepAccessible($company, $stepKey));
    }

    public function accessibleProvider()
    {
        return [
            'office' => ['office', true],
            'sic' => ['sic', true],
            'directors' => ['directors', false],
            'pscs' => ['pscs', false],
        ];
    }



    private function buildSteps(): array
    {
        $company = $this->getMockBuilder(Company::class)
            ->disableOriginalConstructor()
            ->getMock();

        $mockCustomer = $this->getMockBuilder(Customer::class)
            ->disableOriginalConstructor()
            ->getMock();

        $mockCustomer->method('isOnNewIncorporationProcess')->willReturn(false);

        $incorporation = $this->getMockBuilder(CompanyIncorporation::class)
            ->disableOriginalConstructor()
            ->getMock();

        $company->method('isIncorporated')->willReturn(false);
        $company->method('getIncorporationFormSubmission')->willReturn($incorporation);
        $company->method('getCustomer')->willReturn($mockCustomer);

        $stepOffice = new Step(
            'Office',
            'url',
            false,
            true,
            true,
            false,
            [],
            []
        );

        $stepSic = new Step(
            'SIC',
            'url',
            false,
            false,
            true,
            false,
            ['SIC_CODES_FILLED'],
            []
        );

        $stepDirectors = new Step(
            'Directors',
            'url',
            false,
            false,
            false,
            false,
            [],
            []
        );

        $stepFormationPage = new Step(
            'Formation Page',
            'url',
            false,
            false,
            false,
            false,
            [],
            [
                new Step(
                    'REGISTERED_OFFICE_FILLED',
                    'url',
                    false,
                    false,
                    false,
                    false,
                    [],
                    []
                ),
                new Step(
                    'MAIL_FORWARDING',
                    'url',
                    false,
                    false,
                    false,
                    false,
                    [],
                    []
                ),
                new Step(
                    'SIC_CODES_FILLED',
                    'url',
                    false,
                    false,
                    false,
                    false,
                    [],
                    []
                )
            ]
        );

        return [$company, $incorporation, $stepOffice, $stepSic, $stepDirectors, $stepFormationPage];
    }

}
