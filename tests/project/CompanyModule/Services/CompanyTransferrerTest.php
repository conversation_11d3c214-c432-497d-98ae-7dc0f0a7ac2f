<?php

namespace tests\project\CompanyModule\Services;

use CompanyModule\Services\CompanyTransferrer;
use DateTime;
use Dispatcher\Events\CompanyTransferredEvent;
use Entities\Company;
use Entities\CompanyHouse\FormSubmission;
use Entities\Service;
use IdModule\Dto\IdConversion;
use IdModule\Repositories\IIdCompanyInfoRepository;
use IdModule\Verification\IIdValidator;
use Models\Products\RegisterOffice;
use TestModule\Annotations\Inject;
use TestModule\Helpers\DatabaseHelper;
use TestModule\PhpUnit\TestCase;
use tests\helpers\ObjectHelper;
use tests\helpers\ServicesHelper;
use UserModule\Contracts\IUser;
use UserModule\Entities\AdminUser;

/**
 * @large
 */
class CompanyTransferrerTest extends TestCase
{
    /**
     * @var CompanyTransferrer
     */
    private $object;

    /**
     * @var DatabaseHelper
     */
    private $databaseHelper;

    /**
     * @var Service[]
     */
    private $services;

    /**
     * @var IIdCompanyInfoRepository
     */
    private $companyInfoRepository;

    /**
     * @var IIdValidator
     */
    private $idValidator;

    /**
     * @var array
     */
    private array $customers;

    /**
     * @var array
     */
    private array $companies;

    /**
     * @var IUser
     */
    private IUser $user;

    /**
     * @Inject({
     *     "object"="company_module.services.company_transferrer",
     *     "databaseHelper"="test_module.helpers.database_helper",
     *     "companyInfoRepository" = "id_module.repositories.id_info_repository",
     *     "idValidator" = "id_module.verification.id_validator"
     * })
     */
    public function setUpDependencies(
        CompanyTransferrer $object,
        DatabaseHelper $databaseHelper,
        IIdCompanyInfoRepository $companyInfoRepository,
        IIdValidator $idValidator
    )
    {
        $this->object = $object;
        $this->databaseHelper = $databaseHelper;
        $this->companyInfoRepository = $companyInfoRepository;
        $this->idValidator = $idValidator;

        $this->databaseHelper->emptyTables([TBL_CUSTOMERS, TBL_COMPANIES, TBL_SERVICES, TBL_ORDERS, TBL_ORDER_ITEMS]);

        $this->customers = $customers = [
            ObjectHelper::createCustomer('test1', 'n/a', new DateTime('yesterday')),
            ObjectHelper::createCustomer('test2'),
            ObjectHelper::createCustomer('test3')
        ];

        $this->companies = $companies = [
            ObjectHelper::createCompany($customers[0], 'test ltd 1', '1', null, Company::COMPANY_STATUS_ACTIVE, RegisterOffice::POSTCODE),
            ObjectHelper::createCompany($customers[1], 'test ltd 2', '2', null, Company::COMPANY_STATUS_ACTIVE, RegisterOffice::POSTCODE),
            ObjectHelper::createCompany($customers[2], 'test ltd 3', '3'),
        ];

        $formSubmission = new FormSubmission($this->companies[2]);
        $formSubmission->setFormIdentifier(FormSubmission::TYPE_COMPANY_INCORPORATION);
        $this->companies[2]->setFormSubmissions([$formSubmission]);

        $this->services = $services = [
            ServicesHelper::getActiveForCompany($companies[0], Service::TYPE_PACKAGE_PRIVACY),
            ServicesHelper::getActiveForCompany($companies[0], Service::TYPE_ANNUAL_RETURN),
            ServicesHelper::getActiveForCompany($companies[0], Service::TYPE_PACKAGE_PRIVACY),
            ServicesHelper::getActiveForCompany($companies[2], Service::TYPE_PACKAGE_PRIVACY)
        ];

        $this->user = $this->databaseHelper->find(AdminUser::class, 1);

        $this->databaseHelper->saveEntities(array_merge([$formSubmission], $customers, $companies, $services));
        $this->assertCount(1, $this->companies[0]->getEnabledParentServicesWithType(Service::TYPE_ANNUAL_RETURN));
        $this->manuallyValidate($this->companies[0]);
    }

    public function manuallyValidate(Company $company)
    {
        $entities = $this->companyInfoRepository->getIdInfoForCompanyIterator($company);
        foreach ($entities as $entity) {
            $manual = new IdConversion(true, $entity->getCheckNames());
            $this->idValidator->validateManually($entity->getEntity(), $manual, 'phpunit');
        }
    }

    public function testTransferWithoutService()
    {
        /** @var CompanyTransferredEvent $tranferedEvent */
        $tranferedEvent = $this->object->transfer($this->companies[0], $this->customers[1], [], $this->user)->extract();
        $newCompany = $tranferedEvent->getNewCompany();
        $this->assertTrue($newCompany->getIsBronzeCoverLetterPrinted());
        $this->assertTrue($newCompany->getIsCertificatePrinted());
        $this->assertTrue($newCompany->getIsMaCoverLetterPrinted());
        $this->assertTrue($newCompany->getIsMaPrinted());
        $this->assertInstanceOf(CompanyTransferredEvent::class, $tranferedEvent);
    }

    public function testTransferWithServices()
    {
        $tranferedEvent = $this->object->transfer(
            $this->companies[0],
            $this->customers[1],
            [$this->services[0], $this->services[1]],
            $this->user
        )->extract();
        $this->assertInstanceOf(CompanyTransferredEvent::class, $tranferedEvent);
        $this->assertFalse($tranferedEvent->isWarning());
        $this->assertCount(0, $tranferedEvent->getNewCompany()->getEnabledParentServicesWithType(Service::TYPE_REGISTERED_OFFICE));
    }

    public function testTransferWithChildrenServices()
    {
        $children = [
            ServicesHelper::getActiveForCompany($this->companies[0], Service::TYPE_REGISTERED_OFFICE),
            ServicesHelper::getActiveForCompany($this->companies[0], Service::TYPE_FRAUD_PROTECTION),
        ];
        $this->services[0]->addChild($children[0]);
        $this->services[0]->addChild($children[1]);

        $this->assertCount(1, $this->companies[0]->getEnabledParentServicesWithType(Service::TYPE_REGISTERED_OFFICE));

        $this->databaseHelper->saveEntities(array_merge([$this->services[0]], $children));

        $tranferedEvent = $this->object->transfer(
            $this->companies[0],
            $this->customers[1],
            [$this->services[0], $this->services[1]],
            $this->user
        )->extract();
        $this->assertInstanceOf(CompanyTransferredEvent::class, $tranferedEvent);
        $this->assertCount(1, $tranferedEvent->getNewCompany()->getEnabledParentServicesWithType(Service::TYPE_REGISTERED_OFFICE));

    }
}
