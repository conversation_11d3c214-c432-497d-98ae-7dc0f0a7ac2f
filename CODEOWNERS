[Frontend]
/client/ @theopapps
/flow-typed/ @theopapps
/www/admin/ @theopapps
/www/components/ @theopapps
/www/dist/ @theopapps
/www/errorPages/ @theopapps
/www/front/ @theopapps
/www/project/ @theopapps


[Project]
/project/AdminModule/ @frederikh1
/project/AspectModule/ @frederikh1
/project/BankHolidayModule/ @frederikh1
/project/BankingModule/ @theopapps
/project/BarclaysModule/ @theopapps
/project/BasketModule/ @theopapps
/project/bootstrap/ @frederikh1
/project/BusinessDataModule/ @theopapps
/project/BusinessServicesModule/ @theopapps
/project/CashBackModule/ @theopapps
/project/CommonModule/ @frederikh1
/project/CompaniesHouseModule/ @frederikh1
/project/CompanyFormationModule/ @frederikh1
/project/CompanyImportModule/ @frederikh1
/project/CompanyModule/ @frederikh1
/project/CompanySyncModule/ @frederikh1
/project/CompanyUpdateModule/ @frederikh1
/project/config/ @devs
/project/Cron/ @frederikh1
/project/CsmsModule/ @theopapps
/project/CsvParser/ @frederikh1
/project/CustomerModule/ @frederikh1
/project/DataGeneration/ @frederikh1
/project/EmailModule/ @frederikh1
/project/factories/ @frederikh1
/project/FeedbackModule/ @frederikh1
/project/FormModule/ @frederikh1
/project/FormSubmissionModule/ @frederikh1
/project/FraudProtectionModule/ @theopapps
/project/FrontModule/ @theopapps
/project/HomeModule/ @theopapps
/project/HttpModule/ @theopapps
/project/IdCheckModule/ @theopapps
/project/InfusionsoftModule/ @frederikh1
/project/InternationalModule/ @frederikh1
/project/LandingPagesModule/ @frederikh1
/project/libs/ @frederikh1
/project/LoggableModule/ @frederikh1
/project/LoginModule/ @frederikh1
/project/MailgunModule/ @frederikh1
/project/MailScanModule/ @frederikh1
/project/MarketingModule/ @frederikh1
/project/models/ @frederikh1
/project/MyServicesModule/ @theopapps
/project/NotificationModule/ @theopapps
/project/OfferModule/ @theopapps
/project/OrderModule/ @theopapps
/project/OrdersAdminModule/ @theopapps
/project/PackageModule/ @theopapps
/project/PagesModule/ @theopapps
/project/PagesModule/ @frederikh1
/project/PayByPhoneModule/ @frederikh1
/project/PaymentModule/ @theopapps
/project/PeopleWithSignificantControlModule/ @frederikh1
/project/ProductModule/ @frederikh1
/project/PeopleWithSignificantControlModule/ @frederikh1
/project/Remote/ @frederikh1
/project/RenewalModule/ @theopapps
/project/Search/ @theopapps
/project/ServiceActivatorModule/ @theopapps
/project/ServiceCancellationModule/ @theopapps
/project/ServiceModule/ @theopapps
/project/ServiceRemindersModule/ @theopapps
/project/Services/ @theopapps
/project/ServicesAdminModule/ @theopapps
/project/SitemapModule/ @frederikh1
/project/SpecialOffersModule/ @theopapps
/project/TemplatingModule/ @theopapps
/project/ToolkitOfferModule/ @theopapps
/project/TranslationModule/ @frederikh1
/project/UserModule/ @frederikh1
/project/ValidationModule/ @frederikh1
/project/Validators/ @frederikh1
/project/VoServiceModule/ @frederikh1
/project/VoucherModule/ @frederikh1
/project/WholesaleModule/ @frederikh1

project/config/servicesWidget.yml @theopapps


[Vendor]
/vendor/made_simple/msg-emails/ @A2pRMSG
/vendor/made_simple/msg-features/ @carima1
/vendor/made_simple/msg-framework/bootstrap_module/ @frederikh1
/vendor/made_simple/msg-framework/cache_module/ @frederikh1
/vendor/made_simple/msg-framework/chfiling/ @frederikh1
/vendor/made_simple/msg-framework/companies-house/ @frederikh1
/vendor/made_simple/msg-framework/console/ @frederikh1
/vendor/made_simple/msg-framework/content_module/ @frederikh1
/vendor/made_simple/msg-framework/creditsafe_bulkdata_importer/ @frederikh1
/vendor/made_simple/msg-framework/creditsafe_data_uk/ @frederikh1
/vendor/made_simple/msg-framework/cron/ @frederikh1
/vendor/made_simple/msg-framework/cross_site_module/ @frederikh1
/vendor/made_simple/msg-framework/csglobalgateway/ @frederikh1
/vendor/made_simple/msg-framework/csv-parser/ @frederikh1
/vendor/made_simple/msg-framework/datagrid/ @frederikh1
/vendor/made_simple/msg-framework/error_module/ @theopapps
/vendor/made_simple/msg-framework/experiment_module/ @theopapps
/vendor/made_simple/msg-framework/feature_module/ @theopapps
/vendor/made_simple/msg-framework/form_module/ @frederikh1
/vendor/made_simple/msg-framework/functional_module/ @frederikh1
/vendor/made_simple/msg-framework/git-hooks/ @frederikh1
/vendor/made_simple/msg-framework/http-client/ @frederikh1
/vendor/made_simple/msg-framework/http-client-old/ @frederikh1
/vendor/made_simple/msg-framework/id3global-api-client/ @theopapps
/vendor/made_simple/msg-framework/id_module/ @theopapps
/vendor/made_simple/msg-framework/notifier/ @frederikh1
/vendor/made_simple/msg-framework/payment_module/ @theopapps
/vendor/made_simple/msg-framework/PayPalNVP/ @theopapps
/vendor/made_simple/msg-framework/phpcs/ @frederikh1
/vendor/made_simple/msg-framework/router_module/ @frederikh1
/vendor/made_simple/msg-framework/sagepay_rest_api/ @theopapps
/vendor/made_simple/msg-framework/sagepaytoken/ @theopapps
/vendor/made_simple/msg-framework/storage_module/ @theopapps
/vendor/made_simple/msg-framework/test_module/ @frederikh1
/vendor/made_simple/msg-framework/ui_helper/ @theopapps
/vendor/made_simple/msg-framework/user_module/ @frederikh1
/vendor/made_simple/msg-framework/utils/ @frederikh1
/vendor/made_simple/msg-framework/webloader/ @frederikh1
/vendor/made_simple/msg-framework/workflow_engine_module/ @frederikh1
/vendor/made_simple/msg-framework/xml_object_mapping/ @frederikh1
/vendor/made_simple/msg_docker/ @frederikh1


[Migrations]
/storage/database/migrations/ @danielac1


[Copywriting]
/storage/template_variables/ @frederikh1
