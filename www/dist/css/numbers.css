button {
  color: #f3f3f3 !important;
  background-color: #ff8621 !important;
}

button:hover {
  color: #f3f3f3 !important;
  background-color: #fa770a !important;
}

button:disabled {
  background-color: #FFA062 !important;
  border: none !important;
  color: #f3f3f3 !important;
  opacity: 50%;
}

.tab-radio {
  width: 25px;
  height: 25px;
}

.type {
  border: 2px solid #f26646;
  color: #000;
  cursor: pointer;
}

.bg-moneypenny {
  background-color: #f26646;
}

.text-moneypenny {
  color: #f26646;
}

.type-active {
  position: relative;
  background-color: #f26646;
  height: 100px;
  color: #f3f3f3;
}

.types .row input[type=radio].form-check-input:checked {
  background-color: white;
  border: 5px solid #007bff;
}

.numbers .search-input {
  border-top-left-radius: 20px !important;
  border-bottom-left-radius: 20px !important;
}

.numbers .search-input-button {
  border-top-right-radius: 20px;
  border-bottom-right-radius: 20px;
}

.has-search .form-control {
  padding-left: 2.375rem;
}

.has-search .form-control-feedback {
  position: absolute;
  z-index: 2;
  display: block;
  width: 2.375rem;
  height: 2.375rem;
  line-height: 2.375rem;
  text-align: center;
  pointer-events: none;
  color: #aaa;
}

.search-input {
  font-size: 13px;
}

.search-button {
  background-color: #fc7800;
  color: white;
}

i {
  cursor: pointer;
  font-size: 14px;
}

.freephone-text {
  text-align: justify !important;
}

.btn {
  line-height: 3rem;
}

.btn-pay {
  margin-top: 2rem !important;
  font-size: 1.3em !important;
  width: 320px;
}

.success {
  color: #104327;
  background-color: #C7E2D6;
  padding: 1rem;
  width: 100%;
  height: 52px;
  display: flex;
  align-items: center;
}

.circle-icon {
  width: 18px;
  height: 18px;
  margin-left: 4px;
  margin-right: 8px;
}

.error {
  color: #721c24;
  background-color: #f8d7da;
  padding: 1rem;
  width: 100%;
  height: 52px;
  display: flex;
  align-items: center;
}

.mtn-4 {
  margin-top: -4px;
}

.link {
  cursor: pointer;
  color: #26ACE7;
  text-decoration: underline;
}

.link:hover {
  color: #FD7E3F;
}

.stepper-progress-indicator li {
  text-align: left;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  color: #d5d5d5;
  -moz-column-gap: 20px;
       column-gap: 20px;
}

.stepper-progress-indicator li.completed span.step, .stepper-progress-indicator li.active span.step {
  background-color: #f26646;
  color: white;
}

.stepper-progress-indicator li.completed span.progress-title, .stepper-progress-indicator li.active span.progress-title {
  color: #f26646;
}

.stepper-progress-indicator li.completed span.progress-line, .stepper-progress-indicator li.active span.progress-line {
  background-color: #f26646;
}

.stepper-progress-indicator li span.step {
  width: 30px;
  height: 30px;
  background-color: #eff2f3;
  text-align: center;
  color: #b0bcc1;
  border-radius: 50%;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stepper-progress-indicator li span.progress-title {
  flex-shrink: 0;
}

.stepper-progress-indicator li span.progress-line {
  height: 2px;
  width: 100%;
  background-color: #d5d5d5;
  width: 100px;
  margin-right: 10px;
}
