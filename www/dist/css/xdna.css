/*!
 * Font Awesome Free 6.4.2 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 * Copyright 2023 Fonticons, Inc.
 */
.fa {
  font-family: var(--fa-style-family, "Font Awesome 6 Free");
  font-weight: var(--fa-style, 900); }

.fa,
.fa-classic,
.fa-sharp,
.fas,
.fa-solid,
.far,
.fa-regular,
.fab,
.fa-brands {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: var(--fa-display, inline-block);
  font-style: normal;
  font-variant: normal;
  line-height: 1;
  text-rendering: auto; }

.fas,
.fa-classic,
.fa-solid,
.far,
.fa-regular {
  font-family: 'Font Awesome 6 Free'; }

.fab,
.fa-brands {
  font-family: 'Font Awesome 6 Brands'; }

.fa-1x {
  font-size: 1em; }

.fa-2x {
  font-size: 2em; }

.fa-3x {
  font-size: 3em; }

.fa-4x {
  font-size: 4em; }

.fa-5x {
  font-size: 5em; }

.fa-6x {
  font-size: 6em; }

.fa-7x {
  font-size: 7em; }

.fa-8x {
  font-size: 8em; }

.fa-9x {
  font-size: 9em; }

.fa-10x {
  font-size: 10em; }

.fa-2xs {
  font-size: 0.625em;
  line-height: 0.1em;
  vertical-align: 0.225em; }

.fa-xs {
  font-size: 0.75em;
  line-height: 0.08333em;
  vertical-align: 0.125em; }

.fa-sm {
  font-size: 0.875em;
  line-height: 0.07143em;
  vertical-align: 0.05357em; }

.fa-lg {
  font-size: 1.25em;
  line-height: 0.05em;
  vertical-align: -0.075em; }

.fa-xl {
  font-size: 1.5em;
  line-height: 0.04167em;
  vertical-align: -0.125em; }

.fa-2xl {
  font-size: 2em;
  line-height: 0.03125em;
  vertical-align: -0.1875em; }

.fa-fw {
  text-align: center;
  width: 1.25em; }

.fa-ul {
  list-style-type: none;
  margin-left: var(--fa-li-margin, 2.5em);
  padding-left: 0; }
  .fa-ul > li {
    position: relative; }

.fa-li {
  left: calc(var(--fa-li-width, 2em) * -1);
  position: absolute;
  text-align: center;
  width: var(--fa-li-width, 2em);
  line-height: inherit; }

.fa-border {
  border-color: var(--fa-border-color, #eee);
  border-radius: var(--fa-border-radius, 0.1em);
  border-style: var(--fa-border-style, solid);
  border-width: var(--fa-border-width, 0.08em);
  padding: var(--fa-border-padding, 0.2em 0.25em 0.15em); }

.fa-pull-left {
  float: left;
  margin-right: var(--fa-pull-margin, 0.3em); }

.fa-pull-right {
  float: right;
  margin-left: var(--fa-pull-margin, 0.3em); }

.fa-beat {
  -webkit-animation-name: fa-beat;
          animation-name: fa-beat;
  -webkit-animation-delay: var(--fa-animation-delay, 0s);
          animation-delay: var(--fa-animation-delay, 0s);
  -webkit-animation-direction: var(--fa-animation-direction, normal);
          animation-direction: var(--fa-animation-direction, normal);
  -webkit-animation-duration: var(--fa-animation-duration, 1s);
          animation-duration: var(--fa-animation-duration, 1s);
  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
          animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  -webkit-animation-timing-function: var(--fa-animation-timing, ease-in-out);
          animation-timing-function: var(--fa-animation-timing, ease-in-out); }

.fa-bounce {
  -webkit-animation-name: fa-bounce;
          animation-name: fa-bounce;
  -webkit-animation-delay: var(--fa-animation-delay, 0s);
          animation-delay: var(--fa-animation-delay, 0s);
  -webkit-animation-direction: var(--fa-animation-direction, normal);
          animation-direction: var(--fa-animation-direction, normal);
  -webkit-animation-duration: var(--fa-animation-duration, 1s);
          animation-duration: var(--fa-animation-duration, 1s);
  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
          animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  -webkit-animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1));
          animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.28, 0.84, 0.42, 1)); }

.fa-fade {
  -webkit-animation-name: fa-fade;
          animation-name: fa-fade;
  -webkit-animation-delay: var(--fa-animation-delay, 0s);
          animation-delay: var(--fa-animation-delay, 0s);
  -webkit-animation-direction: var(--fa-animation-direction, normal);
          animation-direction: var(--fa-animation-direction, normal);
  -webkit-animation-duration: var(--fa-animation-duration, 1s);
          animation-duration: var(--fa-animation-duration, 1s);
  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
          animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  -webkit-animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));
          animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1)); }

.fa-beat-fade {
  -webkit-animation-name: fa-beat-fade;
          animation-name: fa-beat-fade;
  -webkit-animation-delay: var(--fa-animation-delay, 0s);
          animation-delay: var(--fa-animation-delay, 0s);
  -webkit-animation-direction: var(--fa-animation-direction, normal);
          animation-direction: var(--fa-animation-direction, normal);
  -webkit-animation-duration: var(--fa-animation-duration, 1s);
          animation-duration: var(--fa-animation-duration, 1s);
  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
          animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  -webkit-animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1));
          animation-timing-function: var(--fa-animation-timing, cubic-bezier(0.4, 0, 0.6, 1)); }

.fa-flip {
  -webkit-animation-name: fa-flip;
          animation-name: fa-flip;
  -webkit-animation-delay: var(--fa-animation-delay, 0s);
          animation-delay: var(--fa-animation-delay, 0s);
  -webkit-animation-direction: var(--fa-animation-direction, normal);
          animation-direction: var(--fa-animation-direction, normal);
  -webkit-animation-duration: var(--fa-animation-duration, 1s);
          animation-duration: var(--fa-animation-duration, 1s);
  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
          animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  -webkit-animation-timing-function: var(--fa-animation-timing, ease-in-out);
          animation-timing-function: var(--fa-animation-timing, ease-in-out); }

.fa-shake {
  -webkit-animation-name: fa-shake;
          animation-name: fa-shake;
  -webkit-animation-delay: var(--fa-animation-delay, 0s);
          animation-delay: var(--fa-animation-delay, 0s);
  -webkit-animation-direction: var(--fa-animation-direction, normal);
          animation-direction: var(--fa-animation-direction, normal);
  -webkit-animation-duration: var(--fa-animation-duration, 1s);
          animation-duration: var(--fa-animation-duration, 1s);
  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
          animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  -webkit-animation-timing-function: var(--fa-animation-timing, linear);
          animation-timing-function: var(--fa-animation-timing, linear); }

.fa-spin {
  -webkit-animation-name: fa-spin;
          animation-name: fa-spin;
  -webkit-animation-delay: var(--fa-animation-delay, 0s);
          animation-delay: var(--fa-animation-delay, 0s);
  -webkit-animation-direction: var(--fa-animation-direction, normal);
          animation-direction: var(--fa-animation-direction, normal);
  -webkit-animation-duration: var(--fa-animation-duration, 2s);
          animation-duration: var(--fa-animation-duration, 2s);
  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
          animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  -webkit-animation-timing-function: var(--fa-animation-timing, linear);
          animation-timing-function: var(--fa-animation-timing, linear); }

.fa-spin-reverse {
  --fa-animation-direction: reverse; }

.fa-pulse,
.fa-spin-pulse {
  -webkit-animation-name: fa-spin;
          animation-name: fa-spin;
  -webkit-animation-direction: var(--fa-animation-direction, normal);
          animation-direction: var(--fa-animation-direction, normal);
  -webkit-animation-duration: var(--fa-animation-duration, 1s);
          animation-duration: var(--fa-animation-duration, 1s);
  -webkit-animation-iteration-count: var(--fa-animation-iteration-count, infinite);
          animation-iteration-count: var(--fa-animation-iteration-count, infinite);
  -webkit-animation-timing-function: var(--fa-animation-timing, steps(8));
          animation-timing-function: var(--fa-animation-timing, steps(8)); }

@media (prefers-reduced-motion: reduce) {
  .fa-beat,
  .fa-bounce,
  .fa-fade,
  .fa-beat-fade,
  .fa-flip,
  .fa-pulse,
  .fa-shake,
  .fa-spin,
  .fa-spin-pulse {
    -webkit-animation-delay: -1ms;
            animation-delay: -1ms;
    -webkit-animation-duration: 1ms;
            animation-duration: 1ms;
    -webkit-animation-iteration-count: 1;
            animation-iteration-count: 1;
    -webkit-transition-delay: 0s;
            transition-delay: 0s;
    -webkit-transition-duration: 0s;
            transition-duration: 0s; } }

@-webkit-keyframes fa-beat {
  0%, 90% {
    -webkit-transform: scale(1);
            transform: scale(1); }
  45% {
    -webkit-transform: scale(var(--fa-beat-scale, 1.25));
            transform: scale(var(--fa-beat-scale, 1.25)); } }

@keyframes fa-beat {
  0%, 90% {
    -webkit-transform: scale(1);
            transform: scale(1); }
  45% {
    -webkit-transform: scale(var(--fa-beat-scale, 1.25));
            transform: scale(var(--fa-beat-scale, 1.25)); } }

@-webkit-keyframes fa-bounce {
  0% {
    -webkit-transform: scale(1, 1) translateY(0);
            transform: scale(1, 1) translateY(0); }
  10% {
    -webkit-transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);
            transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0); }
  30% {
    -webkit-transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));
            transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em)); }
  50% {
    -webkit-transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);
            transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0); }
  57% {
    -webkit-transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));
            transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em)); }
  64% {
    -webkit-transform: scale(1, 1) translateY(0);
            transform: scale(1, 1) translateY(0); }
  100% {
    -webkit-transform: scale(1, 1) translateY(0);
            transform: scale(1, 1) translateY(0); } }

@keyframes fa-bounce {
  0% {
    -webkit-transform: scale(1, 1) translateY(0);
            transform: scale(1, 1) translateY(0); }
  10% {
    -webkit-transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0);
            transform: scale(var(--fa-bounce-start-scale-x, 1.1), var(--fa-bounce-start-scale-y, 0.9)) translateY(0); }
  30% {
    -webkit-transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em));
            transform: scale(var(--fa-bounce-jump-scale-x, 0.9), var(--fa-bounce-jump-scale-y, 1.1)) translateY(var(--fa-bounce-height, -0.5em)); }
  50% {
    -webkit-transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0);
            transform: scale(var(--fa-bounce-land-scale-x, 1.05), var(--fa-bounce-land-scale-y, 0.95)) translateY(0); }
  57% {
    -webkit-transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em));
            transform: scale(1, 1) translateY(var(--fa-bounce-rebound, -0.125em)); }
  64% {
    -webkit-transform: scale(1, 1) translateY(0);
            transform: scale(1, 1) translateY(0); }
  100% {
    -webkit-transform: scale(1, 1) translateY(0);
            transform: scale(1, 1) translateY(0); } }

@-webkit-keyframes fa-fade {
  50% {
    opacity: var(--fa-fade-opacity, 0.4); } }

@keyframes fa-fade {
  50% {
    opacity: var(--fa-fade-opacity, 0.4); } }

@-webkit-keyframes fa-beat-fade {
  0%, 100% {
    opacity: var(--fa-beat-fade-opacity, 0.4);
    -webkit-transform: scale(1);
            transform: scale(1); }
  50% {
    opacity: 1;
    -webkit-transform: scale(var(--fa-beat-fade-scale, 1.125));
            transform: scale(var(--fa-beat-fade-scale, 1.125)); } }

@keyframes fa-beat-fade {
  0%, 100% {
    opacity: var(--fa-beat-fade-opacity, 0.4);
    -webkit-transform: scale(1);
            transform: scale(1); }
  50% {
    opacity: 1;
    -webkit-transform: scale(var(--fa-beat-fade-scale, 1.125));
            transform: scale(var(--fa-beat-fade-scale, 1.125)); } }

@-webkit-keyframes fa-flip {
  50% {
    -webkit-transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));
            transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg)); } }

@keyframes fa-flip {
  50% {
    -webkit-transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg));
            transform: rotate3d(var(--fa-flip-x, 0), var(--fa-flip-y, 1), var(--fa-flip-z, 0), var(--fa-flip-angle, -180deg)); } }

@-webkit-keyframes fa-shake {
  0% {
    -webkit-transform: rotate(-15deg);
            transform: rotate(-15deg); }
  4% {
    -webkit-transform: rotate(15deg);
            transform: rotate(15deg); }
  8%, 24% {
    -webkit-transform: rotate(-18deg);
            transform: rotate(-18deg); }
  12%, 28% {
    -webkit-transform: rotate(18deg);
            transform: rotate(18deg); }
  16% {
    -webkit-transform: rotate(-22deg);
            transform: rotate(-22deg); }
  20% {
    -webkit-transform: rotate(22deg);
            transform: rotate(22deg); }
  32% {
    -webkit-transform: rotate(-12deg);
            transform: rotate(-12deg); }
  36% {
    -webkit-transform: rotate(12deg);
            transform: rotate(12deg); }
  40%, 100% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg); } }

@keyframes fa-shake {
  0% {
    -webkit-transform: rotate(-15deg);
            transform: rotate(-15deg); }
  4% {
    -webkit-transform: rotate(15deg);
            transform: rotate(15deg); }
  8%, 24% {
    -webkit-transform: rotate(-18deg);
            transform: rotate(-18deg); }
  12%, 28% {
    -webkit-transform: rotate(18deg);
            transform: rotate(18deg); }
  16% {
    -webkit-transform: rotate(-22deg);
            transform: rotate(-22deg); }
  20% {
    -webkit-transform: rotate(22deg);
            transform: rotate(22deg); }
  32% {
    -webkit-transform: rotate(-12deg);
            transform: rotate(-12deg); }
  36% {
    -webkit-transform: rotate(12deg);
            transform: rotate(12deg); }
  40%, 100% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg); } }

@-webkit-keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg); }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg); } }

@keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg); }
  100% {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg); } }

.fa-rotate-90 {
  -webkit-transform: rotate(90deg);
          transform: rotate(90deg); }

.fa-rotate-180 {
  -webkit-transform: rotate(180deg);
          transform: rotate(180deg); }

.fa-rotate-270 {
  -webkit-transform: rotate(270deg);
          transform: rotate(270deg); }

.fa-flip-horizontal {
  -webkit-transform: scale(-1, 1);
          transform: scale(-1, 1); }

.fa-flip-vertical {
  -webkit-transform: scale(1, -1);
          transform: scale(1, -1); }

.fa-flip-both,
.fa-flip-horizontal.fa-flip-vertical {
  -webkit-transform: scale(-1, -1);
          transform: scale(-1, -1); }

.fa-rotate-by {
  -webkit-transform: rotate(var(--fa-rotate-angle, none));
          transform: rotate(var(--fa-rotate-angle, none)); }

.fa-stack {
  display: inline-block;
  height: 2em;
  line-height: 2em;
  position: relative;
  vertical-align: middle;
  width: 2.5em; }

.fa-stack-1x,
.fa-stack-2x {
  left: 0;
  position: absolute;
  text-align: center;
  width: 100%;
  z-index: var(--fa-stack-z-index, auto); }

.fa-stack-1x {
  line-height: inherit; }

.fa-stack-2x {
  font-size: 2em; }

.fa-inverse {
  color: var(--fa-inverse, #fff); }

/* Font Awesome uses the Unicode Private Use Area (PUA) to ensure screen
readers do not read off random characters that represent icons */

.fa-0::before {
  content: "\30"; }

.fa-1::before {
  content: "\31"; }

.fa-2::before {
  content: "\32"; }

.fa-3::before {
  content: "\33"; }

.fa-4::before {
  content: "\34"; }

.fa-5::before {
  content: "\35"; }

.fa-6::before {
  content: "\36"; }

.fa-7::before {
  content: "\37"; }

.fa-8::before {
  content: "\38"; }

.fa-9::before {
  content: "\39"; }

.fa-fill-drip::before {
  content: "\f576"; }

.fa-arrows-to-circle::before {
  content: "\e4bd"; }

.fa-circle-chevron-right::before {
  content: "\f138"; }

.fa-chevron-circle-right::before {
  content: "\f138"; }

.fa-at::before {
  content: "\40"; }

.fa-trash-can::before {
  content: "\f2ed"; }

.fa-trash-alt::before {
  content: "\f2ed"; }

.fa-text-height::before {
  content: "\f034"; }

.fa-user-xmark::before {
  content: "\f235"; }

.fa-user-times::before {
  content: "\f235"; }

.fa-stethoscope::before {
  content: "\f0f1"; }

.fa-message::before {
  content: "\f27a"; }

.fa-comment-alt::before {
  content: "\f27a"; }

.fa-info::before {
  content: "\f129"; }

.fa-down-left-and-up-right-to-center::before {
  content: "\f422"; }

.fa-compress-alt::before {
  content: "\f422"; }

.fa-explosion::before {
  content: "\e4e9"; }

.fa-file-lines::before {
  content: "\f15c"; }

.fa-file-alt::before {
  content: "\f15c"; }

.fa-file-text::before {
  content: "\f15c"; }

.fa-wave-square::before {
  content: "\f83e"; }

.fa-ring::before {
  content: "\f70b"; }

.fa-building-un::before {
  content: "\e4d9"; }

.fa-dice-three::before {
  content: "\f527"; }

.fa-calendar-days::before {
  content: "\f073"; }

.fa-calendar-alt::before {
  content: "\f073"; }

.fa-anchor-circle-check::before {
  content: "\e4aa"; }

.fa-building-circle-arrow-right::before {
  content: "\e4d1"; }

.fa-volleyball::before {
  content: "\f45f"; }

.fa-volleyball-ball::before {
  content: "\f45f"; }

.fa-arrows-up-to-line::before {
  content: "\e4c2"; }

.fa-sort-down::before {
  content: "\f0dd"; }

.fa-sort-desc::before {
  content: "\f0dd"; }

.fa-circle-minus::before {
  content: "\f056"; }

.fa-minus-circle::before {
  content: "\f056"; }

.fa-door-open::before {
  content: "\f52b"; }

.fa-right-from-bracket::before {
  content: "\f2f5"; }

.fa-sign-out-alt::before {
  content: "\f2f5"; }

.fa-atom::before {
  content: "\f5d2"; }

.fa-soap::before {
  content: "\e06e"; }

.fa-icons::before {
  content: "\f86d"; }

.fa-heart-music-camera-bolt::before {
  content: "\f86d"; }

.fa-microphone-lines-slash::before {
  content: "\f539"; }

.fa-microphone-alt-slash::before {
  content: "\f539"; }

.fa-bridge-circle-check::before {
  content: "\e4c9"; }

.fa-pump-medical::before {
  content: "\e06a"; }

.fa-fingerprint::before {
  content: "\f577"; }

.fa-hand-point-right::before {
  content: "\f0a4"; }

.fa-magnifying-glass-location::before {
  content: "\f689"; }

.fa-search-location::before {
  content: "\f689"; }

.fa-forward-step::before {
  content: "\f051"; }

.fa-step-forward::before {
  content: "\f051"; }

.fa-face-smile-beam::before {
  content: "\f5b8"; }

.fa-smile-beam::before {
  content: "\f5b8"; }

.fa-flag-checkered::before {
  content: "\f11e"; }

.fa-football::before {
  content: "\f44e"; }

.fa-football-ball::before {
  content: "\f44e"; }

.fa-school-circle-exclamation::before {
  content: "\e56c"; }

.fa-crop::before {
  content: "\f125"; }

.fa-angles-down::before {
  content: "\f103"; }

.fa-angle-double-down::before {
  content: "\f103"; }

.fa-users-rectangle::before {
  content: "\e594"; }

.fa-people-roof::before {
  content: "\e537"; }

.fa-people-line::before {
  content: "\e534"; }

.fa-beer-mug-empty::before {
  content: "\f0fc"; }

.fa-beer::before {
  content: "\f0fc"; }

.fa-diagram-predecessor::before {
  content: "\e477"; }

.fa-arrow-up-long::before {
  content: "\f176"; }

.fa-long-arrow-up::before {
  content: "\f176"; }

.fa-fire-flame-simple::before {
  content: "\f46a"; }

.fa-burn::before {
  content: "\f46a"; }

.fa-person::before {
  content: "\f183"; }

.fa-male::before {
  content: "\f183"; }

.fa-laptop::before {
  content: "\f109"; }

.fa-file-csv::before {
  content: "\f6dd"; }

.fa-menorah::before {
  content: "\f676"; }

.fa-truck-plane::before {
  content: "\e58f"; }

.fa-record-vinyl::before {
  content: "\f8d9"; }

.fa-face-grin-stars::before {
  content: "\f587"; }

.fa-grin-stars::before {
  content: "\f587"; }

.fa-bong::before {
  content: "\f55c"; }

.fa-spaghetti-monster-flying::before {
  content: "\f67b"; }

.fa-pastafarianism::before {
  content: "\f67b"; }

.fa-arrow-down-up-across-line::before {
  content: "\e4af"; }

.fa-spoon::before {
  content: "\f2e5"; }

.fa-utensil-spoon::before {
  content: "\f2e5"; }

.fa-jar-wheat::before {
  content: "\e517"; }

.fa-envelopes-bulk::before {
  content: "\f674"; }

.fa-mail-bulk::before {
  content: "\f674"; }

.fa-file-circle-exclamation::before {
  content: "\e4eb"; }

.fa-circle-h::before {
  content: "\f47e"; }

.fa-hospital-symbol::before {
  content: "\f47e"; }

.fa-pager::before {
  content: "\f815"; }

.fa-address-book::before {
  content: "\f2b9"; }

.fa-contact-book::before {
  content: "\f2b9"; }

.fa-strikethrough::before {
  content: "\f0cc"; }

.fa-k::before {
  content: "\4b"; }

.fa-landmark-flag::before {
  content: "\e51c"; }

.fa-pencil::before {
  content: "\f303"; }

.fa-pencil-alt::before {
  content: "\f303"; }

.fa-backward::before {
  content: "\f04a"; }

.fa-caret-right::before {
  content: "\f0da"; }

.fa-comments::before {
  content: "\f086"; }

.fa-paste::before {
  content: "\f0ea"; }

.fa-file-clipboard::before {
  content: "\f0ea"; }

.fa-code-pull-request::before {
  content: "\e13c"; }

.fa-clipboard-list::before {
  content: "\f46d"; }

.fa-truck-ramp-box::before {
  content: "\f4de"; }

.fa-truck-loading::before {
  content: "\f4de"; }

.fa-user-check::before {
  content: "\f4fc"; }

.fa-vial-virus::before {
  content: "\e597"; }

.fa-sheet-plastic::before {
  content: "\e571"; }

.fa-blog::before {
  content: "\f781"; }

.fa-user-ninja::before {
  content: "\f504"; }

.fa-person-arrow-up-from-line::before {
  content: "\e539"; }

.fa-scroll-torah::before {
  content: "\f6a0"; }

.fa-torah::before {
  content: "\f6a0"; }

.fa-broom-ball::before {
  content: "\f458"; }

.fa-quidditch::before {
  content: "\f458"; }

.fa-quidditch-broom-ball::before {
  content: "\f458"; }

.fa-toggle-off::before {
  content: "\f204"; }

.fa-box-archive::before {
  content: "\f187"; }

.fa-archive::before {
  content: "\f187"; }

.fa-person-drowning::before {
  content: "\e545"; }

.fa-arrow-down-9-1::before {
  content: "\f886"; }

.fa-sort-numeric-desc::before {
  content: "\f886"; }

.fa-sort-numeric-down-alt::before {
  content: "\f886"; }

.fa-face-grin-tongue-squint::before {
  content: "\f58a"; }

.fa-grin-tongue-squint::before {
  content: "\f58a"; }

.fa-spray-can::before {
  content: "\f5bd"; }

.fa-truck-monster::before {
  content: "\f63b"; }

.fa-w::before {
  content: "\57"; }

.fa-earth-africa::before {
  content: "\f57c"; }

.fa-globe-africa::before {
  content: "\f57c"; }

.fa-rainbow::before {
  content: "\f75b"; }

.fa-circle-notch::before {
  content: "\f1ce"; }

.fa-tablet-screen-button::before {
  content: "\f3fa"; }

.fa-tablet-alt::before {
  content: "\f3fa"; }

.fa-paw::before {
  content: "\f1b0"; }

.fa-cloud::before {
  content: "\f0c2"; }

.fa-trowel-bricks::before {
  content: "\e58a"; }

.fa-face-flushed::before {
  content: "\f579"; }

.fa-flushed::before {
  content: "\f579"; }

.fa-hospital-user::before {
  content: "\f80d"; }

.fa-tent-arrow-left-right::before {
  content: "\e57f"; }

.fa-gavel::before {
  content: "\f0e3"; }

.fa-legal::before {
  content: "\f0e3"; }

.fa-binoculars::before {
  content: "\f1e5"; }

.fa-microphone-slash::before {
  content: "\f131"; }

.fa-box-tissue::before {
  content: "\e05b"; }

.fa-motorcycle::before {
  content: "\f21c"; }

.fa-bell-concierge::before {
  content: "\f562"; }

.fa-concierge-bell::before {
  content: "\f562"; }

.fa-pen-ruler::before {
  content: "\f5ae"; }

.fa-pencil-ruler::before {
  content: "\f5ae"; }

.fa-people-arrows::before {
  content: "\e068"; }

.fa-people-arrows-left-right::before {
  content: "\e068"; }

.fa-mars-and-venus-burst::before {
  content: "\e523"; }

.fa-square-caret-right::before {
  content: "\f152"; }

.fa-caret-square-right::before {
  content: "\f152"; }

.fa-scissors::before {
  content: "\f0c4"; }

.fa-cut::before {
  content: "\f0c4"; }

.fa-sun-plant-wilt::before {
  content: "\e57a"; }

.fa-toilets-portable::before {
  content: "\e584"; }

.fa-hockey-puck::before {
  content: "\f453"; }

.fa-table::before {
  content: "\f0ce"; }

.fa-magnifying-glass-arrow-right::before {
  content: "\e521"; }

.fa-tachograph-digital::before {
  content: "\f566"; }

.fa-digital-tachograph::before {
  content: "\f566"; }

.fa-users-slash::before {
  content: "\e073"; }

.fa-clover::before {
  content: "\e139"; }

.fa-reply::before {
  content: "\f3e5"; }

.fa-mail-reply::before {
  content: "\f3e5"; }

.fa-star-and-crescent::before {
  content: "\f699"; }

.fa-house-fire::before {
  content: "\e50c"; }

.fa-square-minus::before {
  content: "\f146"; }

.fa-minus-square::before {
  content: "\f146"; }

.fa-helicopter::before {
  content: "\f533"; }

.fa-compass::before {
  content: "\f14e"; }

.fa-square-caret-down::before {
  content: "\f150"; }

.fa-caret-square-down::before {
  content: "\f150"; }

.fa-file-circle-question::before {
  content: "\e4ef"; }

.fa-laptop-code::before {
  content: "\f5fc"; }

.fa-swatchbook::before {
  content: "\f5c3"; }

.fa-prescription-bottle::before {
  content: "\f485"; }

.fa-bars::before {
  content: "\f0c9"; }

.fa-navicon::before {
  content: "\f0c9"; }

.fa-people-group::before {
  content: "\e533"; }

.fa-hourglass-end::before {
  content: "\f253"; }

.fa-hourglass-3::before {
  content: "\f253"; }

.fa-heart-crack::before {
  content: "\f7a9"; }

.fa-heart-broken::before {
  content: "\f7a9"; }

.fa-square-up-right::before {
  content: "\f360"; }

.fa-external-link-square-alt::before {
  content: "\f360"; }

.fa-face-kiss-beam::before {
  content: "\f597"; }

.fa-kiss-beam::before {
  content: "\f597"; }

.fa-film::before {
  content: "\f008"; }

.fa-ruler-horizontal::before {
  content: "\f547"; }

.fa-people-robbery::before {
  content: "\e536"; }

.fa-lightbulb::before {
  content: "\f0eb"; }

.fa-caret-left::before {
  content: "\f0d9"; }

.fa-circle-exclamation::before {
  content: "\f06a"; }

.fa-exclamation-circle::before {
  content: "\f06a"; }

.fa-school-circle-xmark::before {
  content: "\e56d"; }

.fa-arrow-right-from-bracket::before {
  content: "\f08b"; }

.fa-sign-out::before {
  content: "\f08b"; }

.fa-circle-chevron-down::before {
  content: "\f13a"; }

.fa-chevron-circle-down::before {
  content: "\f13a"; }

.fa-unlock-keyhole::before {
  content: "\f13e"; }

.fa-unlock-alt::before {
  content: "\f13e"; }

.fa-cloud-showers-heavy::before {
  content: "\f740"; }

.fa-headphones-simple::before {
  content: "\f58f"; }

.fa-headphones-alt::before {
  content: "\f58f"; }

.fa-sitemap::before {
  content: "\f0e8"; }

.fa-circle-dollar-to-slot::before {
  content: "\f4b9"; }

.fa-donate::before {
  content: "\f4b9"; }

.fa-memory::before {
  content: "\f538"; }

.fa-road-spikes::before {
  content: "\e568"; }

.fa-fire-burner::before {
  content: "\e4f1"; }

.fa-flag::before {
  content: "\f024"; }

.fa-hanukiah::before {
  content: "\f6e6"; }

.fa-feather::before {
  content: "\f52d"; }

.fa-volume-low::before {
  content: "\f027"; }

.fa-volume-down::before {
  content: "\f027"; }

.fa-comment-slash::before {
  content: "\f4b3"; }

.fa-cloud-sun-rain::before {
  content: "\f743"; }

.fa-compress::before {
  content: "\f066"; }

.fa-wheat-awn::before {
  content: "\e2cd"; }

.fa-wheat-alt::before {
  content: "\e2cd"; }

.fa-ankh::before {
  content: "\f644"; }

.fa-hands-holding-child::before {
  content: "\e4fa"; }

.fa-asterisk::before {
  content: "\2a"; }

.fa-square-check::before {
  content: "\f14a"; }

.fa-check-square::before {
  content: "\f14a"; }

.fa-peseta-sign::before {
  content: "\e221"; }

.fa-heading::before {
  content: "\f1dc"; }

.fa-header::before {
  content: "\f1dc"; }

.fa-ghost::before {
  content: "\f6e2"; }

.fa-list::before {
  content: "\f03a"; }

.fa-list-squares::before {
  content: "\f03a"; }

.fa-square-phone-flip::before {
  content: "\f87b"; }

.fa-phone-square-alt::before {
  content: "\f87b"; }

.fa-cart-plus::before {
  content: "\f217"; }

.fa-gamepad::before {
  content: "\f11b"; }

.fa-circle-dot::before {
  content: "\f192"; }

.fa-dot-circle::before {
  content: "\f192"; }

.fa-face-dizzy::before {
  content: "\f567"; }

.fa-dizzy::before {
  content: "\f567"; }

.fa-egg::before {
  content: "\f7fb"; }

.fa-house-medical-circle-xmark::before {
  content: "\e513"; }

.fa-campground::before {
  content: "\f6bb"; }

.fa-folder-plus::before {
  content: "\f65e"; }

.fa-futbol::before {
  content: "\f1e3"; }

.fa-futbol-ball::before {
  content: "\f1e3"; }

.fa-soccer-ball::before {
  content: "\f1e3"; }

.fa-paintbrush::before {
  content: "\f1fc"; }

.fa-paint-brush::before {
  content: "\f1fc"; }

.fa-lock::before {
  content: "\f023"; }

.fa-gas-pump::before {
  content: "\f52f"; }

.fa-hot-tub-person::before {
  content: "\f593"; }

.fa-hot-tub::before {
  content: "\f593"; }

.fa-map-location::before {
  content: "\f59f"; }

.fa-map-marked::before {
  content: "\f59f"; }

.fa-house-flood-water::before {
  content: "\e50e"; }

.fa-tree::before {
  content: "\f1bb"; }

.fa-bridge-lock::before {
  content: "\e4cc"; }

.fa-sack-dollar::before {
  content: "\f81d"; }

.fa-pen-to-square::before {
  content: "\f044"; }

.fa-edit::before {
  content: "\f044"; }

.fa-car-side::before {
  content: "\f5e4"; }

.fa-share-nodes::before {
  content: "\f1e0"; }

.fa-share-alt::before {
  content: "\f1e0"; }

.fa-heart-circle-minus::before {
  content: "\e4ff"; }

.fa-hourglass-half::before {
  content: "\f252"; }

.fa-hourglass-2::before {
  content: "\f252"; }

.fa-microscope::before {
  content: "\f610"; }

.fa-sink::before {
  content: "\e06d"; }

.fa-bag-shopping::before {
  content: "\f290"; }

.fa-shopping-bag::before {
  content: "\f290"; }

.fa-arrow-down-z-a::before {
  content: "\f881"; }

.fa-sort-alpha-desc::before {
  content: "\f881"; }

.fa-sort-alpha-down-alt::before {
  content: "\f881"; }

.fa-mitten::before {
  content: "\f7b5"; }

.fa-person-rays::before {
  content: "\e54d"; }

.fa-users::before {
  content: "\f0c0"; }

.fa-eye-slash::before {
  content: "\f070"; }

.fa-flask-vial::before {
  content: "\e4f3"; }

.fa-hand::before {
  content: "\f256"; }

.fa-hand-paper::before {
  content: "\f256"; }

.fa-om::before {
  content: "\f679"; }

.fa-worm::before {
  content: "\e599"; }

.fa-house-circle-xmark::before {
  content: "\e50b"; }

.fa-plug::before {
  content: "\f1e6"; }

.fa-chevron-up::before {
  content: "\f077"; }

.fa-hand-spock::before {
  content: "\f259"; }

.fa-stopwatch::before {
  content: "\f2f2"; }

.fa-face-kiss::before {
  content: "\f596"; }

.fa-kiss::before {
  content: "\f596"; }

.fa-bridge-circle-xmark::before {
  content: "\e4cb"; }

.fa-face-grin-tongue::before {
  content: "\f589"; }

.fa-grin-tongue::before {
  content: "\f589"; }

.fa-chess-bishop::before {
  content: "\f43a"; }

.fa-face-grin-wink::before {
  content: "\f58c"; }

.fa-grin-wink::before {
  content: "\f58c"; }

.fa-ear-deaf::before {
  content: "\f2a4"; }

.fa-deaf::before {
  content: "\f2a4"; }

.fa-deafness::before {
  content: "\f2a4"; }

.fa-hard-of-hearing::before {
  content: "\f2a4"; }

.fa-road-circle-check::before {
  content: "\e564"; }

.fa-dice-five::before {
  content: "\f523"; }

.fa-square-rss::before {
  content: "\f143"; }

.fa-rss-square::before {
  content: "\f143"; }

.fa-land-mine-on::before {
  content: "\e51b"; }

.fa-i-cursor::before {
  content: "\f246"; }

.fa-stamp::before {
  content: "\f5bf"; }

.fa-stairs::before {
  content: "\e289"; }

.fa-i::before {
  content: "\49"; }

.fa-hryvnia-sign::before {
  content: "\f6f2"; }

.fa-hryvnia::before {
  content: "\f6f2"; }

.fa-pills::before {
  content: "\f484"; }

.fa-face-grin-wide::before {
  content: "\f581"; }

.fa-grin-alt::before {
  content: "\f581"; }

.fa-tooth::before {
  content: "\f5c9"; }

.fa-v::before {
  content: "\56"; }

.fa-bangladeshi-taka-sign::before {
  content: "\e2e6"; }

.fa-bicycle::before {
  content: "\f206"; }

.fa-staff-snake::before {
  content: "\e579"; }

.fa-rod-asclepius::before {
  content: "\e579"; }

.fa-rod-snake::before {
  content: "\e579"; }

.fa-staff-aesculapius::before {
  content: "\e579"; }

.fa-head-side-cough-slash::before {
  content: "\e062"; }

.fa-truck-medical::before {
  content: "\f0f9"; }

.fa-ambulance::before {
  content: "\f0f9"; }

.fa-wheat-awn-circle-exclamation::before {
  content: "\e598"; }

.fa-snowman::before {
  content: "\f7d0"; }

.fa-mortar-pestle::before {
  content: "\f5a7"; }

.fa-road-barrier::before {
  content: "\e562"; }

.fa-school::before {
  content: "\f549"; }

.fa-igloo::before {
  content: "\f7ae"; }

.fa-joint::before {
  content: "\f595"; }

.fa-angle-right::before {
  content: "\f105"; }

.fa-horse::before {
  content: "\f6f0"; }

.fa-q::before {
  content: "\51"; }

.fa-g::before {
  content: "\47"; }

.fa-notes-medical::before {
  content: "\f481"; }

.fa-temperature-half::before {
  content: "\f2c9"; }

.fa-temperature-2::before {
  content: "\f2c9"; }

.fa-thermometer-2::before {
  content: "\f2c9"; }

.fa-thermometer-half::before {
  content: "\f2c9"; }

.fa-dong-sign::before {
  content: "\e169"; }

.fa-capsules::before {
  content: "\f46b"; }

.fa-poo-storm::before {
  content: "\f75a"; }

.fa-poo-bolt::before {
  content: "\f75a"; }

.fa-face-frown-open::before {
  content: "\f57a"; }

.fa-frown-open::before {
  content: "\f57a"; }

.fa-hand-point-up::before {
  content: "\f0a6"; }

.fa-money-bill::before {
  content: "\f0d6"; }

.fa-bookmark::before {
  content: "\f02e"; }

.fa-align-justify::before {
  content: "\f039"; }

.fa-umbrella-beach::before {
  content: "\f5ca"; }

.fa-helmet-un::before {
  content: "\e503"; }

.fa-bullseye::before {
  content: "\f140"; }

.fa-bacon::before {
  content: "\f7e5"; }

.fa-hand-point-down::before {
  content: "\f0a7"; }

.fa-arrow-up-from-bracket::before {
  content: "\e09a"; }

.fa-folder::before {
  content: "\f07b"; }

.fa-folder-blank::before {
  content: "\f07b"; }

.fa-file-waveform::before {
  content: "\f478"; }

.fa-file-medical-alt::before {
  content: "\f478"; }

.fa-radiation::before {
  content: "\f7b9"; }

.fa-chart-simple::before {
  content: "\e473"; }

.fa-mars-stroke::before {
  content: "\f229"; }

.fa-vial::before {
  content: "\f492"; }

.fa-gauge::before {
  content: "\f624"; }

.fa-dashboard::before {
  content: "\f624"; }

.fa-gauge-med::before {
  content: "\f624"; }

.fa-tachometer-alt-average::before {
  content: "\f624"; }

.fa-wand-magic-sparkles::before {
  content: "\e2ca"; }

.fa-magic-wand-sparkles::before {
  content: "\e2ca"; }

.fa-e::before {
  content: "\45"; }

.fa-pen-clip::before {
  content: "\f305"; }

.fa-pen-alt::before {
  content: "\f305"; }

.fa-bridge-circle-exclamation::before {
  content: "\e4ca"; }

.fa-user::before {
  content: "\f007"; }

.fa-school-circle-check::before {
  content: "\e56b"; }

.fa-dumpster::before {
  content: "\f793"; }

.fa-van-shuttle::before {
  content: "\f5b6"; }

.fa-shuttle-van::before {
  content: "\f5b6"; }

.fa-building-user::before {
  content: "\e4da"; }

.fa-square-caret-left::before {
  content: "\f191"; }

.fa-caret-square-left::before {
  content: "\f191"; }

.fa-highlighter::before {
  content: "\f591"; }

.fa-key::before {
  content: "\f084"; }

.fa-bullhorn::before {
  content: "\f0a1"; }

.fa-globe::before {
  content: "\f0ac"; }

.fa-synagogue::before {
  content: "\f69b"; }

.fa-person-half-dress::before {
  content: "\e548"; }

.fa-road-bridge::before {
  content: "\e563"; }

.fa-location-arrow::before {
  content: "\f124"; }

.fa-c::before {
  content: "\43"; }

.fa-tablet-button::before {
  content: "\f10a"; }

.fa-building-lock::before {
  content: "\e4d6"; }

.fa-pizza-slice::before {
  content: "\f818"; }

.fa-money-bill-wave::before {
  content: "\f53a"; }

.fa-chart-area::before {
  content: "\f1fe"; }

.fa-area-chart::before {
  content: "\f1fe"; }

.fa-house-flag::before {
  content: "\e50d"; }

.fa-person-circle-minus::before {
  content: "\e540"; }

.fa-ban::before {
  content: "\f05e"; }

.fa-cancel::before {
  content: "\f05e"; }

.fa-camera-rotate::before {
  content: "\e0d8"; }

.fa-spray-can-sparkles::before {
  content: "\f5d0"; }

.fa-air-freshener::before {
  content: "\f5d0"; }

.fa-star::before {
  content: "\f005"; }

.fa-repeat::before {
  content: "\f363"; }

.fa-cross::before {
  content: "\f654"; }

.fa-box::before {
  content: "\f466"; }

.fa-venus-mars::before {
  content: "\f228"; }

.fa-arrow-pointer::before {
  content: "\f245"; }

.fa-mouse-pointer::before {
  content: "\f245"; }

.fa-maximize::before {
  content: "\f31e"; }

.fa-expand-arrows-alt::before {
  content: "\f31e"; }

.fa-charging-station::before {
  content: "\f5e7"; }

.fa-shapes::before {
  content: "\f61f"; }

.fa-triangle-circle-square::before {
  content: "\f61f"; }

.fa-shuffle::before {
  content: "\f074"; }

.fa-random::before {
  content: "\f074"; }

.fa-person-running::before {
  content: "\f70c"; }

.fa-running::before {
  content: "\f70c"; }

.fa-mobile-retro::before {
  content: "\e527"; }

.fa-grip-lines-vertical::before {
  content: "\f7a5"; }

.fa-spider::before {
  content: "\f717"; }

.fa-hands-bound::before {
  content: "\e4f9"; }

.fa-file-invoice-dollar::before {
  content: "\f571"; }

.fa-plane-circle-exclamation::before {
  content: "\e556"; }

.fa-x-ray::before {
  content: "\f497"; }

.fa-spell-check::before {
  content: "\f891"; }

.fa-slash::before {
  content: "\f715"; }

.fa-computer-mouse::before {
  content: "\f8cc"; }

.fa-mouse::before {
  content: "\f8cc"; }

.fa-arrow-right-to-bracket::before {
  content: "\f090"; }

.fa-sign-in::before {
  content: "\f090"; }

.fa-shop-slash::before {
  content: "\e070"; }

.fa-store-alt-slash::before {
  content: "\e070"; }

.fa-server::before {
  content: "\f233"; }

.fa-virus-covid-slash::before {
  content: "\e4a9"; }

.fa-shop-lock::before {
  content: "\e4a5"; }

.fa-hourglass-start::before {
  content: "\f251"; }

.fa-hourglass-1::before {
  content: "\f251"; }

.fa-blender-phone::before {
  content: "\f6b6"; }

.fa-building-wheat::before {
  content: "\e4db"; }

.fa-person-breastfeeding::before {
  content: "\e53a"; }

.fa-right-to-bracket::before {
  content: "\f2f6"; }

.fa-sign-in-alt::before {
  content: "\f2f6"; }

.fa-venus::before {
  content: "\f221"; }

.fa-passport::before {
  content: "\f5ab"; }

.fa-heart-pulse::before {
  content: "\f21e"; }

.fa-heartbeat::before {
  content: "\f21e"; }

.fa-people-carry-box::before {
  content: "\f4ce"; }

.fa-people-carry::before {
  content: "\f4ce"; }

.fa-temperature-high::before {
  content: "\f769"; }

.fa-microchip::before {
  content: "\f2db"; }

.fa-crown::before {
  content: "\f521"; }

.fa-weight-hanging::before {
  content: "\f5cd"; }

.fa-xmarks-lines::before {
  content: "\e59a"; }

.fa-file-prescription::before {
  content: "\f572"; }

.fa-weight-scale::before {
  content: "\f496"; }

.fa-weight::before {
  content: "\f496"; }

.fa-user-group::before {
  content: "\f500"; }

.fa-user-friends::before {
  content: "\f500"; }

.fa-arrow-up-a-z::before {
  content: "\f15e"; }

.fa-sort-alpha-up::before {
  content: "\f15e"; }

.fa-chess-knight::before {
  content: "\f441"; }

.fa-face-laugh-squint::before {
  content: "\f59b"; }

.fa-laugh-squint::before {
  content: "\f59b"; }

.fa-wheelchair::before {
  content: "\f193"; }

.fa-circle-arrow-up::before {
  content: "\f0aa"; }

.fa-arrow-circle-up::before {
  content: "\f0aa"; }

.fa-toggle-on::before {
  content: "\f205"; }

.fa-person-walking::before {
  content: "\f554"; }

.fa-walking::before {
  content: "\f554"; }

.fa-l::before {
  content: "\4c"; }

.fa-fire::before {
  content: "\f06d"; }

.fa-bed-pulse::before {
  content: "\f487"; }

.fa-procedures::before {
  content: "\f487"; }

.fa-shuttle-space::before {
  content: "\f197"; }

.fa-space-shuttle::before {
  content: "\f197"; }

.fa-face-laugh::before {
  content: "\f599"; }

.fa-laugh::before {
  content: "\f599"; }

.fa-folder-open::before {
  content: "\f07c"; }

.fa-heart-circle-plus::before {
  content: "\e500"; }

.fa-code-fork::before {
  content: "\e13b"; }

.fa-city::before {
  content: "\f64f"; }

.fa-microphone-lines::before {
  content: "\f3c9"; }

.fa-microphone-alt::before {
  content: "\f3c9"; }

.fa-pepper-hot::before {
  content: "\f816"; }

.fa-unlock::before {
  content: "\f09c"; }

.fa-colon-sign::before {
  content: "\e140"; }

.fa-headset::before {
  content: "\f590"; }

.fa-store-slash::before {
  content: "\e071"; }

.fa-road-circle-xmark::before {
  content: "\e566"; }

.fa-user-minus::before {
  content: "\f503"; }

.fa-mars-stroke-up::before {
  content: "\f22a"; }

.fa-mars-stroke-v::before {
  content: "\f22a"; }

.fa-champagne-glasses::before {
  content: "\f79f"; }

.fa-glass-cheers::before {
  content: "\f79f"; }

.fa-clipboard::before {
  content: "\f328"; }

.fa-house-circle-exclamation::before {
  content: "\e50a"; }

.fa-file-arrow-up::before {
  content: "\f574"; }

.fa-file-upload::before {
  content: "\f574"; }

.fa-wifi::before {
  content: "\f1eb"; }

.fa-wifi-3::before {
  content: "\f1eb"; }

.fa-wifi-strong::before {
  content: "\f1eb"; }

.fa-bath::before {
  content: "\f2cd"; }

.fa-bathtub::before {
  content: "\f2cd"; }

.fa-underline::before {
  content: "\f0cd"; }

.fa-user-pen::before {
  content: "\f4ff"; }

.fa-user-edit::before {
  content: "\f4ff"; }

.fa-signature::before {
  content: "\f5b7"; }

.fa-stroopwafel::before {
  content: "\f551"; }

.fa-bold::before {
  content: "\f032"; }

.fa-anchor-lock::before {
  content: "\e4ad"; }

.fa-building-ngo::before {
  content: "\e4d7"; }

.fa-manat-sign::before {
  content: "\e1d5"; }

.fa-not-equal::before {
  content: "\f53e"; }

.fa-border-top-left::before {
  content: "\f853"; }

.fa-border-style::before {
  content: "\f853"; }

.fa-map-location-dot::before {
  content: "\f5a0"; }

.fa-map-marked-alt::before {
  content: "\f5a0"; }

.fa-jedi::before {
  content: "\f669"; }

.fa-square-poll-vertical::before {
  content: "\f681"; }

.fa-poll::before {
  content: "\f681"; }

.fa-mug-hot::before {
  content: "\f7b6"; }

.fa-car-battery::before {
  content: "\f5df"; }

.fa-battery-car::before {
  content: "\f5df"; }

.fa-gift::before {
  content: "\f06b"; }

.fa-dice-two::before {
  content: "\f528"; }

.fa-chess-queen::before {
  content: "\f445"; }

.fa-glasses::before {
  content: "\f530"; }

.fa-chess-board::before {
  content: "\f43c"; }

.fa-building-circle-check::before {
  content: "\e4d2"; }

.fa-person-chalkboard::before {
  content: "\e53d"; }

.fa-mars-stroke-right::before {
  content: "\f22b"; }

.fa-mars-stroke-h::before {
  content: "\f22b"; }

.fa-hand-back-fist::before {
  content: "\f255"; }

.fa-hand-rock::before {
  content: "\f255"; }

.fa-square-caret-up::before {
  content: "\f151"; }

.fa-caret-square-up::before {
  content: "\f151"; }

.fa-cloud-showers-water::before {
  content: "\e4e4"; }

.fa-chart-bar::before {
  content: "\f080"; }

.fa-bar-chart::before {
  content: "\f080"; }

.fa-hands-bubbles::before {
  content: "\e05e"; }

.fa-hands-wash::before {
  content: "\e05e"; }

.fa-less-than-equal::before {
  content: "\f537"; }

.fa-train::before {
  content: "\f238"; }

.fa-eye-low-vision::before {
  content: "\f2a8"; }

.fa-low-vision::before {
  content: "\f2a8"; }

.fa-crow::before {
  content: "\f520"; }

.fa-sailboat::before {
  content: "\e445"; }

.fa-window-restore::before {
  content: "\f2d2"; }

.fa-square-plus::before {
  content: "\f0fe"; }

.fa-plus-square::before {
  content: "\f0fe"; }

.fa-torii-gate::before {
  content: "\f6a1"; }

.fa-frog::before {
  content: "\f52e"; }

.fa-bucket::before {
  content: "\e4cf"; }

.fa-image::before {
  content: "\f03e"; }

.fa-microphone::before {
  content: "\f130"; }

.fa-cow::before {
  content: "\f6c8"; }

.fa-caret-up::before {
  content: "\f0d8"; }

.fa-screwdriver::before {
  content: "\f54a"; }

.fa-folder-closed::before {
  content: "\e185"; }

.fa-house-tsunami::before {
  content: "\e515"; }

.fa-square-nfi::before {
  content: "\e576"; }

.fa-arrow-up-from-ground-water::before {
  content: "\e4b5"; }

.fa-martini-glass::before {
  content: "\f57b"; }

.fa-glass-martini-alt::before {
  content: "\f57b"; }

.fa-rotate-left::before {
  content: "\f2ea"; }

.fa-rotate-back::before {
  content: "\f2ea"; }

.fa-rotate-backward::before {
  content: "\f2ea"; }

.fa-undo-alt::before {
  content: "\f2ea"; }

.fa-table-columns::before {
  content: "\f0db"; }

.fa-columns::before {
  content: "\f0db"; }

.fa-lemon::before {
  content: "\f094"; }

.fa-head-side-mask::before {
  content: "\e063"; }

.fa-handshake::before {
  content: "\f2b5"; }

.fa-gem::before {
  content: "\f3a5"; }

.fa-dolly::before {
  content: "\f472"; }

.fa-dolly-box::before {
  content: "\f472"; }

.fa-smoking::before {
  content: "\f48d"; }

.fa-minimize::before {
  content: "\f78c"; }

.fa-compress-arrows-alt::before {
  content: "\f78c"; }

.fa-monument::before {
  content: "\f5a6"; }

.fa-snowplow::before {
  content: "\f7d2"; }

.fa-angles-right::before {
  content: "\f101"; }

.fa-angle-double-right::before {
  content: "\f101"; }

.fa-cannabis::before {
  content: "\f55f"; }

.fa-circle-play::before {
  content: "\f144"; }

.fa-play-circle::before {
  content: "\f144"; }

.fa-tablets::before {
  content: "\f490"; }

.fa-ethernet::before {
  content: "\f796"; }

.fa-euro-sign::before {
  content: "\f153"; }

.fa-eur::before {
  content: "\f153"; }

.fa-euro::before {
  content: "\f153"; }

.fa-chair::before {
  content: "\f6c0"; }

.fa-circle-check::before {
  content: "\f058"; }

.fa-check-circle::before {
  content: "\f058"; }

.fa-circle-stop::before {
  content: "\f28d"; }

.fa-stop-circle::before {
  content: "\f28d"; }

.fa-compass-drafting::before {
  content: "\f568"; }

.fa-drafting-compass::before {
  content: "\f568"; }

.fa-plate-wheat::before {
  content: "\e55a"; }

.fa-icicles::before {
  content: "\f7ad"; }

.fa-person-shelter::before {
  content: "\e54f"; }

.fa-neuter::before {
  content: "\f22c"; }

.fa-id-badge::before {
  content: "\f2c1"; }

.fa-marker::before {
  content: "\f5a1"; }

.fa-face-laugh-beam::before {
  content: "\f59a"; }

.fa-laugh-beam::before {
  content: "\f59a"; }

.fa-helicopter-symbol::before {
  content: "\e502"; }

.fa-universal-access::before {
  content: "\f29a"; }

.fa-circle-chevron-up::before {
  content: "\f139"; }

.fa-chevron-circle-up::before {
  content: "\f139"; }

.fa-lari-sign::before {
  content: "\e1c8"; }

.fa-volcano::before {
  content: "\f770"; }

.fa-person-walking-dashed-line-arrow-right::before {
  content: "\e553"; }

.fa-sterling-sign::before {
  content: "\f154"; }

.fa-gbp::before {
  content: "\f154"; }

.fa-pound-sign::before {
  content: "\f154"; }

.fa-viruses::before {
  content: "\e076"; }

.fa-square-person-confined::before {
  content: "\e577"; }

.fa-user-tie::before {
  content: "\f508"; }

.fa-arrow-down-long::before {
  content: "\f175"; }

.fa-long-arrow-down::before {
  content: "\f175"; }

.fa-tent-arrow-down-to-line::before {
  content: "\e57e"; }

.fa-certificate::before {
  content: "\f0a3"; }

.fa-reply-all::before {
  content: "\f122"; }

.fa-mail-reply-all::before {
  content: "\f122"; }

.fa-suitcase::before {
  content: "\f0f2"; }

.fa-person-skating::before {
  content: "\f7c5"; }

.fa-skating::before {
  content: "\f7c5"; }

.fa-filter-circle-dollar::before {
  content: "\f662"; }

.fa-funnel-dollar::before {
  content: "\f662"; }

.fa-camera-retro::before {
  content: "\f083"; }

.fa-circle-arrow-down::before {
  content: "\f0ab"; }

.fa-arrow-circle-down::before {
  content: "\f0ab"; }

.fa-file-import::before {
  content: "\f56f"; }

.fa-arrow-right-to-file::before {
  content: "\f56f"; }

.fa-square-arrow-up-right::before {
  content: "\f14c"; }

.fa-external-link-square::before {
  content: "\f14c"; }

.fa-box-open::before {
  content: "\f49e"; }

.fa-scroll::before {
  content: "\f70e"; }

.fa-spa::before {
  content: "\f5bb"; }

.fa-location-pin-lock::before {
  content: "\e51f"; }

.fa-pause::before {
  content: "\f04c"; }

.fa-hill-avalanche::before {
  content: "\e507"; }

.fa-temperature-empty::before {
  content: "\f2cb"; }

.fa-temperature-0::before {
  content: "\f2cb"; }

.fa-thermometer-0::before {
  content: "\f2cb"; }

.fa-thermometer-empty::before {
  content: "\f2cb"; }

.fa-bomb::before {
  content: "\f1e2"; }

.fa-registered::before {
  content: "\f25d"; }

.fa-address-card::before {
  content: "\f2bb"; }

.fa-contact-card::before {
  content: "\f2bb"; }

.fa-vcard::before {
  content: "\f2bb"; }

.fa-scale-unbalanced-flip::before {
  content: "\f516"; }

.fa-balance-scale-right::before {
  content: "\f516"; }

.fa-subscript::before {
  content: "\f12c"; }

.fa-diamond-turn-right::before {
  content: "\f5eb"; }

.fa-directions::before {
  content: "\f5eb"; }

.fa-burst::before {
  content: "\e4dc"; }

.fa-house-laptop::before {
  content: "\e066"; }

.fa-laptop-house::before {
  content: "\e066"; }

.fa-face-tired::before {
  content: "\f5c8"; }

.fa-tired::before {
  content: "\f5c8"; }

.fa-money-bills::before {
  content: "\e1f3"; }

.fa-smog::before {
  content: "\f75f"; }

.fa-crutch::before {
  content: "\f7f7"; }

.fa-cloud-arrow-up::before {
  content: "\f0ee"; }

.fa-cloud-upload::before {
  content: "\f0ee"; }

.fa-cloud-upload-alt::before {
  content: "\f0ee"; }

.fa-palette::before {
  content: "\f53f"; }

.fa-arrows-turn-right::before {
  content: "\e4c0"; }

.fa-vest::before {
  content: "\e085"; }

.fa-ferry::before {
  content: "\e4ea"; }

.fa-arrows-down-to-people::before {
  content: "\e4b9"; }

.fa-seedling::before {
  content: "\f4d8"; }

.fa-sprout::before {
  content: "\f4d8"; }

.fa-left-right::before {
  content: "\f337"; }

.fa-arrows-alt-h::before {
  content: "\f337"; }

.fa-boxes-packing::before {
  content: "\e4c7"; }

.fa-circle-arrow-left::before {
  content: "\f0a8"; }

.fa-arrow-circle-left::before {
  content: "\f0a8"; }

.fa-group-arrows-rotate::before {
  content: "\e4f6"; }

.fa-bowl-food::before {
  content: "\e4c6"; }

.fa-candy-cane::before {
  content: "\f786"; }

.fa-arrow-down-wide-short::before {
  content: "\f160"; }

.fa-sort-amount-asc::before {
  content: "\f160"; }

.fa-sort-amount-down::before {
  content: "\f160"; }

.fa-cloud-bolt::before {
  content: "\f76c"; }

.fa-thunderstorm::before {
  content: "\f76c"; }

.fa-text-slash::before {
  content: "\f87d"; }

.fa-remove-format::before {
  content: "\f87d"; }

.fa-face-smile-wink::before {
  content: "\f4da"; }

.fa-smile-wink::before {
  content: "\f4da"; }

.fa-file-word::before {
  content: "\f1c2"; }

.fa-file-powerpoint::before {
  content: "\f1c4"; }

.fa-arrows-left-right::before {
  content: "\f07e"; }

.fa-arrows-h::before {
  content: "\f07e"; }

.fa-house-lock::before {
  content: "\e510"; }

.fa-cloud-arrow-down::before {
  content: "\f0ed"; }

.fa-cloud-download::before {
  content: "\f0ed"; }

.fa-cloud-download-alt::before {
  content: "\f0ed"; }

.fa-children::before {
  content: "\e4e1"; }

.fa-chalkboard::before {
  content: "\f51b"; }

.fa-blackboard::before {
  content: "\f51b"; }

.fa-user-large-slash::before {
  content: "\f4fa"; }

.fa-user-alt-slash::before {
  content: "\f4fa"; }

.fa-envelope-open::before {
  content: "\f2b6"; }

.fa-handshake-simple-slash::before {
  content: "\e05f"; }

.fa-handshake-alt-slash::before {
  content: "\e05f"; }

.fa-mattress-pillow::before {
  content: "\e525"; }

.fa-guarani-sign::before {
  content: "\e19a"; }

.fa-arrows-rotate::before {
  content: "\f021"; }

.fa-refresh::before {
  content: "\f021"; }

.fa-sync::before {
  content: "\f021"; }

.fa-fire-extinguisher::before {
  content: "\f134"; }

.fa-cruzeiro-sign::before {
  content: "\e152"; }

.fa-greater-than-equal::before {
  content: "\f532"; }

.fa-shield-halved::before {
  content: "\f3ed"; }

.fa-shield-alt::before {
  content: "\f3ed"; }

.fa-book-atlas::before {
  content: "\f558"; }

.fa-atlas::before {
  content: "\f558"; }

.fa-virus::before {
  content: "\e074"; }

.fa-envelope-circle-check::before {
  content: "\e4e8"; }

.fa-layer-group::before {
  content: "\f5fd"; }

.fa-arrows-to-dot::before {
  content: "\e4be"; }

.fa-archway::before {
  content: "\f557"; }

.fa-heart-circle-check::before {
  content: "\e4fd"; }

.fa-house-chimney-crack::before {
  content: "\f6f1"; }

.fa-house-damage::before {
  content: "\f6f1"; }

.fa-file-zipper::before {
  content: "\f1c6"; }

.fa-file-archive::before {
  content: "\f1c6"; }

.fa-square::before {
  content: "\f0c8"; }

.fa-martini-glass-empty::before {
  content: "\f000"; }

.fa-glass-martini::before {
  content: "\f000"; }

.fa-couch::before {
  content: "\f4b8"; }

.fa-cedi-sign::before {
  content: "\e0df"; }

.fa-italic::before {
  content: "\f033"; }

.fa-church::before {
  content: "\f51d"; }

.fa-comments-dollar::before {
  content: "\f653"; }

.fa-democrat::before {
  content: "\f747"; }

.fa-z::before {
  content: "\5a"; }

.fa-person-skiing::before {
  content: "\f7c9"; }

.fa-skiing::before {
  content: "\f7c9"; }

.fa-road-lock::before {
  content: "\e567"; }

.fa-a::before {
  content: "\41"; }

.fa-temperature-arrow-down::before {
  content: "\e03f"; }

.fa-temperature-down::before {
  content: "\e03f"; }

.fa-feather-pointed::before {
  content: "\f56b"; }

.fa-feather-alt::before {
  content: "\f56b"; }

.fa-p::before {
  content: "\50"; }

.fa-snowflake::before {
  content: "\f2dc"; }

.fa-newspaper::before {
  content: "\f1ea"; }

.fa-rectangle-ad::before {
  content: "\f641"; }

.fa-ad::before {
  content: "\f641"; }

.fa-circle-arrow-right::before {
  content: "\f0a9"; }

.fa-arrow-circle-right::before {
  content: "\f0a9"; }

.fa-filter-circle-xmark::before {
  content: "\e17b"; }

.fa-locust::before {
  content: "\e520"; }

.fa-sort::before {
  content: "\f0dc"; }

.fa-unsorted::before {
  content: "\f0dc"; }

.fa-list-ol::before {
  content: "\f0cb"; }

.fa-list-1-2::before {
  content: "\f0cb"; }

.fa-list-numeric::before {
  content: "\f0cb"; }

.fa-person-dress-burst::before {
  content: "\e544"; }

.fa-money-check-dollar::before {
  content: "\f53d"; }

.fa-money-check-alt::before {
  content: "\f53d"; }

.fa-vector-square::before {
  content: "\f5cb"; }

.fa-bread-slice::before {
  content: "\f7ec"; }

.fa-language::before {
  content: "\f1ab"; }

.fa-face-kiss-wink-heart::before {
  content: "\f598"; }

.fa-kiss-wink-heart::before {
  content: "\f598"; }

.fa-filter::before {
  content: "\f0b0"; }

.fa-question::before {
  content: "\3f"; }

.fa-file-signature::before {
  content: "\f573"; }

.fa-up-down-left-right::before {
  content: "\f0b2"; }

.fa-arrows-alt::before {
  content: "\f0b2"; }

.fa-house-chimney-user::before {
  content: "\e065"; }

.fa-hand-holding-heart::before {
  content: "\f4be"; }

.fa-puzzle-piece::before {
  content: "\f12e"; }

.fa-money-check::before {
  content: "\f53c"; }

.fa-star-half-stroke::before {
  content: "\f5c0"; }

.fa-star-half-alt::before {
  content: "\f5c0"; }

.fa-code::before {
  content: "\f121"; }

.fa-whiskey-glass::before {
  content: "\f7a0"; }

.fa-glass-whiskey::before {
  content: "\f7a0"; }

.fa-building-circle-exclamation::before {
  content: "\e4d3"; }

.fa-magnifying-glass-chart::before {
  content: "\e522"; }

.fa-arrow-up-right-from-square::before {
  content: "\f08e"; }

.fa-external-link::before {
  content: "\f08e"; }

.fa-cubes-stacked::before {
  content: "\e4e6"; }

.fa-won-sign::before {
  content: "\f159"; }

.fa-krw::before {
  content: "\f159"; }

.fa-won::before {
  content: "\f159"; }

.fa-virus-covid::before {
  content: "\e4a8"; }

.fa-austral-sign::before {
  content: "\e0a9"; }

.fa-f::before {
  content: "\46"; }

.fa-leaf::before {
  content: "\f06c"; }

.fa-road::before {
  content: "\f018"; }

.fa-taxi::before {
  content: "\f1ba"; }

.fa-cab::before {
  content: "\f1ba"; }

.fa-person-circle-plus::before {
  content: "\e541"; }

.fa-chart-pie::before {
  content: "\f200"; }

.fa-pie-chart::before {
  content: "\f200"; }

.fa-bolt-lightning::before {
  content: "\e0b7"; }

.fa-sack-xmark::before {
  content: "\e56a"; }

.fa-file-excel::before {
  content: "\f1c3"; }

.fa-file-contract::before {
  content: "\f56c"; }

.fa-fish-fins::before {
  content: "\e4f2"; }

.fa-building-flag::before {
  content: "\e4d5"; }

.fa-face-grin-beam::before {
  content: "\f582"; }

.fa-grin-beam::before {
  content: "\f582"; }

.fa-object-ungroup::before {
  content: "\f248"; }

.fa-poop::before {
  content: "\f619"; }

.fa-location-pin::before {
  content: "\f041"; }

.fa-map-marker::before {
  content: "\f041"; }

.fa-kaaba::before {
  content: "\f66b"; }

.fa-toilet-paper::before {
  content: "\f71e"; }

.fa-helmet-safety::before {
  content: "\f807"; }

.fa-hard-hat::before {
  content: "\f807"; }

.fa-hat-hard::before {
  content: "\f807"; }

.fa-eject::before {
  content: "\f052"; }

.fa-circle-right::before {
  content: "\f35a"; }

.fa-arrow-alt-circle-right::before {
  content: "\f35a"; }

.fa-plane-circle-check::before {
  content: "\e555"; }

.fa-face-rolling-eyes::before {
  content: "\f5a5"; }

.fa-meh-rolling-eyes::before {
  content: "\f5a5"; }

.fa-object-group::before {
  content: "\f247"; }

.fa-chart-line::before {
  content: "\f201"; }

.fa-line-chart::before {
  content: "\f201"; }

.fa-mask-ventilator::before {
  content: "\e524"; }

.fa-arrow-right::before {
  content: "\f061"; }

.fa-signs-post::before {
  content: "\f277"; }

.fa-map-signs::before {
  content: "\f277"; }

.fa-cash-register::before {
  content: "\f788"; }

.fa-person-circle-question::before {
  content: "\e542"; }

.fa-h::before {
  content: "\48"; }

.fa-tarp::before {
  content: "\e57b"; }

.fa-screwdriver-wrench::before {
  content: "\f7d9"; }

.fa-tools::before {
  content: "\f7d9"; }

.fa-arrows-to-eye::before {
  content: "\e4bf"; }

.fa-plug-circle-bolt::before {
  content: "\e55b"; }

.fa-heart::before {
  content: "\f004"; }

.fa-mars-and-venus::before {
  content: "\f224"; }

.fa-house-user::before {
  content: "\e1b0"; }

.fa-home-user::before {
  content: "\e1b0"; }

.fa-dumpster-fire::before {
  content: "\f794"; }

.fa-house-crack::before {
  content: "\e3b1"; }

.fa-martini-glass-citrus::before {
  content: "\f561"; }

.fa-cocktail::before {
  content: "\f561"; }

.fa-face-surprise::before {
  content: "\f5c2"; }

.fa-surprise::before {
  content: "\f5c2"; }

.fa-bottle-water::before {
  content: "\e4c5"; }

.fa-circle-pause::before {
  content: "\f28b"; }

.fa-pause-circle::before {
  content: "\f28b"; }

.fa-toilet-paper-slash::before {
  content: "\e072"; }

.fa-apple-whole::before {
  content: "\f5d1"; }

.fa-apple-alt::before {
  content: "\f5d1"; }

.fa-kitchen-set::before {
  content: "\e51a"; }

.fa-r::before {
  content: "\52"; }

.fa-temperature-quarter::before {
  content: "\f2ca"; }

.fa-temperature-1::before {
  content: "\f2ca"; }

.fa-thermometer-1::before {
  content: "\f2ca"; }

.fa-thermometer-quarter::before {
  content: "\f2ca"; }

.fa-cube::before {
  content: "\f1b2"; }

.fa-bitcoin-sign::before {
  content: "\e0b4"; }

.fa-shield-dog::before {
  content: "\e573"; }

.fa-solar-panel::before {
  content: "\f5ba"; }

.fa-lock-open::before {
  content: "\f3c1"; }

.fa-elevator::before {
  content: "\e16d"; }

.fa-money-bill-transfer::before {
  content: "\e528"; }

.fa-money-bill-trend-up::before {
  content: "\e529"; }

.fa-house-flood-water-circle-arrow-right::before {
  content: "\e50f"; }

.fa-square-poll-horizontal::before {
  content: "\f682"; }

.fa-poll-h::before {
  content: "\f682"; }

.fa-circle::before {
  content: "\f111"; }

.fa-backward-fast::before {
  content: "\f049"; }

.fa-fast-backward::before {
  content: "\f049"; }

.fa-recycle::before {
  content: "\f1b8"; }

.fa-user-astronaut::before {
  content: "\f4fb"; }

.fa-plane-slash::before {
  content: "\e069"; }

.fa-trademark::before {
  content: "\f25c"; }

.fa-basketball::before {
  content: "\f434"; }

.fa-basketball-ball::before {
  content: "\f434"; }

.fa-satellite-dish::before {
  content: "\f7c0"; }

.fa-circle-up::before {
  content: "\f35b"; }

.fa-arrow-alt-circle-up::before {
  content: "\f35b"; }

.fa-mobile-screen-button::before {
  content: "\f3cd"; }

.fa-mobile-alt::before {
  content: "\f3cd"; }

.fa-volume-high::before {
  content: "\f028"; }

.fa-volume-up::before {
  content: "\f028"; }

.fa-users-rays::before {
  content: "\e593"; }

.fa-wallet::before {
  content: "\f555"; }

.fa-clipboard-check::before {
  content: "\f46c"; }

.fa-file-audio::before {
  content: "\f1c7"; }

.fa-burger::before {
  content: "\f805"; }

.fa-hamburger::before {
  content: "\f805"; }

.fa-wrench::before {
  content: "\f0ad"; }

.fa-bugs::before {
  content: "\e4d0"; }

.fa-rupee-sign::before {
  content: "\f156"; }

.fa-rupee::before {
  content: "\f156"; }

.fa-file-image::before {
  content: "\f1c5"; }

.fa-circle-question::before {
  content: "\f059"; }

.fa-question-circle::before {
  content: "\f059"; }

.fa-plane-departure::before {
  content: "\f5b0"; }

.fa-handshake-slash::before {
  content: "\e060"; }

.fa-book-bookmark::before {
  content: "\e0bb"; }

.fa-code-branch::before {
  content: "\f126"; }

.fa-hat-cowboy::before {
  content: "\f8c0"; }

.fa-bridge::before {
  content: "\e4c8"; }

.fa-phone-flip::before {
  content: "\f879"; }

.fa-phone-alt::before {
  content: "\f879"; }

.fa-truck-front::before {
  content: "\e2b7"; }

.fa-cat::before {
  content: "\f6be"; }

.fa-anchor-circle-exclamation::before {
  content: "\e4ab"; }

.fa-truck-field::before {
  content: "\e58d"; }

.fa-route::before {
  content: "\f4d7"; }

.fa-clipboard-question::before {
  content: "\e4e3"; }

.fa-panorama::before {
  content: "\e209"; }

.fa-comment-medical::before {
  content: "\f7f5"; }

.fa-teeth-open::before {
  content: "\f62f"; }

.fa-file-circle-minus::before {
  content: "\e4ed"; }

.fa-tags::before {
  content: "\f02c"; }

.fa-wine-glass::before {
  content: "\f4e3"; }

.fa-forward-fast::before {
  content: "\f050"; }

.fa-fast-forward::before {
  content: "\f050"; }

.fa-face-meh-blank::before {
  content: "\f5a4"; }

.fa-meh-blank::before {
  content: "\f5a4"; }

.fa-square-parking::before {
  content: "\f540"; }

.fa-parking::before {
  content: "\f540"; }

.fa-house-signal::before {
  content: "\e012"; }

.fa-bars-progress::before {
  content: "\f828"; }

.fa-tasks-alt::before {
  content: "\f828"; }

.fa-faucet-drip::before {
  content: "\e006"; }

.fa-cart-flatbed::before {
  content: "\f474"; }

.fa-dolly-flatbed::before {
  content: "\f474"; }

.fa-ban-smoking::before {
  content: "\f54d"; }

.fa-smoking-ban::before {
  content: "\f54d"; }

.fa-terminal::before {
  content: "\f120"; }

.fa-mobile-button::before {
  content: "\f10b"; }

.fa-house-medical-flag::before {
  content: "\e514"; }

.fa-basket-shopping::before {
  content: "\f291"; }

.fa-shopping-basket::before {
  content: "\f291"; }

.fa-tape::before {
  content: "\f4db"; }

.fa-bus-simple::before {
  content: "\f55e"; }

.fa-bus-alt::before {
  content: "\f55e"; }

.fa-eye::before {
  content: "\f06e"; }

.fa-face-sad-cry::before {
  content: "\f5b3"; }

.fa-sad-cry::before {
  content: "\f5b3"; }

.fa-audio-description::before {
  content: "\f29e"; }

.fa-person-military-to-person::before {
  content: "\e54c"; }

.fa-file-shield::before {
  content: "\e4f0"; }

.fa-user-slash::before {
  content: "\f506"; }

.fa-pen::before {
  content: "\f304"; }

.fa-tower-observation::before {
  content: "\e586"; }

.fa-file-code::before {
  content: "\f1c9"; }

.fa-signal::before {
  content: "\f012"; }

.fa-signal-5::before {
  content: "\f012"; }

.fa-signal-perfect::before {
  content: "\f012"; }

.fa-bus::before {
  content: "\f207"; }

.fa-heart-circle-xmark::before {
  content: "\e501"; }

.fa-house-chimney::before {
  content: "\e3af"; }

.fa-home-lg::before {
  content: "\e3af"; }

.fa-window-maximize::before {
  content: "\f2d0"; }

.fa-face-frown::before {
  content: "\f119"; }

.fa-frown::before {
  content: "\f119"; }

.fa-prescription::before {
  content: "\f5b1"; }

.fa-shop::before {
  content: "\f54f"; }

.fa-store-alt::before {
  content: "\f54f"; }

.fa-floppy-disk::before {
  content: "\f0c7"; }

.fa-save::before {
  content: "\f0c7"; }

.fa-vihara::before {
  content: "\f6a7"; }

.fa-scale-unbalanced::before {
  content: "\f515"; }

.fa-balance-scale-left::before {
  content: "\f515"; }

.fa-sort-up::before {
  content: "\f0de"; }

.fa-sort-asc::before {
  content: "\f0de"; }

.fa-comment-dots::before {
  content: "\f4ad"; }

.fa-commenting::before {
  content: "\f4ad"; }

.fa-plant-wilt::before {
  content: "\e5aa"; }

.fa-diamond::before {
  content: "\f219"; }

.fa-face-grin-squint::before {
  content: "\f585"; }

.fa-grin-squint::before {
  content: "\f585"; }

.fa-hand-holding-dollar::before {
  content: "\f4c0"; }

.fa-hand-holding-usd::before {
  content: "\f4c0"; }

.fa-bacterium::before {
  content: "\e05a"; }

.fa-hand-pointer::before {
  content: "\f25a"; }

.fa-drum-steelpan::before {
  content: "\f56a"; }

.fa-hand-scissors::before {
  content: "\f257"; }

.fa-hands-praying::before {
  content: "\f684"; }

.fa-praying-hands::before {
  content: "\f684"; }

.fa-arrow-rotate-right::before {
  content: "\f01e"; }

.fa-arrow-right-rotate::before {
  content: "\f01e"; }

.fa-arrow-rotate-forward::before {
  content: "\f01e"; }

.fa-redo::before {
  content: "\f01e"; }

.fa-biohazard::before {
  content: "\f780"; }

.fa-location-crosshairs::before {
  content: "\f601"; }

.fa-location::before {
  content: "\f601"; }

.fa-mars-double::before {
  content: "\f227"; }

.fa-child-dress::before {
  content: "\e59c"; }

.fa-users-between-lines::before {
  content: "\e591"; }

.fa-lungs-virus::before {
  content: "\e067"; }

.fa-face-grin-tears::before {
  content: "\f588"; }

.fa-grin-tears::before {
  content: "\f588"; }

.fa-phone::before {
  content: "\f095"; }

.fa-calendar-xmark::before {
  content: "\f273"; }

.fa-calendar-times::before {
  content: "\f273"; }

.fa-child-reaching::before {
  content: "\e59d"; }

.fa-head-side-virus::before {
  content: "\e064"; }

.fa-user-gear::before {
  content: "\f4fe"; }

.fa-user-cog::before {
  content: "\f4fe"; }

.fa-arrow-up-1-9::before {
  content: "\f163"; }

.fa-sort-numeric-up::before {
  content: "\f163"; }

.fa-door-closed::before {
  content: "\f52a"; }

.fa-shield-virus::before {
  content: "\e06c"; }

.fa-dice-six::before {
  content: "\f526"; }

.fa-mosquito-net::before {
  content: "\e52c"; }

.fa-bridge-water::before {
  content: "\e4ce"; }

.fa-person-booth::before {
  content: "\f756"; }

.fa-text-width::before {
  content: "\f035"; }

.fa-hat-wizard::before {
  content: "\f6e8"; }

.fa-pen-fancy::before {
  content: "\f5ac"; }

.fa-person-digging::before {
  content: "\f85e"; }

.fa-digging::before {
  content: "\f85e"; }

.fa-trash::before {
  content: "\f1f8"; }

.fa-gauge-simple::before {
  content: "\f629"; }

.fa-gauge-simple-med::before {
  content: "\f629"; }

.fa-tachometer-average::before {
  content: "\f629"; }

.fa-book-medical::before {
  content: "\f7e6"; }

.fa-poo::before {
  content: "\f2fe"; }

.fa-quote-right::before {
  content: "\f10e"; }

.fa-quote-right-alt::before {
  content: "\f10e"; }

.fa-shirt::before {
  content: "\f553"; }

.fa-t-shirt::before {
  content: "\f553"; }

.fa-tshirt::before {
  content: "\f553"; }

.fa-cubes::before {
  content: "\f1b3"; }

.fa-divide::before {
  content: "\f529"; }

.fa-tenge-sign::before {
  content: "\f7d7"; }

.fa-tenge::before {
  content: "\f7d7"; }

.fa-headphones::before {
  content: "\f025"; }

.fa-hands-holding::before {
  content: "\f4c2"; }

.fa-hands-clapping::before {
  content: "\e1a8"; }

.fa-republican::before {
  content: "\f75e"; }

.fa-arrow-left::before {
  content: "\f060"; }

.fa-person-circle-xmark::before {
  content: "\e543"; }

.fa-ruler::before {
  content: "\f545"; }

.fa-align-left::before {
  content: "\f036"; }

.fa-dice-d6::before {
  content: "\f6d1"; }

.fa-restroom::before {
  content: "\f7bd"; }

.fa-j::before {
  content: "\4a"; }

.fa-users-viewfinder::before {
  content: "\e595"; }

.fa-file-video::before {
  content: "\f1c8"; }

.fa-up-right-from-square::before {
  content: "\f35d"; }

.fa-external-link-alt::before {
  content: "\f35d"; }

.fa-table-cells::before {
  content: "\f00a"; }

.fa-th::before {
  content: "\f00a"; }

.fa-file-pdf::before {
  content: "\f1c1"; }

.fa-book-bible::before {
  content: "\f647"; }

.fa-bible::before {
  content: "\f647"; }

.fa-o::before {
  content: "\4f"; }

.fa-suitcase-medical::before {
  content: "\f0fa"; }

.fa-medkit::before {
  content: "\f0fa"; }

.fa-user-secret::before {
  content: "\f21b"; }

.fa-otter::before {
  content: "\f700"; }

.fa-person-dress::before {
  content: "\f182"; }

.fa-female::before {
  content: "\f182"; }

.fa-comment-dollar::before {
  content: "\f651"; }

.fa-business-time::before {
  content: "\f64a"; }

.fa-briefcase-clock::before {
  content: "\f64a"; }

.fa-table-cells-large::before {
  content: "\f009"; }

.fa-th-large::before {
  content: "\f009"; }

.fa-book-tanakh::before {
  content: "\f827"; }

.fa-tanakh::before {
  content: "\f827"; }

.fa-phone-volume::before {
  content: "\f2a0"; }

.fa-volume-control-phone::before {
  content: "\f2a0"; }

.fa-hat-cowboy-side::before {
  content: "\f8c1"; }

.fa-clipboard-user::before {
  content: "\f7f3"; }

.fa-child::before {
  content: "\f1ae"; }

.fa-lira-sign::before {
  content: "\f195"; }

.fa-satellite::before {
  content: "\f7bf"; }

.fa-plane-lock::before {
  content: "\e558"; }

.fa-tag::before {
  content: "\f02b"; }

.fa-comment::before {
  content: "\f075"; }

.fa-cake-candles::before {
  content: "\f1fd"; }

.fa-birthday-cake::before {
  content: "\f1fd"; }

.fa-cake::before {
  content: "\f1fd"; }

.fa-envelope::before {
  content: "\f0e0"; }

.fa-angles-up::before {
  content: "\f102"; }

.fa-angle-double-up::before {
  content: "\f102"; }

.fa-paperclip::before {
  content: "\f0c6"; }

.fa-arrow-right-to-city::before {
  content: "\e4b3"; }

.fa-ribbon::before {
  content: "\f4d6"; }

.fa-lungs::before {
  content: "\f604"; }

.fa-arrow-up-9-1::before {
  content: "\f887"; }

.fa-sort-numeric-up-alt::before {
  content: "\f887"; }

.fa-litecoin-sign::before {
  content: "\e1d3"; }

.fa-border-none::before {
  content: "\f850"; }

.fa-circle-nodes::before {
  content: "\e4e2"; }

.fa-parachute-box::before {
  content: "\f4cd"; }

.fa-indent::before {
  content: "\f03c"; }

.fa-truck-field-un::before {
  content: "\e58e"; }

.fa-hourglass::before {
  content: "\f254"; }

.fa-hourglass-empty::before {
  content: "\f254"; }

.fa-mountain::before {
  content: "\f6fc"; }

.fa-user-doctor::before {
  content: "\f0f0"; }

.fa-user-md::before {
  content: "\f0f0"; }

.fa-circle-info::before {
  content: "\f05a"; }

.fa-info-circle::before {
  content: "\f05a"; }

.fa-cloud-meatball::before {
  content: "\f73b"; }

.fa-camera::before {
  content: "\f030"; }

.fa-camera-alt::before {
  content: "\f030"; }

.fa-square-virus::before {
  content: "\e578"; }

.fa-meteor::before {
  content: "\f753"; }

.fa-car-on::before {
  content: "\e4dd"; }

.fa-sleigh::before {
  content: "\f7cc"; }

.fa-arrow-down-1-9::before {
  content: "\f162"; }

.fa-sort-numeric-asc::before {
  content: "\f162"; }

.fa-sort-numeric-down::before {
  content: "\f162"; }

.fa-hand-holding-droplet::before {
  content: "\f4c1"; }

.fa-hand-holding-water::before {
  content: "\f4c1"; }

.fa-water::before {
  content: "\f773"; }

.fa-calendar-check::before {
  content: "\f274"; }

.fa-braille::before {
  content: "\f2a1"; }

.fa-prescription-bottle-medical::before {
  content: "\f486"; }

.fa-prescription-bottle-alt::before {
  content: "\f486"; }

.fa-landmark::before {
  content: "\f66f"; }

.fa-truck::before {
  content: "\f0d1"; }

.fa-crosshairs::before {
  content: "\f05b"; }

.fa-person-cane::before {
  content: "\e53c"; }

.fa-tent::before {
  content: "\e57d"; }

.fa-vest-patches::before {
  content: "\e086"; }

.fa-check-double::before {
  content: "\f560"; }

.fa-arrow-down-a-z::before {
  content: "\f15d"; }

.fa-sort-alpha-asc::before {
  content: "\f15d"; }

.fa-sort-alpha-down::before {
  content: "\f15d"; }

.fa-money-bill-wheat::before {
  content: "\e52a"; }

.fa-cookie::before {
  content: "\f563"; }

.fa-arrow-rotate-left::before {
  content: "\f0e2"; }

.fa-arrow-left-rotate::before {
  content: "\f0e2"; }

.fa-arrow-rotate-back::before {
  content: "\f0e2"; }

.fa-arrow-rotate-backward::before {
  content: "\f0e2"; }

.fa-undo::before {
  content: "\f0e2"; }

.fa-hard-drive::before {
  content: "\f0a0"; }

.fa-hdd::before {
  content: "\f0a0"; }

.fa-face-grin-squint-tears::before {
  content: "\f586"; }

.fa-grin-squint-tears::before {
  content: "\f586"; }

.fa-dumbbell::before {
  content: "\f44b"; }

.fa-rectangle-list::before {
  content: "\f022"; }

.fa-list-alt::before {
  content: "\f022"; }

.fa-tarp-droplet::before {
  content: "\e57c"; }

.fa-house-medical-circle-check::before {
  content: "\e511"; }

.fa-person-skiing-nordic::before {
  content: "\f7ca"; }

.fa-skiing-nordic::before {
  content: "\f7ca"; }

.fa-calendar-plus::before {
  content: "\f271"; }

.fa-plane-arrival::before {
  content: "\f5af"; }

.fa-circle-left::before {
  content: "\f359"; }

.fa-arrow-alt-circle-left::before {
  content: "\f359"; }

.fa-train-subway::before {
  content: "\f239"; }

.fa-subway::before {
  content: "\f239"; }

.fa-chart-gantt::before {
  content: "\e0e4"; }

.fa-indian-rupee-sign::before {
  content: "\e1bc"; }

.fa-indian-rupee::before {
  content: "\e1bc"; }

.fa-inr::before {
  content: "\e1bc"; }

.fa-crop-simple::before {
  content: "\f565"; }

.fa-crop-alt::before {
  content: "\f565"; }

.fa-money-bill-1::before {
  content: "\f3d1"; }

.fa-money-bill-alt::before {
  content: "\f3d1"; }

.fa-left-long::before {
  content: "\f30a"; }

.fa-long-arrow-alt-left::before {
  content: "\f30a"; }

.fa-dna::before {
  content: "\f471"; }

.fa-virus-slash::before {
  content: "\e075"; }

.fa-minus::before {
  content: "\f068"; }

.fa-subtract::before {
  content: "\f068"; }

.fa-chess::before {
  content: "\f439"; }

.fa-arrow-left-long::before {
  content: "\f177"; }

.fa-long-arrow-left::before {
  content: "\f177"; }

.fa-plug-circle-check::before {
  content: "\e55c"; }

.fa-street-view::before {
  content: "\f21d"; }

.fa-franc-sign::before {
  content: "\e18f"; }

.fa-volume-off::before {
  content: "\f026"; }

.fa-hands-asl-interpreting::before {
  content: "\f2a3"; }

.fa-american-sign-language-interpreting::before {
  content: "\f2a3"; }

.fa-asl-interpreting::before {
  content: "\f2a3"; }

.fa-hands-american-sign-language-interpreting::before {
  content: "\f2a3"; }

.fa-gear::before {
  content: "\f013"; }

.fa-cog::before {
  content: "\f013"; }

.fa-droplet-slash::before {
  content: "\f5c7"; }

.fa-tint-slash::before {
  content: "\f5c7"; }

.fa-mosque::before {
  content: "\f678"; }

.fa-mosquito::before {
  content: "\e52b"; }

.fa-star-of-david::before {
  content: "\f69a"; }

.fa-person-military-rifle::before {
  content: "\e54b"; }

.fa-cart-shopping::before {
  content: "\f07a"; }

.fa-shopping-cart::before {
  content: "\f07a"; }

.fa-vials::before {
  content: "\f493"; }

.fa-plug-circle-plus::before {
  content: "\e55f"; }

.fa-place-of-worship::before {
  content: "\f67f"; }

.fa-grip-vertical::before {
  content: "\f58e"; }

.fa-arrow-turn-up::before {
  content: "\f148"; }

.fa-level-up::before {
  content: "\f148"; }

.fa-u::before {
  content: "\55"; }

.fa-square-root-variable::before {
  content: "\f698"; }

.fa-square-root-alt::before {
  content: "\f698"; }

.fa-clock::before {
  content: "\f017"; }

.fa-clock-four::before {
  content: "\f017"; }

.fa-backward-step::before {
  content: "\f048"; }

.fa-step-backward::before {
  content: "\f048"; }

.fa-pallet::before {
  content: "\f482"; }

.fa-faucet::before {
  content: "\e005"; }

.fa-baseball-bat-ball::before {
  content: "\f432"; }

.fa-s::before {
  content: "\53"; }

.fa-timeline::before {
  content: "\e29c"; }

.fa-keyboard::before {
  content: "\f11c"; }

.fa-caret-down::before {
  content: "\f0d7"; }

.fa-house-chimney-medical::before {
  content: "\f7f2"; }

.fa-clinic-medical::before {
  content: "\f7f2"; }

.fa-temperature-three-quarters::before {
  content: "\f2c8"; }

.fa-temperature-3::before {
  content: "\f2c8"; }

.fa-thermometer-3::before {
  content: "\f2c8"; }

.fa-thermometer-three-quarters::before {
  content: "\f2c8"; }

.fa-mobile-screen::before {
  content: "\f3cf"; }

.fa-mobile-android-alt::before {
  content: "\f3cf"; }

.fa-plane-up::before {
  content: "\e22d"; }

.fa-piggy-bank::before {
  content: "\f4d3"; }

.fa-battery-half::before {
  content: "\f242"; }

.fa-battery-3::before {
  content: "\f242"; }

.fa-mountain-city::before {
  content: "\e52e"; }

.fa-coins::before {
  content: "\f51e"; }

.fa-khanda::before {
  content: "\f66d"; }

.fa-sliders::before {
  content: "\f1de"; }

.fa-sliders-h::before {
  content: "\f1de"; }

.fa-folder-tree::before {
  content: "\f802"; }

.fa-network-wired::before {
  content: "\f6ff"; }

.fa-map-pin::before {
  content: "\f276"; }

.fa-hamsa::before {
  content: "\f665"; }

.fa-cent-sign::before {
  content: "\e3f5"; }

.fa-flask::before {
  content: "\f0c3"; }

.fa-person-pregnant::before {
  content: "\e31e"; }

.fa-wand-sparkles::before {
  content: "\f72b"; }

.fa-ellipsis-vertical::before {
  content: "\f142"; }

.fa-ellipsis-v::before {
  content: "\f142"; }

.fa-ticket::before {
  content: "\f145"; }

.fa-power-off::before {
  content: "\f011"; }

.fa-right-long::before {
  content: "\f30b"; }

.fa-long-arrow-alt-right::before {
  content: "\f30b"; }

.fa-flag-usa::before {
  content: "\f74d"; }

.fa-laptop-file::before {
  content: "\e51d"; }

.fa-tty::before {
  content: "\f1e4"; }

.fa-teletype::before {
  content: "\f1e4"; }

.fa-diagram-next::before {
  content: "\e476"; }

.fa-person-rifle::before {
  content: "\e54e"; }

.fa-house-medical-circle-exclamation::before {
  content: "\e512"; }

.fa-closed-captioning::before {
  content: "\f20a"; }

.fa-person-hiking::before {
  content: "\f6ec"; }

.fa-hiking::before {
  content: "\f6ec"; }

.fa-venus-double::before {
  content: "\f226"; }

.fa-images::before {
  content: "\f302"; }

.fa-calculator::before {
  content: "\f1ec"; }

.fa-people-pulling::before {
  content: "\e535"; }

.fa-n::before {
  content: "\4e"; }

.fa-cable-car::before {
  content: "\f7da"; }

.fa-tram::before {
  content: "\f7da"; }

.fa-cloud-rain::before {
  content: "\f73d"; }

.fa-building-circle-xmark::before {
  content: "\e4d4"; }

.fa-ship::before {
  content: "\f21a"; }

.fa-arrows-down-to-line::before {
  content: "\e4b8"; }

.fa-download::before {
  content: "\f019"; }

.fa-face-grin::before {
  content: "\f580"; }

.fa-grin::before {
  content: "\f580"; }

.fa-delete-left::before {
  content: "\f55a"; }

.fa-backspace::before {
  content: "\f55a"; }

.fa-eye-dropper::before {
  content: "\f1fb"; }

.fa-eye-dropper-empty::before {
  content: "\f1fb"; }

.fa-eyedropper::before {
  content: "\f1fb"; }

.fa-file-circle-check::before {
  content: "\e5a0"; }

.fa-forward::before {
  content: "\f04e"; }

.fa-mobile::before {
  content: "\f3ce"; }

.fa-mobile-android::before {
  content: "\f3ce"; }

.fa-mobile-phone::before {
  content: "\f3ce"; }

.fa-face-meh::before {
  content: "\f11a"; }

.fa-meh::before {
  content: "\f11a"; }

.fa-align-center::before {
  content: "\f037"; }

.fa-book-skull::before {
  content: "\f6b7"; }

.fa-book-dead::before {
  content: "\f6b7"; }

.fa-id-card::before {
  content: "\f2c2"; }

.fa-drivers-license::before {
  content: "\f2c2"; }

.fa-outdent::before {
  content: "\f03b"; }

.fa-dedent::before {
  content: "\f03b"; }

.fa-heart-circle-exclamation::before {
  content: "\e4fe"; }

.fa-house::before {
  content: "\f015"; }

.fa-home::before {
  content: "\f015"; }

.fa-home-alt::before {
  content: "\f015"; }

.fa-home-lg-alt::before {
  content: "\f015"; }

.fa-calendar-week::before {
  content: "\f784"; }

.fa-laptop-medical::before {
  content: "\f812"; }

.fa-b::before {
  content: "\42"; }

.fa-file-medical::before {
  content: "\f477"; }

.fa-dice-one::before {
  content: "\f525"; }

.fa-kiwi-bird::before {
  content: "\f535"; }

.fa-arrow-right-arrow-left::before {
  content: "\f0ec"; }

.fa-exchange::before {
  content: "\f0ec"; }

.fa-rotate-right::before {
  content: "\f2f9"; }

.fa-redo-alt::before {
  content: "\f2f9"; }

.fa-rotate-forward::before {
  content: "\f2f9"; }

.fa-utensils::before {
  content: "\f2e7"; }

.fa-cutlery::before {
  content: "\f2e7"; }

.fa-arrow-up-wide-short::before {
  content: "\f161"; }

.fa-sort-amount-up::before {
  content: "\f161"; }

.fa-mill-sign::before {
  content: "\e1ed"; }

.fa-bowl-rice::before {
  content: "\e2eb"; }

.fa-skull::before {
  content: "\f54c"; }

.fa-tower-broadcast::before {
  content: "\f519"; }

.fa-broadcast-tower::before {
  content: "\f519"; }

.fa-truck-pickup::before {
  content: "\f63c"; }

.fa-up-long::before {
  content: "\f30c"; }

.fa-long-arrow-alt-up::before {
  content: "\f30c"; }

.fa-stop::before {
  content: "\f04d"; }

.fa-code-merge::before {
  content: "\f387"; }

.fa-upload::before {
  content: "\f093"; }

.fa-hurricane::before {
  content: "\f751"; }

.fa-mound::before {
  content: "\e52d"; }

.fa-toilet-portable::before {
  content: "\e583"; }

.fa-compact-disc::before {
  content: "\f51f"; }

.fa-file-arrow-down::before {
  content: "\f56d"; }

.fa-file-download::before {
  content: "\f56d"; }

.fa-caravan::before {
  content: "\f8ff"; }

.fa-shield-cat::before {
  content: "\e572"; }

.fa-bolt::before {
  content: "\f0e7"; }

.fa-zap::before {
  content: "\f0e7"; }

.fa-glass-water::before {
  content: "\e4f4"; }

.fa-oil-well::before {
  content: "\e532"; }

.fa-vault::before {
  content: "\e2c5"; }

.fa-mars::before {
  content: "\f222"; }

.fa-toilet::before {
  content: "\f7d8"; }

.fa-plane-circle-xmark::before {
  content: "\e557"; }

.fa-yen-sign::before {
  content: "\f157"; }

.fa-cny::before {
  content: "\f157"; }

.fa-jpy::before {
  content: "\f157"; }

.fa-rmb::before {
  content: "\f157"; }

.fa-yen::before {
  content: "\f157"; }

.fa-ruble-sign::before {
  content: "\f158"; }

.fa-rouble::before {
  content: "\f158"; }

.fa-rub::before {
  content: "\f158"; }

.fa-ruble::before {
  content: "\f158"; }

.fa-sun::before {
  content: "\f185"; }

.fa-guitar::before {
  content: "\f7a6"; }

.fa-face-laugh-wink::before {
  content: "\f59c"; }

.fa-laugh-wink::before {
  content: "\f59c"; }

.fa-horse-head::before {
  content: "\f7ab"; }

.fa-bore-hole::before {
  content: "\e4c3"; }

.fa-industry::before {
  content: "\f275"; }

.fa-circle-down::before {
  content: "\f358"; }

.fa-arrow-alt-circle-down::before {
  content: "\f358"; }

.fa-arrows-turn-to-dots::before {
  content: "\e4c1"; }

.fa-florin-sign::before {
  content: "\e184"; }

.fa-arrow-down-short-wide::before {
  content: "\f884"; }

.fa-sort-amount-desc::before {
  content: "\f884"; }

.fa-sort-amount-down-alt::before {
  content: "\f884"; }

.fa-less-than::before {
  content: "\3c"; }

.fa-angle-down::before {
  content: "\f107"; }

.fa-car-tunnel::before {
  content: "\e4de"; }

.fa-head-side-cough::before {
  content: "\e061"; }

.fa-grip-lines::before {
  content: "\f7a4"; }

.fa-thumbs-down::before {
  content: "\f165"; }

.fa-user-lock::before {
  content: "\f502"; }

.fa-arrow-right-long::before {
  content: "\f178"; }

.fa-long-arrow-right::before {
  content: "\f178"; }

.fa-anchor-circle-xmark::before {
  content: "\e4ac"; }

.fa-ellipsis::before {
  content: "\f141"; }

.fa-ellipsis-h::before {
  content: "\f141"; }

.fa-chess-pawn::before {
  content: "\f443"; }

.fa-kit-medical::before {
  content: "\f479"; }

.fa-first-aid::before {
  content: "\f479"; }

.fa-person-through-window::before {
  content: "\e5a9"; }

.fa-toolbox::before {
  content: "\f552"; }

.fa-hands-holding-circle::before {
  content: "\e4fb"; }

.fa-bug::before {
  content: "\f188"; }

.fa-credit-card::before {
  content: "\f09d"; }

.fa-credit-card-alt::before {
  content: "\f09d"; }

.fa-car::before {
  content: "\f1b9"; }

.fa-automobile::before {
  content: "\f1b9"; }

.fa-hand-holding-hand::before {
  content: "\e4f7"; }

.fa-book-open-reader::before {
  content: "\f5da"; }

.fa-book-reader::before {
  content: "\f5da"; }

.fa-mountain-sun::before {
  content: "\e52f"; }

.fa-arrows-left-right-to-line::before {
  content: "\e4ba"; }

.fa-dice-d20::before {
  content: "\f6cf"; }

.fa-truck-droplet::before {
  content: "\e58c"; }

.fa-file-circle-xmark::before {
  content: "\e5a1"; }

.fa-temperature-arrow-up::before {
  content: "\e040"; }

.fa-temperature-up::before {
  content: "\e040"; }

.fa-medal::before {
  content: "\f5a2"; }

.fa-bed::before {
  content: "\f236"; }

.fa-square-h::before {
  content: "\f0fd"; }

.fa-h-square::before {
  content: "\f0fd"; }

.fa-podcast::before {
  content: "\f2ce"; }

.fa-temperature-full::before {
  content: "\f2c7"; }

.fa-temperature-4::before {
  content: "\f2c7"; }

.fa-thermometer-4::before {
  content: "\f2c7"; }

.fa-thermometer-full::before {
  content: "\f2c7"; }

.fa-bell::before {
  content: "\f0f3"; }

.fa-superscript::before {
  content: "\f12b"; }

.fa-plug-circle-xmark::before {
  content: "\e560"; }

.fa-star-of-life::before {
  content: "\f621"; }

.fa-phone-slash::before {
  content: "\f3dd"; }

.fa-paint-roller::before {
  content: "\f5aa"; }

.fa-handshake-angle::before {
  content: "\f4c4"; }

.fa-hands-helping::before {
  content: "\f4c4"; }

.fa-location-dot::before {
  content: "\f3c5"; }

.fa-map-marker-alt::before {
  content: "\f3c5"; }

.fa-file::before {
  content: "\f15b"; }

.fa-greater-than::before {
  content: "\3e"; }

.fa-person-swimming::before {
  content: "\f5c4"; }

.fa-swimmer::before {
  content: "\f5c4"; }

.fa-arrow-down::before {
  content: "\f063"; }

.fa-droplet::before {
  content: "\f043"; }

.fa-tint::before {
  content: "\f043"; }

.fa-eraser::before {
  content: "\f12d"; }

.fa-earth-americas::before {
  content: "\f57d"; }

.fa-earth::before {
  content: "\f57d"; }

.fa-earth-america::before {
  content: "\f57d"; }

.fa-globe-americas::before {
  content: "\f57d"; }

.fa-person-burst::before {
  content: "\e53b"; }

.fa-dove::before {
  content: "\f4ba"; }

.fa-battery-empty::before {
  content: "\f244"; }

.fa-battery-0::before {
  content: "\f244"; }

.fa-socks::before {
  content: "\f696"; }

.fa-inbox::before {
  content: "\f01c"; }

.fa-section::before {
  content: "\e447"; }

.fa-gauge-high::before {
  content: "\f625"; }

.fa-tachometer-alt::before {
  content: "\f625"; }

.fa-tachometer-alt-fast::before {
  content: "\f625"; }

.fa-envelope-open-text::before {
  content: "\f658"; }

.fa-hospital::before {
  content: "\f0f8"; }

.fa-hospital-alt::before {
  content: "\f0f8"; }

.fa-hospital-wide::before {
  content: "\f0f8"; }

.fa-wine-bottle::before {
  content: "\f72f"; }

.fa-chess-rook::before {
  content: "\f447"; }

.fa-bars-staggered::before {
  content: "\f550"; }

.fa-reorder::before {
  content: "\f550"; }

.fa-stream::before {
  content: "\f550"; }

.fa-dharmachakra::before {
  content: "\f655"; }

.fa-hotdog::before {
  content: "\f80f"; }

.fa-person-walking-with-cane::before {
  content: "\f29d"; }

.fa-blind::before {
  content: "\f29d"; }

.fa-drum::before {
  content: "\f569"; }

.fa-ice-cream::before {
  content: "\f810"; }

.fa-heart-circle-bolt::before {
  content: "\e4fc"; }

.fa-fax::before {
  content: "\f1ac"; }

.fa-paragraph::before {
  content: "\f1dd"; }

.fa-check-to-slot::before {
  content: "\f772"; }

.fa-vote-yea::before {
  content: "\f772"; }

.fa-star-half::before {
  content: "\f089"; }

.fa-boxes-stacked::before {
  content: "\f468"; }

.fa-boxes::before {
  content: "\f468"; }

.fa-boxes-alt::before {
  content: "\f468"; }

.fa-link::before {
  content: "\f0c1"; }

.fa-chain::before {
  content: "\f0c1"; }

.fa-ear-listen::before {
  content: "\f2a2"; }

.fa-assistive-listening-systems::before {
  content: "\f2a2"; }

.fa-tree-city::before {
  content: "\e587"; }

.fa-play::before {
  content: "\f04b"; }

.fa-font::before {
  content: "\f031"; }

.fa-rupiah-sign::before {
  content: "\e23d"; }

.fa-magnifying-glass::before {
  content: "\f002"; }

.fa-search::before {
  content: "\f002"; }

.fa-table-tennis-paddle-ball::before {
  content: "\f45d"; }

.fa-ping-pong-paddle-ball::before {
  content: "\f45d"; }

.fa-table-tennis::before {
  content: "\f45d"; }

.fa-person-dots-from-line::before {
  content: "\f470"; }

.fa-diagnoses::before {
  content: "\f470"; }

.fa-trash-can-arrow-up::before {
  content: "\f82a"; }

.fa-trash-restore-alt::before {
  content: "\f82a"; }

.fa-naira-sign::before {
  content: "\e1f6"; }

.fa-cart-arrow-down::before {
  content: "\f218"; }

.fa-walkie-talkie::before {
  content: "\f8ef"; }

.fa-file-pen::before {
  content: "\f31c"; }

.fa-file-edit::before {
  content: "\f31c"; }

.fa-receipt::before {
  content: "\f543"; }

.fa-square-pen::before {
  content: "\f14b"; }

.fa-pen-square::before {
  content: "\f14b"; }

.fa-pencil-square::before {
  content: "\f14b"; }

.fa-suitcase-rolling::before {
  content: "\f5c1"; }

.fa-person-circle-exclamation::before {
  content: "\e53f"; }

.fa-chevron-down::before {
  content: "\f078"; }

.fa-battery-full::before {
  content: "\f240"; }

.fa-battery::before {
  content: "\f240"; }

.fa-battery-5::before {
  content: "\f240"; }

.fa-skull-crossbones::before {
  content: "\f714"; }

.fa-code-compare::before {
  content: "\e13a"; }

.fa-list-ul::before {
  content: "\f0ca"; }

.fa-list-dots::before {
  content: "\f0ca"; }

.fa-school-lock::before {
  content: "\e56f"; }

.fa-tower-cell::before {
  content: "\e585"; }

.fa-down-long::before {
  content: "\f309"; }

.fa-long-arrow-alt-down::before {
  content: "\f309"; }

.fa-ranking-star::before {
  content: "\e561"; }

.fa-chess-king::before {
  content: "\f43f"; }

.fa-person-harassing::before {
  content: "\e549"; }

.fa-brazilian-real-sign::before {
  content: "\e46c"; }

.fa-landmark-dome::before {
  content: "\f752"; }

.fa-landmark-alt::before {
  content: "\f752"; }

.fa-arrow-up::before {
  content: "\f062"; }

.fa-tv::before {
  content: "\f26c"; }

.fa-television::before {
  content: "\f26c"; }

.fa-tv-alt::before {
  content: "\f26c"; }

.fa-shrimp::before {
  content: "\e448"; }

.fa-list-check::before {
  content: "\f0ae"; }

.fa-tasks::before {
  content: "\f0ae"; }

.fa-jug-detergent::before {
  content: "\e519"; }

.fa-circle-user::before {
  content: "\f2bd"; }

.fa-user-circle::before {
  content: "\f2bd"; }

.fa-user-shield::before {
  content: "\f505"; }

.fa-wind::before {
  content: "\f72e"; }

.fa-car-burst::before {
  content: "\f5e1"; }

.fa-car-crash::before {
  content: "\f5e1"; }

.fa-y::before {
  content: "\59"; }

.fa-person-snowboarding::before {
  content: "\f7ce"; }

.fa-snowboarding::before {
  content: "\f7ce"; }

.fa-truck-fast::before {
  content: "\f48b"; }

.fa-shipping-fast::before {
  content: "\f48b"; }

.fa-fish::before {
  content: "\f578"; }

.fa-user-graduate::before {
  content: "\f501"; }

.fa-circle-half-stroke::before {
  content: "\f042"; }

.fa-adjust::before {
  content: "\f042"; }

.fa-clapperboard::before {
  content: "\e131"; }

.fa-circle-radiation::before {
  content: "\f7ba"; }

.fa-radiation-alt::before {
  content: "\f7ba"; }

.fa-baseball::before {
  content: "\f433"; }

.fa-baseball-ball::before {
  content: "\f433"; }

.fa-jet-fighter-up::before {
  content: "\e518"; }

.fa-diagram-project::before {
  content: "\f542"; }

.fa-project-diagram::before {
  content: "\f542"; }

.fa-copy::before {
  content: "\f0c5"; }

.fa-volume-xmark::before {
  content: "\f6a9"; }

.fa-volume-mute::before {
  content: "\f6a9"; }

.fa-volume-times::before {
  content: "\f6a9"; }

.fa-hand-sparkles::before {
  content: "\e05d"; }

.fa-grip::before {
  content: "\f58d"; }

.fa-grip-horizontal::before {
  content: "\f58d"; }

.fa-share-from-square::before {
  content: "\f14d"; }

.fa-share-square::before {
  content: "\f14d"; }

.fa-child-combatant::before {
  content: "\e4e0"; }

.fa-child-rifle::before {
  content: "\e4e0"; }

.fa-gun::before {
  content: "\e19b"; }

.fa-square-phone::before {
  content: "\f098"; }

.fa-phone-square::before {
  content: "\f098"; }

.fa-plus::before {
  content: "\2b"; }

.fa-add::before {
  content: "\2b"; }

.fa-expand::before {
  content: "\f065"; }

.fa-computer::before {
  content: "\e4e5"; }

.fa-xmark::before {
  content: "\f00d"; }

.fa-close::before {
  content: "\f00d"; }

.fa-multiply::before {
  content: "\f00d"; }

.fa-remove::before {
  content: "\f00d"; }

.fa-times::before {
  content: "\f00d"; }

.fa-arrows-up-down-left-right::before {
  content: "\f047"; }

.fa-arrows::before {
  content: "\f047"; }

.fa-chalkboard-user::before {
  content: "\f51c"; }

.fa-chalkboard-teacher::before {
  content: "\f51c"; }

.fa-peso-sign::before {
  content: "\e222"; }

.fa-building-shield::before {
  content: "\e4d8"; }

.fa-baby::before {
  content: "\f77c"; }

.fa-users-line::before {
  content: "\e592"; }

.fa-quote-left::before {
  content: "\f10d"; }

.fa-quote-left-alt::before {
  content: "\f10d"; }

.fa-tractor::before {
  content: "\f722"; }

.fa-trash-arrow-up::before {
  content: "\f829"; }

.fa-trash-restore::before {
  content: "\f829"; }

.fa-arrow-down-up-lock::before {
  content: "\e4b0"; }

.fa-lines-leaning::before {
  content: "\e51e"; }

.fa-ruler-combined::before {
  content: "\f546"; }

.fa-copyright::before {
  content: "\f1f9"; }

.fa-equals::before {
  content: "\3d"; }

.fa-blender::before {
  content: "\f517"; }

.fa-teeth::before {
  content: "\f62e"; }

.fa-shekel-sign::before {
  content: "\f20b"; }

.fa-ils::before {
  content: "\f20b"; }

.fa-shekel::before {
  content: "\f20b"; }

.fa-sheqel::before {
  content: "\f20b"; }

.fa-sheqel-sign::before {
  content: "\f20b"; }

.fa-map::before {
  content: "\f279"; }

.fa-rocket::before {
  content: "\f135"; }

.fa-photo-film::before {
  content: "\f87c"; }

.fa-photo-video::before {
  content: "\f87c"; }

.fa-folder-minus::before {
  content: "\f65d"; }

.fa-store::before {
  content: "\f54e"; }

.fa-arrow-trend-up::before {
  content: "\e098"; }

.fa-plug-circle-minus::before {
  content: "\e55e"; }

.fa-sign-hanging::before {
  content: "\f4d9"; }

.fa-sign::before {
  content: "\f4d9"; }

.fa-bezier-curve::before {
  content: "\f55b"; }

.fa-bell-slash::before {
  content: "\f1f6"; }

.fa-tablet::before {
  content: "\f3fb"; }

.fa-tablet-android::before {
  content: "\f3fb"; }

.fa-school-flag::before {
  content: "\e56e"; }

.fa-fill::before {
  content: "\f575"; }

.fa-angle-up::before {
  content: "\f106"; }

.fa-drumstick-bite::before {
  content: "\f6d7"; }

.fa-holly-berry::before {
  content: "\f7aa"; }

.fa-chevron-left::before {
  content: "\f053"; }

.fa-bacteria::before {
  content: "\e059"; }

.fa-hand-lizard::before {
  content: "\f258"; }

.fa-notdef::before {
  content: "\e1fe"; }

.fa-disease::before {
  content: "\f7fa"; }

.fa-briefcase-medical::before {
  content: "\f469"; }

.fa-genderless::before {
  content: "\f22d"; }

.fa-chevron-right::before {
  content: "\f054"; }

.fa-retweet::before {
  content: "\f079"; }

.fa-car-rear::before {
  content: "\f5de"; }

.fa-car-alt::before {
  content: "\f5de"; }

.fa-pump-soap::before {
  content: "\e06b"; }

.fa-video-slash::before {
  content: "\f4e2"; }

.fa-battery-quarter::before {
  content: "\f243"; }

.fa-battery-2::before {
  content: "\f243"; }

.fa-radio::before {
  content: "\f8d7"; }

.fa-baby-carriage::before {
  content: "\f77d"; }

.fa-carriage-baby::before {
  content: "\f77d"; }

.fa-traffic-light::before {
  content: "\f637"; }

.fa-thermometer::before {
  content: "\f491"; }

.fa-vr-cardboard::before {
  content: "\f729"; }

.fa-hand-middle-finger::before {
  content: "\f806"; }

.fa-percent::before {
  content: "\25"; }

.fa-percentage::before {
  content: "\25"; }

.fa-truck-moving::before {
  content: "\f4df"; }

.fa-glass-water-droplet::before {
  content: "\e4f5"; }

.fa-display::before {
  content: "\e163"; }

.fa-face-smile::before {
  content: "\f118"; }

.fa-smile::before {
  content: "\f118"; }

.fa-thumbtack::before {
  content: "\f08d"; }

.fa-thumb-tack::before {
  content: "\f08d"; }

.fa-trophy::before {
  content: "\f091"; }

.fa-person-praying::before {
  content: "\f683"; }

.fa-pray::before {
  content: "\f683"; }

.fa-hammer::before {
  content: "\f6e3"; }

.fa-hand-peace::before {
  content: "\f25b"; }

.fa-rotate::before {
  content: "\f2f1"; }

.fa-sync-alt::before {
  content: "\f2f1"; }

.fa-spinner::before {
  content: "\f110"; }

.fa-robot::before {
  content: "\f544"; }

.fa-peace::before {
  content: "\f67c"; }

.fa-gears::before {
  content: "\f085"; }

.fa-cogs::before {
  content: "\f085"; }

.fa-warehouse::before {
  content: "\f494"; }

.fa-arrow-up-right-dots::before {
  content: "\e4b7"; }

.fa-splotch::before {
  content: "\f5bc"; }

.fa-face-grin-hearts::before {
  content: "\f584"; }

.fa-grin-hearts::before {
  content: "\f584"; }

.fa-dice-four::before {
  content: "\f524"; }

.fa-sim-card::before {
  content: "\f7c4"; }

.fa-transgender::before {
  content: "\f225"; }

.fa-transgender-alt::before {
  content: "\f225"; }

.fa-mercury::before {
  content: "\f223"; }

.fa-arrow-turn-down::before {
  content: "\f149"; }

.fa-level-down::before {
  content: "\f149"; }

.fa-person-falling-burst::before {
  content: "\e547"; }

.fa-award::before {
  content: "\f559"; }

.fa-ticket-simple::before {
  content: "\f3ff"; }

.fa-ticket-alt::before {
  content: "\f3ff"; }

.fa-building::before {
  content: "\f1ad"; }

.fa-angles-left::before {
  content: "\f100"; }

.fa-angle-double-left::before {
  content: "\f100"; }

.fa-qrcode::before {
  content: "\f029"; }

.fa-clock-rotate-left::before {
  content: "\f1da"; }

.fa-history::before {
  content: "\f1da"; }

.fa-face-grin-beam-sweat::before {
  content: "\f583"; }

.fa-grin-beam-sweat::before {
  content: "\f583"; }

.fa-file-export::before {
  content: "\f56e"; }

.fa-arrow-right-from-file::before {
  content: "\f56e"; }

.fa-shield::before {
  content: "\f132"; }

.fa-shield-blank::before {
  content: "\f132"; }

.fa-arrow-up-short-wide::before {
  content: "\f885"; }

.fa-sort-amount-up-alt::before {
  content: "\f885"; }

.fa-house-medical::before {
  content: "\e3b2"; }

.fa-golf-ball-tee::before {
  content: "\f450"; }

.fa-golf-ball::before {
  content: "\f450"; }

.fa-circle-chevron-left::before {
  content: "\f137"; }

.fa-chevron-circle-left::before {
  content: "\f137"; }

.fa-house-chimney-window::before {
  content: "\e00d"; }

.fa-pen-nib::before {
  content: "\f5ad"; }

.fa-tent-arrow-turn-left::before {
  content: "\e580"; }

.fa-tents::before {
  content: "\e582"; }

.fa-wand-magic::before {
  content: "\f0d0"; }

.fa-magic::before {
  content: "\f0d0"; }

.fa-dog::before {
  content: "\f6d3"; }

.fa-carrot::before {
  content: "\f787"; }

.fa-moon::before {
  content: "\f186"; }

.fa-wine-glass-empty::before {
  content: "\f5ce"; }

.fa-wine-glass-alt::before {
  content: "\f5ce"; }

.fa-cheese::before {
  content: "\f7ef"; }

.fa-yin-yang::before {
  content: "\f6ad"; }

.fa-music::before {
  content: "\f001"; }

.fa-code-commit::before {
  content: "\f386"; }

.fa-temperature-low::before {
  content: "\f76b"; }

.fa-person-biking::before {
  content: "\f84a"; }

.fa-biking::before {
  content: "\f84a"; }

.fa-broom::before {
  content: "\f51a"; }

.fa-shield-heart::before {
  content: "\e574"; }

.fa-gopuram::before {
  content: "\f664"; }

.fa-earth-oceania::before {
  content: "\e47b"; }

.fa-globe-oceania::before {
  content: "\e47b"; }

.fa-square-xmark::before {
  content: "\f2d3"; }

.fa-times-square::before {
  content: "\f2d3"; }

.fa-xmark-square::before {
  content: "\f2d3"; }

.fa-hashtag::before {
  content: "\23"; }

.fa-up-right-and-down-left-from-center::before {
  content: "\f424"; }

.fa-expand-alt::before {
  content: "\f424"; }

.fa-oil-can::before {
  content: "\f613"; }

.fa-t::before {
  content: "\54"; }

.fa-hippo::before {
  content: "\f6ed"; }

.fa-chart-column::before {
  content: "\e0e3"; }

.fa-infinity::before {
  content: "\f534"; }

.fa-vial-circle-check::before {
  content: "\e596"; }

.fa-person-arrow-down-to-line::before {
  content: "\e538"; }

.fa-voicemail::before {
  content: "\f897"; }

.fa-fan::before {
  content: "\f863"; }

.fa-person-walking-luggage::before {
  content: "\e554"; }

.fa-up-down::before {
  content: "\f338"; }

.fa-arrows-alt-v::before {
  content: "\f338"; }

.fa-cloud-moon-rain::before {
  content: "\f73c"; }

.fa-calendar::before {
  content: "\f133"; }

.fa-trailer::before {
  content: "\e041"; }

.fa-bahai::before {
  content: "\f666"; }

.fa-haykal::before {
  content: "\f666"; }

.fa-sd-card::before {
  content: "\f7c2"; }

.fa-dragon::before {
  content: "\f6d5"; }

.fa-shoe-prints::before {
  content: "\f54b"; }

.fa-circle-plus::before {
  content: "\f055"; }

.fa-plus-circle::before {
  content: "\f055"; }

.fa-face-grin-tongue-wink::before {
  content: "\f58b"; }

.fa-grin-tongue-wink::before {
  content: "\f58b"; }

.fa-hand-holding::before {
  content: "\f4bd"; }

.fa-plug-circle-exclamation::before {
  content: "\e55d"; }

.fa-link-slash::before {
  content: "\f127"; }

.fa-chain-broken::before {
  content: "\f127"; }

.fa-chain-slash::before {
  content: "\f127"; }

.fa-unlink::before {
  content: "\f127"; }

.fa-clone::before {
  content: "\f24d"; }

.fa-person-walking-arrow-loop-left::before {
  content: "\e551"; }

.fa-arrow-up-z-a::before {
  content: "\f882"; }

.fa-sort-alpha-up-alt::before {
  content: "\f882"; }

.fa-fire-flame-curved::before {
  content: "\f7e4"; }

.fa-fire-alt::before {
  content: "\f7e4"; }

.fa-tornado::before {
  content: "\f76f"; }

.fa-file-circle-plus::before {
  content: "\e494"; }

.fa-book-quran::before {
  content: "\f687"; }

.fa-quran::before {
  content: "\f687"; }

.fa-anchor::before {
  content: "\f13d"; }

.fa-border-all::before {
  content: "\f84c"; }

.fa-face-angry::before {
  content: "\f556"; }

.fa-angry::before {
  content: "\f556"; }

.fa-cookie-bite::before {
  content: "\f564"; }

.fa-arrow-trend-down::before {
  content: "\e097"; }

.fa-rss::before {
  content: "\f09e"; }

.fa-feed::before {
  content: "\f09e"; }

.fa-draw-polygon::before {
  content: "\f5ee"; }

.fa-scale-balanced::before {
  content: "\f24e"; }

.fa-balance-scale::before {
  content: "\f24e"; }

.fa-gauge-simple-high::before {
  content: "\f62a"; }

.fa-tachometer::before {
  content: "\f62a"; }

.fa-tachometer-fast::before {
  content: "\f62a"; }

.fa-shower::before {
  content: "\f2cc"; }

.fa-desktop::before {
  content: "\f390"; }

.fa-desktop-alt::before {
  content: "\f390"; }

.fa-m::before {
  content: "\4d"; }

.fa-table-list::before {
  content: "\f00b"; }

.fa-th-list::before {
  content: "\f00b"; }

.fa-comment-sms::before {
  content: "\f7cd"; }

.fa-sms::before {
  content: "\f7cd"; }

.fa-book::before {
  content: "\f02d"; }

.fa-user-plus::before {
  content: "\f234"; }

.fa-check::before {
  content: "\f00c"; }

.fa-battery-three-quarters::before {
  content: "\f241"; }

.fa-battery-4::before {
  content: "\f241"; }

.fa-house-circle-check::before {
  content: "\e509"; }

.fa-angle-left::before {
  content: "\f104"; }

.fa-diagram-successor::before {
  content: "\e47a"; }

.fa-truck-arrow-right::before {
  content: "\e58b"; }

.fa-arrows-split-up-and-left::before {
  content: "\e4bc"; }

.fa-hand-fist::before {
  content: "\f6de"; }

.fa-fist-raised::before {
  content: "\f6de"; }

.fa-cloud-moon::before {
  content: "\f6c3"; }

.fa-briefcase::before {
  content: "\f0b1"; }

.fa-person-falling::before {
  content: "\e546"; }

.fa-image-portrait::before {
  content: "\f3e0"; }

.fa-portrait::before {
  content: "\f3e0"; }

.fa-user-tag::before {
  content: "\f507"; }

.fa-rug::before {
  content: "\e569"; }

.fa-earth-europe::before {
  content: "\f7a2"; }

.fa-globe-europe::before {
  content: "\f7a2"; }

.fa-cart-flatbed-suitcase::before {
  content: "\f59d"; }

.fa-luggage-cart::before {
  content: "\f59d"; }

.fa-rectangle-xmark::before {
  content: "\f410"; }

.fa-rectangle-times::before {
  content: "\f410"; }

.fa-times-rectangle::before {
  content: "\f410"; }

.fa-window-close::before {
  content: "\f410"; }

.fa-baht-sign::before {
  content: "\e0ac"; }

.fa-book-open::before {
  content: "\f518"; }

.fa-book-journal-whills::before {
  content: "\f66a"; }

.fa-journal-whills::before {
  content: "\f66a"; }

.fa-handcuffs::before {
  content: "\e4f8"; }

.fa-triangle-exclamation::before {
  content: "\f071"; }

.fa-exclamation-triangle::before {
  content: "\f071"; }

.fa-warning::before {
  content: "\f071"; }

.fa-database::before {
  content: "\f1c0"; }

.fa-share::before {
  content: "\f064"; }

.fa-arrow-turn-right::before {
  content: "\f064"; }

.fa-mail-forward::before {
  content: "\f064"; }

.fa-bottle-droplet::before {
  content: "\e4c4"; }

.fa-mask-face::before {
  content: "\e1d7"; }

.fa-hill-rockslide::before {
  content: "\e508"; }

.fa-right-left::before {
  content: "\f362"; }

.fa-exchange-alt::before {
  content: "\f362"; }

.fa-paper-plane::before {
  content: "\f1d8"; }

.fa-road-circle-exclamation::before {
  content: "\e565"; }

.fa-dungeon::before {
  content: "\f6d9"; }

.fa-align-right::before {
  content: "\f038"; }

.fa-money-bill-1-wave::before {
  content: "\f53b"; }

.fa-money-bill-wave-alt::before {
  content: "\f53b"; }

.fa-life-ring::before {
  content: "\f1cd"; }

.fa-hands::before {
  content: "\f2a7"; }

.fa-sign-language::before {
  content: "\f2a7"; }

.fa-signing::before {
  content: "\f2a7"; }

.fa-calendar-day::before {
  content: "\f783"; }

.fa-water-ladder::before {
  content: "\f5c5"; }

.fa-ladder-water::before {
  content: "\f5c5"; }

.fa-swimming-pool::before {
  content: "\f5c5"; }

.fa-arrows-up-down::before {
  content: "\f07d"; }

.fa-arrows-v::before {
  content: "\f07d"; }

.fa-face-grimace::before {
  content: "\f57f"; }

.fa-grimace::before {
  content: "\f57f"; }

.fa-wheelchair-move::before {
  content: "\e2ce"; }

.fa-wheelchair-alt::before {
  content: "\e2ce"; }

.fa-turn-down::before {
  content: "\f3be"; }

.fa-level-down-alt::before {
  content: "\f3be"; }

.fa-person-walking-arrow-right::before {
  content: "\e552"; }

.fa-square-envelope::before {
  content: "\f199"; }

.fa-envelope-square::before {
  content: "\f199"; }

.fa-dice::before {
  content: "\f522"; }

.fa-bowling-ball::before {
  content: "\f436"; }

.fa-brain::before {
  content: "\f5dc"; }

.fa-bandage::before {
  content: "\f462"; }

.fa-band-aid::before {
  content: "\f462"; }

.fa-calendar-minus::before {
  content: "\f272"; }

.fa-circle-xmark::before {
  content: "\f057"; }

.fa-times-circle::before {
  content: "\f057"; }

.fa-xmark-circle::before {
  content: "\f057"; }

.fa-gifts::before {
  content: "\f79c"; }

.fa-hotel::before {
  content: "\f594"; }

.fa-earth-asia::before {
  content: "\f57e"; }

.fa-globe-asia::before {
  content: "\f57e"; }

.fa-id-card-clip::before {
  content: "\f47f"; }

.fa-id-card-alt::before {
  content: "\f47f"; }

.fa-magnifying-glass-plus::before {
  content: "\f00e"; }

.fa-search-plus::before {
  content: "\f00e"; }

.fa-thumbs-up::before {
  content: "\f164"; }

.fa-user-clock::before {
  content: "\f4fd"; }

.fa-hand-dots::before {
  content: "\f461"; }

.fa-allergies::before {
  content: "\f461"; }

.fa-file-invoice::before {
  content: "\f570"; }

.fa-window-minimize::before {
  content: "\f2d1"; }

.fa-mug-saucer::before {
  content: "\f0f4"; }

.fa-coffee::before {
  content: "\f0f4"; }

.fa-brush::before {
  content: "\f55d"; }

.fa-mask::before {
  content: "\f6fa"; }

.fa-magnifying-glass-minus::before {
  content: "\f010"; }

.fa-search-minus::before {
  content: "\f010"; }

.fa-ruler-vertical::before {
  content: "\f548"; }

.fa-user-large::before {
  content: "\f406"; }

.fa-user-alt::before {
  content: "\f406"; }

.fa-train-tram::before {
  content: "\e5b4"; }

.fa-user-nurse::before {
  content: "\f82f"; }

.fa-syringe::before {
  content: "\f48e"; }

.fa-cloud-sun::before {
  content: "\f6c4"; }

.fa-stopwatch-20::before {
  content: "\e06f"; }

.fa-square-full::before {
  content: "\f45c"; }

.fa-magnet::before {
  content: "\f076"; }

.fa-jar::before {
  content: "\e516"; }

.fa-note-sticky::before {
  content: "\f249"; }

.fa-sticky-note::before {
  content: "\f249"; }

.fa-bug-slash::before {
  content: "\e490"; }

.fa-arrow-up-from-water-pump::before {
  content: "\e4b6"; }

.fa-bone::before {
  content: "\f5d7"; }

.fa-user-injured::before {
  content: "\f728"; }

.fa-face-sad-tear::before {
  content: "\f5b4"; }

.fa-sad-tear::before {
  content: "\f5b4"; }

.fa-plane::before {
  content: "\f072"; }

.fa-tent-arrows-down::before {
  content: "\e581"; }

.fa-exclamation::before {
  content: "\21"; }

.fa-arrows-spin::before {
  content: "\e4bb"; }

.fa-print::before {
  content: "\f02f"; }

.fa-turkish-lira-sign::before {
  content: "\e2bb"; }

.fa-try::before {
  content: "\e2bb"; }

.fa-turkish-lira::before {
  content: "\e2bb"; }

.fa-dollar-sign::before {
  content: "\24"; }

.fa-dollar::before {
  content: "\24"; }

.fa-usd::before {
  content: "\24"; }

.fa-x::before {
  content: "\58"; }

.fa-magnifying-glass-dollar::before {
  content: "\f688"; }

.fa-search-dollar::before {
  content: "\f688"; }

.fa-users-gear::before {
  content: "\f509"; }

.fa-users-cog::before {
  content: "\f509"; }

.fa-person-military-pointing::before {
  content: "\e54a"; }

.fa-building-columns::before {
  content: "\f19c"; }

.fa-bank::before {
  content: "\f19c"; }

.fa-institution::before {
  content: "\f19c"; }

.fa-museum::before {
  content: "\f19c"; }

.fa-university::before {
  content: "\f19c"; }

.fa-umbrella::before {
  content: "\f0e9"; }

.fa-trowel::before {
  content: "\e589"; }

.fa-d::before {
  content: "\44"; }

.fa-stapler::before {
  content: "\e5af"; }

.fa-masks-theater::before {
  content: "\f630"; }

.fa-theater-masks::before {
  content: "\f630"; }

.fa-kip-sign::before {
  content: "\e1c4"; }

.fa-hand-point-left::before {
  content: "\f0a5"; }

.fa-handshake-simple::before {
  content: "\f4c6"; }

.fa-handshake-alt::before {
  content: "\f4c6"; }

.fa-jet-fighter::before {
  content: "\f0fb"; }

.fa-fighter-jet::before {
  content: "\f0fb"; }

.fa-square-share-nodes::before {
  content: "\f1e1"; }

.fa-share-alt-square::before {
  content: "\f1e1"; }

.fa-barcode::before {
  content: "\f02a"; }

.fa-plus-minus::before {
  content: "\e43c"; }

.fa-video::before {
  content: "\f03d"; }

.fa-video-camera::before {
  content: "\f03d"; }

.fa-graduation-cap::before {
  content: "\f19d"; }

.fa-mortar-board::before {
  content: "\f19d"; }

.fa-hand-holding-medical::before {
  content: "\e05c"; }

.fa-person-circle-check::before {
  content: "\e53e"; }

.fa-turn-up::before {
  content: "\f3bf"; }

.fa-level-up-alt::before {
  content: "\f3bf"; }

.sr-only,
.fa-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0; }

.sr-only-focusable:not(:focus),
.fa-sr-only-focusable:not(:focus) {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0; }
:root, :host {
  --fa-style-family-brands: 'Font Awesome 6 Brands';
  --fa-font-brands: normal 400 1em/1 'Font Awesome 6 Brands'; }

@font-face {
  font-family: 'Font Awesome 6 Brands';
  font-style: normal;
  font-weight: 400;
  font-display: block;
  src: url("/dist/assets/fonts/fa-brands-400.woff2") format("woff2"), url("/dist/assets/fonts/fa-brands-400.ttf") format("truetype"); }

.fab,
.fa-brands {
  font-weight: 400; }

.fa-monero:before {
  content: "\f3d0"; }

.fa-hooli:before {
  content: "\f427"; }

.fa-yelp:before {
  content: "\f1e9"; }

.fa-cc-visa:before {
  content: "\f1f0"; }

.fa-lastfm:before {
  content: "\f202"; }

.fa-shopware:before {
  content: "\f5b5"; }

.fa-creative-commons-nc:before {
  content: "\f4e8"; }

.fa-aws:before {
  content: "\f375"; }

.fa-redhat:before {
  content: "\f7bc"; }

.fa-yoast:before {
  content: "\f2b1"; }

.fa-cloudflare:before {
  content: "\e07d"; }

.fa-ups:before {
  content: "\f7e0"; }

.fa-wpexplorer:before {
  content: "\f2de"; }

.fa-dyalog:before {
  content: "\f399"; }

.fa-bity:before {
  content: "\f37a"; }

.fa-stackpath:before {
  content: "\f842"; }

.fa-buysellads:before {
  content: "\f20d"; }

.fa-first-order:before {
  content: "\f2b0"; }

.fa-modx:before {
  content: "\f285"; }

.fa-guilded:before {
  content: "\e07e"; }

.fa-vnv:before {
  content: "\f40b"; }

.fa-square-js:before {
  content: "\f3b9"; }

.fa-js-square:before {
  content: "\f3b9"; }

.fa-microsoft:before {
  content: "\f3ca"; }

.fa-qq:before {
  content: "\f1d6"; }

.fa-orcid:before {
  content: "\f8d2"; }

.fa-java:before {
  content: "\f4e4"; }

.fa-invision:before {
  content: "\f7b0"; }

.fa-creative-commons-pd-alt:before {
  content: "\f4ed"; }

.fa-centercode:before {
  content: "\f380"; }

.fa-glide-g:before {
  content: "\f2a6"; }

.fa-drupal:before {
  content: "\f1a9"; }

.fa-hire-a-helper:before {
  content: "\f3b0"; }

.fa-creative-commons-by:before {
  content: "\f4e7"; }

.fa-unity:before {
  content: "\e049"; }

.fa-whmcs:before {
  content: "\f40d"; }

.fa-rocketchat:before {
  content: "\f3e8"; }

.fa-vk:before {
  content: "\f189"; }

.fa-untappd:before {
  content: "\f405"; }

.fa-mailchimp:before {
  content: "\f59e"; }

.fa-css3-alt:before {
  content: "\f38b"; }

.fa-square-reddit:before {
  content: "\f1a2"; }

.fa-reddit-square:before {
  content: "\f1a2"; }

.fa-vimeo-v:before {
  content: "\f27d"; }

.fa-contao:before {
  content: "\f26d"; }

.fa-square-font-awesome:before {
  content: "\e5ad"; }

.fa-deskpro:before {
  content: "\f38f"; }

.fa-sistrix:before {
  content: "\f3ee"; }

.fa-square-instagram:before {
  content: "\e055"; }

.fa-instagram-square:before {
  content: "\e055"; }

.fa-battle-net:before {
  content: "\f835"; }

.fa-the-red-yeti:before {
  content: "\f69d"; }

.fa-square-hacker-news:before {
  content: "\f3af"; }

.fa-hacker-news-square:before {
  content: "\f3af"; }

.fa-edge:before {
  content: "\f282"; }

.fa-threads:before {
  content: "\e618"; }

.fa-napster:before {
  content: "\f3d2"; }

.fa-square-snapchat:before {
  content: "\f2ad"; }

.fa-snapchat-square:before {
  content: "\f2ad"; }

.fa-google-plus-g:before {
  content: "\f0d5"; }

.fa-artstation:before {
  content: "\f77a"; }

.fa-markdown:before {
  content: "\f60f"; }

.fa-sourcetree:before {
  content: "\f7d3"; }

.fa-google-plus:before {
  content: "\f2b3"; }

.fa-diaspora:before {
  content: "\f791"; }

.fa-foursquare:before {
  content: "\f180"; }

.fa-stack-overflow:before {
  content: "\f16c"; }

.fa-github-alt:before {
  content: "\f113"; }

.fa-phoenix-squadron:before {
  content: "\f511"; }

.fa-pagelines:before {
  content: "\f18c"; }

.fa-algolia:before {
  content: "\f36c"; }

.fa-red-river:before {
  content: "\f3e3"; }

.fa-creative-commons-sa:before {
  content: "\f4ef"; }

.fa-safari:before {
  content: "\f267"; }

.fa-google:before {
  content: "\f1a0"; }

.fa-square-font-awesome-stroke:before {
  content: "\f35c"; }

.fa-font-awesome-alt:before {
  content: "\f35c"; }

.fa-atlassian:before {
  content: "\f77b"; }

.fa-linkedin-in:before {
  content: "\f0e1"; }

.fa-digital-ocean:before {
  content: "\f391"; }

.fa-nimblr:before {
  content: "\f5a8"; }

.fa-chromecast:before {
  content: "\f838"; }

.fa-evernote:before {
  content: "\f839"; }

.fa-hacker-news:before {
  content: "\f1d4"; }

.fa-creative-commons-sampling:before {
  content: "\f4f0"; }

.fa-adversal:before {
  content: "\f36a"; }

.fa-creative-commons:before {
  content: "\f25e"; }

.fa-watchman-monitoring:before {
  content: "\e087"; }

.fa-fonticons:before {
  content: "\f280"; }

.fa-weixin:before {
  content: "\f1d7"; }

.fa-shirtsinbulk:before {
  content: "\f214"; }

.fa-codepen:before {
  content: "\f1cb"; }

.fa-git-alt:before {
  content: "\f841"; }

.fa-lyft:before {
  content: "\f3c3"; }

.fa-rev:before {
  content: "\f5b2"; }

.fa-windows:before {
  content: "\f17a"; }

.fa-wizards-of-the-coast:before {
  content: "\f730"; }

.fa-square-viadeo:before {
  content: "\f2aa"; }

.fa-viadeo-square:before {
  content: "\f2aa"; }

.fa-meetup:before {
  content: "\f2e0"; }

.fa-centos:before {
  content: "\f789"; }

.fa-adn:before {
  content: "\f170"; }

.fa-cloudsmith:before {
  content: "\f384"; }

.fa-pied-piper-alt:before {
  content: "\f1a8"; }

.fa-square-dribbble:before {
  content: "\f397"; }

.fa-dribbble-square:before {
  content: "\f397"; }

.fa-codiepie:before {
  content: "\f284"; }

.fa-node:before {
  content: "\f419"; }

.fa-mix:before {
  content: "\f3cb"; }

.fa-steam:before {
  content: "\f1b6"; }

.fa-cc-apple-pay:before {
  content: "\f416"; }

.fa-scribd:before {
  content: "\f28a"; }

.fa-debian:before {
  content: "\e60b"; }

.fa-openid:before {
  content: "\f19b"; }

.fa-instalod:before {
  content: "\e081"; }

.fa-expeditedssl:before {
  content: "\f23e"; }

.fa-sellcast:before {
  content: "\f2da"; }

.fa-square-twitter:before {
  content: "\f081"; }

.fa-twitter-square:before {
  content: "\f081"; }

.fa-r-project:before {
  content: "\f4f7"; }

.fa-delicious:before {
  content: "\f1a5"; }

.fa-freebsd:before {
  content: "\f3a4"; }

.fa-vuejs:before {
  content: "\f41f"; }

.fa-accusoft:before {
  content: "\f369"; }

.fa-ioxhost:before {
  content: "\f208"; }

.fa-fonticons-fi:before {
  content: "\f3a2"; }

.fa-app-store:before {
  content: "\f36f"; }

.fa-cc-mastercard:before {
  content: "\f1f1"; }

.fa-itunes-note:before {
  content: "\f3b5"; }

.fa-golang:before {
  content: "\e40f"; }

.fa-kickstarter:before {
  content: "\f3bb"; }

.fa-grav:before {
  content: "\f2d6"; }

.fa-weibo:before {
  content: "\f18a"; }

.fa-uncharted:before {
  content: "\e084"; }

.fa-firstdraft:before {
  content: "\f3a1"; }

.fa-square-youtube:before {
  content: "\f431"; }

.fa-youtube-square:before {
  content: "\f431"; }

.fa-wikipedia-w:before {
  content: "\f266"; }

.fa-wpressr:before {
  content: "\f3e4"; }

.fa-rendact:before {
  content: "\f3e4"; }

.fa-angellist:before {
  content: "\f209"; }

.fa-galactic-republic:before {
  content: "\f50c"; }

.fa-nfc-directional:before {
  content: "\e530"; }

.fa-skype:before {
  content: "\f17e"; }

.fa-joget:before {
  content: "\f3b7"; }

.fa-fedora:before {
  content: "\f798"; }

.fa-stripe-s:before {
  content: "\f42a"; }

.fa-meta:before {
  content: "\e49b"; }

.fa-laravel:before {
  content: "\f3bd"; }

.fa-hotjar:before {
  content: "\f3b1"; }

.fa-bluetooth-b:before {
  content: "\f294"; }

.fa-sticker-mule:before {
  content: "\f3f7"; }

.fa-creative-commons-zero:before {
  content: "\f4f3"; }

.fa-hips:before {
  content: "\f452"; }

.fa-behance:before {
  content: "\f1b4"; }

.fa-reddit:before {
  content: "\f1a1"; }

.fa-discord:before {
  content: "\f392"; }

.fa-chrome:before {
  content: "\f268"; }

.fa-app-store-ios:before {
  content: "\f370"; }

.fa-cc-discover:before {
  content: "\f1f2"; }

.fa-wpbeginner:before {
  content: "\f297"; }

.fa-confluence:before {
  content: "\f78d"; }

.fa-mdb:before {
  content: "\f8ca"; }

.fa-dochub:before {
  content: "\f394"; }

.fa-accessible-icon:before {
  content: "\f368"; }

.fa-ebay:before {
  content: "\f4f4"; }

.fa-amazon:before {
  content: "\f270"; }

.fa-unsplash:before {
  content: "\e07c"; }

.fa-yarn:before {
  content: "\f7e3"; }

.fa-square-steam:before {
  content: "\f1b7"; }

.fa-steam-square:before {
  content: "\f1b7"; }

.fa-500px:before {
  content: "\f26e"; }

.fa-square-vimeo:before {
  content: "\f194"; }

.fa-vimeo-square:before {
  content: "\f194"; }

.fa-asymmetrik:before {
  content: "\f372"; }

.fa-font-awesome:before {
  content: "\f2b4"; }

.fa-font-awesome-flag:before {
  content: "\f2b4"; }

.fa-font-awesome-logo-full:before {
  content: "\f2b4"; }

.fa-gratipay:before {
  content: "\f184"; }

.fa-apple:before {
  content: "\f179"; }

.fa-hive:before {
  content: "\e07f"; }

.fa-gitkraken:before {
  content: "\f3a6"; }

.fa-keybase:before {
  content: "\f4f5"; }

.fa-apple-pay:before {
  content: "\f415"; }

.fa-padlet:before {
  content: "\e4a0"; }

.fa-amazon-pay:before {
  content: "\f42c"; }

.fa-square-github:before {
  content: "\f092"; }

.fa-github-square:before {
  content: "\f092"; }

.fa-stumbleupon:before {
  content: "\f1a4"; }

.fa-fedex:before {
  content: "\f797"; }

.fa-phoenix-framework:before {
  content: "\f3dc"; }

.fa-shopify:before {
  content: "\e057"; }

.fa-neos:before {
  content: "\f612"; }

.fa-square-threads:before {
  content: "\e619"; }

.fa-hackerrank:before {
  content: "\f5f7"; }

.fa-researchgate:before {
  content: "\f4f8"; }

.fa-swift:before {
  content: "\f8e1"; }

.fa-angular:before {
  content: "\f420"; }

.fa-speakap:before {
  content: "\f3f3"; }

.fa-angrycreative:before {
  content: "\f36e"; }

.fa-y-combinator:before {
  content: "\f23b"; }

.fa-empire:before {
  content: "\f1d1"; }

.fa-envira:before {
  content: "\f299"; }

.fa-square-gitlab:before {
  content: "\e5ae"; }

.fa-gitlab-square:before {
  content: "\e5ae"; }

.fa-studiovinari:before {
  content: "\f3f8"; }

.fa-pied-piper:before {
  content: "\f2ae"; }

.fa-wordpress:before {
  content: "\f19a"; }

.fa-product-hunt:before {
  content: "\f288"; }

.fa-firefox:before {
  content: "\f269"; }

.fa-linode:before {
  content: "\f2b8"; }

.fa-goodreads:before {
  content: "\f3a8"; }

.fa-square-odnoklassniki:before {
  content: "\f264"; }

.fa-odnoklassniki-square:before {
  content: "\f264"; }

.fa-jsfiddle:before {
  content: "\f1cc"; }

.fa-sith:before {
  content: "\f512"; }

.fa-themeisle:before {
  content: "\f2b2"; }

.fa-page4:before {
  content: "\f3d7"; }

.fa-hashnode:before {
  content: "\e499"; }

.fa-react:before {
  content: "\f41b"; }

.fa-cc-paypal:before {
  content: "\f1f4"; }

.fa-squarespace:before {
  content: "\f5be"; }

.fa-cc-stripe:before {
  content: "\f1f5"; }

.fa-creative-commons-share:before {
  content: "\f4f2"; }

.fa-bitcoin:before {
  content: "\f379"; }

.fa-keycdn:before {
  content: "\f3ba"; }

.fa-opera:before {
  content: "\f26a"; }

.fa-itch-io:before {
  content: "\f83a"; }

.fa-umbraco:before {
  content: "\f8e8"; }

.fa-galactic-senate:before {
  content: "\f50d"; }

.fa-ubuntu:before {
  content: "\f7df"; }

.fa-draft2digital:before {
  content: "\f396"; }

.fa-stripe:before {
  content: "\f429"; }

.fa-houzz:before {
  content: "\f27c"; }

.fa-gg:before {
  content: "\f260"; }

.fa-dhl:before {
  content: "\f790"; }

.fa-square-pinterest:before {
  content: "\f0d3"; }

.fa-pinterest-square:before {
  content: "\f0d3"; }

.fa-xing:before {
  content: "\f168"; }

.fa-blackberry:before {
  content: "\f37b"; }

.fa-creative-commons-pd:before {
  content: "\f4ec"; }

.fa-playstation:before {
  content: "\f3df"; }

.fa-quinscape:before {
  content: "\f459"; }

.fa-less:before {
  content: "\f41d"; }

.fa-blogger-b:before {
  content: "\f37d"; }

.fa-opencart:before {
  content: "\f23d"; }

.fa-vine:before {
  content: "\f1ca"; }

.fa-paypal:before {
  content: "\f1ed"; }

.fa-gitlab:before {
  content: "\f296"; }

.fa-typo3:before {
  content: "\f42b"; }

.fa-reddit-alien:before {
  content: "\f281"; }

.fa-yahoo:before {
  content: "\f19e"; }

.fa-dailymotion:before {
  content: "\e052"; }

.fa-affiliatetheme:before {
  content: "\f36b"; }

.fa-pied-piper-pp:before {
  content: "\f1a7"; }

.fa-bootstrap:before {
  content: "\f836"; }

.fa-odnoklassniki:before {
  content: "\f263"; }

.fa-nfc-symbol:before {
  content: "\e531"; }

.fa-ethereum:before {
  content: "\f42e"; }

.fa-speaker-deck:before {
  content: "\f83c"; }

.fa-creative-commons-nc-eu:before {
  content: "\f4e9"; }

.fa-patreon:before {
  content: "\f3d9"; }

.fa-avianex:before {
  content: "\f374"; }

.fa-ello:before {
  content: "\f5f1"; }

.fa-gofore:before {
  content: "\f3a7"; }

.fa-bimobject:before {
  content: "\f378"; }

.fa-facebook-f:before {
  content: "\f39e"; }

.fa-square-google-plus:before {
  content: "\f0d4"; }

.fa-google-plus-square:before {
  content: "\f0d4"; }

.fa-mandalorian:before {
  content: "\f50f"; }

.fa-first-order-alt:before {
  content: "\f50a"; }

.fa-osi:before {
  content: "\f41a"; }

.fa-google-wallet:before {
  content: "\f1ee"; }

.fa-d-and-d-beyond:before {
  content: "\f6ca"; }

.fa-periscope:before {
  content: "\f3da"; }

.fa-fulcrum:before {
  content: "\f50b"; }

.fa-cloudscale:before {
  content: "\f383"; }

.fa-forumbee:before {
  content: "\f211"; }

.fa-mizuni:before {
  content: "\f3cc"; }

.fa-schlix:before {
  content: "\f3ea"; }

.fa-square-xing:before {
  content: "\f169"; }

.fa-xing-square:before {
  content: "\f169"; }

.fa-bandcamp:before {
  content: "\f2d5"; }

.fa-wpforms:before {
  content: "\f298"; }

.fa-cloudversify:before {
  content: "\f385"; }

.fa-usps:before {
  content: "\f7e1"; }

.fa-megaport:before {
  content: "\f5a3"; }

.fa-magento:before {
  content: "\f3c4"; }

.fa-spotify:before {
  content: "\f1bc"; }

.fa-optin-monster:before {
  content: "\f23c"; }

.fa-fly:before {
  content: "\f417"; }

.fa-aviato:before {
  content: "\f421"; }

.fa-itunes:before {
  content: "\f3b4"; }

.fa-cuttlefish:before {
  content: "\f38c"; }

.fa-blogger:before {
  content: "\f37c"; }

.fa-flickr:before {
  content: "\f16e"; }

.fa-viber:before {
  content: "\f409"; }

.fa-soundcloud:before {
  content: "\f1be"; }

.fa-digg:before {
  content: "\f1a6"; }

.fa-tencent-weibo:before {
  content: "\f1d5"; }

.fa-symfony:before {
  content: "\f83d"; }

.fa-maxcdn:before {
  content: "\f136"; }

.fa-etsy:before {
  content: "\f2d7"; }

.fa-facebook-messenger:before {
  content: "\f39f"; }

.fa-audible:before {
  content: "\f373"; }

.fa-think-peaks:before {
  content: "\f731"; }

.fa-bilibili:before {
  content: "\e3d9"; }

.fa-erlang:before {
  content: "\f39d"; }

.fa-x-twitter:before {
  content: "\e61b"; }

.fa-cotton-bureau:before {
  content: "\f89e"; }

.fa-dashcube:before {
  content: "\f210"; }

.fa-42-group:before {
  content: "\e080"; }

.fa-innosoft:before {
  content: "\e080"; }

.fa-stack-exchange:before {
  content: "\f18d"; }

.fa-elementor:before {
  content: "\f430"; }

.fa-square-pied-piper:before {
  content: "\e01e"; }

.fa-pied-piper-square:before {
  content: "\e01e"; }

.fa-creative-commons-nd:before {
  content: "\f4eb"; }

.fa-palfed:before {
  content: "\f3d8"; }

.fa-superpowers:before {
  content: "\f2dd"; }

.fa-resolving:before {
  content: "\f3e7"; }

.fa-xbox:before {
  content: "\f412"; }

.fa-searchengin:before {
  content: "\f3eb"; }

.fa-tiktok:before {
  content: "\e07b"; }

.fa-square-facebook:before {
  content: "\f082"; }

.fa-facebook-square:before {
  content: "\f082"; }

.fa-renren:before {
  content: "\f18b"; }

.fa-linux:before {
  content: "\f17c"; }

.fa-glide:before {
  content: "\f2a5"; }

.fa-linkedin:before {
  content: "\f08c"; }

.fa-hubspot:before {
  content: "\f3b2"; }

.fa-deploydog:before {
  content: "\f38e"; }

.fa-twitch:before {
  content: "\f1e8"; }

.fa-ravelry:before {
  content: "\f2d9"; }

.fa-mixer:before {
  content: "\e056"; }

.fa-square-lastfm:before {
  content: "\f203"; }

.fa-lastfm-square:before {
  content: "\f203"; }

.fa-vimeo:before {
  content: "\f40a"; }

.fa-mendeley:before {
  content: "\f7b3"; }

.fa-uniregistry:before {
  content: "\f404"; }

.fa-figma:before {
  content: "\f799"; }

.fa-creative-commons-remix:before {
  content: "\f4ee"; }

.fa-cc-amazon-pay:before {
  content: "\f42d"; }

.fa-dropbox:before {
  content: "\f16b"; }

.fa-instagram:before {
  content: "\f16d"; }

.fa-cmplid:before {
  content: "\e360"; }

.fa-facebook:before {
  content: "\f09a"; }

.fa-gripfire:before {
  content: "\f3ac"; }

.fa-jedi-order:before {
  content: "\f50e"; }

.fa-uikit:before {
  content: "\f403"; }

.fa-fort-awesome-alt:before {
  content: "\f3a3"; }

.fa-phabricator:before {
  content: "\f3db"; }

.fa-ussunnah:before {
  content: "\f407"; }

.fa-earlybirds:before {
  content: "\f39a"; }

.fa-trade-federation:before {
  content: "\f513"; }

.fa-autoprefixer:before {
  content: "\f41c"; }

.fa-whatsapp:before {
  content: "\f232"; }

.fa-slideshare:before {
  content: "\f1e7"; }

.fa-google-play:before {
  content: "\f3ab"; }

.fa-viadeo:before {
  content: "\f2a9"; }

.fa-line:before {
  content: "\f3c0"; }

.fa-google-drive:before {
  content: "\f3aa"; }

.fa-servicestack:before {
  content: "\f3ec"; }

.fa-simplybuilt:before {
  content: "\f215"; }

.fa-bitbucket:before {
  content: "\f171"; }

.fa-imdb:before {
  content: "\f2d8"; }

.fa-deezer:before {
  content: "\e077"; }

.fa-raspberry-pi:before {
  content: "\f7bb"; }

.fa-jira:before {
  content: "\f7b1"; }

.fa-docker:before {
  content: "\f395"; }

.fa-screenpal:before {
  content: "\e570"; }

.fa-bluetooth:before {
  content: "\f293"; }

.fa-gitter:before {
  content: "\f426"; }

.fa-d-and-d:before {
  content: "\f38d"; }

.fa-microblog:before {
  content: "\e01a"; }

.fa-cc-diners-club:before {
  content: "\f24c"; }

.fa-gg-circle:before {
  content: "\f261"; }

.fa-pied-piper-hat:before {
  content: "\f4e5"; }

.fa-kickstarter-k:before {
  content: "\f3bc"; }

.fa-yandex:before {
  content: "\f413"; }

.fa-readme:before {
  content: "\f4d5"; }

.fa-html5:before {
  content: "\f13b"; }

.fa-sellsy:before {
  content: "\f213"; }

.fa-sass:before {
  content: "\f41e"; }

.fa-wirsindhandwerk:before {
  content: "\e2d0"; }

.fa-wsh:before {
  content: "\e2d0"; }

.fa-buromobelexperte:before {
  content: "\f37f"; }

.fa-salesforce:before {
  content: "\f83b"; }

.fa-octopus-deploy:before {
  content: "\e082"; }

.fa-medapps:before {
  content: "\f3c6"; }

.fa-ns8:before {
  content: "\f3d5"; }

.fa-pinterest-p:before {
  content: "\f231"; }

.fa-apper:before {
  content: "\f371"; }

.fa-fort-awesome:before {
  content: "\f286"; }

.fa-waze:before {
  content: "\f83f"; }

.fa-cc-jcb:before {
  content: "\f24b"; }

.fa-snapchat:before {
  content: "\f2ab"; }

.fa-snapchat-ghost:before {
  content: "\f2ab"; }

.fa-fantasy-flight-games:before {
  content: "\f6dc"; }

.fa-rust:before {
  content: "\e07a"; }

.fa-wix:before {
  content: "\f5cf"; }

.fa-square-behance:before {
  content: "\f1b5"; }

.fa-behance-square:before {
  content: "\f1b5"; }

.fa-supple:before {
  content: "\f3f9"; }

.fa-rebel:before {
  content: "\f1d0"; }

.fa-css3:before {
  content: "\f13c"; }

.fa-staylinked:before {
  content: "\f3f5"; }

.fa-kaggle:before {
  content: "\f5fa"; }

.fa-space-awesome:before {
  content: "\e5ac"; }

.fa-deviantart:before {
  content: "\f1bd"; }

.fa-cpanel:before {
  content: "\f388"; }

.fa-goodreads-g:before {
  content: "\f3a9"; }

.fa-square-git:before {
  content: "\f1d2"; }

.fa-git-square:before {
  content: "\f1d2"; }

.fa-square-tumblr:before {
  content: "\f174"; }

.fa-tumblr-square:before {
  content: "\f174"; }

.fa-trello:before {
  content: "\f181"; }

.fa-creative-commons-nc-jp:before {
  content: "\f4ea"; }

.fa-get-pocket:before {
  content: "\f265"; }

.fa-perbyte:before {
  content: "\e083"; }

.fa-grunt:before {
  content: "\f3ad"; }

.fa-weebly:before {
  content: "\f5cc"; }

.fa-connectdevelop:before {
  content: "\f20e"; }

.fa-leanpub:before {
  content: "\f212"; }

.fa-black-tie:before {
  content: "\f27e"; }

.fa-themeco:before {
  content: "\f5c6"; }

.fa-python:before {
  content: "\f3e2"; }

.fa-android:before {
  content: "\f17b"; }

.fa-bots:before {
  content: "\e340"; }

.fa-free-code-camp:before {
  content: "\f2c5"; }

.fa-hornbill:before {
  content: "\f592"; }

.fa-js:before {
  content: "\f3b8"; }

.fa-ideal:before {
  content: "\e013"; }

.fa-git:before {
  content: "\f1d3"; }

.fa-dev:before {
  content: "\f6cc"; }

.fa-sketch:before {
  content: "\f7c6"; }

.fa-yandex-international:before {
  content: "\f414"; }

.fa-cc-amex:before {
  content: "\f1f3"; }

.fa-uber:before {
  content: "\f402"; }

.fa-github:before {
  content: "\f09b"; }

.fa-php:before {
  content: "\f457"; }

.fa-alipay:before {
  content: "\f642"; }

.fa-youtube:before {
  content: "\f167"; }

.fa-skyatlas:before {
  content: "\f216"; }

.fa-firefox-browser:before {
  content: "\e007"; }

.fa-replyd:before {
  content: "\f3e6"; }

.fa-suse:before {
  content: "\f7d6"; }

.fa-jenkins:before {
  content: "\f3b6"; }

.fa-twitter:before {
  content: "\f099"; }

.fa-rockrms:before {
  content: "\f3e9"; }

.fa-pinterest:before {
  content: "\f0d2"; }

.fa-buffer:before {
  content: "\f837"; }

.fa-npm:before {
  content: "\f3d4"; }

.fa-yammer:before {
  content: "\f840"; }

.fa-btc:before {
  content: "\f15a"; }

.fa-dribbble:before {
  content: "\f17d"; }

.fa-stumbleupon-circle:before {
  content: "\f1a3"; }

.fa-internet-explorer:before {
  content: "\f26b"; }

.fa-stubber:before {
  content: "\e5c7"; }

.fa-telegram:before {
  content: "\f2c6"; }

.fa-telegram-plane:before {
  content: "\f2c6"; }

.fa-old-republic:before {
  content: "\f510"; }

.fa-odysee:before {
  content: "\e5c6"; }

.fa-square-whatsapp:before {
  content: "\f40c"; }

.fa-whatsapp-square:before {
  content: "\f40c"; }

.fa-node-js:before {
  content: "\f3d3"; }

.fa-edge-legacy:before {
  content: "\e078"; }

.fa-slack:before {
  content: "\f198"; }

.fa-slack-hash:before {
  content: "\f198"; }

.fa-medrt:before {
  content: "\f3c8"; }

.fa-usb:before {
  content: "\f287"; }

.fa-tumblr:before {
  content: "\f173"; }

.fa-vaadin:before {
  content: "\f408"; }

.fa-quora:before {
  content: "\f2c4"; }

.fa-square-x-twitter:before {
  content: "\e61a"; }

.fa-reacteurope:before {
  content: "\f75d"; }

.fa-medium:before {
  content: "\f23a"; }

.fa-medium-m:before {
  content: "\f23a"; }

.fa-amilia:before {
  content: "\f36d"; }

.fa-mixcloud:before {
  content: "\f289"; }

.fa-flipboard:before {
  content: "\f44d"; }

.fa-viacoin:before {
  content: "\f237"; }

.fa-critical-role:before {
  content: "\f6c9"; }

.fa-sitrox:before {
  content: "\e44a"; }

.fa-discourse:before {
  content: "\f393"; }

.fa-joomla:before {
  content: "\f1aa"; }

.fa-mastodon:before {
  content: "\f4f6"; }

.fa-airbnb:before {
  content: "\f834"; }

.fa-wolf-pack-battalion:before {
  content: "\f514"; }

.fa-buy-n-large:before {
  content: "\f8a6"; }

.fa-gulp:before {
  content: "\f3ae"; }

.fa-creative-commons-sampling-plus:before {
  content: "\f4f1"; }

.fa-strava:before {
  content: "\f428"; }

.fa-ember:before {
  content: "\f423"; }

.fa-canadian-maple-leaf:before {
  content: "\f785"; }

.fa-teamspeak:before {
  content: "\f4f9"; }

.fa-pushed:before {
  content: "\f3e1"; }

.fa-wordpress-simple:before {
  content: "\f411"; }

.fa-nutritionix:before {
  content: "\f3d6"; }

.fa-wodu:before {
  content: "\e088"; }

.fa-google-pay:before {
  content: "\e079"; }

.fa-intercom:before {
  content: "\f7af"; }

.fa-zhihu:before {
  content: "\f63f"; }

.fa-korvue:before {
  content: "\f42f"; }

.fa-pix:before {
  content: "\e43a"; }

.fa-steam-symbol:before {
  content: "\f3f6"; }
:root, :host {
  --fa-style-family-classic: 'Font Awesome 6 Free';
  --fa-font-regular: normal 400 1em/1 'Font Awesome 6 Free'; }

@font-face {
  font-family: 'Font Awesome 6 Free';
  font-style: normal;
  font-weight: 400;
  font-display: block;
  src: url("/dist/assets/fonts/fa-regular-400.woff2") format("woff2"), url("/dist/assets/fonts/fa-regular-400.ttf") format("truetype"); }

.far,
.fa-regular {
  font-weight: 400; }
:root, :host {
  --fa-style-family-classic: 'Font Awesome 6 Free';
  --fa-font-solid: normal 900 1em/1 'Font Awesome 6 Free'; }

@font-face {
  font-family: 'Font Awesome 6 Free';
  font-style: normal;
  font-weight: 900;
  font-display: block;
  src: url("/dist/assets/fonts/fa-solid-900.woff2") format("woff2"), url("/dist/assets/fonts/fa-solid-900.ttf") format("truetype"); }

.fas,
.fa-solid {
  font-weight: 900; }
@font-face {
  font-family: 'Font Awesome 5 Brands';
  font-display: block;
  font-weight: 400;
  src: url("/dist/assets/fonts/fa-brands-400.woff2") format("woff2"), url("/dist/assets/fonts/fa-brands-400.ttf") format("truetype"); }

@font-face {
  font-family: 'Font Awesome 5 Free';
  font-display: block;
  font-weight: 900;
  src: url("/dist/assets/fonts/fa-solid-900.woff2") format("woff2"), url("/dist/assets/fonts/fa-solid-900.ttf") format("truetype"); }

@font-face {
  font-family: 'Font Awesome 5 Free';
  font-display: block;
  font-weight: 400;
  src: url("/dist/assets/fonts/fa-regular-400.woff2") format("woff2"), url("/dist/assets/fonts/fa-regular-400.ttf") format("truetype"); }
@font-face {
  font-family: 'FontAwesome';
  font-display: block;
  src: url("/dist/assets/fonts/fa-solid-900.woff2") format("woff2"), url("/dist/assets/fonts/fa-solid-900.ttf") format("truetype"); }

@font-face {
  font-family: 'FontAwesome';
  font-display: block;
  src: url("/dist/assets/fonts/fa-brands-400.woff2") format("woff2"), url("/dist/assets/fonts/fa-brands-400.ttf") format("truetype"); }

@font-face {
  font-family: 'FontAwesome';
  font-display: block;
  src: url("/dist/assets/fonts/fa-regular-400.woff2") format("woff2"), url("/dist/assets/fonts/fa-regular-400.ttf") format("truetype");
  unicode-range: U+F003,U+F006,U+F014,U+F016-F017,U+F01A-F01B,U+F01D,U+F022,U+F03E,U+F044,U+F046,U+F05C-F05D,U+F06E,U+F070,U+F087-F088,U+F08A,U+F094,U+F096-F097,U+F09D,U+F0A0,U+F0A2,U+F0A4-F0A7,U+F0C5,U+F0C7,U+F0E5-F0E6,U+F0EB,U+F0F6-F0F8,U+F10C,U+F114-F115,U+F118-F11A,U+F11C-F11D,U+F133,U+F147,U+F14E,U+F150-F152,U+F185-F186,U+F18E,U+F190-F192,U+F196,U+F1C1-F1C9,U+F1D9,U+F1DB,U+F1E3,U+F1EA,U+F1F7,U+F1F9,U+F20A,U+F247-F248,U+F24A,U+F24D,U+F255-F25B,U+F25D,U+F271-F274,U+F278,U+F27B,U+F28C,U+F28E,U+F29C,U+F2B5,U+F2B7,U+F2BA,U+F2BC,U+F2BE,U+F2C0-F2C1,U+F2C3,U+F2D0,U+F2D2,U+F2D4,U+F2DC; }

@font-face {
  font-family: 'FontAwesome';
  font-display: block;
  src: url("/dist/assets/fonts/fa-v4compatibility.woff2") format("woff2"), url("/dist/assets/fonts/fa-v4compatibility.ttf") format("truetype");
  unicode-range: U+F041,U+F047,U+F065-F066,U+F07D-F07E,U+F080,U+F08B,U+F08E,U+F090,U+F09A,U+F0AC,U+F0AE,U+F0B2,U+F0D0,U+F0D6,U+F0E4,U+F0EC,U+F10A-F10B,U+F123,U+F13E,U+F148-F149,U+F14C,U+F156,U+F15E,U+F160-F161,U+F163,U+F175-F178,U+F195,U+F1F8,U+F219,U+F27A; }

@charset "UTF-8";
.bs {
  /*!
   * Bootstrap  v5.2.2 (https://getbootstrap.com/)
   * Copyright 2011-2022 The Bootstrap Authors
   * Copyright 2011-2022 Twitter, Inc.
   * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
   */
  /* rtl:raw:
  [type="tel"],
  [type="url"],
  [type="email"],
  [type="number"] {
    direction: ltr;
  }
  */
  /* rtl:begin:ignore */
  /* rtl:end:ignore */
  /* rtl:begin:ignore */
  /* rtl:end:ignore */
  /* rtl:begin:ignore */
  /* rtl:end:ignore */
  /* rtl:begin:ignore */
  /* rtl:end:ignore */
  /* rtl:begin:ignore */
  /* rtl:end:ignore */
  /* rtl:options: {
    "autoRename": true,
    "stringMap":[ {
      "name"    : "prev-next",
      "search"  : "prev",
      "replace" : "next"
    } ]
  } */
  /* rtl:begin:remove */
  /* rtl:end:remove */
  font-size: var(--bs-body-font-size);
}
.bs :root {
  --bs-blue: #0d6efd;
  --bs-indigo: #6610f2;
  --bs-purple: #6f42c1;
  --bs-pink: #d63384;
  --bs-red: #dc3545;
  --bs-orange: #fd7e14;
  --bs-yellow: #ffc107;
  --bs-green: #198754;
  --bs-teal: #20c997;
  --bs-cyan: #0dcaf0;
  --bs-black: #000;
  --bs-white: #fff;
  --bs-gray: #6c757d;
  --bs-gray-dark: #343a40;
  --bs-gray-100: #f8f9fa;
  --bs-gray-200: #e9ecef;
  --bs-gray-300: #dee2e6;
  --bs-gray-400: #ced4da;
  --bs-gray-500: #adb5bd;
  --bs-gray-600: #6c757d;
  --bs-gray-700: #495057;
  --bs-gray-800: #343a40;
  --bs-gray-900: #212529;
  --bs-primary: #0d6efd;
  --bs-secondary: #6c757d;
  --bs-success: #198754;
  --bs-info: #0dcaf0;
  --bs-warning: #ffc107;
  --bs-danger: #dc3545;
  --bs-light: #f8f9fa;
  --bs-dark: #212529;
  --bs-primary-rgb: 13, 110, 253;
  --bs-secondary-rgb: 108, 117, 125;
  --bs-success-rgb: 25, 135, 84;
  --bs-info-rgb: 13, 202, 240;
  --bs-warning-rgb: 255, 193, 7;
  --bs-danger-rgb: 220, 53, 69;
  --bs-light-rgb: 248, 249, 250;
  --bs-dark-rgb: 33, 37, 41;
  --bs-white-rgb: 255, 255, 255;
  --bs-black-rgb: 0, 0, 0;
  --bs-body-color-rgb: 33, 37, 41;
  --bs-body-bg-rgb: 255, 255, 255;
  --bs-font-sans-serif: system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", "Noto Sans", "Liberation Sans", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --bs-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
  --bs-body-font-family: var(--bs-font-sans-serif);
  --bs-body-font-size: 1rem;
  --bs-body-font-weight: 400;
  --bs-body-line-height: 1.5;
  --bs-body-color: #212529;
  --bs-body-bg: #fff;
  --bs-border-width: 1px;
  --bs-border-style: solid;
  --bs-border-color: #dee2e6;
  --bs-border-color-translucent: rgba(0, 0, 0, 0.175);
  --bs-border-radius: 0.375rem;
  --bs-border-radius-sm: 0.25rem;
  --bs-border-radius-lg: 0.5rem;
  --bs-border-radius-xl: 1rem;
  --bs-border-radius-2xl: 2rem;
  --bs-border-radius-pill: 50rem;
  --bs-link-color: #0d6efd;
  --bs-link-hover-color: rgb(10.4, 88, 202.4);
  --bs-code-color: #d63384;
  --bs-highlight-bg: rgb(255, 242.6, 205.4);
}
.bs *,
.bs *::before,
.bs *::after {
  box-sizing: border-box;
}
@media (prefers-reduced-motion: no-preference) {
  .bs :root {
    scroll-behavior: smooth;
  }
}
.bs body {
  margin: 0;
  font-family: var(--bs-body-font-family);
  font-size: var(--bs-body-font-size);
  font-weight: var(--bs-body-font-weight);
  line-height: var(--bs-body-line-height);
  color: var(--bs-body-color);
  text-align: var(--bs-body-text-align);
  background-color: var(--bs-body-bg);
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}
.bs hr {
  margin: 1rem 0;
  color: inherit;
  border: 0;
  border-top: 1px solid;
  opacity: 0.25;
}
.bs h6, .bs .h6, .bs h5, .bs .h5, .bs h4, .bs .h4, .bs h3, .bs .h3, .bs h2, .bs .h2, .bs h1, .bs .h1 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-weight: 500;
  line-height: 1.2;
}
.bs h1, .bs .h1 {
  font-size: calc(1.375rem + 1.5vw);
}
@media (min-width: 1200px) {
  .bs h1, .bs .h1 {
    font-size: 2.5rem;
  }
}
.bs h2, .bs .h2 {
  font-size: calc(1.325rem + 0.9vw);
}
@media (min-width: 1200px) {
  .bs h2, .bs .h2 {
    font-size: 2rem;
  }
}
.bs h3, .bs .h3 {
  font-size: calc(1.3rem + 0.6vw);
}
@media (min-width: 1200px) {
  .bs h3, .bs .h3 {
    font-size: 1.75rem;
  }
}
.bs h4, .bs .h4 {
  font-size: calc(1.275rem + 0.3vw);
}
@media (min-width: 1200px) {
  .bs h4, .bs .h4 {
    font-size: 1.5rem;
  }
}
.bs h5, .bs .h5 {
  font-size: 1.25rem;
}
.bs h6, .bs .h6 {
  font-size: 1rem;
}
.bs p {
  margin-top: 0;
  margin-bottom: 1rem;
}
.bs abbr[title] {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
  cursor: help;
  -webkit-text-decoration-skip-ink: none;
          text-decoration-skip-ink: none;
}
.bs address {
  margin-bottom: 1rem;
  font-style: normal;
  line-height: inherit;
}
.bs ol,
.bs ul {
  padding-left: 2rem;
}
.bs ol,
.bs ul,
.bs dl {
  margin-top: 0;
  margin-bottom: 1rem;
}
.bs ol ol,
.bs ul ul,
.bs ol ul,
.bs ul ol {
  margin-bottom: 0;
}
.bs dt {
  font-weight: 700;
}
.bs dd {
  margin-bottom: 0.5rem;
  margin-left: 0;
}
.bs blockquote {
  margin: 0 0 1rem;
}
.bs b,
.bs strong {
  font-weight: bolder;
}
.bs small, .bs .small {
  font-size: 0.875em;
}
.bs mark, .bs .mark {
  padding: 0.1875em;
  background-color: var(--bs-highlight-bg);
}
.bs sub,
.bs sup {
  position: relative;
  font-size: 0.75em;
  line-height: 0;
  vertical-align: baseline;
}
.bs sub {
  bottom: -0.25em;
}
.bs sup {
  top: -0.5em;
}
.bs a {
  color: var(--bs-link-color);
  text-decoration: underline;
}
.bs a:hover {
  color: var(--bs-link-hover-color);
}
.bs a:not([href]):not([class]), .bs a:not([href]):not([class]):hover {
  color: inherit;
  text-decoration: none;
}
.bs pre,
.bs code,
.bs kbd,
.bs samp {
  font-family: var(--bs-font-monospace);
  font-size: 1em;
}
.bs pre {
  display: block;
  margin-top: 0;
  margin-bottom: 1rem;
  overflow: auto;
  font-size: 0.875em;
}
.bs pre code {
  font-size: inherit;
  color: inherit;
  word-break: normal;
}
.bs code {
  font-size: 0.875em;
  color: var(--bs-code-color);
  word-wrap: break-word;
}
a > .bs code {
  color: inherit;
}
.bs kbd {
  padding: 0.1875rem 0.375rem;
  font-size: 0.875em;
  color: var(--bs-body-bg);
  background-color: var(--bs-body-color);
  border-radius: 0.25rem;
}
.bs kbd kbd {
  padding: 0;
  font-size: 1em;
}
.bs figure {
  margin: 0 0 1rem;
}
.bs img,
.bs svg {
  vertical-align: middle;
}
.bs table {
  caption-side: bottom;
  border-collapse: collapse;
}
.bs caption {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  color: #6c757d;
  text-align: left;
}
.bs th {
  text-align: inherit;
  text-align: -webkit-match-parent;
}
.bs thead,
.bs tbody,
.bs tfoot,
.bs tr,
.bs td,
.bs th {
  border-color: inherit;
  border-style: solid;
  border-width: 0;
}
.bs label {
  display: inline-block;
}
.bs button {
  border-radius: 0;
}
.bs button:focus:not(:focus-visible) {
  outline: 0;
}
.bs input,
.bs button,
.bs select,
.bs optgroup,
.bs textarea {
  margin: 0;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}
.bs button,
.bs select {
  text-transform: none;
}
.bs [role=button] {
  cursor: pointer;
}
.bs select {
  word-wrap: normal;
}
.bs select:disabled {
  opacity: 1;
}
.bs [list]:not([type=date]):not([type=datetime-local]):not([type=month]):not([type=week]):not([type=time])::-webkit-calendar-picker-indicator {
  display: none !important;
}
.bs button,
.bs [type=button],
.bs [type=reset],
.bs [type=submit] {
  -webkit-appearance: button;
}
.bs button:not(:disabled),
.bs [type=button]:not(:disabled),
.bs [type=reset]:not(:disabled),
.bs [type=submit]:not(:disabled) {
  cursor: pointer;
}
.bs ::-moz-focus-inner {
  padding: 0;
  border-style: none;
}
.bs textarea {
  resize: vertical;
}
.bs fieldset {
  min-width: 0;
  padding: 0;
  margin: 0;
  border: 0;
}
.bs legend {
  float: left;
  width: 100%;
  padding: 0;
  margin-bottom: 0.5rem;
  font-size: calc(1.275rem + 0.3vw);
  line-height: inherit;
}
@media (min-width: 1200px) {
  .bs legend {
    font-size: 1.5rem;
  }
}
.bs legend + * {
  clear: left;
}
.bs ::-webkit-datetime-edit-fields-wrapper,
.bs ::-webkit-datetime-edit-text,
.bs ::-webkit-datetime-edit-minute,
.bs ::-webkit-datetime-edit-hour-field,
.bs ::-webkit-datetime-edit-day-field,
.bs ::-webkit-datetime-edit-month-field,
.bs ::-webkit-datetime-edit-year-field {
  padding: 0;
}
.bs ::-webkit-inner-spin-button {
  height: auto;
}
.bs [type=search] {
  outline-offset: -2px;
  -webkit-appearance: textfield;
}
.bs ::-webkit-search-decoration {
  -webkit-appearance: none;
}
.bs ::-webkit-color-swatch-wrapper {
  padding: 0;
}
.bs ::file-selector-button {
  font: inherit;
  -webkit-appearance: button;
}
.bs output {
  display: inline-block;
}
.bs iframe {
  border: 0;
}
.bs summary {
  display: list-item;
  cursor: pointer;
}
.bs progress {
  vertical-align: baseline;
}
.bs [hidden] {
  display: none !important;
}
.bs .lead {
  font-size: 1.25rem;
  font-weight: 300;
}
.bs .display-1 {
  font-size: calc(1.625rem + 4.5vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .bs .display-1 {
    font-size: 5rem;
  }
}
.bs .display-2 {
  font-size: calc(1.575rem + 3.9vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .bs .display-2 {
    font-size: 4.5rem;
  }
}
.bs .display-3 {
  font-size: calc(1.525rem + 3.3vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .bs .display-3 {
    font-size: 4rem;
  }
}
.bs .display-4 {
  font-size: calc(1.475rem + 2.7vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .bs .display-4 {
    font-size: 3.5rem;
  }
}
.bs .display-5 {
  font-size: calc(1.425rem + 2.1vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .bs .display-5 {
    font-size: 3rem;
  }
}
.bs .display-6 {
  font-size: calc(1.375rem + 1.5vw);
  font-weight: 300;
  line-height: 1.2;
}
@media (min-width: 1200px) {
  .bs .display-6 {
    font-size: 2.5rem;
  }
}
.bs .list-unstyled {
  padding-left: 0;
  list-style: none;
}
.bs .list-inline {
  padding-left: 0;
  list-style: none;
}
.bs .list-inline-item {
  display: inline-block;
}
.bs .list-inline-item:not(:last-child) {
  margin-right: 0.5rem;
}
.bs .initialism {
  font-size: 0.875em;
  text-transform: uppercase;
}
.bs .blockquote {
  margin-bottom: 1rem;
  font-size: 1.25rem;
}
.bs .blockquote > :last-child {
  margin-bottom: 0;
}
.bs .blockquote-footer {
  margin-top: -1rem;
  margin-bottom: 1rem;
  font-size: 0.875em;
  color: #6c757d;
}
.bs .blockquote-footer::before {
  content: "— ";
}
.bs .img-fluid {
  max-width: 100%;
  height: auto;
}
.bs .img-thumbnail {
  padding: 0.25rem;
  background-color: #fff;
  border: 1px solid var(--bs-border-color);
  border-radius: 0.375rem;
  max-width: 100%;
  height: auto;
}
.bs .figure {
  display: inline-block;
}
.bs .figure-img {
  margin-bottom: 0.5rem;
  line-height: 1;
}
.bs .figure-caption {
  font-size: 0.875em;
  color: #6c757d;
}
.bs .container,
.bs .container-fluid,
.bs .container-xxl,
.bs .container-xl,
.bs .container-lg,
.bs .container-md,
.bs .container-sm {
  --bs-gutter-x: 1.5rem;
  --bs-gutter-y: 0;
  width: 100%;
  padding-right: calc(var(--bs-gutter-x) * 0.5);
  padding-left: calc(var(--bs-gutter-x) * 0.5);
  margin-right: auto;
  margin-left: auto;
}
@media (min-width: 576px) {
  .bs .container-sm, .bs .container {
    max-width: 540px;
  }
}
@media (min-width: 768px) {
  .bs .container-md, .bs .container-sm, .bs .container {
    max-width: 720px;
  }
}
@media (min-width: 992px) {
  .bs .container-lg, .bs .container-md, .bs .container-sm, .bs .container {
    max-width: 960px;
  }
}
@media (min-width: 1200px) {
  .bs .container-xl, .bs .container-lg, .bs .container-md, .bs .container-sm, .bs .container {
    max-width: 1140px;
  }
}
@media (min-width: 1400px) {
  .bs .container-xxl, .bs .container-xl, .bs .container-lg, .bs .container-md, .bs .container-sm, .bs .container {
    max-width: 1320px;
  }
}
.bs .row {
  --bs-gutter-x: 1.5rem;
  --bs-gutter-y: 0;
  display: flex;
  flex-wrap: wrap;
  margin-top: calc(-1 * var(--bs-gutter-y));
  margin-right: calc(-0.5 * var(--bs-gutter-x));
  margin-left: calc(-0.5 * var(--bs-gutter-x));
}
.bs .row > * {
  flex-shrink: 0;
  width: 100%;
  max-width: 100%;
  padding-right: calc(var(--bs-gutter-x) * 0.5);
  padding-left: calc(var(--bs-gutter-x) * 0.5);
  margin-top: var(--bs-gutter-y);
}
.bs .col {
  flex: 1 0 0%;
}
.bs .row-cols-auto > * {
  flex: 0 0 auto;
  width: auto;
}
.bs .row-cols-1 > * {
  flex: 0 0 auto;
  width: 100%;
}
.bs .row-cols-2 > * {
  flex: 0 0 auto;
  width: 50%;
}
.bs .row-cols-3 > * {
  flex: 0 0 auto;
  width: 33.3333333333%;
}
.bs .row-cols-4 > * {
  flex: 0 0 auto;
  width: 25%;
}
.bs .row-cols-5 > * {
  flex: 0 0 auto;
  width: 20%;
}
.bs .row-cols-6 > * {
  flex: 0 0 auto;
  width: 16.6666666667%;
}
.bs .col-auto {
  flex: 0 0 auto;
  width: auto;
}
.bs .col-1 {
  flex: 0 0 auto;
  width: 8.33333333%;
}
.bs .col-2 {
  flex: 0 0 auto;
  width: 16.66666667%;
}
.bs .col-3 {
  flex: 0 0 auto;
  width: 25%;
}
.bs .col-4 {
  flex: 0 0 auto;
  width: 33.33333333%;
}
.bs .col-5 {
  flex: 0 0 auto;
  width: 41.66666667%;
}
.bs .col-6 {
  flex: 0 0 auto;
  width: 50%;
}
.bs .col-7 {
  flex: 0 0 auto;
  width: 58.33333333%;
}
.bs .col-8 {
  flex: 0 0 auto;
  width: 66.66666667%;
}
.bs .col-9 {
  flex: 0 0 auto;
  width: 75%;
}
.bs .col-10 {
  flex: 0 0 auto;
  width: 83.33333333%;
}
.bs .col-11 {
  flex: 0 0 auto;
  width: 91.66666667%;
}
.bs .col-12 {
  flex: 0 0 auto;
  width: 100%;
}
.bs .offset-1 {
  margin-left: 8.33333333%;
}
.bs .offset-2 {
  margin-left: 16.66666667%;
}
.bs .offset-3 {
  margin-left: 25%;
}
.bs .offset-4 {
  margin-left: 33.33333333%;
}
.bs .offset-5 {
  margin-left: 41.66666667%;
}
.bs .offset-6 {
  margin-left: 50%;
}
.bs .offset-7 {
  margin-left: 58.33333333%;
}
.bs .offset-8 {
  margin-left: 66.66666667%;
}
.bs .offset-9 {
  margin-left: 75%;
}
.bs .offset-10 {
  margin-left: 83.33333333%;
}
.bs .offset-11 {
  margin-left: 91.66666667%;
}
.bs .g-0,
.bs .gx-0 {
  --bs-gutter-x: 0;
}
.bs .g-0,
.bs .gy-0 {
  --bs-gutter-y: 0;
}
.bs .g-1,
.bs .gx-1 {
  --bs-gutter-x: 0.25rem;
}
.bs .g-1,
.bs .gy-1 {
  --bs-gutter-y: 0.25rem;
}
.bs .g-2,
.bs .gx-2 {
  --bs-gutter-x: 0.5rem;
}
.bs .g-2,
.bs .gy-2 {
  --bs-gutter-y: 0.5rem;
}
.bs .g-3,
.bs .gx-3 {
  --bs-gutter-x: 1rem;
}
.bs .g-3,
.bs .gy-3 {
  --bs-gutter-y: 1rem;
}
.bs .g-4,
.bs .gx-4 {
  --bs-gutter-x: 1.5rem;
}
.bs .g-4,
.bs .gy-4 {
  --bs-gutter-y: 1.5rem;
}
.bs .g-5,
.bs .gx-5 {
  --bs-gutter-x: 3rem;
}
.bs .g-5,
.bs .gy-5 {
  --bs-gutter-y: 3rem;
}
@media (min-width: 576px) {
  .bs .col-sm {
    flex: 1 0 0%;
  }
  .bs .row-cols-sm-auto > * {
    flex: 0 0 auto;
    width: auto;
  }
  .bs .row-cols-sm-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .bs .row-cols-sm-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }
  .bs .row-cols-sm-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .bs .row-cols-sm-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }
  .bs .row-cols-sm-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .bs .row-cols-sm-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
  .bs .col-sm-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .bs .col-sm-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .bs .col-sm-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .bs .col-sm-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .bs .col-sm-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .bs .col-sm-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .bs .col-sm-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .bs .col-sm-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .bs .col-sm-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .bs .col-sm-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .bs .col-sm-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .bs .col-sm-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }
  .bs .col-sm-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .bs .offset-sm-0 {
    margin-left: 0;
  }
  .bs .offset-sm-1 {
    margin-left: 8.33333333%;
  }
  .bs .offset-sm-2 {
    margin-left: 16.66666667%;
  }
  .bs .offset-sm-3 {
    margin-left: 25%;
  }
  .bs .offset-sm-4 {
    margin-left: 33.33333333%;
  }
  .bs .offset-sm-5 {
    margin-left: 41.66666667%;
  }
  .bs .offset-sm-6 {
    margin-left: 50%;
  }
  .bs .offset-sm-7 {
    margin-left: 58.33333333%;
  }
  .bs .offset-sm-8 {
    margin-left: 66.66666667%;
  }
  .bs .offset-sm-9 {
    margin-left: 75%;
  }
  .bs .offset-sm-10 {
    margin-left: 83.33333333%;
  }
  .bs .offset-sm-11 {
    margin-left: 91.66666667%;
  }
  .bs .g-sm-0,
  .bs .gx-sm-0 {
    --bs-gutter-x: 0;
  }
  .bs .g-sm-0,
  .bs .gy-sm-0 {
    --bs-gutter-y: 0;
  }
  .bs .g-sm-1,
  .bs .gx-sm-1 {
    --bs-gutter-x: 0.25rem;
  }
  .bs .g-sm-1,
  .bs .gy-sm-1 {
    --bs-gutter-y: 0.25rem;
  }
  .bs .g-sm-2,
  .bs .gx-sm-2 {
    --bs-gutter-x: 0.5rem;
  }
  .bs .g-sm-2,
  .bs .gy-sm-2 {
    --bs-gutter-y: 0.5rem;
  }
  .bs .g-sm-3,
  .bs .gx-sm-3 {
    --bs-gutter-x: 1rem;
  }
  .bs .g-sm-3,
  .bs .gy-sm-3 {
    --bs-gutter-y: 1rem;
  }
  .bs .g-sm-4,
  .bs .gx-sm-4 {
    --bs-gutter-x: 1.5rem;
  }
  .bs .g-sm-4,
  .bs .gy-sm-4 {
    --bs-gutter-y: 1.5rem;
  }
  .bs .g-sm-5,
  .bs .gx-sm-5 {
    --bs-gutter-x: 3rem;
  }
  .bs .g-sm-5,
  .bs .gy-sm-5 {
    --bs-gutter-y: 3rem;
  }
}
@media (min-width: 768px) {
  .bs .col-md {
    flex: 1 0 0%;
  }
  .bs .row-cols-md-auto > * {
    flex: 0 0 auto;
    width: auto;
  }
  .bs .row-cols-md-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .bs .row-cols-md-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }
  .bs .row-cols-md-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .bs .row-cols-md-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }
  .bs .row-cols-md-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .bs .row-cols-md-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
  .bs .col-md-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .bs .col-md-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .bs .col-md-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .bs .col-md-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .bs .col-md-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .bs .col-md-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .bs .col-md-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .bs .col-md-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .bs .col-md-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .bs .col-md-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .bs .col-md-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .bs .col-md-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }
  .bs .col-md-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .bs .offset-md-0 {
    margin-left: 0;
  }
  .bs .offset-md-1 {
    margin-left: 8.33333333%;
  }
  .bs .offset-md-2 {
    margin-left: 16.66666667%;
  }
  .bs .offset-md-3 {
    margin-left: 25%;
  }
  .bs .offset-md-4 {
    margin-left: 33.33333333%;
  }
  .bs .offset-md-5 {
    margin-left: 41.66666667%;
  }
  .bs .offset-md-6 {
    margin-left: 50%;
  }
  .bs .offset-md-7 {
    margin-left: 58.33333333%;
  }
  .bs .offset-md-8 {
    margin-left: 66.66666667%;
  }
  .bs .offset-md-9 {
    margin-left: 75%;
  }
  .bs .offset-md-10 {
    margin-left: 83.33333333%;
  }
  .bs .offset-md-11 {
    margin-left: 91.66666667%;
  }
  .bs .g-md-0,
  .bs .gx-md-0 {
    --bs-gutter-x: 0;
  }
  .bs .g-md-0,
  .bs .gy-md-0 {
    --bs-gutter-y: 0;
  }
  .bs .g-md-1,
  .bs .gx-md-1 {
    --bs-gutter-x: 0.25rem;
  }
  .bs .g-md-1,
  .bs .gy-md-1 {
    --bs-gutter-y: 0.25rem;
  }
  .bs .g-md-2,
  .bs .gx-md-2 {
    --bs-gutter-x: 0.5rem;
  }
  .bs .g-md-2,
  .bs .gy-md-2 {
    --bs-gutter-y: 0.5rem;
  }
  .bs .g-md-3,
  .bs .gx-md-3 {
    --bs-gutter-x: 1rem;
  }
  .bs .g-md-3,
  .bs .gy-md-3 {
    --bs-gutter-y: 1rem;
  }
  .bs .g-md-4,
  .bs .gx-md-4 {
    --bs-gutter-x: 1.5rem;
  }
  .bs .g-md-4,
  .bs .gy-md-4 {
    --bs-gutter-y: 1.5rem;
  }
  .bs .g-md-5,
  .bs .gx-md-5 {
    --bs-gutter-x: 3rem;
  }
  .bs .g-md-5,
  .bs .gy-md-5 {
    --bs-gutter-y: 3rem;
  }
}
@media (min-width: 992px) {
  .bs .col-lg {
    flex: 1 0 0%;
  }
  .bs .row-cols-lg-auto > * {
    flex: 0 0 auto;
    width: auto;
  }
  .bs .row-cols-lg-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .bs .row-cols-lg-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }
  .bs .row-cols-lg-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .bs .row-cols-lg-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }
  .bs .row-cols-lg-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .bs .row-cols-lg-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
  .bs .col-lg-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .bs .col-lg-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .bs .col-lg-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .bs .col-lg-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .bs .col-lg-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .bs .col-lg-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .bs .col-lg-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .bs .col-lg-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .bs .col-lg-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .bs .col-lg-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .bs .col-lg-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .bs .col-lg-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }
  .bs .col-lg-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .bs .offset-lg-0 {
    margin-left: 0;
  }
  .bs .offset-lg-1 {
    margin-left: 8.33333333%;
  }
  .bs .offset-lg-2 {
    margin-left: 16.66666667%;
  }
  .bs .offset-lg-3 {
    margin-left: 25%;
  }
  .bs .offset-lg-4 {
    margin-left: 33.33333333%;
  }
  .bs .offset-lg-5 {
    margin-left: 41.66666667%;
  }
  .bs .offset-lg-6 {
    margin-left: 50%;
  }
  .bs .offset-lg-7 {
    margin-left: 58.33333333%;
  }
  .bs .offset-lg-8 {
    margin-left: 66.66666667%;
  }
  .bs .offset-lg-9 {
    margin-left: 75%;
  }
  .bs .offset-lg-10 {
    margin-left: 83.33333333%;
  }
  .bs .offset-lg-11 {
    margin-left: 91.66666667%;
  }
  .bs .g-lg-0,
  .bs .gx-lg-0 {
    --bs-gutter-x: 0;
  }
  .bs .g-lg-0,
  .bs .gy-lg-0 {
    --bs-gutter-y: 0;
  }
  .bs .g-lg-1,
  .bs .gx-lg-1 {
    --bs-gutter-x: 0.25rem;
  }
  .bs .g-lg-1,
  .bs .gy-lg-1 {
    --bs-gutter-y: 0.25rem;
  }
  .bs .g-lg-2,
  .bs .gx-lg-2 {
    --bs-gutter-x: 0.5rem;
  }
  .bs .g-lg-2,
  .bs .gy-lg-2 {
    --bs-gutter-y: 0.5rem;
  }
  .bs .g-lg-3,
  .bs .gx-lg-3 {
    --bs-gutter-x: 1rem;
  }
  .bs .g-lg-3,
  .bs .gy-lg-3 {
    --bs-gutter-y: 1rem;
  }
  .bs .g-lg-4,
  .bs .gx-lg-4 {
    --bs-gutter-x: 1.5rem;
  }
  .bs .g-lg-4,
  .bs .gy-lg-4 {
    --bs-gutter-y: 1.5rem;
  }
  .bs .g-lg-5,
  .bs .gx-lg-5 {
    --bs-gutter-x: 3rem;
  }
  .bs .g-lg-5,
  .bs .gy-lg-5 {
    --bs-gutter-y: 3rem;
  }
}
@media (min-width: 1200px) {
  .bs .col-xl {
    flex: 1 0 0%;
  }
  .bs .row-cols-xl-auto > * {
    flex: 0 0 auto;
    width: auto;
  }
  .bs .row-cols-xl-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .bs .row-cols-xl-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }
  .bs .row-cols-xl-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .bs .row-cols-xl-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }
  .bs .row-cols-xl-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .bs .row-cols-xl-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
  .bs .col-xl-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .bs .col-xl-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .bs .col-xl-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .bs .col-xl-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .bs .col-xl-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .bs .col-xl-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .bs .col-xl-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .bs .col-xl-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .bs .col-xl-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .bs .col-xl-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .bs .col-xl-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .bs .col-xl-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }
  .bs .col-xl-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .bs .offset-xl-0 {
    margin-left: 0;
  }
  .bs .offset-xl-1 {
    margin-left: 8.33333333%;
  }
  .bs .offset-xl-2 {
    margin-left: 16.66666667%;
  }
  .bs .offset-xl-3 {
    margin-left: 25%;
  }
  .bs .offset-xl-4 {
    margin-left: 33.33333333%;
  }
  .bs .offset-xl-5 {
    margin-left: 41.66666667%;
  }
  .bs .offset-xl-6 {
    margin-left: 50%;
  }
  .bs .offset-xl-7 {
    margin-left: 58.33333333%;
  }
  .bs .offset-xl-8 {
    margin-left: 66.66666667%;
  }
  .bs .offset-xl-9 {
    margin-left: 75%;
  }
  .bs .offset-xl-10 {
    margin-left: 83.33333333%;
  }
  .bs .offset-xl-11 {
    margin-left: 91.66666667%;
  }
  .bs .g-xl-0,
  .bs .gx-xl-0 {
    --bs-gutter-x: 0;
  }
  .bs .g-xl-0,
  .bs .gy-xl-0 {
    --bs-gutter-y: 0;
  }
  .bs .g-xl-1,
  .bs .gx-xl-1 {
    --bs-gutter-x: 0.25rem;
  }
  .bs .g-xl-1,
  .bs .gy-xl-1 {
    --bs-gutter-y: 0.25rem;
  }
  .bs .g-xl-2,
  .bs .gx-xl-2 {
    --bs-gutter-x: 0.5rem;
  }
  .bs .g-xl-2,
  .bs .gy-xl-2 {
    --bs-gutter-y: 0.5rem;
  }
  .bs .g-xl-3,
  .bs .gx-xl-3 {
    --bs-gutter-x: 1rem;
  }
  .bs .g-xl-3,
  .bs .gy-xl-3 {
    --bs-gutter-y: 1rem;
  }
  .bs .g-xl-4,
  .bs .gx-xl-4 {
    --bs-gutter-x: 1.5rem;
  }
  .bs .g-xl-4,
  .bs .gy-xl-4 {
    --bs-gutter-y: 1.5rem;
  }
  .bs .g-xl-5,
  .bs .gx-xl-5 {
    --bs-gutter-x: 3rem;
  }
  .bs .g-xl-5,
  .bs .gy-xl-5 {
    --bs-gutter-y: 3rem;
  }
}
@media (min-width: 1400px) {
  .bs .col-xxl {
    flex: 1 0 0%;
  }
  .bs .row-cols-xxl-auto > * {
    flex: 0 0 auto;
    width: auto;
  }
  .bs .row-cols-xxl-1 > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .bs .row-cols-xxl-2 > * {
    flex: 0 0 auto;
    width: 50%;
  }
  .bs .row-cols-xxl-3 > * {
    flex: 0 0 auto;
    width: 33.3333333333%;
  }
  .bs .row-cols-xxl-4 > * {
    flex: 0 0 auto;
    width: 25%;
  }
  .bs .row-cols-xxl-5 > * {
    flex: 0 0 auto;
    width: 20%;
  }
  .bs .row-cols-xxl-6 > * {
    flex: 0 0 auto;
    width: 16.6666666667%;
  }
  .bs .col-xxl-auto {
    flex: 0 0 auto;
    width: auto;
  }
  .bs .col-xxl-1 {
    flex: 0 0 auto;
    width: 8.33333333%;
  }
  .bs .col-xxl-2 {
    flex: 0 0 auto;
    width: 16.66666667%;
  }
  .bs .col-xxl-3 {
    flex: 0 0 auto;
    width: 25%;
  }
  .bs .col-xxl-4 {
    flex: 0 0 auto;
    width: 33.33333333%;
  }
  .bs .col-xxl-5 {
    flex: 0 0 auto;
    width: 41.66666667%;
  }
  .bs .col-xxl-6 {
    flex: 0 0 auto;
    width: 50%;
  }
  .bs .col-xxl-7 {
    flex: 0 0 auto;
    width: 58.33333333%;
  }
  .bs .col-xxl-8 {
    flex: 0 0 auto;
    width: 66.66666667%;
  }
  .bs .col-xxl-9 {
    flex: 0 0 auto;
    width: 75%;
  }
  .bs .col-xxl-10 {
    flex: 0 0 auto;
    width: 83.33333333%;
  }
  .bs .col-xxl-11 {
    flex: 0 0 auto;
    width: 91.66666667%;
  }
  .bs .col-xxl-12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .bs .offset-xxl-0 {
    margin-left: 0;
  }
  .bs .offset-xxl-1 {
    margin-left: 8.33333333%;
  }
  .bs .offset-xxl-2 {
    margin-left: 16.66666667%;
  }
  .bs .offset-xxl-3 {
    margin-left: 25%;
  }
  .bs .offset-xxl-4 {
    margin-left: 33.33333333%;
  }
  .bs .offset-xxl-5 {
    margin-left: 41.66666667%;
  }
  .bs .offset-xxl-6 {
    margin-left: 50%;
  }
  .bs .offset-xxl-7 {
    margin-left: 58.33333333%;
  }
  .bs .offset-xxl-8 {
    margin-left: 66.66666667%;
  }
  .bs .offset-xxl-9 {
    margin-left: 75%;
  }
  .bs .offset-xxl-10 {
    margin-left: 83.33333333%;
  }
  .bs .offset-xxl-11 {
    margin-left: 91.66666667%;
  }
  .bs .g-xxl-0,
  .bs .gx-xxl-0 {
    --bs-gutter-x: 0;
  }
  .bs .g-xxl-0,
  .bs .gy-xxl-0 {
    --bs-gutter-y: 0;
  }
  .bs .g-xxl-1,
  .bs .gx-xxl-1 {
    --bs-gutter-x: 0.25rem;
  }
  .bs .g-xxl-1,
  .bs .gy-xxl-1 {
    --bs-gutter-y: 0.25rem;
  }
  .bs .g-xxl-2,
  .bs .gx-xxl-2 {
    --bs-gutter-x: 0.5rem;
  }
  .bs .g-xxl-2,
  .bs .gy-xxl-2 {
    --bs-gutter-y: 0.5rem;
  }
  .bs .g-xxl-3,
  .bs .gx-xxl-3 {
    --bs-gutter-x: 1rem;
  }
  .bs .g-xxl-3,
  .bs .gy-xxl-3 {
    --bs-gutter-y: 1rem;
  }
  .bs .g-xxl-4,
  .bs .gx-xxl-4 {
    --bs-gutter-x: 1.5rem;
  }
  .bs .g-xxl-4,
  .bs .gy-xxl-4 {
    --bs-gutter-y: 1.5rem;
  }
  .bs .g-xxl-5,
  .bs .gx-xxl-5 {
    --bs-gutter-x: 3rem;
  }
  .bs .g-xxl-5,
  .bs .gy-xxl-5 {
    --bs-gutter-y: 3rem;
  }
}
.bs .table {
  --bs-table-color: var(--bs-body-color);
  --bs-table-bg: transparent;
  --bs-table-border-color: var(--bs-border-color);
  --bs-table-accent-bg: transparent;
  --bs-table-striped-color: var(--bs-body-color);
  --bs-table-striped-bg: rgba(0, 0, 0, 0.05);
  --bs-table-active-color: var(--bs-body-color);
  --bs-table-active-bg: rgba(0, 0, 0, 0.1);
  --bs-table-hover-color: var(--bs-body-color);
  --bs-table-hover-bg: rgba(0, 0, 0, 0.075);
  width: 100%;
  margin-bottom: 1rem;
  color: var(--bs-table-color);
  vertical-align: top;
  border-color: var(--bs-table-border-color);
}
.bs .table > :not(caption) > * > * {
  padding: 0.5rem 0.5rem;
  background-color: var(--bs-table-bg);
  border-bottom-width: 1px;
  box-shadow: inset 0 0 0 9999px var(--bs-table-accent-bg);
}
.bs .table > tbody {
  vertical-align: inherit;
}
.bs .table > thead {
  vertical-align: bottom;
}
.bs .table-group-divider {
  border-top: 2px solid currentcolor;
}
.bs .caption-top {
  caption-side: top;
}
.bs .table-sm > :not(caption) > * > * {
  padding: 0.25rem 0.25rem;
}
.bs .table-bordered > :not(caption) > * {
  border-width: 1px 0;
}
.bs .table-bordered > :not(caption) > * > * {
  border-width: 0 1px;
}
.bs .table-borderless > :not(caption) > * > * {
  border-bottom-width: 0;
}
.bs .table-borderless > :not(:first-child) {
  border-top-width: 0;
}
.bs .table-striped > tbody > tr:nth-of-type(odd) > * {
  --bs-table-accent-bg: var(--bs-table-striped-bg);
  color: var(--bs-table-striped-color);
}
.bs .table-striped-columns > :not(caption) > tr > :nth-child(even) {
  --bs-table-accent-bg: var(--bs-table-striped-bg);
  color: var(--bs-table-striped-color);
}
.bs .table-active {
  --bs-table-accent-bg: var(--bs-table-active-bg);
  color: var(--bs-table-active-color);
}
.bs .table-hover > tbody > tr:hover > * {
  --bs-table-accent-bg: var(--bs-table-hover-bg);
  color: var(--bs-table-hover-color);
}
.bs .table-primary {
  --bs-table-color: #000;
  --bs-table-bg: rgb(206.6, 226, 254.6);
  --bs-table-border-color: rgb(185.94, 203.4, 229.14);
  --bs-table-striped-bg: rgb(196.27, 214.7, 241.87);
  --bs-table-striped-color: #000;
  --bs-table-active-bg: rgb(185.94, 203.4, 229.14);
  --bs-table-active-color: #000;
  --bs-table-hover-bg: rgb(191.105, 209.05, 235.505);
  --bs-table-hover-color: #000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}
.bs .table-secondary {
  --bs-table-color: #000;
  --bs-table-bg: rgb(225.6, 227.4, 229);
  --bs-table-border-color: rgb(203.04, 204.66, 206.1);
  --bs-table-striped-bg: rgb(214.32, 216.03, 217.55);
  --bs-table-striped-color: #000;
  --bs-table-active-bg: rgb(203.04, 204.66, 206.1);
  --bs-table-active-color: #000;
  --bs-table-hover-bg: rgb(208.68, 210.345, 211.825);
  --bs-table-hover-color: #000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}
.bs .table-success {
  --bs-table-color: #000;
  --bs-table-bg: rgb(209, 231, 220.8);
  --bs-table-border-color: rgb(188.1, 207.9, 198.72);
  --bs-table-striped-bg: rgb(198.55, 219.45, 209.76);
  --bs-table-striped-color: #000;
  --bs-table-active-bg: rgb(188.1, 207.9, 198.72);
  --bs-table-active-color: #000;
  --bs-table-hover-bg: rgb(193.325, 213.675, 204.24);
  --bs-table-hover-color: #000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}
.bs .table-info {
  --bs-table-color: #000;
  --bs-table-bg: rgb(206.6, 244.4, 252);
  --bs-table-border-color: rgb(185.94, 219.96, 226.8);
  --bs-table-striped-bg: rgb(196.27, 232.18, 239.4);
  --bs-table-striped-color: #000;
  --bs-table-active-bg: rgb(185.94, 219.96, 226.8);
  --bs-table-active-color: #000;
  --bs-table-hover-bg: rgb(191.105, 226.07, 233.1);
  --bs-table-hover-color: #000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}
.bs .table-warning {
  --bs-table-color: #000;
  --bs-table-bg: rgb(255, 242.6, 205.4);
  --bs-table-border-color: rgb(229.5, 218.34, 184.86);
  --bs-table-striped-bg: rgb(242.25, 230.47, 195.13);
  --bs-table-striped-color: #000;
  --bs-table-active-bg: rgb(229.5, 218.34, 184.86);
  --bs-table-active-color: #000;
  --bs-table-hover-bg: rgb(235.875, 224.405, 189.995);
  --bs-table-hover-color: #000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}
.bs .table-danger {
  --bs-table-color: #000;
  --bs-table-bg: rgb(248, 214.6, 217.8);
  --bs-table-border-color: rgb(223.2, 193.14, 196.02);
  --bs-table-striped-bg: rgb(235.6, 203.87, 206.91);
  --bs-table-striped-color: #000;
  --bs-table-active-bg: rgb(223.2, 193.14, 196.02);
  --bs-table-active-color: #000;
  --bs-table-hover-bg: rgb(229.4, 198.505, 201.465);
  --bs-table-hover-color: #000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}
.bs .table-light {
  --bs-table-color: #000;
  --bs-table-bg: #f8f9fa;
  --bs-table-border-color: rgb(223.2, 224.1, 225);
  --bs-table-striped-bg: rgb(235.6, 236.55, 237.5);
  --bs-table-striped-color: #000;
  --bs-table-active-bg: rgb(223.2, 224.1, 225);
  --bs-table-active-color: #000;
  --bs-table-hover-bg: rgb(229.4, 230.325, 231.25);
  --bs-table-hover-color: #000;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}
.bs .table-dark {
  --bs-table-color: #fff;
  --bs-table-bg: #212529;
  --bs-table-border-color: rgb(55.2, 58.8, 62.4);
  --bs-table-striped-bg: rgb(44.1, 47.9, 51.7);
  --bs-table-striped-color: #fff;
  --bs-table-active-bg: rgb(55.2, 58.8, 62.4);
  --bs-table-active-color: #fff;
  --bs-table-hover-bg: rgb(49.65, 53.35, 57.05);
  --bs-table-hover-color: #fff;
  color: var(--bs-table-color);
  border-color: var(--bs-table-border-color);
}
.bs .table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}
@media (max-width: 575.98px) {
  .bs .table-responsive-sm {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 767.98px) {
  .bs .table-responsive-md {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 991.98px) {
  .bs .table-responsive-lg {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 1199.98px) {
  .bs .table-responsive-xl {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
@media (max-width: 1399.98px) {
  .bs .table-responsive-xxl {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
.bs .form-label {
  margin-bottom: 0.5rem;
}
.bs .col-form-label {
  padding-top: calc(0.375rem + 1px);
  padding-bottom: calc(0.375rem + 1px);
  margin-bottom: 0;
  font-size: inherit;
  line-height: 1.5;
}
.bs .col-form-label-lg {
  padding-top: calc(0.5rem + 1px);
  padding-bottom: calc(0.5rem + 1px);
  font-size: 1.25rem;
}
.bs .col-form-label-sm {
  padding-top: calc(0.25rem + 1px);
  padding-bottom: calc(0.25rem + 1px);
  font-size: 0.875rem;
}
.bs .form-text {
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: #6c757d;
}
.bs .form-control {
  display: block;
  width: 100%;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .bs .form-control {
    transition: none;
  }
}
.bs .form-control[type=file] {
  overflow: hidden;
}
.bs .form-control[type=file]:not(:disabled):not([readonly]) {
  cursor: pointer;
}
.bs .form-control:focus {
  color: #212529;
  background-color: #fff;
  border-color: rgb(134, 182.5, 254);
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}
.bs .form-control::-webkit-date-and-time-value {
  height: 1.5em;
}
.bs .form-control::-moz-placeholder {
  color: #6c757d;
  opacity: 1;
}
.bs .form-control::placeholder {
  color: #6c757d;
  opacity: 1;
}
.bs .form-control:disabled {
  background-color: #e9ecef;
  opacity: 1;
}
.bs .form-control::file-selector-button {
  padding: 0.375rem 0.75rem;
  margin: -0.375rem -0.75rem;
  margin-inline-end: 0.75rem;
  color: #212529;
  background-color: #e9ecef;
  pointer-events: none;
  border-color: inherit;
  border-style: solid;
  border-width: 0;
  border-inline-end-width: 1px;
  border-radius: 0;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .bs .form-control::file-selector-button {
    transition: none;
  }
}
.bs .form-control:hover:not(:disabled):not([readonly])::file-selector-button {
  background-color: rgb(221.35, 224.2, 227.05);
}
.bs .form-control-plaintext {
  display: block;
  width: 100%;
  padding: 0.375rem 0;
  margin-bottom: 0;
  line-height: 1.5;
  color: #212529;
  background-color: transparent;
  border: solid transparent;
  border-width: 1px 0;
}
.bs .form-control-plaintext:focus {
  outline: 0;
}
.bs .form-control-plaintext.form-control-sm, .bs .form-control-plaintext.form-control-lg {
  padding-right: 0;
  padding-left: 0;
}
.bs .form-control-sm {
  min-height: calc(1.5em + 0.5rem + 2px);
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
}
.bs .form-control-sm::file-selector-button {
  padding: 0.25rem 0.5rem;
  margin: -0.25rem -0.5rem;
  margin-inline-end: 0.5rem;
}
.bs .form-control-lg {
  min-height: calc(1.5em + 1rem + 2px);
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  border-radius: 0.5rem;
}
.bs .form-control-lg::file-selector-button {
  padding: 0.5rem 1rem;
  margin: -0.5rem -1rem;
  margin-inline-end: 1rem;
}
.bs textarea.form-control {
  min-height: calc(1.5em + 0.75rem + 2px);
}
.bs textarea.form-control-sm {
  min-height: calc(1.5em + 0.5rem + 2px);
}
.bs textarea.form-control-lg {
  min-height: calc(1.5em + 1rem + 2px);
}
.bs .form-control-color {
  width: 3rem;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem;
}
.bs .form-control-color:not(:disabled):not([readonly]) {
  cursor: pointer;
}
.bs .form-control-color::-moz-color-swatch {
  border: 0 !important;
  border-radius: 0.375rem;
}
.bs .form-control-color::-webkit-color-swatch {
  border-radius: 0.375rem;
}
.bs .form-control-color.form-control-sm {
  height: calc(1.5em + 0.5rem + 2px);
}
.bs .form-control-color.form-control-lg {
  height: calc(1.5em + 1rem + 2px);
}
.bs .form-select {
  display: block;
  width: 100%;
  padding: 0.375rem 2.25rem 0.375rem 0.75rem;
  -moz-padding-start: calc(0.75rem - 3px);
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  background-color: #fff;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 16px 12px;
  border: 1px solid #ced4da;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .bs .form-select {
    transition: none;
  }
}
.bs .form-select:focus {
  border-color: rgb(134, 182.5, 254);
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}
.bs .form-select[multiple], .bs .form-select[size]:not([size="1"]) {
  padding-right: 0.75rem;
  background-image: none;
}
.bs .form-select:disabled {
  background-color: #e9ecef;
}
.bs .form-select:-moz-focusring {
  color: transparent;
  text-shadow: 0 0 0 #212529;
}
.bs .form-select-sm {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
}
.bs .form-select-lg {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  font-size: 1.25rem;
  border-radius: 0.5rem;
}
.bs .form-check {
  display: block;
  min-height: 1.5rem;
  padding-left: 1.5em;
  margin-bottom: 0.125rem;
}
.bs .form-check .form-check-input {
  float: left;
  margin-left: -1.5em;
}
.bs .form-check-reverse {
  padding-right: 1.5em;
  padding-left: 0;
  text-align: right;
}
.bs .form-check-reverse .form-check-input {
  float: right;
  margin-right: -1.5em;
  margin-left: 0;
}
.bs .form-check-input {
  width: 1em;
  height: 1em;
  margin-top: 0.25em;
  vertical-align: top;
  background-color: #fff;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  border: 1px solid rgba(0, 0, 0, 0.25);
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  print-color-adjust: exact;
}
.bs .form-check-input[type=checkbox] {
  border-radius: 0.25em;
}
.bs .form-check-input[type=radio] {
  border-radius: 50%;
}
.bs .form-check-input:active {
  filter: brightness(90%);
}
.bs .form-check-input:focus {
  border-color: rgb(134, 182.5, 254);
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}
.bs .form-check-input:checked {
  background-color: #0d6efd;
  border-color: #0d6efd;
}
.bs .form-check-input:checked[type=checkbox] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");
}
.bs .form-check-input:checked[type=radio] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23fff'/%3e%3c/svg%3e");
}
.bs .form-check-input[type=checkbox]:indeterminate {
  background-color: #0d6efd;
  border-color: #0d6efd;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");
}
.bs .form-check-input:disabled {
  pointer-events: none;
  filter: none;
  opacity: 0.5;
}
.bs .form-check-input[disabled] ~ .form-check-label, .bs .form-check-input:disabled ~ .form-check-label {
  cursor: default;
  opacity: 0.5;
}
.bs .form-switch {
  padding-left: 2.5em;
}
.bs .form-switch .form-check-input {
  width: 2em;
  margin-left: -2.5em;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.25%29'/%3e%3c/svg%3e");
  background-position: left center;
  border-radius: 2em;
  transition: background-position 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .bs .form-switch .form-check-input {
    transition: none;
  }
}
.bs .form-switch .form-check-input:focus {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgb%28134, 182.5, 254%29'/%3e%3c/svg%3e");
}
.bs .form-switch .form-check-input:checked {
  background-position: right center;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
}
.bs .form-switch.form-check-reverse {
  padding-right: 2.5em;
  padding-left: 0;
}
.bs .form-switch.form-check-reverse .form-check-input {
  margin-right: -2.5em;
  margin-left: 0;
}
.bs .form-check-inline {
  display: inline-block;
  margin-right: 1rem;
}
.bs .btn-check {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}
.bs .btn-check[disabled] + .btn, .bs .btn-check:disabled + .btn {
  pointer-events: none;
  filter: none;
  opacity: 0.65;
}
.bs .form-range {
  width: 100%;
  height: 1.5rem;
  padding: 0;
  background-color: transparent;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.bs .form-range:focus {
  outline: 0;
}
.bs .form-range:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}
.bs .form-range:focus::-moz-range-thumb {
  box-shadow: 0 0 0 1px #fff, 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}
.bs .form-range::-moz-focus-outer {
  border: 0;
}
.bs .form-range::-webkit-slider-thumb {
  width: 1rem;
  height: 1rem;
  margin-top: -0.25rem;
  background-color: #0d6efd;
  border: 0;
  border-radius: 1rem;
  -webkit-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -webkit-appearance: none;
          appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .bs .form-range::-webkit-slider-thumb {
    -webkit-transition: none;
    transition: none;
  }
}
.bs .form-range::-webkit-slider-thumb:active {
  background-color: rgb(182.4, 211.5, 254.4);
}
.bs .form-range::-webkit-slider-runnable-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: #dee2e6;
  border-color: transparent;
  border-radius: 1rem;
}
.bs .form-range::-moz-range-thumb {
  width: 1rem;
  height: 1rem;
  background-color: #0d6efd;
  border: 0;
  border-radius: 1rem;
  -moz-transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  -moz-appearance: none;
       appearance: none;
}
@media (prefers-reduced-motion: reduce) {
  .bs .form-range::-moz-range-thumb {
    -moz-transition: none;
    transition: none;
  }
}
.bs .form-range::-moz-range-thumb:active {
  background-color: rgb(182.4, 211.5, 254.4);
}
.bs .form-range::-moz-range-track {
  width: 100%;
  height: 0.5rem;
  color: transparent;
  cursor: pointer;
  background-color: #dee2e6;
  border-color: transparent;
  border-radius: 1rem;
}
.bs .form-range:disabled {
  pointer-events: none;
}
.bs .form-range:disabled::-webkit-slider-thumb {
  background-color: #adb5bd;
}
.bs .form-range:disabled::-moz-range-thumb {
  background-color: #adb5bd;
}
.bs .form-floating {
  position: relative;
}
.bs .form-floating > .form-control,
.bs .form-floating > .form-control-plaintext,
.bs .form-floating > .form-select {
  height: calc(3.5rem + 2px);
  line-height: 1.25;
}
.bs .form-floating > label {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 1rem 0.75rem;
  overflow: hidden;
  text-align: start;
  text-overflow: ellipsis;
  white-space: nowrap;
  pointer-events: none;
  border: 1px solid transparent;
  transform-origin: 0 0;
  transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .bs .form-floating > label {
    transition: none;
  }
}
.bs .form-floating > .form-control,
.bs .form-floating > .form-control-plaintext {
  padding: 1rem 0.75rem;
}
.bs .form-floating > .form-control::-moz-placeholder, .bs .form-floating > .form-control-plaintext::-moz-placeholder {
  color: transparent;
}
.bs .form-floating > .form-control::placeholder,
.bs .form-floating > .form-control-plaintext::placeholder {
  color: transparent;
}
.bs .form-floating > .form-control:not(:-moz-placeholder-shown), .bs .form-floating > .form-control-plaintext:not(:-moz-placeholder-shown) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.bs .form-floating > .form-control:focus, .bs .form-floating > .form-control:not(:placeholder-shown),
.bs .form-floating > .form-control-plaintext:focus,
.bs .form-floating > .form-control-plaintext:not(:placeholder-shown) {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.bs .form-floating > .form-control:-webkit-autofill,
.bs .form-floating > .form-control-plaintext:-webkit-autofill {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.bs .form-floating > .form-select {
  padding-top: 1.625rem;
  padding-bottom: 0.625rem;
}
.bs .form-floating > .form-control:not(:-moz-placeholder-shown) ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.bs .form-floating > .form-control:focus ~ label,
.bs .form-floating > .form-control:not(:placeholder-shown) ~ label,
.bs .form-floating > .form-control-plaintext ~ label,
.bs .form-floating > .form-select ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.bs .form-floating > .form-control:-webkit-autofill ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}
.bs .form-floating > .form-control-plaintext ~ label {
  border-width: 1px 0;
}
.bs .input-group {
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  width: 100%;
}
.bs .input-group > .form-control,
.bs .input-group > .form-select,
.bs .input-group > .form-floating {
  position: relative;
  flex: 1 1 auto;
  width: 1%;
  min-width: 0;
}
.bs .input-group > .form-control:focus,
.bs .input-group > .form-select:focus,
.bs .input-group > .form-floating:focus-within {
  z-index: 5;
}
.bs .input-group .btn {
  position: relative;
  z-index: 2;
}
.bs .input-group .btn:focus {
  z-index: 5;
}
.bs .input-group-text {
  display: flex;
  align-items: center;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #212529;
  text-align: center;
  white-space: nowrap;
  background-color: #e9ecef;
  border: 1px solid #ced4da;
  border-radius: 0.375rem;
}
.bs .input-group-lg > .form-control,
.bs .input-group-lg > .form-select,
.bs .input-group-lg > .input-group-text,
.bs .input-group-lg > .btn {
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  border-radius: 0.5rem;
}
.bs .input-group-sm > .form-control,
.bs .input-group-sm > .form-select,
.bs .input-group-sm > .input-group-text,
.bs .input-group-sm > .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
}
.bs .input-group-lg > .form-select,
.bs .input-group-sm > .form-select {
  padding-right: 3rem;
}
.bs .input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating),
.bs .input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n+3),
.bs .input-group:not(.has-validation) > .form-floating:not(:last-child) > .form-control,
.bs .input-group:not(.has-validation) > .form-floating:not(:last-child) > .form-select {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.bs .input-group.has-validation > :nth-last-child(n+3):not(.dropdown-toggle):not(.dropdown-menu):not(.form-floating),
.bs .input-group.has-validation > .dropdown-toggle:nth-last-child(n+4),
.bs .input-group.has-validation > .form-floating:nth-last-child(n+3) > .form-control,
.bs .input-group.has-validation > .form-floating:nth-last-child(n+3) > .form-select {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.bs .input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback) {
  margin-left: -1px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.bs .input-group > .form-floating:not(:first-child) > .form-control,
.bs .input-group > .form-floating:not(:first-child) > .form-select {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.bs .valid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: #198754;
}
.bs .valid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  color: #fff;
  background-color: rgba(25, 135, 84, 0.9);
  border-radius: 0.375rem;
}
.was-validated .bs:valid ~ .valid-feedback,
.was-validated .bs:valid ~ .valid-tooltip, .bs.is-valid ~ .valid-feedback,
.bs.is-valid ~ .valid-tooltip {
  display: block;
}
.was-validated .bs .form-control:valid, .bs .form-control.is-valid {
  border-color: #198754;
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.was-validated .bs .form-control:valid:focus, .bs .form-control.is-valid:focus {
  border-color: #198754;
  box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
}
.was-validated .bs textarea.form-control:valid, .bs textarea.form-control.is-valid {
  padding-right: calc(1.5em + 0.75rem);
  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);
}
.was-validated .bs .form-select:valid, .bs .form-select.is-valid {
  border-color: #198754;
}
.was-validated .bs .form-select:valid:not([multiple]):not([size]), .was-validated .bs .form-select:valid:not([multiple])[size="1"], .bs .form-select.is-valid:not([multiple]):not([size]), .bs .form-select.is-valid:not([multiple])[size="1"] {
  padding-right: 4.125rem;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
  background-position: right 0.75rem center, center right 2.25rem;
  background-size: 16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.was-validated .bs .form-select:valid:focus, .bs .form-select.is-valid:focus {
  border-color: #198754;
  box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
}
.was-validated .bs .form-control-color:valid, .bs .form-control-color.is-valid {
  width: calc(3rem + calc(1.5em + 0.75rem));
}
.was-validated .bs .form-check-input:valid, .bs .form-check-input.is-valid {
  border-color: #198754;
}
.was-validated .bs .form-check-input:valid:checked, .bs .form-check-input.is-valid:checked {
  background-color: #198754;
}
.was-validated .bs .form-check-input:valid:focus, .bs .form-check-input.is-valid:focus {
  box-shadow: 0 0 0 0.25rem rgba(25, 135, 84, 0.25);
}
.was-validated .bs .form-check-input:valid ~ .form-check-label, .bs .form-check-input.is-valid ~ .form-check-label {
  color: #198754;
}
.bs .form-check-inline .form-check-input ~ .valid-feedback {
  margin-left: 0.5em;
}
.was-validated .bs .input-group > .form-control:not(:focus):valid, .bs .input-group > .form-control:not(:focus).is-valid,
.was-validated .bs .input-group > .form-select:not(:focus):valid,
.bs .input-group > .form-select:not(:focus).is-valid,
.was-validated .bs .input-group > .form-floating:not(:focus-within):valid,
.bs .input-group > .form-floating:not(:focus-within).is-valid {
  z-index: 3;
}
.bs .invalid-feedback {
  display: none;
  width: 100%;
  margin-top: 0.25rem;
  font-size: 0.875em;
  color: #dc3545;
}
.bs .invalid-tooltip {
  position: absolute;
  top: 100%;
  z-index: 5;
  display: none;
  max-width: 100%;
  padding: 0.25rem 0.5rem;
  margin-top: 0.1rem;
  font-size: 0.875rem;
  color: #fff;
  background-color: rgba(220, 53, 69, 0.9);
  border-radius: 0.375rem;
}
.was-validated .bs:invalid ~ .invalid-feedback,
.was-validated .bs:invalid ~ .invalid-tooltip, .bs.is-invalid ~ .invalid-feedback,
.bs.is-invalid ~ .invalid-tooltip {
  display: block;
}
.was-validated .bs .form-control:invalid, .bs .form-control.is-invalid {
  border-color: #dc3545;
  padding-right: calc(1.5em + 0.75rem);
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right calc(0.375em + 0.1875rem) center;
  background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.was-validated .bs .form-control:invalid:focus, .bs .form-control.is-invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}
.was-validated .bs textarea.form-control:invalid, .bs textarea.form-control.is-invalid {
  padding-right: calc(1.5em + 0.75rem);
  background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);
}
.was-validated .bs .form-select:invalid, .bs .form-select.is-invalid {
  border-color: #dc3545;
}
.was-validated .bs .form-select:invalid:not([multiple]):not([size]), .was-validated .bs .form-select:invalid:not([multiple])[size="1"], .bs .form-select.is-invalid:not([multiple]):not([size]), .bs .form-select.is-invalid:not([multiple])[size="1"] {
  padding-right: 4.125rem;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
  background-position: right 0.75rem center, center right 2.25rem;
  background-size: 16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.was-validated .bs .form-select:invalid:focus, .bs .form-select.is-invalid:focus {
  border-color: #dc3545;
  box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}
.was-validated .bs .form-control-color:invalid, .bs .form-control-color.is-invalid {
  width: calc(3rem + calc(1.5em + 0.75rem));
}
.was-validated .bs .form-check-input:invalid, .bs .form-check-input.is-invalid {
  border-color: #dc3545;
}
.was-validated .bs .form-check-input:invalid:checked, .bs .form-check-input.is-invalid:checked {
  background-color: #dc3545;
}
.was-validated .bs .form-check-input:invalid:focus, .bs .form-check-input.is-invalid:focus {
  box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);
}
.was-validated .bs .form-check-input:invalid ~ .form-check-label, .bs .form-check-input.is-invalid ~ .form-check-label {
  color: #dc3545;
}
.bs .form-check-inline .form-check-input ~ .invalid-feedback {
  margin-left: 0.5em;
}
.was-validated .bs .input-group > .form-control:not(:focus):invalid, .bs .input-group > .form-control:not(:focus).is-invalid,
.was-validated .bs .input-group > .form-select:not(:focus):invalid,
.bs .input-group > .form-select:not(:focus).is-invalid,
.was-validated .bs .input-group > .form-floating:not(:focus-within):invalid,
.bs .input-group > .form-floating:not(:focus-within).is-invalid {
  z-index: 4;
}
.bs .btn {
  --bs-btn-padding-x: 0.75rem;
  --bs-btn-padding-y: 0.375rem;
  --bs-btn-font-family: ;
  --bs-btn-font-size: 1rem;
  --bs-btn-font-weight: 400;
  --bs-btn-line-height: 1.5;
  --bs-btn-color: #212529;
  --bs-btn-bg: transparent;
  --bs-btn-border-width: 1px;
  --bs-btn-border-color: transparent;
  --bs-btn-border-radius: 0.375rem;
  --bs-btn-hover-border-color: transparent;
  --bs-btn-box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.15), 0 1px 1px rgba(0, 0, 0, 0.075);
  --bs-btn-disabled-opacity: 0.65;
  --bs-btn-focus-box-shadow: 0 0 0 0.25rem rgba(var(--bs-btn-focus-shadow-rgb), .5);
  display: inline-block;
  padding: var(--bs-btn-padding-y) var(--bs-btn-padding-x);
  font-family: var(--bs-btn-font-family);
  font-size: var(--bs-btn-font-size);
  font-weight: var(--bs-btn-font-weight);
  line-height: var(--bs-btn-line-height);
  color: var(--bs-btn-color);
  text-align: center;
  text-decoration: none;
  vertical-align: middle;
  cursor: pointer;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  border: var(--bs-btn-border-width) solid var(--bs-btn-border-color);
  border-radius: var(--bs-btn-border-radius);
  background-color: var(--bs-btn-bg);
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .bs .btn {
    transition: none;
  }
}
.bs .btn:hover {
  color: var(--bs-btn-hover-color);
  background-color: var(--bs-btn-hover-bg);
  border-color: var(--bs-btn-hover-border-color);
}
.btn-check + .bs .btn:hover {
  color: var(--bs-btn-color);
  background-color: var(--bs-btn-bg);
  border-color: var(--bs-btn-border-color);
}
.bs .btn:focus-visible {
  color: var(--bs-btn-hover-color);
  background-color: var(--bs-btn-hover-bg);
  border-color: var(--bs-btn-hover-border-color);
  outline: 0;
  box-shadow: var(--bs-btn-focus-box-shadow);
}
.btn-check:focus-visible + .bs .btn {
  border-color: var(--bs-btn-hover-border-color);
  outline: 0;
  box-shadow: var(--bs-btn-focus-box-shadow);
}
.btn-check:checked + .bs .btn, :not(.btn-check) + .bs .btn:active, .bs .btn:first-child:active, .bs .btn.active, .bs .btn.show {
  color: var(--bs-btn-active-color);
  background-color: var(--bs-btn-active-bg);
  border-color: var(--bs-btn-active-border-color);
}
.btn-check:checked + .bs .btn:focus-visible, :not(.btn-check) + .bs .btn:active:focus-visible, .bs .btn:first-child:active:focus-visible, .bs .btn.active:focus-visible, .bs .btn.show:focus-visible {
  box-shadow: var(--bs-btn-focus-box-shadow);
}
.bs .btn:disabled, .bs .btn.disabled, fieldset:disabled .bs .btn {
  color: var(--bs-btn-disabled-color);
  pointer-events: none;
  background-color: var(--bs-btn-disabled-bg);
  border-color: var(--bs-btn-disabled-border-color);
  opacity: var(--bs-btn-disabled-opacity);
}
.bs .btn-primary {
  --bs-btn-color: #fff;
  --bs-btn-bg: #0d6efd;
  --bs-btn-border-color: #0d6efd;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: rgb(11.05, 93.5, 215.05);
  --bs-btn-hover-border-color: rgb(10.4, 88, 202.4);
  --bs-btn-focus-shadow-rgb: 49, 132, 253;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: rgb(10.4, 88, 202.4);
  --bs-btn-active-border-color: rgb(9.75, 82.5, 189.75);
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #fff;
  --bs-btn-disabled-bg: #0d6efd;
  --bs-btn-disabled-border-color: #0d6efd;
}
.bs .btn-secondary {
  --bs-btn-color: #fff;
  --bs-btn-bg: #6c757d;
  --bs-btn-border-color: #6c757d;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: rgb(91.8, 99.45, 106.25);
  --bs-btn-hover-border-color: rgb(86.4, 93.6, 100);
  --bs-btn-focus-shadow-rgb: 130, 138, 145;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: rgb(86.4, 93.6, 100);
  --bs-btn-active-border-color: rgb(81, 87.75, 93.75);
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #fff;
  --bs-btn-disabled-bg: #6c757d;
  --bs-btn-disabled-border-color: #6c757d;
}
.bs .btn-success {
  --bs-btn-color: #fff;
  --bs-btn-bg: #198754;
  --bs-btn-border-color: #198754;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: rgb(21.25, 114.75, 71.4);
  --bs-btn-hover-border-color: rgb(20, 108, 67.2);
  --bs-btn-focus-shadow-rgb: 60, 153, 110;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: rgb(20, 108, 67.2);
  --bs-btn-active-border-color: rgb(18.75, 101.25, 63);
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #fff;
  --bs-btn-disabled-bg: #198754;
  --bs-btn-disabled-border-color: #198754;
}
.bs .btn-info {
  --bs-btn-color: #000;
  --bs-btn-bg: #0dcaf0;
  --bs-btn-border-color: #0dcaf0;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: rgb(49.3, 209.95, 242.25);
  --bs-btn-hover-border-color: rgb(37.2, 207.3, 241.5);
  --bs-btn-focus-shadow-rgb: 11, 172, 204;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: rgb(61.4, 212.6, 243);
  --bs-btn-active-border-color: rgb(37.2, 207.3, 241.5);
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #0dcaf0;
  --bs-btn-disabled-border-color: #0dcaf0;
}
.bs .btn-warning {
  --bs-btn-color: #000;
  --bs-btn-bg: #ffc107;
  --bs-btn-border-color: #ffc107;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: rgb(255, 202.3, 44.2);
  --bs-btn-hover-border-color: rgb(255, 199.2, 31.8);
  --bs-btn-focus-shadow-rgb: 217, 164, 6;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: rgb(255, 205.4, 56.6);
  --bs-btn-active-border-color: rgb(255, 199.2, 31.8);
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #ffc107;
  --bs-btn-disabled-border-color: #ffc107;
}
.bs .btn-danger {
  --bs-btn-color: #fff;
  --bs-btn-bg: #dc3545;
  --bs-btn-border-color: #dc3545;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: rgb(187, 45.05, 58.65);
  --bs-btn-hover-border-color: rgb(176, 42.4, 55.2);
  --bs-btn-focus-shadow-rgb: 225, 83, 97;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: rgb(176, 42.4, 55.2);
  --bs-btn-active-border-color: rgb(165, 39.75, 51.75);
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #fff;
  --bs-btn-disabled-bg: #dc3545;
  --bs-btn-disabled-border-color: #dc3545;
}
.bs .btn-light {
  --bs-btn-color: #000;
  --bs-btn-bg: #f8f9fa;
  --bs-btn-border-color: #f8f9fa;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: rgb(210.8, 211.65, 212.5);
  --bs-btn-hover-border-color: rgb(198.4, 199.2, 200);
  --bs-btn-focus-shadow-rgb: 211, 212, 213;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: rgb(198.4, 199.2, 200);
  --bs-btn-active-border-color: rgb(186, 186.75, 187.5);
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #000;
  --bs-btn-disabled-bg: #f8f9fa;
  --bs-btn-disabled-border-color: #f8f9fa;
}
.bs .btn-dark {
  --bs-btn-color: #fff;
  --bs-btn-bg: #212529;
  --bs-btn-border-color: #212529;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: rgb(66.3, 69.7, 73.1);
  --bs-btn-hover-border-color: rgb(55.2, 58.8, 62.4);
  --bs-btn-focus-shadow-rgb: 66, 70, 73;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: rgb(77.4, 80.6, 83.8);
  --bs-btn-active-border-color: rgb(55.2, 58.8, 62.4);
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #fff;
  --bs-btn-disabled-bg: #212529;
  --bs-btn-disabled-border-color: #212529;
}
.bs .btn-outline-primary {
  --bs-btn-color: #0d6efd;
  --bs-btn-border-color: #0d6efd;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #0d6efd;
  --bs-btn-hover-border-color: #0d6efd;
  --bs-btn-focus-shadow-rgb: 13, 110, 253;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #0d6efd;
  --bs-btn-active-border-color: #0d6efd;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #0d6efd;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #0d6efd;
  --bs-gradient: none;
}
.bs .btn-outline-secondary {
  --bs-btn-color: #6c757d;
  --bs-btn-border-color: #6c757d;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #6c757d;
  --bs-btn-hover-border-color: #6c757d;
  --bs-btn-focus-shadow-rgb: 108, 117, 125;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #6c757d;
  --bs-btn-active-border-color: #6c757d;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #6c757d;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #6c757d;
  --bs-gradient: none;
}
.bs .btn-outline-success {
  --bs-btn-color: #198754;
  --bs-btn-border-color: #198754;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #198754;
  --bs-btn-hover-border-color: #198754;
  --bs-btn-focus-shadow-rgb: 25, 135, 84;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #198754;
  --bs-btn-active-border-color: #198754;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #198754;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #198754;
  --bs-gradient: none;
}
.bs .btn-outline-info {
  --bs-btn-color: #0dcaf0;
  --bs-btn-border-color: #0dcaf0;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #0dcaf0;
  --bs-btn-hover-border-color: #0dcaf0;
  --bs-btn-focus-shadow-rgb: 13, 202, 240;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #0dcaf0;
  --bs-btn-active-border-color: #0dcaf0;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #0dcaf0;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #0dcaf0;
  --bs-gradient: none;
}
.bs .btn-outline-warning {
  --bs-btn-color: #ffc107;
  --bs-btn-border-color: #ffc107;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #ffc107;
  --bs-btn-hover-border-color: #ffc107;
  --bs-btn-focus-shadow-rgb: 255, 193, 7;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #ffc107;
  --bs-btn-active-border-color: #ffc107;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #ffc107;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #ffc107;
  --bs-gradient: none;
}
.bs .btn-outline-danger {
  --bs-btn-color: #dc3545;
  --bs-btn-border-color: #dc3545;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #dc3545;
  --bs-btn-hover-border-color: #dc3545;
  --bs-btn-focus-shadow-rgb: 220, 53, 69;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #dc3545;
  --bs-btn-active-border-color: #dc3545;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #dc3545;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #dc3545;
  --bs-gradient: none;
}
.bs .btn-outline-light {
  --bs-btn-color: #f8f9fa;
  --bs-btn-border-color: #f8f9fa;
  --bs-btn-hover-color: #000;
  --bs-btn-hover-bg: #f8f9fa;
  --bs-btn-hover-border-color: #f8f9fa;
  --bs-btn-focus-shadow-rgb: 248, 249, 250;
  --bs-btn-active-color: #000;
  --bs-btn-active-bg: #f8f9fa;
  --bs-btn-active-border-color: #f8f9fa;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #f8f9fa;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #f8f9fa;
  --bs-gradient: none;
}
.bs .btn-outline-dark {
  --bs-btn-color: #212529;
  --bs-btn-border-color: #212529;
  --bs-btn-hover-color: #fff;
  --bs-btn-hover-bg: #212529;
  --bs-btn-hover-border-color: #212529;
  --bs-btn-focus-shadow-rgb: 33, 37, 41;
  --bs-btn-active-color: #fff;
  --bs-btn-active-bg: #212529;
  --bs-btn-active-border-color: #212529;
  --bs-btn-active-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  --bs-btn-disabled-color: #212529;
  --bs-btn-disabled-bg: transparent;
  --bs-btn-disabled-border-color: #212529;
  --bs-gradient: none;
}
.bs .btn-link {
  --bs-btn-font-weight: 400;
  --bs-btn-color: var(--bs-link-color);
  --bs-btn-bg: transparent;
  --bs-btn-border-color: transparent;
  --bs-btn-hover-color: var(--bs-link-hover-color);
  --bs-btn-hover-border-color: transparent;
  --bs-btn-active-color: var(--bs-link-hover-color);
  --bs-btn-active-border-color: transparent;
  --bs-btn-disabled-color: #6c757d;
  --bs-btn-disabled-border-color: transparent;
  --bs-btn-box-shadow: none;
  --bs-btn-focus-shadow-rgb: 49, 132, 253;
  text-decoration: underline;
}
.bs .btn-link:focus-visible {
  color: var(--bs-btn-color);
}
.bs .btn-link:hover {
  color: var(--bs-btn-hover-color);
}
.bs .btn-lg, .bs .btn-group-lg > .btn {
  --bs-btn-padding-y: 0.5rem;
  --bs-btn-padding-x: 1rem;
  --bs-btn-font-size: 1.25rem;
  --bs-btn-border-radius: 0.5rem;
}
.bs .btn-sm, .bs .btn-group-sm > .btn {
  --bs-btn-padding-y: 0.25rem;
  --bs-btn-padding-x: 0.5rem;
  --bs-btn-font-size: 0.875rem;
  --bs-btn-border-radius: 0.25rem;
}
.bs .fade {
  transition: opacity 0.15s linear;
}
@media (prefers-reduced-motion: reduce) {
  .bs .fade {
    transition: none;
  }
}
.bs .fade:not(.show) {
  opacity: 0;
}
.bs .collapse:not(.show) {
  display: none;
}
.bs .collapsing {
  height: 0;
  overflow: hidden;
  transition: height 0.35s ease;
}
@media (prefers-reduced-motion: reduce) {
  .bs .collapsing {
    transition: none;
  }
}
.bs .collapsing.collapse-horizontal {
  width: 0;
  height: auto;
  transition: width 0.35s ease;
}
@media (prefers-reduced-motion: reduce) {
  .bs .collapsing.collapse-horizontal {
    transition: none;
  }
}
.bs .dropup,
.bs .dropend,
.bs .dropdown,
.bs .dropstart,
.bs .dropup-center,
.bs .dropdown-center {
  position: relative;
}
.bs .dropdown-toggle {
  white-space: nowrap;
}
.bs .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
}
.bs .dropdown-toggle:empty::after {
  margin-left: 0;
}
.bs .dropdown-menu {
  --bs-dropdown-zindex: 1000;
  --bs-dropdown-min-width: 10rem;
  --bs-dropdown-padding-x: 0;
  --bs-dropdown-padding-y: 0.5rem;
  --bs-dropdown-spacer: 0.125rem;
  --bs-dropdown-font-size: 1rem;
  --bs-dropdown-color: #212529;
  --bs-dropdown-bg: #fff;
  --bs-dropdown-border-color: var(--bs-border-color-translucent);
  --bs-dropdown-border-radius: 0.375rem;
  --bs-dropdown-border-width: 1px;
  --bs-dropdown-inner-border-radius: calc(0.375rem - 1px);
  --bs-dropdown-divider-bg: var(--bs-border-color-translucent);
  --bs-dropdown-divider-margin-y: 0.5rem;
  --bs-dropdown-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --bs-dropdown-link-color: #212529;
  --bs-dropdown-link-hover-color: rgb(29.7, 33.3, 36.9);
  --bs-dropdown-link-hover-bg: #e9ecef;
  --bs-dropdown-link-active-color: #fff;
  --bs-dropdown-link-active-bg: #0d6efd;
  --bs-dropdown-link-disabled-color: #adb5bd;
  --bs-dropdown-item-padding-x: 1rem;
  --bs-dropdown-item-padding-y: 0.25rem;
  --bs-dropdown-header-color: #6c757d;
  --bs-dropdown-header-padding-x: 1rem;
  --bs-dropdown-header-padding-y: 0.5rem;
  position: absolute;
  z-index: var(--bs-dropdown-zindex);
  display: none;
  min-width: var(--bs-dropdown-min-width);
  padding: var(--bs-dropdown-padding-y) var(--bs-dropdown-padding-x);
  margin: 0;
  font-size: var(--bs-dropdown-font-size);
  color: var(--bs-dropdown-color);
  text-align: left;
  list-style: none;
  background-color: var(--bs-dropdown-bg);
  background-clip: padding-box;
  border: var(--bs-dropdown-border-width) solid var(--bs-dropdown-border-color);
  border-radius: var(--bs-dropdown-border-radius);
}
.bs .dropdown-menu[data-bs-popper] {
  top: 100%;
  left: 0;
  margin-top: var(--bs-dropdown-spacer);
}
.bs .dropdown-menu-start {
  --bs-position: start;
}
.bs .dropdown-menu-start[data-bs-popper] {
  right: auto;
  left: 0;
}
.bs .dropdown-menu-end {
  --bs-position: end;
}
.bs .dropdown-menu-end[data-bs-popper] {
  right: 0;
  left: auto;
}
@media (min-width: 576px) {
  .bs .dropdown-menu-sm-start {
    --bs-position: start;
  }
  .bs .dropdown-menu-sm-start[data-bs-popper] {
    right: auto;
    left: 0;
  }
  .bs .dropdown-menu-sm-end {
    --bs-position: end;
  }
  .bs .dropdown-menu-sm-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 768px) {
  .bs .dropdown-menu-md-start {
    --bs-position: start;
  }
  .bs .dropdown-menu-md-start[data-bs-popper] {
    right: auto;
    left: 0;
  }
  .bs .dropdown-menu-md-end {
    --bs-position: end;
  }
  .bs .dropdown-menu-md-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 992px) {
  .bs .dropdown-menu-lg-start {
    --bs-position: start;
  }
  .bs .dropdown-menu-lg-start[data-bs-popper] {
    right: auto;
    left: 0;
  }
  .bs .dropdown-menu-lg-end {
    --bs-position: end;
  }
  .bs .dropdown-menu-lg-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 1200px) {
  .bs .dropdown-menu-xl-start {
    --bs-position: start;
  }
  .bs .dropdown-menu-xl-start[data-bs-popper] {
    right: auto;
    left: 0;
  }
  .bs .dropdown-menu-xl-end {
    --bs-position: end;
  }
  .bs .dropdown-menu-xl-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
@media (min-width: 1400px) {
  .bs .dropdown-menu-xxl-start {
    --bs-position: start;
  }
  .bs .dropdown-menu-xxl-start[data-bs-popper] {
    right: auto;
    left: 0;
  }
  .bs .dropdown-menu-xxl-end {
    --bs-position: end;
  }
  .bs .dropdown-menu-xxl-end[data-bs-popper] {
    right: 0;
    left: auto;
  }
}
.bs .dropup .dropdown-menu[data-bs-popper] {
  top: auto;
  bottom: 100%;
  margin-top: 0;
  margin-bottom: var(--bs-dropdown-spacer);
}
.bs .dropup .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0;
  border-right: 0.3em solid transparent;
  border-bottom: 0.3em solid;
  border-left: 0.3em solid transparent;
}
.bs .dropup .dropdown-toggle:empty::after {
  margin-left: 0;
}
.bs .dropend .dropdown-menu[data-bs-popper] {
  top: 0;
  right: auto;
  left: 100%;
  margin-top: 0;
  margin-left: var(--bs-dropdown-spacer);
}
.bs .dropend .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0;
  border-bottom: 0.3em solid transparent;
  border-left: 0.3em solid;
}
.bs .dropend .dropdown-toggle:empty::after {
  margin-left: 0;
}
.bs .dropend .dropdown-toggle::after {
  vertical-align: 0;
}
.bs .dropstart .dropdown-menu[data-bs-popper] {
  top: 0;
  right: 100%;
  left: auto;
  margin-top: 0;
  margin-right: var(--bs-dropdown-spacer);
}
.bs .dropstart .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
}
.bs .dropstart .dropdown-toggle::after {
  display: none;
}
.bs .dropstart .dropdown-toggle::before {
  display: inline-block;
  margin-right: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid transparent;
  border-right: 0.3em solid;
  border-bottom: 0.3em solid transparent;
}
.bs .dropstart .dropdown-toggle:empty::after {
  margin-left: 0;
}
.bs .dropstart .dropdown-toggle::before {
  vertical-align: 0;
}
.bs .dropdown-divider {
  height: 0;
  margin: var(--bs-dropdown-divider-margin-y) 0;
  overflow: hidden;
  border-top: 1px solid var(--bs-dropdown-divider-bg);
  opacity: 1;
}
.bs .dropdown-item {
  display: block;
  width: 100%;
  padding: var(--bs-dropdown-item-padding-y) var(--bs-dropdown-item-padding-x);
  clear: both;
  font-weight: 400;
  color: var(--bs-dropdown-link-color);
  text-align: inherit;
  text-decoration: none;
  white-space: nowrap;
  background-color: transparent;
  border: 0;
}
.bs .dropdown-item:hover, .bs .dropdown-item:focus {
  color: var(--bs-dropdown-link-hover-color);
  background-color: var(--bs-dropdown-link-hover-bg);
}
.bs .dropdown-item.active, .bs .dropdown-item:active {
  color: var(--bs-dropdown-link-active-color);
  text-decoration: none;
  background-color: var(--bs-dropdown-link-active-bg);
}
.bs .dropdown-item.disabled, .bs .dropdown-item:disabled {
  color: var(--bs-dropdown-link-disabled-color);
  pointer-events: none;
  background-color: transparent;
}
.bs .dropdown-menu.show {
  display: block;
}
.bs .dropdown-header {
  display: block;
  padding: var(--bs-dropdown-header-padding-y) var(--bs-dropdown-header-padding-x);
  margin-bottom: 0;
  font-size: 0.875rem;
  color: var(--bs-dropdown-header-color);
  white-space: nowrap;
}
.bs .dropdown-item-text {
  display: block;
  padding: var(--bs-dropdown-item-padding-y) var(--bs-dropdown-item-padding-x);
  color: var(--bs-dropdown-link-color);
}
.bs .dropdown-menu-dark {
  --bs-dropdown-color: #dee2e6;
  --bs-dropdown-bg: #343a40;
  --bs-dropdown-border-color: var(--bs-border-color-translucent);
  --bs-dropdown-box-shadow: ;
  --bs-dropdown-link-color: #dee2e6;
  --bs-dropdown-link-hover-color: #fff;
  --bs-dropdown-divider-bg: var(--bs-border-color-translucent);
  --bs-dropdown-link-hover-bg: rgba(255, 255, 255, 0.15);
  --bs-dropdown-link-active-color: #fff;
  --bs-dropdown-link-active-bg: #0d6efd;
  --bs-dropdown-link-disabled-color: #adb5bd;
  --bs-dropdown-header-color: #adb5bd;
}
.bs .btn-group,
.bs .btn-group-vertical {
  position: relative;
  display: inline-flex;
  vertical-align: middle;
}
.bs .btn-group > .btn,
.bs .btn-group-vertical > .btn {
  position: relative;
  flex: 1 1 auto;
}
.bs .btn-group > .btn-check:checked + .btn,
.bs .btn-group > .btn-check:focus + .btn,
.bs .btn-group > .btn:hover,
.bs .btn-group > .btn:focus,
.bs .btn-group > .btn:active,
.bs .btn-group > .btn.active,
.bs .btn-group-vertical > .btn-check:checked + .btn,
.bs .btn-group-vertical > .btn-check:focus + .btn,
.bs .btn-group-vertical > .btn:hover,
.bs .btn-group-vertical > .btn:focus,
.bs .btn-group-vertical > .btn:active,
.bs .btn-group-vertical > .btn.active {
  z-index: 1;
}
.bs .btn-toolbar {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}
.bs .btn-toolbar .input-group {
  width: auto;
}
.bs .btn-group {
  border-radius: 0.375rem;
}
.bs .btn-group > :not(.btn-check:first-child) + .btn,
.bs .btn-group > .btn-group:not(:first-child) {
  margin-left: -1px;
}
.bs .btn-group > .btn:not(:last-child):not(.dropdown-toggle),
.bs .btn-group > .btn.dropdown-toggle-split:first-child,
.bs .btn-group > .btn-group:not(:last-child) > .btn {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.bs .btn-group > .btn:nth-child(n+3),
.bs .btn-group > :not(.btn-check) + .btn,
.bs .btn-group > .btn-group:not(:first-child) > .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.bs .dropdown-toggle-split {
  padding-right: 0.5625rem;
  padding-left: 0.5625rem;
}
.bs .dropdown-toggle-split::after, .dropup .bs .dropdown-toggle-split::after, .dropend .bs .dropdown-toggle-split::after {
  margin-left: 0;
}
.dropstart .bs .dropdown-toggle-split::before {
  margin-right: 0;
}
.bs .btn-sm + .dropdown-toggle-split, .bs .btn-group-sm > .btn + .dropdown-toggle-split {
  padding-right: 0.375rem;
  padding-left: 0.375rem;
}
.bs .btn-lg + .dropdown-toggle-split, .bs .btn-group-lg > .btn + .dropdown-toggle-split {
  padding-right: 0.75rem;
  padding-left: 0.75rem;
}
.bs .btn-group-vertical {
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}
.bs .btn-group-vertical > .btn,
.bs .btn-group-vertical > .btn-group {
  width: 100%;
}
.bs .btn-group-vertical > .btn:not(:first-child),
.bs .btn-group-vertical > .btn-group:not(:first-child) {
  margin-top: -1px;
}
.bs .btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle),
.bs .btn-group-vertical > .btn-group:not(:last-child) > .btn {
  border-bottom-right-radius: 0;
  border-bottom-left-radius: 0;
}
.bs .btn-group-vertical > .btn ~ .btn,
.bs .btn-group-vertical > .btn-group:not(:first-child) > .btn {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.bs .nav {
  --bs-nav-link-padding-x: 1rem;
  --bs-nav-link-padding-y: 0.5rem;
  --bs-nav-link-font-weight: ;
  --bs-nav-link-color: var(--bs-link-color);
  --bs-nav-link-hover-color: var(--bs-link-hover-color);
  --bs-nav-link-disabled-color: #6c757d;
  display: flex;
  flex-wrap: wrap;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}
.bs .nav-link {
  display: block;
  padding: var(--bs-nav-link-padding-y) var(--bs-nav-link-padding-x);
  font-size: var(--bs-nav-link-font-size);
  font-weight: var(--bs-nav-link-font-weight);
  color: var(--bs-nav-link-color);
  text-decoration: none;
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .bs .nav-link {
    transition: none;
  }
}
.bs .nav-link:hover, .bs .nav-link:focus {
  color: var(--bs-nav-link-hover-color);
}
.bs .nav-link.disabled {
  color: var(--bs-nav-link-disabled-color);
  pointer-events: none;
  cursor: default;
}
.bs .nav-tabs {
  --bs-nav-tabs-border-width: 1px;
  --bs-nav-tabs-border-color: #dee2e6;
  --bs-nav-tabs-border-radius: 0.375rem;
  --bs-nav-tabs-link-hover-border-color: #e9ecef #e9ecef #dee2e6;
  --bs-nav-tabs-link-active-color: #495057;
  --bs-nav-tabs-link-active-bg: #fff;
  --bs-nav-tabs-link-active-border-color: #dee2e6 #dee2e6 #fff;
  border-bottom: var(--bs-nav-tabs-border-width) solid var(--bs-nav-tabs-border-color);
}
.bs .nav-tabs .nav-link {
  margin-bottom: calc(-1 * var(--bs-nav-tabs-border-width));
  background: none;
  border: var(--bs-nav-tabs-border-width) solid transparent;
  border-top-left-radius: var(--bs-nav-tabs-border-radius);
  border-top-right-radius: var(--bs-nav-tabs-border-radius);
}
.bs .nav-tabs .nav-link:hover, .bs .nav-tabs .nav-link:focus {
  isolation: isolate;
  border-color: var(--bs-nav-tabs-link-hover-border-color);
}
.bs .nav-tabs .nav-link.disabled, .bs .nav-tabs .nav-link:disabled {
  color: var(--bs-nav-link-disabled-color);
  background-color: transparent;
  border-color: transparent;
}
.bs .nav-tabs .nav-link.active,
.bs .nav-tabs .nav-item.show .nav-link {
  color: var(--bs-nav-tabs-link-active-color);
  background-color: var(--bs-nav-tabs-link-active-bg);
  border-color: var(--bs-nav-tabs-link-active-border-color);
}
.bs .nav-tabs .dropdown-menu {
  margin-top: calc(-1 * var(--bs-nav-tabs-border-width));
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}
.bs .nav-pills {
  --bs-nav-pills-border-radius: 0.375rem;
  --bs-nav-pills-link-active-color: #fff;
  --bs-nav-pills-link-active-bg: #0d6efd;
}
.bs .nav-pills .nav-link {
  background: none;
  border: 0;
  border-radius: var(--bs-nav-pills-border-radius);
}
.bs .nav-pills .nav-link:disabled {
  color: var(--bs-nav-link-disabled-color);
  background-color: transparent;
  border-color: transparent;
}
.bs .nav-pills .nav-link.active,
.bs .nav-pills .show > .nav-link {
  color: var(--bs-nav-pills-link-active-color);
  background-color: var(--bs-nav-pills-link-active-bg);
}
.bs .nav-fill > .nav-link,
.bs .nav-fill .nav-item {
  flex: 1 1 auto;
  text-align: center;
}
.bs .nav-justified > .nav-link,
.bs .nav-justified .nav-item {
  flex-basis: 0;
  flex-grow: 1;
  text-align: center;
}
.bs .nav-fill .nav-item .nav-link,
.bs .nav-justified .nav-item .nav-link {
  width: 100%;
}
.bs .tab-content > .tab-pane {
  display: none;
}
.bs .tab-content > .active {
  display: block;
}
.bs .navbar {
  --bs-navbar-padding-x: 0;
  --bs-navbar-padding-y: 0.5rem;
  --bs-navbar-color: rgba(0, 0, 0, 0.55);
  --bs-navbar-hover-color: rgba(0, 0, 0, 0.7);
  --bs-navbar-disabled-color: rgba(0, 0, 0, 0.3);
  --bs-navbar-active-color: rgba(0, 0, 0, 0.9);
  --bs-navbar-brand-padding-y: 0.3125rem;
  --bs-navbar-brand-margin-end: 1rem;
  --bs-navbar-brand-font-size: 1.25rem;
  --bs-navbar-brand-color: rgba(0, 0, 0, 0.9);
  --bs-navbar-brand-hover-color: rgba(0, 0, 0, 0.9);
  --bs-navbar-nav-link-padding-x: 0.5rem;
  --bs-navbar-toggler-padding-y: 0.25rem;
  --bs-navbar-toggler-padding-x: 0.75rem;
  --bs-navbar-toggler-font-size: 1.25rem;
  --bs-navbar-toggler-icon-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%280, 0, 0, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
  --bs-navbar-toggler-border-color: rgba(0, 0, 0, 0.1);
  --bs-navbar-toggler-border-radius: 0.375rem;
  --bs-navbar-toggler-focus-width: 0.25rem;
  --bs-navbar-toggler-transition: box-shadow 0.15s ease-in-out;
  position: relative;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: space-between;
  padding: var(--bs-navbar-padding-y) var(--bs-navbar-padding-x);
}
.bs .navbar > .container,
.bs .navbar > .container-fluid,
.bs .navbar > .container-sm,
.bs .navbar > .container-md,
.bs .navbar > .container-lg,
.bs .navbar > .container-xl,
.bs .navbar > .container-xxl {
  display: flex;
  flex-wrap: inherit;
  align-items: center;
  justify-content: space-between;
}
.bs .navbar-brand {
  padding-top: var(--bs-navbar-brand-padding-y);
  padding-bottom: var(--bs-navbar-brand-padding-y);
  margin-right: var(--bs-navbar-brand-margin-end);
  font-size: var(--bs-navbar-brand-font-size);
  color: var(--bs-navbar-brand-color);
  text-decoration: none;
  white-space: nowrap;
}
.bs .navbar-brand:hover, .bs .navbar-brand:focus {
  color: var(--bs-navbar-brand-hover-color);
}
.bs .navbar-nav {
  --bs-nav-link-padding-x: 0;
  --bs-nav-link-padding-y: 0.5rem;
  --bs-nav-link-font-weight: ;
  --bs-nav-link-color: var(--bs-navbar-color);
  --bs-nav-link-hover-color: var(--bs-navbar-hover-color);
  --bs-nav-link-disabled-color: var(--bs-navbar-disabled-color);
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  list-style: none;
}
.bs .navbar-nav .show > .nav-link,
.bs .navbar-nav .nav-link.active {
  color: var(--bs-navbar-active-color);
}
.bs .navbar-nav .dropdown-menu {
  position: static;
}
.bs .navbar-text {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  color: var(--bs-navbar-color);
}
.bs .navbar-text a,
.bs .navbar-text a:hover,
.bs .navbar-text a:focus {
  color: var(--bs-navbar-active-color);
}
.bs .navbar-collapse {
  flex-basis: 100%;
  flex-grow: 1;
  align-items: center;
}
.bs .navbar-toggler {
  padding: var(--bs-navbar-toggler-padding-y) var(--bs-navbar-toggler-padding-x);
  font-size: var(--bs-navbar-toggler-font-size);
  line-height: 1;
  color: var(--bs-navbar-color);
  background-color: transparent;
  border: var(--bs-border-width) solid var(--bs-navbar-toggler-border-color);
  border-radius: var(--bs-navbar-toggler-border-radius);
  transition: var(--bs-navbar-toggler-transition);
}
@media (prefers-reduced-motion: reduce) {
  .bs .navbar-toggler {
    transition: none;
  }
}
.bs .navbar-toggler:hover {
  text-decoration: none;
}
.bs .navbar-toggler:focus {
  text-decoration: none;
  outline: 0;
  box-shadow: 0 0 0 var(--bs-navbar-toggler-focus-width);
}
.bs .navbar-toggler-icon {
  display: inline-block;
  width: 1.5em;
  height: 1.5em;
  vertical-align: middle;
  background-image: var(--bs-navbar-toggler-icon-bg);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100%;
}
.bs .navbar-nav-scroll {
  max-height: var(--bs-scroll-height, 75vh);
  overflow-y: auto;
}
@media (min-width: 576px) {
  .bs .navbar-expand-sm {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .bs .navbar-expand-sm .navbar-nav {
    flex-direction: row;
  }
  .bs .navbar-expand-sm .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .bs .navbar-expand-sm .navbar-nav .nav-link {
    padding-right: var(--bs-navbar-nav-link-padding-x);
    padding-left: var(--bs-navbar-nav-link-padding-x);
  }
  .bs .navbar-expand-sm .navbar-nav-scroll {
    overflow: visible;
  }
  .bs .navbar-expand-sm .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .bs .navbar-expand-sm .navbar-toggler {
    display: none;
  }
  .bs .navbar-expand-sm .offcanvas {
    position: static;
    z-index: auto;
    flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    transform: none !important;
    transition: none;
  }
  .bs .navbar-expand-sm .offcanvas .offcanvas-header {
    display: none;
  }
  .bs .navbar-expand-sm .offcanvas .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 768px) {
  .bs .navbar-expand-md {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .bs .navbar-expand-md .navbar-nav {
    flex-direction: row;
  }
  .bs .navbar-expand-md .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .bs .navbar-expand-md .navbar-nav .nav-link {
    padding-right: var(--bs-navbar-nav-link-padding-x);
    padding-left: var(--bs-navbar-nav-link-padding-x);
  }
  .bs .navbar-expand-md .navbar-nav-scroll {
    overflow: visible;
  }
  .bs .navbar-expand-md .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .bs .navbar-expand-md .navbar-toggler {
    display: none;
  }
  .bs .navbar-expand-md .offcanvas {
    position: static;
    z-index: auto;
    flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    transform: none !important;
    transition: none;
  }
  .bs .navbar-expand-md .offcanvas .offcanvas-header {
    display: none;
  }
  .bs .navbar-expand-md .offcanvas .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 992px) {
  .bs .navbar-expand-lg {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .bs .navbar-expand-lg .navbar-nav {
    flex-direction: row;
  }
  .bs .navbar-expand-lg .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .bs .navbar-expand-lg .navbar-nav .nav-link {
    padding-right: var(--bs-navbar-nav-link-padding-x);
    padding-left: var(--bs-navbar-nav-link-padding-x);
  }
  .bs .navbar-expand-lg .navbar-nav-scroll {
    overflow: visible;
  }
  .bs .navbar-expand-lg .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .bs .navbar-expand-lg .navbar-toggler {
    display: none;
  }
  .bs .navbar-expand-lg .offcanvas {
    position: static;
    z-index: auto;
    flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    transform: none !important;
    transition: none;
  }
  .bs .navbar-expand-lg .offcanvas .offcanvas-header {
    display: none;
  }
  .bs .navbar-expand-lg .offcanvas .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 1200px) {
  .bs .navbar-expand-xl {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .bs .navbar-expand-xl .navbar-nav {
    flex-direction: row;
  }
  .bs .navbar-expand-xl .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .bs .navbar-expand-xl .navbar-nav .nav-link {
    padding-right: var(--bs-navbar-nav-link-padding-x);
    padding-left: var(--bs-navbar-nav-link-padding-x);
  }
  .bs .navbar-expand-xl .navbar-nav-scroll {
    overflow: visible;
  }
  .bs .navbar-expand-xl .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .bs .navbar-expand-xl .navbar-toggler {
    display: none;
  }
  .bs .navbar-expand-xl .offcanvas {
    position: static;
    z-index: auto;
    flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    transform: none !important;
    transition: none;
  }
  .bs .navbar-expand-xl .offcanvas .offcanvas-header {
    display: none;
  }
  .bs .navbar-expand-xl .offcanvas .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
@media (min-width: 1400px) {
  .bs .navbar-expand-xxl {
    flex-wrap: nowrap;
    justify-content: flex-start;
  }
  .bs .navbar-expand-xxl .navbar-nav {
    flex-direction: row;
  }
  .bs .navbar-expand-xxl .navbar-nav .dropdown-menu {
    position: absolute;
  }
  .bs .navbar-expand-xxl .navbar-nav .nav-link {
    padding-right: var(--bs-navbar-nav-link-padding-x);
    padding-left: var(--bs-navbar-nav-link-padding-x);
  }
  .bs .navbar-expand-xxl .navbar-nav-scroll {
    overflow: visible;
  }
  .bs .navbar-expand-xxl .navbar-collapse {
    display: flex !important;
    flex-basis: auto;
  }
  .bs .navbar-expand-xxl .navbar-toggler {
    display: none;
  }
  .bs .navbar-expand-xxl .offcanvas {
    position: static;
    z-index: auto;
    flex-grow: 1;
    width: auto !important;
    height: auto !important;
    visibility: visible !important;
    background-color: transparent !important;
    border: 0 !important;
    transform: none !important;
    transition: none;
  }
  .bs .navbar-expand-xxl .offcanvas .offcanvas-header {
    display: none;
  }
  .bs .navbar-expand-xxl .offcanvas .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
  }
}
.bs .navbar-expand {
  flex-wrap: nowrap;
  justify-content: flex-start;
}
.bs .navbar-expand .navbar-nav {
  flex-direction: row;
}
.bs .navbar-expand .navbar-nav .dropdown-menu {
  position: absolute;
}
.bs .navbar-expand .navbar-nav .nav-link {
  padding-right: var(--bs-navbar-nav-link-padding-x);
  padding-left: var(--bs-navbar-nav-link-padding-x);
}
.bs .navbar-expand .navbar-nav-scroll {
  overflow: visible;
}
.bs .navbar-expand .navbar-collapse {
  display: flex !important;
  flex-basis: auto;
}
.bs .navbar-expand .navbar-toggler {
  display: none;
}
.bs .navbar-expand .offcanvas {
  position: static;
  z-index: auto;
  flex-grow: 1;
  width: auto !important;
  height: auto !important;
  visibility: visible !important;
  background-color: transparent !important;
  border: 0 !important;
  transform: none !important;
  transition: none;
}
.bs .navbar-expand .offcanvas .offcanvas-header {
  display: none;
}
.bs .navbar-expand .offcanvas .offcanvas-body {
  display: flex;
  flex-grow: 0;
  padding: 0;
  overflow-y: visible;
}
.bs .navbar-dark {
  --bs-navbar-color: rgba(255, 255, 255, 0.55);
  --bs-navbar-hover-color: rgba(255, 255, 255, 0.75);
  --bs-navbar-disabled-color: rgba(255, 255, 255, 0.25);
  --bs-navbar-active-color: #fff;
  --bs-navbar-brand-color: #fff;
  --bs-navbar-brand-hover-color: #fff;
  --bs-navbar-toggler-border-color: rgba(255, 255, 255, 0.1);
  --bs-navbar-toggler-icon-bg: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");
}
.bs .card {
  --bs-card-spacer-y: 1rem;
  --bs-card-spacer-x: 1rem;
  --bs-card-title-spacer-y: 0.5rem;
  --bs-card-border-width: 1px;
  --bs-card-border-color: var(--bs-border-color-translucent);
  --bs-card-border-radius: 0.375rem;
  --bs-card-box-shadow: ;
  --bs-card-inner-border-radius: calc(0.375rem - 1px);
  --bs-card-cap-padding-y: 0.5rem;
  --bs-card-cap-padding-x: 1rem;
  --bs-card-cap-bg: rgba(0, 0, 0, 0.03);
  --bs-card-cap-color: ;
  --bs-card-height: ;
  --bs-card-color: ;
  --bs-card-bg: #fff;
  --bs-card-img-overlay-padding: 1rem;
  --bs-card-group-margin: 0.75rem;
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  height: var(--bs-card-height);
  word-wrap: break-word;
  background-color: var(--bs-card-bg);
  background-clip: border-box;
  border: var(--bs-card-border-width) solid var(--bs-card-border-color);
  border-radius: var(--bs-card-border-radius);
}
.bs .card > hr {
  margin-right: 0;
  margin-left: 0;
}
.bs .card > .list-group {
  border-top: inherit;
  border-bottom: inherit;
}
.bs .card > .list-group:first-child {
  border-top-width: 0;
  border-top-left-radius: var(--bs-card-inner-border-radius);
  border-top-right-radius: var(--bs-card-inner-border-radius);
}
.bs .card > .list-group:last-child {
  border-bottom-width: 0;
  border-bottom-right-radius: var(--bs-card-inner-border-radius);
  border-bottom-left-radius: var(--bs-card-inner-border-radius);
}
.bs .card > .card-header + .list-group,
.bs .card > .list-group + .card-footer {
  border-top: 0;
}
.bs .card-body {
  flex: 1 1 auto;
  padding: var(--bs-card-spacer-y) var(--bs-card-spacer-x);
  color: var(--bs-card-color);
}
.bs .card-title {
  margin-bottom: var(--bs-card-title-spacer-y);
}
.bs .card-subtitle {
  margin-top: calc(-0.5 * var(--bs-card-title-spacer-y));
  margin-bottom: 0;
}
.bs .card-text:last-child {
  margin-bottom: 0;
}
.bs .card-link + .card-link {
  margin-left: var(--bs-card-spacer-x);
}
.bs .card-header {
  padding: var(--bs-card-cap-padding-y) var(--bs-card-cap-padding-x);
  margin-bottom: 0;
  color: var(--bs-card-cap-color);
  background-color: var(--bs-card-cap-bg);
  border-bottom: var(--bs-card-border-width) solid var(--bs-card-border-color);
}
.bs .card-header:first-child {
  border-radius: var(--bs-card-inner-border-radius) var(--bs-card-inner-border-radius) 0 0;
}
.bs .card-footer {
  padding: var(--bs-card-cap-padding-y) var(--bs-card-cap-padding-x);
  color: var(--bs-card-cap-color);
  background-color: var(--bs-card-cap-bg);
  border-top: var(--bs-card-border-width) solid var(--bs-card-border-color);
}
.bs .card-footer:last-child {
  border-radius: 0 0 var(--bs-card-inner-border-radius) var(--bs-card-inner-border-radius);
}
.bs .card-header-tabs {
  margin-right: calc(-0.5 * var(--bs-card-cap-padding-x));
  margin-bottom: calc(-1 * var(--bs-card-cap-padding-y));
  margin-left: calc(-0.5 * var(--bs-card-cap-padding-x));
  border-bottom: 0;
}
.bs .card-header-tabs .nav-link.active {
  background-color: var(--bs-card-bg);
  border-bottom-color: var(--bs-card-bg);
}
.bs .card-header-pills {
  margin-right: calc(-0.5 * var(--bs-card-cap-padding-x));
  margin-left: calc(-0.5 * var(--bs-card-cap-padding-x));
}
.bs .card-img-overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  padding: var(--bs-card-img-overlay-padding);
  border-radius: var(--bs-card-inner-border-radius);
}
.bs .card-img,
.bs .card-img-top,
.bs .card-img-bottom {
  width: 100%;
}
.bs .card-img,
.bs .card-img-top {
  border-top-left-radius: var(--bs-card-inner-border-radius);
  border-top-right-radius: var(--bs-card-inner-border-radius);
}
.bs .card-img,
.bs .card-img-bottom {
  border-bottom-right-radius: var(--bs-card-inner-border-radius);
  border-bottom-left-radius: var(--bs-card-inner-border-radius);
}
.bs .card-group > .card {
  margin-bottom: var(--bs-card-group-margin);
}
@media (min-width: 576px) {
  .bs .card-group {
    display: flex;
    flex-flow: row wrap;
  }
  .bs .card-group > .card {
    flex: 1 0 0%;
    margin-bottom: 0;
  }
  .bs .card-group > .card + .card {
    margin-left: 0;
    border-left: 0;
  }
  .bs .card-group > .card:not(:last-child) {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .bs .card-group > .card:not(:last-child) .card-img-top,
  .bs .card-group > .card:not(:last-child) .card-header {
    border-top-right-radius: 0;
  }
  .bs .card-group > .card:not(:last-child) .card-img-bottom,
  .bs .card-group > .card:not(:last-child) .card-footer {
    border-bottom-right-radius: 0;
  }
  .bs .card-group > .card:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
  .bs .card-group > .card:not(:first-child) .card-img-top,
  .bs .card-group > .card:not(:first-child) .card-header {
    border-top-left-radius: 0;
  }
  .bs .card-group > .card:not(:first-child) .card-img-bottom,
  .bs .card-group > .card:not(:first-child) .card-footer {
    border-bottom-left-radius: 0;
  }
}
.bs .accordion {
  --bs-accordion-color: #212529;
  --bs-accordion-bg: #fff;
  --bs-accordion-transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, border-radius 0.15s ease;
  --bs-accordion-border-color: var(--bs-border-color);
  --bs-accordion-border-width: 1px;
  --bs-accordion-border-radius: 0.375rem;
  --bs-accordion-inner-border-radius: calc(0.375rem - 1px);
  --bs-accordion-btn-padding-x: 1.25rem;
  --bs-accordion-btn-padding-y: 1rem;
  --bs-accordion-btn-color: #212529;
  --bs-accordion-btn-bg: var(--bs-accordion-bg);
  --bs-accordion-btn-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23212529'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
  --bs-accordion-btn-icon-width: 1.25rem;
  --bs-accordion-btn-icon-transform: rotate(-180deg);
  --bs-accordion-btn-icon-transition: transform 0.2s ease-in-out;
  --bs-accordion-btn-active-icon: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='rgb%2811.7, 99, 227.7%29'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
  --bs-accordion-btn-focus-border-color: rgb(134, 182.5, 254);
  --bs-accordion-btn-focus-box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
  --bs-accordion-body-padding-x: 1.25rem;
  --bs-accordion-body-padding-y: 1rem;
  --bs-accordion-active-color: rgb(11.7, 99, 227.7);
  --bs-accordion-active-bg: rgb(230.8, 240.5, 254.8);
}
.bs .accordion-button {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  padding: var(--bs-accordion-btn-padding-y) var(--bs-accordion-btn-padding-x);
  font-size: 1rem;
  color: var(--bs-accordion-btn-color);
  text-align: left;
  background-color: var(--bs-accordion-btn-bg);
  border: 0;
  border-radius: 0;
  overflow-anchor: none;
  transition: var(--bs-accordion-transition);
}
@media (prefers-reduced-motion: reduce) {
  .bs .accordion-button {
    transition: none;
  }
}
.bs .accordion-button:not(.collapsed) {
  color: var(--bs-accordion-active-color);
  background-color: var(--bs-accordion-active-bg);
  box-shadow: inset 0 calc(-1 * var(--bs-accordion-border-width)) 0 var(--bs-accordion-border-color);
}
.bs .accordion-button:not(.collapsed)::after {
  background-image: var(--bs-accordion-btn-active-icon);
  transform: var(--bs-accordion-btn-icon-transform);
}
.bs .accordion-button::after {
  flex-shrink: 0;
  width: var(--bs-accordion-btn-icon-width);
  height: var(--bs-accordion-btn-icon-width);
  margin-left: auto;
  content: "";
  background-image: var(--bs-accordion-btn-icon);
  background-repeat: no-repeat;
  background-size: var(--bs-accordion-btn-icon-width);
  transition: var(--bs-accordion-btn-icon-transition);
}
@media (prefers-reduced-motion: reduce) {
  .bs .accordion-button::after {
    transition: none;
  }
}
.bs .accordion-button:hover {
  z-index: 2;
}
.bs .accordion-button:focus {
  z-index: 3;
  border-color: var(--bs-accordion-btn-focus-border-color);
  outline: 0;
  box-shadow: var(--bs-accordion-btn-focus-box-shadow);
}
.bs .accordion-header {
  margin-bottom: 0;
}
.bs .accordion-item {
  color: var(--bs-accordion-color);
  background-color: var(--bs-accordion-bg);
  border: var(--bs-accordion-border-width) solid var(--bs-accordion-border-color);
}
.bs .accordion-item:first-of-type {
  border-top-left-radius: var(--bs-accordion-border-radius);
  border-top-right-radius: var(--bs-accordion-border-radius);
}
.bs .accordion-item:first-of-type .accordion-button {
  border-top-left-radius: var(--bs-accordion-inner-border-radius);
  border-top-right-radius: var(--bs-accordion-inner-border-radius);
}
.bs .accordion-item:not(:first-of-type) {
  border-top: 0;
}
.bs .accordion-item:last-of-type {
  border-bottom-right-radius: var(--bs-accordion-border-radius);
  border-bottom-left-radius: var(--bs-accordion-border-radius);
}
.bs .accordion-item:last-of-type .accordion-button.collapsed {
  border-bottom-right-radius: var(--bs-accordion-inner-border-radius);
  border-bottom-left-radius: var(--bs-accordion-inner-border-radius);
}
.bs .accordion-item:last-of-type .accordion-collapse {
  border-bottom-right-radius: var(--bs-accordion-border-radius);
  border-bottom-left-radius: var(--bs-accordion-border-radius);
}
.bs .accordion-body {
  padding: var(--bs-accordion-body-padding-y) var(--bs-accordion-body-padding-x);
}
.bs .accordion-flush .accordion-collapse {
  border-width: 0;
}
.bs .accordion-flush .accordion-item {
  border-right: 0;
  border-left: 0;
  border-radius: 0;
}
.bs .accordion-flush .accordion-item:first-child {
  border-top: 0;
}
.bs .accordion-flush .accordion-item:last-child {
  border-bottom: 0;
}
.bs .accordion-flush .accordion-item .accordion-button, .bs .accordion-flush .accordion-item .accordion-button.collapsed {
  border-radius: 0;
}
.bs .breadcrumb {
  --bs-breadcrumb-padding-x: 0;
  --bs-breadcrumb-padding-y: 0;
  --bs-breadcrumb-margin-bottom: 1rem;
  --bs-breadcrumb-bg: ;
  --bs-breadcrumb-border-radius: ;
  --bs-breadcrumb-divider-color: #6c757d;
  --bs-breadcrumb-item-padding-x: 0.5rem;
  --bs-breadcrumb-item-active-color: #6c757d;
  display: flex;
  flex-wrap: wrap;
  padding: var(--bs-breadcrumb-padding-y) var(--bs-breadcrumb-padding-x);
  margin-bottom: var(--bs-breadcrumb-margin-bottom);
  font-size: var(--bs-breadcrumb-font-size);
  list-style: none;
  background-color: var(--bs-breadcrumb-bg);
  border-radius: var(--bs-breadcrumb-border-radius);
}
.bs .breadcrumb-item + .breadcrumb-item {
  padding-left: var(--bs-breadcrumb-item-padding-x);
}
.bs .breadcrumb-item + .breadcrumb-item::before {
  float: left;
  padding-right: var(--bs-breadcrumb-item-padding-x);
  color: var(--bs-breadcrumb-divider-color);
  content: var(--bs-breadcrumb-divider, "/") /* rtl: var(--bs-breadcrumb-divider, "/") */;
}
.bs .breadcrumb-item.active {
  color: var(--bs-breadcrumb-item-active-color);
}
.bs .pagination {
  --bs-pagination-padding-x: 0.75rem;
  --bs-pagination-padding-y: 0.375rem;
  --bs-pagination-font-size: 1rem;
  --bs-pagination-color: var(--bs-link-color);
  --bs-pagination-bg: #fff;
  --bs-pagination-border-width: 1px;
  --bs-pagination-border-color: #dee2e6;
  --bs-pagination-border-radius: 0.375rem;
  --bs-pagination-hover-color: var(--bs-link-hover-color);
  --bs-pagination-hover-bg: #e9ecef;
  --bs-pagination-hover-border-color: #dee2e6;
  --bs-pagination-focus-color: var(--bs-link-hover-color);
  --bs-pagination-focus-bg: #e9ecef;
  --bs-pagination-focus-box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
  --bs-pagination-active-color: #fff;
  --bs-pagination-active-bg: #0d6efd;
  --bs-pagination-active-border-color: #0d6efd;
  --bs-pagination-disabled-color: #6c757d;
  --bs-pagination-disabled-bg: #fff;
  --bs-pagination-disabled-border-color: #dee2e6;
  display: flex;
  padding-left: 0;
  list-style: none;
}
.bs .page-link {
  position: relative;
  display: block;
  padding: var(--bs-pagination-padding-y) var(--bs-pagination-padding-x);
  font-size: var(--bs-pagination-font-size);
  color: var(--bs-pagination-color);
  text-decoration: none;
  background-color: var(--bs-pagination-bg);
  border: var(--bs-pagination-border-width) solid var(--bs-pagination-border-color);
  transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .bs .page-link {
    transition: none;
  }
}
.bs .page-link:hover {
  z-index: 2;
  color: var(--bs-pagination-hover-color);
  background-color: var(--bs-pagination-hover-bg);
  border-color: var(--bs-pagination-hover-border-color);
}
.bs .page-link:focus {
  z-index: 3;
  color: var(--bs-pagination-focus-color);
  background-color: var(--bs-pagination-focus-bg);
  outline: 0;
  box-shadow: var(--bs-pagination-focus-box-shadow);
}
.bs .page-link.active, .active > .bs .page-link {
  z-index: 3;
  color: var(--bs-pagination-active-color);
  background-color: var(--bs-pagination-active-bg);
  border-color: var(--bs-pagination-active-border-color);
}
.bs .page-link.disabled, .disabled > .bs .page-link {
  color: var(--bs-pagination-disabled-color);
  pointer-events: none;
  background-color: var(--bs-pagination-disabled-bg);
  border-color: var(--bs-pagination-disabled-border-color);
}
.bs .page-item:not(:first-child) .page-link {
  margin-left: -1px;
}
.bs .page-item:first-child .page-link {
  border-top-left-radius: var(--bs-pagination-border-radius);
  border-bottom-left-radius: var(--bs-pagination-border-radius);
}
.bs .page-item:last-child .page-link {
  border-top-right-radius: var(--bs-pagination-border-radius);
  border-bottom-right-radius: var(--bs-pagination-border-radius);
}
.bs .pagination-lg {
  --bs-pagination-padding-x: 1.5rem;
  --bs-pagination-padding-y: 0.75rem;
  --bs-pagination-font-size: 1.25rem;
  --bs-pagination-border-radius: 0.5rem;
}
.bs .pagination-sm {
  --bs-pagination-padding-x: 0.5rem;
  --bs-pagination-padding-y: 0.25rem;
  --bs-pagination-font-size: 0.875rem;
  --bs-pagination-border-radius: 0.25rem;
}
.bs .badge {
  --bs-badge-padding-x: 0.65em;
  --bs-badge-padding-y: 0.35em;
  --bs-badge-font-size: 0.75em;
  --bs-badge-font-weight: 700;
  --bs-badge-color: #fff;
  --bs-badge-border-radius: 0.375rem;
  display: inline-block;
  padding: var(--bs-badge-padding-y) var(--bs-badge-padding-x);
  font-size: var(--bs-badge-font-size);
  font-weight: var(--bs-badge-font-weight);
  line-height: 1;
  color: var(--bs-badge-color);
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: var(--bs-badge-border-radius);
}
.bs .badge:empty {
  display: none;
}
.bs .btn .badge {
  position: relative;
  top: -1px;
}
.bs .alert {
  --bs-alert-bg: transparent;
  --bs-alert-padding-x: 1rem;
  --bs-alert-padding-y: 1rem;
  --bs-alert-margin-bottom: 1rem;
  --bs-alert-color: inherit;
  --bs-alert-border-color: transparent;
  --bs-alert-border: 1px solid var(--bs-alert-border-color);
  --bs-alert-border-radius: 0.375rem;
  position: relative;
  padding: var(--bs-alert-padding-y) var(--bs-alert-padding-x);
  margin-bottom: var(--bs-alert-margin-bottom);
  color: var(--bs-alert-color);
  background-color: var(--bs-alert-bg);
  border: var(--bs-alert-border);
  border-radius: var(--bs-alert-border-radius);
}
.bs .alert-heading {
  color: inherit;
}
.bs .alert-link {
  font-weight: 700;
}
.bs .alert-dismissible {
  padding-right: 3rem;
}
.bs .alert-dismissible .btn-close {
  position: absolute;
  top: 0;
  right: 0;
  z-index: 2;
  padding: 1.25rem 1rem;
}
.bs .alert-primary {
  --bs-alert-color: rgb(7.8, 66, 151.8);
  --bs-alert-bg: rgb(206.6, 226, 254.6);
  --bs-alert-border-color: rgb(182.4, 211.5, 254.4);
}
.bs .alert-primary .alert-link {
  color: rgb(6.24, 52.8, 121.44);
}
.bs .alert-secondary {
  --bs-alert-color: rgb(64.8, 70.2, 75);
  --bs-alert-bg: rgb(225.6, 227.4, 229);
  --bs-alert-border-color: rgb(210.9, 213.6, 216);
}
.bs .alert-secondary .alert-link {
  color: rgb(51.84, 56.16, 60);
}
.bs .alert-success {
  --bs-alert-color: rgb(15, 81, 50.4);
  --bs-alert-bg: rgb(209, 231, 220.8);
  --bs-alert-border-color: rgb(186, 219, 203.7);
}
.bs .alert-success .alert-link {
  color: rgb(12, 64.8, 40.32);
}
.bs .alert-info {
  --bs-alert-color: rgb(5.2, 80.8, 96);
  --bs-alert-bg: rgb(206.6, 244.4, 252);
  --bs-alert-border-color: rgb(182.4, 239.1, 250.5);
}
.bs .alert-info .alert-link {
  color: rgb(4.16, 64.64, 76.8);
}
.bs .alert-warning {
  --bs-alert-color: rgb(102, 77.2, 2.8);
  --bs-alert-bg: rgb(255, 242.6, 205.4);
  --bs-alert-border-color: rgb(255, 236.4, 180.6);
}
.bs .alert-warning .alert-link {
  color: rgb(81.6, 61.76, 2.24);
}
.bs .alert-danger {
  --bs-alert-color: rgb(132, 31.8, 41.4);
  --bs-alert-bg: rgb(248, 214.6, 217.8);
  --bs-alert-border-color: rgb(244.5, 194.4, 199.2);
}
.bs .alert-danger .alert-link {
  color: rgb(105.6, 25.44, 33.12);
}
.bs .alert-light {
  --bs-alert-color: rgb(99.2, 99.6, 100);
  --bs-alert-bg: rgb(253.6, 253.8, 254);
  --bs-alert-border-color: rgb(252.9, 253.2, 253.5);
}
.bs .alert-light .alert-link {
  color: rgb(79.36, 79.68, 80);
}
.bs .alert-dark {
  --bs-alert-color: rgb(19.8, 22.2, 24.6);
  --bs-alert-bg: rgb(210.6, 211.4, 212.2);
  --bs-alert-border-color: rgb(188.4, 189.6, 190.8);
}
.bs .alert-dark .alert-link {
  color: rgb(15.84, 17.76, 19.68);
}
@keyframes progress-bar-stripes {
  0% {
    background-position-x: 1rem;
  }
}
.bs .progress {
  --bs-progress-height: 1rem;
  --bs-progress-font-size: 0.75rem;
  --bs-progress-bg: #e9ecef;
  --bs-progress-border-radius: 0.375rem;
  --bs-progress-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.075);
  --bs-progress-bar-color: #fff;
  --bs-progress-bar-bg: #0d6efd;
  --bs-progress-bar-transition: width 0.6s ease;
  display: flex;
  height: var(--bs-progress-height);
  overflow: hidden;
  font-size: var(--bs-progress-font-size);
  background-color: var(--bs-progress-bg);
  border-radius: var(--bs-progress-border-radius);
}
.bs .progress-bar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  color: var(--bs-progress-bar-color);
  text-align: center;
  white-space: nowrap;
  background-color: var(--bs-progress-bar-bg);
  transition: var(--bs-progress-bar-transition);
}
@media (prefers-reduced-motion: reduce) {
  .bs .progress-bar {
    transition: none;
  }
}
.bs .progress-bar-striped {
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: var(--bs-progress-height) var(--bs-progress-height);
}
.bs .progress-bar-animated {
  animation: 1s linear infinite progress-bar-stripes;
}
@media (prefers-reduced-motion: reduce) {
  .bs .progress-bar-animated {
    animation: none;
  }
}
.bs .list-group {
  --bs-list-group-color: #212529;
  --bs-list-group-bg: #fff;
  --bs-list-group-border-color: rgba(0, 0, 0, 0.125);
  --bs-list-group-border-width: 1px;
  --bs-list-group-border-radius: 0.375rem;
  --bs-list-group-item-padding-x: 1rem;
  --bs-list-group-item-padding-y: 0.5rem;
  --bs-list-group-action-color: #495057;
  --bs-list-group-action-hover-color: #495057;
  --bs-list-group-action-hover-bg: #f8f9fa;
  --bs-list-group-action-active-color: #212529;
  --bs-list-group-action-active-bg: #e9ecef;
  --bs-list-group-disabled-color: #6c757d;
  --bs-list-group-disabled-bg: #fff;
  --bs-list-group-active-color: #fff;
  --bs-list-group-active-bg: #0d6efd;
  --bs-list-group-active-border-color: #0d6efd;
  display: flex;
  flex-direction: column;
  padding-left: 0;
  margin-bottom: 0;
  border-radius: var(--bs-list-group-border-radius);
}
.bs .list-group-numbered {
  list-style-type: none;
  counter-reset: section;
}
.bs .list-group-numbered > .list-group-item::before {
  content: counters(section, ".") ". ";
  counter-increment: section;
}
.bs .list-group-item-action {
  width: 100%;
  color: var(--bs-list-group-action-color);
  text-align: inherit;
}
.bs .list-group-item-action:hover, .bs .list-group-item-action:focus {
  z-index: 1;
  color: var(--bs-list-group-action-hover-color);
  text-decoration: none;
  background-color: var(--bs-list-group-action-hover-bg);
}
.bs .list-group-item-action:active {
  color: var(--bs-list-group-action-active-color);
  background-color: var(--bs-list-group-action-active-bg);
}
.bs .list-group-item {
  position: relative;
  display: block;
  padding: var(--bs-list-group-item-padding-y) var(--bs-list-group-item-padding-x);
  color: var(--bs-list-group-color);
  text-decoration: none;
  background-color: var(--bs-list-group-bg);
  border: var(--bs-list-group-border-width) solid var(--bs-list-group-border-color);
}
.bs .list-group-item:first-child {
  border-top-left-radius: inherit;
  border-top-right-radius: inherit;
}
.bs .list-group-item:last-child {
  border-bottom-right-radius: inherit;
  border-bottom-left-radius: inherit;
}
.bs .list-group-item.disabled, .bs .list-group-item:disabled {
  color: var(--bs-list-group-disabled-color);
  pointer-events: none;
  background-color: var(--bs-list-group-disabled-bg);
}
.bs .list-group-item.active {
  z-index: 2;
  color: var(--bs-list-group-active-color);
  background-color: var(--bs-list-group-active-bg);
  border-color: var(--bs-list-group-active-border-color);
}
.bs .list-group-item + .list-group-item {
  border-top-width: 0;
}
.bs .list-group-item + .list-group-item.active {
  margin-top: calc(-1 * var(--bs-list-group-border-width));
  border-top-width: var(--bs-list-group-border-width);
}
.bs .list-group-horizontal {
  flex-direction: row;
}
.bs .list-group-horizontal > .list-group-item:first-child:not(:last-child) {
  border-bottom-left-radius: var(--bs-list-group-border-radius);
  border-top-right-radius: 0;
}
.bs .list-group-horizontal > .list-group-item:last-child:not(:first-child) {
  border-top-right-radius: var(--bs-list-group-border-radius);
  border-bottom-left-radius: 0;
}
.bs .list-group-horizontal > .list-group-item.active {
  margin-top: 0;
}
.bs .list-group-horizontal > .list-group-item + .list-group-item {
  border-top-width: var(--bs-list-group-border-width);
  border-left-width: 0;
}
.bs .list-group-horizontal > .list-group-item + .list-group-item.active {
  margin-left: calc(-1 * var(--bs-list-group-border-width));
  border-left-width: var(--bs-list-group-border-width);
}
@media (min-width: 576px) {
  .bs .list-group-horizontal-sm {
    flex-direction: row;
  }
  .bs .list-group-horizontal-sm > .list-group-item:first-child:not(:last-child) {
    border-bottom-left-radius: var(--bs-list-group-border-radius);
    border-top-right-radius: 0;
  }
  .bs .list-group-horizontal-sm > .list-group-item:last-child:not(:first-child) {
    border-top-right-radius: var(--bs-list-group-border-radius);
    border-bottom-left-radius: 0;
  }
  .bs .list-group-horizontal-sm > .list-group-item.active {
    margin-top: 0;
  }
  .bs .list-group-horizontal-sm > .list-group-item + .list-group-item {
    border-top-width: var(--bs-list-group-border-width);
    border-left-width: 0;
  }
  .bs .list-group-horizontal-sm > .list-group-item + .list-group-item.active {
    margin-left: calc(-1 * var(--bs-list-group-border-width));
    border-left-width: var(--bs-list-group-border-width);
  }
}
@media (min-width: 768px) {
  .bs .list-group-horizontal-md {
    flex-direction: row;
  }
  .bs .list-group-horizontal-md > .list-group-item:first-child:not(:last-child) {
    border-bottom-left-radius: var(--bs-list-group-border-radius);
    border-top-right-radius: 0;
  }
  .bs .list-group-horizontal-md > .list-group-item:last-child:not(:first-child) {
    border-top-right-radius: var(--bs-list-group-border-radius);
    border-bottom-left-radius: 0;
  }
  .bs .list-group-horizontal-md > .list-group-item.active {
    margin-top: 0;
  }
  .bs .list-group-horizontal-md > .list-group-item + .list-group-item {
    border-top-width: var(--bs-list-group-border-width);
    border-left-width: 0;
  }
  .bs .list-group-horizontal-md > .list-group-item + .list-group-item.active {
    margin-left: calc(-1 * var(--bs-list-group-border-width));
    border-left-width: var(--bs-list-group-border-width);
  }
}
@media (min-width: 992px) {
  .bs .list-group-horizontal-lg {
    flex-direction: row;
  }
  .bs .list-group-horizontal-lg > .list-group-item:first-child:not(:last-child) {
    border-bottom-left-radius: var(--bs-list-group-border-radius);
    border-top-right-radius: 0;
  }
  .bs .list-group-horizontal-lg > .list-group-item:last-child:not(:first-child) {
    border-top-right-radius: var(--bs-list-group-border-radius);
    border-bottom-left-radius: 0;
  }
  .bs .list-group-horizontal-lg > .list-group-item.active {
    margin-top: 0;
  }
  .bs .list-group-horizontal-lg > .list-group-item + .list-group-item {
    border-top-width: var(--bs-list-group-border-width);
    border-left-width: 0;
  }
  .bs .list-group-horizontal-lg > .list-group-item + .list-group-item.active {
    margin-left: calc(-1 * var(--bs-list-group-border-width));
    border-left-width: var(--bs-list-group-border-width);
  }
}
@media (min-width: 1200px) {
  .bs .list-group-horizontal-xl {
    flex-direction: row;
  }
  .bs .list-group-horizontal-xl > .list-group-item:first-child:not(:last-child) {
    border-bottom-left-radius: var(--bs-list-group-border-radius);
    border-top-right-radius: 0;
  }
  .bs .list-group-horizontal-xl > .list-group-item:last-child:not(:first-child) {
    border-top-right-radius: var(--bs-list-group-border-radius);
    border-bottom-left-radius: 0;
  }
  .bs .list-group-horizontal-xl > .list-group-item.active {
    margin-top: 0;
  }
  .bs .list-group-horizontal-xl > .list-group-item + .list-group-item {
    border-top-width: var(--bs-list-group-border-width);
    border-left-width: 0;
  }
  .bs .list-group-horizontal-xl > .list-group-item + .list-group-item.active {
    margin-left: calc(-1 * var(--bs-list-group-border-width));
    border-left-width: var(--bs-list-group-border-width);
  }
}
@media (min-width: 1400px) {
  .bs .list-group-horizontal-xxl {
    flex-direction: row;
  }
  .bs .list-group-horizontal-xxl > .list-group-item:first-child:not(:last-child) {
    border-bottom-left-radius: var(--bs-list-group-border-radius);
    border-top-right-radius: 0;
  }
  .bs .list-group-horizontal-xxl > .list-group-item:last-child:not(:first-child) {
    border-top-right-radius: var(--bs-list-group-border-radius);
    border-bottom-left-radius: 0;
  }
  .bs .list-group-horizontal-xxl > .list-group-item.active {
    margin-top: 0;
  }
  .bs .list-group-horizontal-xxl > .list-group-item + .list-group-item {
    border-top-width: var(--bs-list-group-border-width);
    border-left-width: 0;
  }
  .bs .list-group-horizontal-xxl > .list-group-item + .list-group-item.active {
    margin-left: calc(-1 * var(--bs-list-group-border-width));
    border-left-width: var(--bs-list-group-border-width);
  }
}
.bs .list-group-flush {
  border-radius: 0;
}
.bs .list-group-flush > .list-group-item {
  border-width: 0 0 var(--bs-list-group-border-width);
}
.bs .list-group-flush > .list-group-item:last-child {
  border-bottom-width: 0;
}
.bs .list-group-item-primary {
  color: rgb(7.8, 66, 151.8);
  background-color: rgb(206.6, 226, 254.6);
}
.bs .list-group-item-primary.list-group-item-action:hover, .bs .list-group-item-primary.list-group-item-action:focus {
  color: rgb(7.8, 66, 151.8);
  background-color: rgb(185.94, 203.4, 229.14);
}
.bs .list-group-item-primary.list-group-item-action.active {
  color: #fff;
  background-color: rgb(7.8, 66, 151.8);
  border-color: rgb(7.8, 66, 151.8);
}
.bs .list-group-item-secondary {
  color: rgb(64.8, 70.2, 75);
  background-color: rgb(225.6, 227.4, 229);
}
.bs .list-group-item-secondary.list-group-item-action:hover, .bs .list-group-item-secondary.list-group-item-action:focus {
  color: rgb(64.8, 70.2, 75);
  background-color: rgb(203.04, 204.66, 206.1);
}
.bs .list-group-item-secondary.list-group-item-action.active {
  color: #fff;
  background-color: rgb(64.8, 70.2, 75);
  border-color: rgb(64.8, 70.2, 75);
}
.bs .list-group-item-success {
  color: rgb(15, 81, 50.4);
  background-color: rgb(209, 231, 220.8);
}
.bs .list-group-item-success.list-group-item-action:hover, .bs .list-group-item-success.list-group-item-action:focus {
  color: rgb(15, 81, 50.4);
  background-color: rgb(188.1, 207.9, 198.72);
}
.bs .list-group-item-success.list-group-item-action.active {
  color: #fff;
  background-color: rgb(15, 81, 50.4);
  border-color: rgb(15, 81, 50.4);
}
.bs .list-group-item-info {
  color: rgb(5.2, 80.8, 96);
  background-color: rgb(206.6, 244.4, 252);
}
.bs .list-group-item-info.list-group-item-action:hover, .bs .list-group-item-info.list-group-item-action:focus {
  color: rgb(5.2, 80.8, 96);
  background-color: rgb(185.94, 219.96, 226.8);
}
.bs .list-group-item-info.list-group-item-action.active {
  color: #fff;
  background-color: rgb(5.2, 80.8, 96);
  border-color: rgb(5.2, 80.8, 96);
}
.bs .list-group-item-warning {
  color: rgb(102, 77.2, 2.8);
  background-color: rgb(255, 242.6, 205.4);
}
.bs .list-group-item-warning.list-group-item-action:hover, .bs .list-group-item-warning.list-group-item-action:focus {
  color: rgb(102, 77.2, 2.8);
  background-color: rgb(229.5, 218.34, 184.86);
}
.bs .list-group-item-warning.list-group-item-action.active {
  color: #fff;
  background-color: rgb(102, 77.2, 2.8);
  border-color: rgb(102, 77.2, 2.8);
}
.bs .list-group-item-danger {
  color: rgb(132, 31.8, 41.4);
  background-color: rgb(248, 214.6, 217.8);
}
.bs .list-group-item-danger.list-group-item-action:hover, .bs .list-group-item-danger.list-group-item-action:focus {
  color: rgb(132, 31.8, 41.4);
  background-color: rgb(223.2, 193.14, 196.02);
}
.bs .list-group-item-danger.list-group-item-action.active {
  color: #fff;
  background-color: rgb(132, 31.8, 41.4);
  border-color: rgb(132, 31.8, 41.4);
}
.bs .list-group-item-light {
  color: rgb(99.2, 99.6, 100);
  background-color: rgb(253.6, 253.8, 254);
}
.bs .list-group-item-light.list-group-item-action:hover, .bs .list-group-item-light.list-group-item-action:focus {
  color: rgb(99.2, 99.6, 100);
  background-color: rgb(228.24, 228.42, 228.6);
}
.bs .list-group-item-light.list-group-item-action.active {
  color: #fff;
  background-color: rgb(99.2, 99.6, 100);
  border-color: rgb(99.2, 99.6, 100);
}
.bs .list-group-item-dark {
  color: rgb(19.8, 22.2, 24.6);
  background-color: rgb(210.6, 211.4, 212.2);
}
.bs .list-group-item-dark.list-group-item-action:hover, .bs .list-group-item-dark.list-group-item-action:focus {
  color: rgb(19.8, 22.2, 24.6);
  background-color: rgb(189.54, 190.26, 190.98);
}
.bs .list-group-item-dark.list-group-item-action.active {
  color: #fff;
  background-color: rgb(19.8, 22.2, 24.6);
  border-color: rgb(19.8, 22.2, 24.6);
}
.bs .btn-close {
  box-sizing: content-box;
  width: 1em;
  height: 1em;
  padding: 0.25em 0.25em;
  color: #000;
  background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000'%3e%3cpath d='M.293.293a1 1 0 0 1 1.414 0L8 6.586 14.293.293a1 1 0 1 1 1.414 1.414L9.414 8l6.293 6.293a1 1 0 0 1-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 0 1-1.414-1.414L6.586 8 .293 1.707a1 1 0 0 1 0-1.414z'/%3e%3c/svg%3e") center/1em auto no-repeat;
  border: 0;
  border-radius: 0.375rem;
  opacity: 0.5;
}
.bs .btn-close:hover {
  color: #000;
  text-decoration: none;
  opacity: 0.75;
}
.bs .btn-close:focus {
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
  opacity: 1;
}
.bs .btn-close:disabled, .bs .btn-close.disabled {
  pointer-events: none;
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
  opacity: 0.25;
}
.bs .btn-close-white {
  filter: invert(1) grayscale(100%) brightness(200%);
}
.bs .toast {
  --bs-toast-zindex: 1090;
  --bs-toast-padding-x: 0.75rem;
  --bs-toast-padding-y: 0.5rem;
  --bs-toast-spacing: 1.5rem;
  --bs-toast-max-width: 350px;
  --bs-toast-font-size: 0.875rem;
  --bs-toast-color: ;
  --bs-toast-bg: rgba(255, 255, 255, 0.85);
  --bs-toast-border-width: 1px;
  --bs-toast-border-color: var(--bs-border-color-translucent);
  --bs-toast-border-radius: 0.375rem;
  --bs-toast-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --bs-toast-header-color: #6c757d;
  --bs-toast-header-bg: rgba(255, 255, 255, 0.85);
  --bs-toast-header-border-color: rgba(0, 0, 0, 0.05);
  width: var(--bs-toast-max-width);
  max-width: 100%;
  font-size: var(--bs-toast-font-size);
  color: var(--bs-toast-color);
  pointer-events: auto;
  background-color: var(--bs-toast-bg);
  background-clip: padding-box;
  border: var(--bs-toast-border-width) solid var(--bs-toast-border-color);
  box-shadow: var(--bs-toast-box-shadow);
  border-radius: var(--bs-toast-border-radius);
}
.bs .toast.showing {
  opacity: 0;
}
.bs .toast:not(.show) {
  display: none;
}
.bs .toast-container {
  --bs-toast-zindex: 1090;
  position: absolute;
  z-index: var(--bs-toast-zindex);
  width: -moz-max-content;
  width: max-content;
  max-width: 100%;
  pointer-events: none;
}
.bs .toast-container > :not(:last-child) {
  margin-bottom: var(--bs-toast-spacing);
}
.bs .toast-header {
  display: flex;
  align-items: center;
  padding: var(--bs-toast-padding-y) var(--bs-toast-padding-x);
  color: var(--bs-toast-header-color);
  background-color: var(--bs-toast-header-bg);
  background-clip: padding-box;
  border-bottom: var(--bs-toast-border-width) solid var(--bs-toast-header-border-color);
  border-top-left-radius: calc(var(--bs-toast-border-radius) - var(--bs-toast-border-width));
  border-top-right-radius: calc(var(--bs-toast-border-radius) - var(--bs-toast-border-width));
}
.bs .toast-header .btn-close {
  margin-right: calc(-0.5 * var(--bs-toast-padding-x));
  margin-left: var(--bs-toast-padding-x);
}
.bs .toast-body {
  padding: var(--bs-toast-padding-x);
  word-wrap: break-word;
}
.bs .modal {
  --bs-modal-zindex: 1055;
  --bs-modal-width: 500px;
  --bs-modal-padding: 1rem;
  --bs-modal-margin: 0.5rem;
  --bs-modal-color: ;
  --bs-modal-bg: #fff;
  --bs-modal-border-color: var(--bs-border-color-translucent);
  --bs-modal-border-width: 1px;
  --bs-modal-border-radius: 0.5rem;
  --bs-modal-box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  --bs-modal-inner-border-radius: calc(0.5rem - 1px);
  --bs-modal-header-padding-x: 1rem;
  --bs-modal-header-padding-y: 1rem;
  --bs-modal-header-padding: 1rem 1rem;
  --bs-modal-header-border-color: var(--bs-border-color);
  --bs-modal-header-border-width: 1px;
  --bs-modal-title-line-height: 1.5;
  --bs-modal-footer-gap: 0.5rem;
  --bs-modal-footer-bg: ;
  --bs-modal-footer-border-color: var(--bs-border-color);
  --bs-modal-footer-border-width: 1px;
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--bs-modal-zindex);
  display: none;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  outline: 0;
}
.bs .modal-dialog {
  position: relative;
  width: auto;
  margin: var(--bs-modal-margin);
  pointer-events: none;
}
.modal.fade .bs .modal-dialog {
  transition: transform 0.3s ease-out;
  transform: translate(0, -50px);
}
@media (prefers-reduced-motion: reduce) {
  .modal.fade .bs .modal-dialog {
    transition: none;
  }
}
.modal.show .bs .modal-dialog {
  transform: none;
}
.modal.modal-static .bs .modal-dialog {
  transform: scale(1.02);
}
.bs .modal-dialog-scrollable {
  height: calc(100% - var(--bs-modal-margin) * 2);
}
.bs .modal-dialog-scrollable .modal-content {
  max-height: 100%;
  overflow: hidden;
}
.bs .modal-dialog-scrollable .modal-body {
  overflow-y: auto;
}
.bs .modal-dialog-centered {
  display: flex;
  align-items: center;
  min-height: calc(100% - var(--bs-modal-margin) * 2);
}
.bs .modal-content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  color: var(--bs-modal-color);
  pointer-events: auto;
  background-color: var(--bs-modal-bg);
  background-clip: padding-box;
  border: var(--bs-modal-border-width) solid var(--bs-modal-border-color);
  border-radius: var(--bs-modal-border-radius);
  outline: 0;
}
.bs .modal-backdrop {
  --bs-backdrop-zindex: 1050;
  --bs-backdrop-bg: #000;
  --bs-backdrop-opacity: 0.5;
  position: fixed;
  top: 0;
  left: 0;
  z-index: var(--bs-backdrop-zindex);
  width: 100vw;
  height: 100vh;
  background-color: var(--bs-backdrop-bg);
}
.bs .modal-backdrop.fade {
  opacity: 0;
}
.bs .modal-backdrop.show {
  opacity: var(--bs-backdrop-opacity);
}
.bs .modal-header {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: space-between;
  padding: var(--bs-modal-header-padding);
  border-bottom: var(--bs-modal-header-border-width) solid var(--bs-modal-header-border-color);
  border-top-left-radius: var(--bs-modal-inner-border-radius);
  border-top-right-radius: var(--bs-modal-inner-border-radius);
}
.bs .modal-header .btn-close {
  padding: calc(var(--bs-modal-header-padding-y) * 0.5) calc(var(--bs-modal-header-padding-x) * 0.5);
  margin: calc(-0.5 * var(--bs-modal-header-padding-y)) calc(-0.5 * var(--bs-modal-header-padding-x)) calc(-0.5 * var(--bs-modal-header-padding-y)) auto;
}
.bs .modal-title {
  margin-bottom: 0;
  line-height: var(--bs-modal-title-line-height);
}
.bs .modal-body {
  position: relative;
  flex: 1 1 auto;
  padding: var(--bs-modal-padding);
}
.bs .modal-footer {
  display: flex;
  flex-shrink: 0;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-end;
  padding: calc(var(--bs-modal-padding) - var(--bs-modal-footer-gap) * 0.5);
  background-color: var(--bs-modal-footer-bg);
  border-top: var(--bs-modal-footer-border-width) solid var(--bs-modal-footer-border-color);
  border-bottom-right-radius: var(--bs-modal-inner-border-radius);
  border-bottom-left-radius: var(--bs-modal-inner-border-radius);
}
.bs .modal-footer > * {
  margin: calc(var(--bs-modal-footer-gap) * 0.5);
}
@media (min-width: 576px) {
  .bs .modal {
    --bs-modal-margin: 1.75rem;
    --bs-modal-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  }
  .bs .modal-dialog {
    max-width: var(--bs-modal-width);
    margin-right: auto;
    margin-left: auto;
  }
  .bs .modal-sm {
    --bs-modal-width: 300px;
  }
}
@media (min-width: 992px) {
  .bs .modal-lg,
  .bs .modal-xl {
    --bs-modal-width: 800px;
  }
}
@media (min-width: 1200px) {
  .bs .modal-xl {
    --bs-modal-width: 1140px;
  }
}
.bs .modal-fullscreen {
  width: 100vw;
  max-width: none;
  height: 100%;
  margin: 0;
}
.bs .modal-fullscreen .modal-content {
  height: 100%;
  border: 0;
  border-radius: 0;
}
.bs .modal-fullscreen .modal-header,
.bs .modal-fullscreen .modal-footer {
  border-radius: 0;
}
.bs .modal-fullscreen .modal-body {
  overflow-y: auto;
}
@media (max-width: 575.98px) {
  .bs .modal-fullscreen-sm-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .bs .modal-fullscreen-sm-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .bs .modal-fullscreen-sm-down .modal-header,
  .bs .modal-fullscreen-sm-down .modal-footer {
    border-radius: 0;
  }
  .bs .modal-fullscreen-sm-down .modal-body {
    overflow-y: auto;
  }
}
@media (max-width: 767.98px) {
  .bs .modal-fullscreen-md-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .bs .modal-fullscreen-md-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .bs .modal-fullscreen-md-down .modal-header,
  .bs .modal-fullscreen-md-down .modal-footer {
    border-radius: 0;
  }
  .bs .modal-fullscreen-md-down .modal-body {
    overflow-y: auto;
  }
}
@media (max-width: 991.98px) {
  .bs .modal-fullscreen-lg-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .bs .modal-fullscreen-lg-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .bs .modal-fullscreen-lg-down .modal-header,
  .bs .modal-fullscreen-lg-down .modal-footer {
    border-radius: 0;
  }
  .bs .modal-fullscreen-lg-down .modal-body {
    overflow-y: auto;
  }
}
@media (max-width: 1199.98px) {
  .bs .modal-fullscreen-xl-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .bs .modal-fullscreen-xl-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .bs .modal-fullscreen-xl-down .modal-header,
  .bs .modal-fullscreen-xl-down .modal-footer {
    border-radius: 0;
  }
  .bs .modal-fullscreen-xl-down .modal-body {
    overflow-y: auto;
  }
}
@media (max-width: 1399.98px) {
  .bs .modal-fullscreen-xxl-down {
    width: 100vw;
    max-width: none;
    height: 100%;
    margin: 0;
  }
  .bs .modal-fullscreen-xxl-down .modal-content {
    height: 100%;
    border: 0;
    border-radius: 0;
  }
  .bs .modal-fullscreen-xxl-down .modal-header,
  .bs .modal-fullscreen-xxl-down .modal-footer {
    border-radius: 0;
  }
  .bs .modal-fullscreen-xxl-down .modal-body {
    overflow-y: auto;
  }
}
.bs .tooltip {
  --bs-tooltip-zindex: 1080;
  --bs-tooltip-max-width: 200px;
  --bs-tooltip-padding-x: 0.5rem;
  --bs-tooltip-padding-y: 0.25rem;
  --bs-tooltip-margin: ;
  --bs-tooltip-font-size: 0.875rem;
  --bs-tooltip-color: #fff;
  --bs-tooltip-bg: #000;
  --bs-tooltip-border-radius: 0.375rem;
  --bs-tooltip-opacity: 0.9;
  --bs-tooltip-arrow-width: 0.8rem;
  --bs-tooltip-arrow-height: 0.4rem;
  z-index: var(--bs-tooltip-zindex);
  display: block;
  padding: var(--bs-tooltip-arrow-height);
  margin: var(--bs-tooltip-margin);
  font-family: var(--bs-font-sans-serif);
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  white-space: normal;
  word-spacing: normal;
  line-break: auto;
  font-size: var(--bs-tooltip-font-size);
  word-wrap: break-word;
  opacity: 0;
}
.bs .tooltip.show {
  opacity: var(--bs-tooltip-opacity);
}
.bs .tooltip .tooltip-arrow {
  display: block;
  width: var(--bs-tooltip-arrow-width);
  height: var(--bs-tooltip-arrow-height);
}
.bs .tooltip .tooltip-arrow::before {
  position: absolute;
  content: "";
  border-color: transparent;
  border-style: solid;
}
.bs .bs-tooltip-top .tooltip-arrow, .bs .bs-tooltip-auto[data-popper-placement^=top] .tooltip-arrow {
  bottom: 0;
}
.bs .bs-tooltip-top .tooltip-arrow::before, .bs .bs-tooltip-auto[data-popper-placement^=top] .tooltip-arrow::before {
  top: -1px;
  border-width: var(--bs-tooltip-arrow-height) calc(var(--bs-tooltip-arrow-width) * 0.5) 0;
  border-top-color: var(--bs-tooltip-bg);
}
.bs .bs-tooltip-end .tooltip-arrow, .bs .bs-tooltip-auto[data-popper-placement^=right] .tooltip-arrow {
  left: 0;
  width: var(--bs-tooltip-arrow-height);
  height: var(--bs-tooltip-arrow-width);
}
.bs .bs-tooltip-end .tooltip-arrow::before, .bs .bs-tooltip-auto[data-popper-placement^=right] .tooltip-arrow::before {
  right: -1px;
  border-width: calc(var(--bs-tooltip-arrow-width) * 0.5) var(--bs-tooltip-arrow-height) calc(var(--bs-tooltip-arrow-width) * 0.5) 0;
  border-right-color: var(--bs-tooltip-bg);
}
.bs .bs-tooltip-bottom .tooltip-arrow, .bs .bs-tooltip-auto[data-popper-placement^=bottom] .tooltip-arrow {
  top: 0;
}
.bs .bs-tooltip-bottom .tooltip-arrow::before, .bs .bs-tooltip-auto[data-popper-placement^=bottom] .tooltip-arrow::before {
  bottom: -1px;
  border-width: 0 calc(var(--bs-tooltip-arrow-width) * 0.5) var(--bs-tooltip-arrow-height);
  border-bottom-color: var(--bs-tooltip-bg);
}
.bs .bs-tooltip-start .tooltip-arrow, .bs .bs-tooltip-auto[data-popper-placement^=left] .tooltip-arrow {
  right: 0;
  width: var(--bs-tooltip-arrow-height);
  height: var(--bs-tooltip-arrow-width);
}
.bs .bs-tooltip-start .tooltip-arrow::before, .bs .bs-tooltip-auto[data-popper-placement^=left] .tooltip-arrow::before {
  left: -1px;
  border-width: calc(var(--bs-tooltip-arrow-width) * 0.5) 0 calc(var(--bs-tooltip-arrow-width) * 0.5) var(--bs-tooltip-arrow-height);
  border-left-color: var(--bs-tooltip-bg);
}
.bs .tooltip-inner {
  max-width: var(--bs-tooltip-max-width);
  padding: var(--bs-tooltip-padding-y) var(--bs-tooltip-padding-x);
  color: var(--bs-tooltip-color);
  text-align: center;
  background-color: var(--bs-tooltip-bg);
  border-radius: var(--bs-tooltip-border-radius);
}
.bs .popover {
  --bs-popover-zindex: 1070;
  --bs-popover-max-width: 276px;
  --bs-popover-font-size: 0.875rem;
  --bs-popover-bg: #fff;
  --bs-popover-border-width: 1px;
  --bs-popover-border-color: var(--bs-border-color-translucent);
  --bs-popover-border-radius: 0.5rem;
  --bs-popover-inner-border-radius: calc(0.5rem - 1px);
  --bs-popover-box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  --bs-popover-header-padding-x: 1rem;
  --bs-popover-header-padding-y: 0.5rem;
  --bs-popover-header-font-size: 1rem;
  --bs-popover-header-color: ;
  --bs-popover-header-bg: rgb(239.7, 239.7, 239.7);
  --bs-popover-body-padding-x: 1rem;
  --bs-popover-body-padding-y: 1rem;
  --bs-popover-body-color: #212529;
  --bs-popover-arrow-width: 1rem;
  --bs-popover-arrow-height: 0.5rem;
  --bs-popover-arrow-border: var(--bs-popover-border-color);
  z-index: var(--bs-popover-zindex);
  display: block;
  max-width: var(--bs-popover-max-width);
  font-family: var(--bs-font-sans-serif);
  font-style: normal;
  font-weight: 400;
  line-height: 1.5;
  text-align: left;
  text-align: start;
  text-decoration: none;
  text-shadow: none;
  text-transform: none;
  letter-spacing: normal;
  word-break: normal;
  white-space: normal;
  word-spacing: normal;
  line-break: auto;
  font-size: var(--bs-popover-font-size);
  word-wrap: break-word;
  background-color: var(--bs-popover-bg);
  background-clip: padding-box;
  border: var(--bs-popover-border-width) solid var(--bs-popover-border-color);
  border-radius: var(--bs-popover-border-radius);
}
.bs .popover .popover-arrow {
  display: block;
  width: var(--bs-popover-arrow-width);
  height: var(--bs-popover-arrow-height);
}
.bs .popover .popover-arrow::before, .bs .popover .popover-arrow::after {
  position: absolute;
  display: block;
  content: "";
  border-color: transparent;
  border-style: solid;
  border-width: 0;
}
.bs .bs-popover-top > .popover-arrow, .bs .bs-popover-auto[data-popper-placement^=top] > .popover-arrow {
  bottom: calc(-1 * (var(--bs-popover-arrow-height)) - var(--bs-popover-border-width));
}
.bs .bs-popover-top > .popover-arrow::before, .bs .bs-popover-auto[data-popper-placement^=top] > .popover-arrow::before, .bs .bs-popover-top > .popover-arrow::after, .bs .bs-popover-auto[data-popper-placement^=top] > .popover-arrow::after {
  border-width: var(--bs-popover-arrow-height) calc(var(--bs-popover-arrow-width) * 0.5) 0;
}
.bs .bs-popover-top > .popover-arrow::before, .bs .bs-popover-auto[data-popper-placement^=top] > .popover-arrow::before {
  bottom: 0;
  border-top-color: var(--bs-popover-arrow-border);
}
.bs .bs-popover-top > .popover-arrow::after, .bs .bs-popover-auto[data-popper-placement^=top] > .popover-arrow::after {
  bottom: var(--bs-popover-border-width);
  border-top-color: var(--bs-popover-bg);
}
.bs .bs-popover-end > .popover-arrow, .bs .bs-popover-auto[data-popper-placement^=right] > .popover-arrow {
  left: calc(-1 * (var(--bs-popover-arrow-height)) - var(--bs-popover-border-width));
  width: var(--bs-popover-arrow-height);
  height: var(--bs-popover-arrow-width);
}
.bs .bs-popover-end > .popover-arrow::before, .bs .bs-popover-auto[data-popper-placement^=right] > .popover-arrow::before, .bs .bs-popover-end > .popover-arrow::after, .bs .bs-popover-auto[data-popper-placement^=right] > .popover-arrow::after {
  border-width: calc(var(--bs-popover-arrow-width) * 0.5) var(--bs-popover-arrow-height) calc(var(--bs-popover-arrow-width) * 0.5) 0;
}
.bs .bs-popover-end > .popover-arrow::before, .bs .bs-popover-auto[data-popper-placement^=right] > .popover-arrow::before {
  left: 0;
  border-right-color: var(--bs-popover-arrow-border);
}
.bs .bs-popover-end > .popover-arrow::after, .bs .bs-popover-auto[data-popper-placement^=right] > .popover-arrow::after {
  left: var(--bs-popover-border-width);
  border-right-color: var(--bs-popover-bg);
}
.bs .bs-popover-bottom > .popover-arrow, .bs .bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow {
  top: calc(-1 * (var(--bs-popover-arrow-height)) - var(--bs-popover-border-width));
}
.bs .bs-popover-bottom > .popover-arrow::before, .bs .bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow::before, .bs .bs-popover-bottom > .popover-arrow::after, .bs .bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow::after {
  border-width: 0 calc(var(--bs-popover-arrow-width) * 0.5) var(--bs-popover-arrow-height);
}
.bs .bs-popover-bottom > .popover-arrow::before, .bs .bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow::before {
  top: 0;
  border-bottom-color: var(--bs-popover-arrow-border);
}
.bs .bs-popover-bottom > .popover-arrow::after, .bs .bs-popover-auto[data-popper-placement^=bottom] > .popover-arrow::after {
  top: var(--bs-popover-border-width);
  border-bottom-color: var(--bs-popover-bg);
}
.bs .bs-popover-bottom .popover-header::before, .bs .bs-popover-auto[data-popper-placement^=bottom] .popover-header::before {
  position: absolute;
  top: 0;
  left: 50%;
  display: block;
  width: var(--bs-popover-arrow-width);
  margin-left: calc(-0.5 * var(--bs-popover-arrow-width));
  content: "";
  border-bottom: var(--bs-popover-border-width) solid var(--bs-popover-header-bg);
}
.bs .bs-popover-start > .popover-arrow, .bs .bs-popover-auto[data-popper-placement^=left] > .popover-arrow {
  right: calc(-1 * (var(--bs-popover-arrow-height)) - var(--bs-popover-border-width));
  width: var(--bs-popover-arrow-height);
  height: var(--bs-popover-arrow-width);
}
.bs .bs-popover-start > .popover-arrow::before, .bs .bs-popover-auto[data-popper-placement^=left] > .popover-arrow::before, .bs .bs-popover-start > .popover-arrow::after, .bs .bs-popover-auto[data-popper-placement^=left] > .popover-arrow::after {
  border-width: calc(var(--bs-popover-arrow-width) * 0.5) 0 calc(var(--bs-popover-arrow-width) * 0.5) var(--bs-popover-arrow-height);
}
.bs .bs-popover-start > .popover-arrow::before, .bs .bs-popover-auto[data-popper-placement^=left] > .popover-arrow::before {
  right: 0;
  border-left-color: var(--bs-popover-arrow-border);
}
.bs .bs-popover-start > .popover-arrow::after, .bs .bs-popover-auto[data-popper-placement^=left] > .popover-arrow::after {
  right: var(--bs-popover-border-width);
  border-left-color: var(--bs-popover-bg);
}
.bs .popover-header {
  padding: var(--bs-popover-header-padding-y) var(--bs-popover-header-padding-x);
  margin-bottom: 0;
  font-size: var(--bs-popover-header-font-size);
  color: var(--bs-popover-header-color);
  background-color: var(--bs-popover-header-bg);
  border-bottom: var(--bs-popover-border-width) solid var(--bs-popover-border-color);
  border-top-left-radius: var(--bs-popover-inner-border-radius);
  border-top-right-radius: var(--bs-popover-inner-border-radius);
}
.bs .popover-header:empty {
  display: none;
}
.bs .popover-body {
  padding: var(--bs-popover-body-padding-y) var(--bs-popover-body-padding-x);
  color: var(--bs-popover-body-color);
}
.bs .carousel {
  position: relative;
}
.bs .carousel.pointer-event {
  touch-action: pan-y;
}
.bs .carousel-inner {
  position: relative;
  width: 100%;
  overflow: hidden;
}
.bs .carousel-inner::after {
  display: block;
  clear: both;
  content: "";
}
.bs .carousel-item {
  position: relative;
  display: none;
  float: left;
  width: 100%;
  margin-right: -100%;
  backface-visibility: hidden;
  transition: transform 0.6s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .bs .carousel-item {
    transition: none;
  }
}
.bs .carousel-item.active,
.bs .carousel-item-next,
.bs .carousel-item-prev {
  display: block;
}
.bs .carousel-item-next:not(.carousel-item-start),
.bs .active.carousel-item-end {
  transform: translateX(100%);
}
.bs .carousel-item-prev:not(.carousel-item-end),
.bs .active.carousel-item-start {
  transform: translateX(-100%);
}
.bs .carousel-fade .carousel-item {
  opacity: 0;
  transition-property: opacity;
  transform: none;
}
.bs .carousel-fade .carousel-item.active,
.bs .carousel-fade .carousel-item-next.carousel-item-start,
.bs .carousel-fade .carousel-item-prev.carousel-item-end {
  z-index: 1;
  opacity: 1;
}
.bs .carousel-fade .active.carousel-item-start,
.bs .carousel-fade .active.carousel-item-end {
  z-index: 0;
  opacity: 0;
  transition: opacity 0s 0.6s;
}
@media (prefers-reduced-motion: reduce) {
  .bs .carousel-fade .active.carousel-item-start,
  .bs .carousel-fade .active.carousel-item-end {
    transition: none;
  }
}
.bs .carousel-control-prev,
.bs .carousel-control-next {
  position: absolute;
  top: 0;
  bottom: 0;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 15%;
  padding: 0;
  color: #fff;
  text-align: center;
  background: none;
  border: 0;
  opacity: 0.5;
  transition: opacity 0.15s ease;
}
@media (prefers-reduced-motion: reduce) {
  .bs .carousel-control-prev,
  .bs .carousel-control-next {
    transition: none;
  }
}
.bs .carousel-control-prev:hover, .bs .carousel-control-prev:focus,
.bs .carousel-control-next:hover,
.bs .carousel-control-next:focus {
  color: #fff;
  text-decoration: none;
  outline: 0;
  opacity: 0.9;
}
.bs .carousel-control-prev {
  left: 0;
}
.bs .carousel-control-next {
  right: 0;
}
.bs .carousel-control-prev-icon,
.bs .carousel-control-next-icon {
  display: inline-block;
  width: 2rem;
  height: 2rem;
  background-repeat: no-repeat;
  background-position: 50%;
  background-size: 100% 100%;
}
.bs .carousel-control-prev-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/%3e%3c/svg%3e");
}
.bs .carousel-control-next-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}
.bs .carousel-indicators {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2;
  display: flex;
  justify-content: center;
  padding: 0;
  margin-right: 15%;
  margin-bottom: 1rem;
  margin-left: 15%;
  list-style: none;
}
.bs .carousel-indicators [data-bs-target] {
  box-sizing: content-box;
  flex: 0 1 auto;
  width: 30px;
  height: 3px;
  padding: 0;
  margin-right: 3px;
  margin-left: 3px;
  text-indent: -999px;
  cursor: pointer;
  background-color: #fff;
  background-clip: padding-box;
  border: 0;
  border-top: 10px solid transparent;
  border-bottom: 10px solid transparent;
  opacity: 0.5;
  transition: opacity 0.6s ease;
}
@media (prefers-reduced-motion: reduce) {
  .bs .carousel-indicators [data-bs-target] {
    transition: none;
  }
}
.bs .carousel-indicators .active {
  opacity: 1;
}
.bs .carousel-caption {
  position: absolute;
  right: 15%;
  bottom: 1.25rem;
  left: 15%;
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
  color: #fff;
  text-align: center;
}
.bs .carousel-dark .carousel-control-prev-icon,
.bs .carousel-dark .carousel-control-next-icon {
  filter: invert(1) grayscale(100);
}
.bs .carousel-dark .carousel-indicators [data-bs-target] {
  background-color: #000;
}
.bs .carousel-dark .carousel-caption {
  color: #000;
}
.bs .spinner-grow,
.bs .spinner-border {
  display: inline-block;
  width: var(--bs-spinner-width);
  height: var(--bs-spinner-height);
  vertical-align: var(--bs-spinner-vertical-align);
  border-radius: 50%;
  animation: var(--bs-spinner-animation-speed) linear infinite var(--bs-spinner-animation-name);
}
@keyframes spinner-border {
  to {
    transform: rotate(360deg) /* rtl:ignore */;
  }
}
.bs .spinner-border {
  --bs-spinner-width: 2rem;
  --bs-spinner-height: 2rem;
  --bs-spinner-vertical-align: -0.125em;
  --bs-spinner-border-width: 0.25em;
  --bs-spinner-animation-speed: 0.75s;
  --bs-spinner-animation-name: spinner-border;
  border: var(--bs-spinner-border-width) solid currentcolor;
  border-right-color: transparent;
}
.bs .spinner-border-sm {
  --bs-spinner-width: 1rem;
  --bs-spinner-height: 1rem;
  --bs-spinner-border-width: 0.2em;
}
@keyframes spinner-grow {
  0% {
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: none;
  }
}
.bs .spinner-grow {
  --bs-spinner-width: 2rem;
  --bs-spinner-height: 2rem;
  --bs-spinner-vertical-align: -0.125em;
  --bs-spinner-animation-speed: 0.75s;
  --bs-spinner-animation-name: spinner-grow;
  background-color: currentcolor;
  opacity: 0;
}
.bs .spinner-grow-sm {
  --bs-spinner-width: 1rem;
  --bs-spinner-height: 1rem;
}
@media (prefers-reduced-motion: reduce) {
  .bs .spinner-border,
  .bs .spinner-grow {
    --bs-spinner-animation-speed: 1.5s;
  }
}
.bs .offcanvas, .bs .offcanvas-xxl, .bs .offcanvas-xl, .bs .offcanvas-lg, .bs .offcanvas-md, .bs .offcanvas-sm {
  --bs-offcanvas-zindex: 1045;
  --bs-offcanvas-width: 400px;
  --bs-offcanvas-height: 30vh;
  --bs-offcanvas-padding-x: 1rem;
  --bs-offcanvas-padding-y: 1rem;
  --bs-offcanvas-color: ;
  --bs-offcanvas-bg: #fff;
  --bs-offcanvas-border-width: 1px;
  --bs-offcanvas-border-color: var(--bs-border-color-translucent);
  --bs-offcanvas-box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}
@media (max-width: 575.98px) {
  .bs .offcanvas-sm {
    position: fixed;
    bottom: 0;
    z-index: var(--bs-offcanvas-zindex);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    color: var(--bs-offcanvas-color);
    visibility: hidden;
    background-color: var(--bs-offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    transition: transform 0.3s ease-in-out;
  }
}
@media (max-width: 575.98px) and (prefers-reduced-motion: reduce) {
  .bs .offcanvas-sm {
    transition: none;
  }
}
@media (max-width: 575.98px) {
  .bs .offcanvas-sm.offcanvas-start {
    top: 0;
    left: 0;
    width: var(--bs-offcanvas-width);
    border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(-100%);
  }
  .bs .offcanvas-sm.offcanvas-end {
    top: 0;
    right: 0;
    width: var(--bs-offcanvas-width);
    border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(100%);
  }
  .bs .offcanvas-sm.offcanvas-top {
    top: 0;
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(-100%);
  }
  .bs .offcanvas-sm.offcanvas-bottom {
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(100%);
  }
  .bs .offcanvas-sm.showing, .bs .offcanvas-sm.show:not(.hiding) {
    transform: none;
  }
  .bs .offcanvas-sm.showing, .bs .offcanvas-sm.hiding, .bs .offcanvas-sm.show {
    visibility: visible;
  }
}
@media (min-width: 576px) {
  .bs .offcanvas-sm {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .bs .offcanvas-sm .offcanvas-header {
    display: none;
  }
  .bs .offcanvas-sm .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
    background-color: transparent !important;
  }
}
@media (max-width: 767.98px) {
  .bs .offcanvas-md {
    position: fixed;
    bottom: 0;
    z-index: var(--bs-offcanvas-zindex);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    color: var(--bs-offcanvas-color);
    visibility: hidden;
    background-color: var(--bs-offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    transition: transform 0.3s ease-in-out;
  }
}
@media (max-width: 767.98px) and (prefers-reduced-motion: reduce) {
  .bs .offcanvas-md {
    transition: none;
  }
}
@media (max-width: 767.98px) {
  .bs .offcanvas-md.offcanvas-start {
    top: 0;
    left: 0;
    width: var(--bs-offcanvas-width);
    border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(-100%);
  }
  .bs .offcanvas-md.offcanvas-end {
    top: 0;
    right: 0;
    width: var(--bs-offcanvas-width);
    border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(100%);
  }
  .bs .offcanvas-md.offcanvas-top {
    top: 0;
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(-100%);
  }
  .bs .offcanvas-md.offcanvas-bottom {
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(100%);
  }
  .bs .offcanvas-md.showing, .bs .offcanvas-md.show:not(.hiding) {
    transform: none;
  }
  .bs .offcanvas-md.showing, .bs .offcanvas-md.hiding, .bs .offcanvas-md.show {
    visibility: visible;
  }
}
@media (min-width: 768px) {
  .bs .offcanvas-md {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .bs .offcanvas-md .offcanvas-header {
    display: none;
  }
  .bs .offcanvas-md .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
    background-color: transparent !important;
  }
}
@media (max-width: 991.98px) {
  .bs .offcanvas-lg {
    position: fixed;
    bottom: 0;
    z-index: var(--bs-offcanvas-zindex);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    color: var(--bs-offcanvas-color);
    visibility: hidden;
    background-color: var(--bs-offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    transition: transform 0.3s ease-in-out;
  }
}
@media (max-width: 991.98px) and (prefers-reduced-motion: reduce) {
  .bs .offcanvas-lg {
    transition: none;
  }
}
@media (max-width: 991.98px) {
  .bs .offcanvas-lg.offcanvas-start {
    top: 0;
    left: 0;
    width: var(--bs-offcanvas-width);
    border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(-100%);
  }
  .bs .offcanvas-lg.offcanvas-end {
    top: 0;
    right: 0;
    width: var(--bs-offcanvas-width);
    border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(100%);
  }
  .bs .offcanvas-lg.offcanvas-top {
    top: 0;
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(-100%);
  }
  .bs .offcanvas-lg.offcanvas-bottom {
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(100%);
  }
  .bs .offcanvas-lg.showing, .bs .offcanvas-lg.show:not(.hiding) {
    transform: none;
  }
  .bs .offcanvas-lg.showing, .bs .offcanvas-lg.hiding, .bs .offcanvas-lg.show {
    visibility: visible;
  }
}
@media (min-width: 992px) {
  .bs .offcanvas-lg {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .bs .offcanvas-lg .offcanvas-header {
    display: none;
  }
  .bs .offcanvas-lg .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
    background-color: transparent !important;
  }
}
@media (max-width: 1199.98px) {
  .bs .offcanvas-xl {
    position: fixed;
    bottom: 0;
    z-index: var(--bs-offcanvas-zindex);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    color: var(--bs-offcanvas-color);
    visibility: hidden;
    background-color: var(--bs-offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    transition: transform 0.3s ease-in-out;
  }
}
@media (max-width: 1199.98px) and (prefers-reduced-motion: reduce) {
  .bs .offcanvas-xl {
    transition: none;
  }
}
@media (max-width: 1199.98px) {
  .bs .offcanvas-xl.offcanvas-start {
    top: 0;
    left: 0;
    width: var(--bs-offcanvas-width);
    border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(-100%);
  }
  .bs .offcanvas-xl.offcanvas-end {
    top: 0;
    right: 0;
    width: var(--bs-offcanvas-width);
    border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(100%);
  }
  .bs .offcanvas-xl.offcanvas-top {
    top: 0;
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(-100%);
  }
  .bs .offcanvas-xl.offcanvas-bottom {
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(100%);
  }
  .bs .offcanvas-xl.showing, .bs .offcanvas-xl.show:not(.hiding) {
    transform: none;
  }
  .bs .offcanvas-xl.showing, .bs .offcanvas-xl.hiding, .bs .offcanvas-xl.show {
    visibility: visible;
  }
}
@media (min-width: 1200px) {
  .bs .offcanvas-xl {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .bs .offcanvas-xl .offcanvas-header {
    display: none;
  }
  .bs .offcanvas-xl .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
    background-color: transparent !important;
  }
}
@media (max-width: 1399.98px) {
  .bs .offcanvas-xxl {
    position: fixed;
    bottom: 0;
    z-index: var(--bs-offcanvas-zindex);
    display: flex;
    flex-direction: column;
    max-width: 100%;
    color: var(--bs-offcanvas-color);
    visibility: hidden;
    background-color: var(--bs-offcanvas-bg);
    background-clip: padding-box;
    outline: 0;
    transition: transform 0.3s ease-in-out;
  }
}
@media (max-width: 1399.98px) and (prefers-reduced-motion: reduce) {
  .bs .offcanvas-xxl {
    transition: none;
  }
}
@media (max-width: 1399.98px) {
  .bs .offcanvas-xxl.offcanvas-start {
    top: 0;
    left: 0;
    width: var(--bs-offcanvas-width);
    border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(-100%);
  }
  .bs .offcanvas-xxl.offcanvas-end {
    top: 0;
    right: 0;
    width: var(--bs-offcanvas-width);
    border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateX(100%);
  }
  .bs .offcanvas-xxl.offcanvas-top {
    top: 0;
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(-100%);
  }
  .bs .offcanvas-xxl.offcanvas-bottom {
    right: 0;
    left: 0;
    height: var(--bs-offcanvas-height);
    max-height: 100%;
    border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
    transform: translateY(100%);
  }
  .bs .offcanvas-xxl.showing, .bs .offcanvas-xxl.show:not(.hiding) {
    transform: none;
  }
  .bs .offcanvas-xxl.showing, .bs .offcanvas-xxl.hiding, .bs .offcanvas-xxl.show {
    visibility: visible;
  }
}
@media (min-width: 1400px) {
  .bs .offcanvas-xxl {
    --bs-offcanvas-height: auto;
    --bs-offcanvas-border-width: 0;
    background-color: transparent !important;
  }
  .bs .offcanvas-xxl .offcanvas-header {
    display: none;
  }
  .bs .offcanvas-xxl .offcanvas-body {
    display: flex;
    flex-grow: 0;
    padding: 0;
    overflow-y: visible;
    background-color: transparent !important;
  }
}
.bs .offcanvas {
  position: fixed;
  bottom: 0;
  z-index: var(--bs-offcanvas-zindex);
  display: flex;
  flex-direction: column;
  max-width: 100%;
  color: var(--bs-offcanvas-color);
  visibility: hidden;
  background-color: var(--bs-offcanvas-bg);
  background-clip: padding-box;
  outline: 0;
  transition: transform 0.3s ease-in-out;
}
@media (prefers-reduced-motion: reduce) {
  .bs .offcanvas {
    transition: none;
  }
}
.bs .offcanvas.offcanvas-start {
  top: 0;
  left: 0;
  width: var(--bs-offcanvas-width);
  border-right: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
  transform: translateX(-100%);
}
.bs .offcanvas.offcanvas-end {
  top: 0;
  right: 0;
  width: var(--bs-offcanvas-width);
  border-left: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
  transform: translateX(100%);
}
.bs .offcanvas.offcanvas-top {
  top: 0;
  right: 0;
  left: 0;
  height: var(--bs-offcanvas-height);
  max-height: 100%;
  border-bottom: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
  transform: translateY(-100%);
}
.bs .offcanvas.offcanvas-bottom {
  right: 0;
  left: 0;
  height: var(--bs-offcanvas-height);
  max-height: 100%;
  border-top: var(--bs-offcanvas-border-width) solid var(--bs-offcanvas-border-color);
  transform: translateY(100%);
}
.bs .offcanvas.showing, .bs .offcanvas.show:not(.hiding) {
  transform: none;
}
.bs .offcanvas.showing, .bs .offcanvas.hiding, .bs .offcanvas.show {
  visibility: visible;
}
.bs .offcanvas-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1040;
  width: 100vw;
  height: 100vh;
  background-color: #000;
}
.bs .offcanvas-backdrop.fade {
  opacity: 0;
}
.bs .offcanvas-backdrop.show {
  opacity: 0.5;
}
.bs .offcanvas-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--bs-offcanvas-padding-y) var(--bs-offcanvas-padding-x);
}
.bs .offcanvas-header .btn-close {
  padding: calc(var(--bs-offcanvas-padding-y) * 0.5) calc(var(--bs-offcanvas-padding-x) * 0.5);
  margin-top: calc(-0.5 * var(--bs-offcanvas-padding-y));
  margin-right: calc(-0.5 * var(--bs-offcanvas-padding-x));
  margin-bottom: calc(-0.5 * var(--bs-offcanvas-padding-y));
}
.bs .offcanvas-title {
  margin-bottom: 0;
  line-height: 1.5;
}
.bs .offcanvas-body {
  flex-grow: 1;
  padding: var(--bs-offcanvas-padding-y) var(--bs-offcanvas-padding-x);
  overflow-y: auto;
}
.bs .placeholder {
  display: inline-block;
  min-height: 1em;
  vertical-align: middle;
  cursor: wait;
  background-color: currentcolor;
  opacity: 0.5;
}
.bs .placeholder.btn::before {
  display: inline-block;
  content: "";
}
.bs .placeholder-xs {
  min-height: 0.6em;
}
.bs .placeholder-sm {
  min-height: 0.8em;
}
.bs .placeholder-lg {
  min-height: 1.2em;
}
.bs .placeholder-glow .placeholder {
  animation: placeholder-glow 2s ease-in-out infinite;
}
@keyframes placeholder-glow {
  50% {
    opacity: 0.2;
  }
}
.bs .placeholder-wave {
  -webkit-mask-image: linear-gradient(130deg, #000 55%, rgba(0, 0, 0, 0.8) 75%, #000 95%);
          mask-image: linear-gradient(130deg, #000 55%, rgba(0, 0, 0, 0.8) 75%, #000 95%);
  -webkit-mask-size: 200% 100%;
          mask-size: 200% 100%;
  animation: placeholder-wave 2s linear infinite;
}
@keyframes placeholder-wave {
  100% {
    -webkit-mask-position: -200% 0%;
            mask-position: -200% 0%;
  }
}
.bs .clearfix::after {
  display: block;
  clear: both;
  content: "";
}
.bs .text-bg-primary {
  color: #fff !important;
  background-color: RGBA(13, 110, 253, var(--bs-bg-opacity, 1)) !important;
}
.bs .text-bg-secondary {
  color: #fff !important;
  background-color: RGBA(108, 117, 125, var(--bs-bg-opacity, 1)) !important;
}
.bs .text-bg-success {
  color: #fff !important;
  background-color: RGBA(25, 135, 84, var(--bs-bg-opacity, 1)) !important;
}
.bs .text-bg-info {
  color: #000 !important;
  background-color: RGBA(13, 202, 240, var(--bs-bg-opacity, 1)) !important;
}
.bs .text-bg-warning {
  color: #000 !important;
  background-color: RGBA(255, 193, 7, var(--bs-bg-opacity, 1)) !important;
}
.bs .text-bg-danger {
  color: #fff !important;
  background-color: RGBA(220, 53, 69, var(--bs-bg-opacity, 1)) !important;
}
.bs .text-bg-light {
  color: #000 !important;
  background-color: RGBA(248, 249, 250, var(--bs-bg-opacity, 1)) !important;
}
.bs .text-bg-dark {
  color: #fff !important;
  background-color: RGBA(33, 37, 41, var(--bs-bg-opacity, 1)) !important;
}
.bs .link-primary {
  color: #0d6efd !important;
}
.bs .link-primary:hover, .bs .link-primary:focus {
  color: rgb(10.4, 88, 202.4) !important;
}
.bs .link-secondary {
  color: #6c757d !important;
}
.bs .link-secondary:hover, .bs .link-secondary:focus {
  color: rgb(86.4, 93.6, 100) !important;
}
.bs .link-success {
  color: #198754 !important;
}
.bs .link-success:hover, .bs .link-success:focus {
  color: rgb(20, 108, 67.2) !important;
}
.bs .link-info {
  color: #0dcaf0 !important;
}
.bs .link-info:hover, .bs .link-info:focus {
  color: rgb(61.4, 212.6, 243) !important;
}
.bs .link-warning {
  color: #ffc107 !important;
}
.bs .link-warning:hover, .bs .link-warning:focus {
  color: rgb(255, 205.4, 56.6) !important;
}
.bs .link-danger {
  color: #dc3545 !important;
}
.bs .link-danger:hover, .bs .link-danger:focus {
  color: rgb(176, 42.4, 55.2) !important;
}
.bs .link-light {
  color: #f8f9fa !important;
}
.bs .link-light:hover, .bs .link-light:focus {
  color: rgb(249.4, 250.2, 251) !important;
}
.bs .link-dark {
  color: #212529 !important;
}
.bs .link-dark:hover, .bs .link-dark:focus {
  color: rgb(26.4, 29.6, 32.8) !important;
}
.bs .ratio {
  position: relative;
  width: 100%;
}
.bs .ratio::before {
  display: block;
  padding-top: var(--bs-aspect-ratio);
  content: "";
}
.bs .ratio > * {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.bs .ratio-1x1 {
  --bs-aspect-ratio: 100%;
}
.bs .ratio-4x3 {
  --bs-aspect-ratio: 75%;
}
.bs .ratio-16x9 {
  --bs-aspect-ratio: 56.25%;
}
.bs .ratio-21x9 {
  --bs-aspect-ratio: 42.8571428571%;
}
.bs .fixed-top {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 1030;
}
.bs .fixed-bottom {
  position: fixed;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030;
}
.bs .sticky-top {
  position: sticky;
  top: 0;
  z-index: 1020;
}
.bs .sticky-bottom {
  position: sticky;
  bottom: 0;
  z-index: 1020;
}
@media (min-width: 576px) {
  .bs .sticky-sm-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
  .bs .sticky-sm-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020;
  }
}
@media (min-width: 768px) {
  .bs .sticky-md-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
  .bs .sticky-md-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020;
  }
}
@media (min-width: 992px) {
  .bs .sticky-lg-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
  .bs .sticky-lg-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020;
  }
}
@media (min-width: 1200px) {
  .bs .sticky-xl-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
  .bs .sticky-xl-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020;
  }
}
@media (min-width: 1400px) {
  .bs .sticky-xxl-top {
    position: sticky;
    top: 0;
    z-index: 1020;
  }
  .bs .sticky-xxl-bottom {
    position: sticky;
    bottom: 0;
    z-index: 1020;
  }
}
.bs .hstack {
  display: flex;
  flex-direction: row;
  align-items: center;
  align-self: stretch;
}
.bs .vstack {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  align-self: stretch;
}
.bs .visually-hidden,
.bs .visually-hidden-focusable:not(:focus):not(:focus-within) {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}
.bs .stretched-link::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  content: "";
}
.bs .text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.bs .vr {
  display: inline-block;
  align-self: stretch;
  width: 1px;
  min-height: 1em;
  background-color: currentcolor;
  opacity: 0.25;
}
.bs .align-baseline {
  vertical-align: baseline !important;
}
.bs .align-top {
  vertical-align: top !important;
}
.bs .align-middle {
  vertical-align: middle !important;
}
.bs .align-bottom {
  vertical-align: bottom !important;
}
.bs .align-text-bottom {
  vertical-align: text-bottom !important;
}
.bs .align-text-top {
  vertical-align: text-top !important;
}
.bs .float-start {
  float: left !important;
}
.bs .float-end {
  float: right !important;
}
.bs .float-none {
  float: none !important;
}
.bs .opacity-0 {
  opacity: 0 !important;
}
.bs .opacity-25 {
  opacity: 0.25 !important;
}
.bs .opacity-50 {
  opacity: 0.5 !important;
}
.bs .opacity-75 {
  opacity: 0.75 !important;
}
.bs .opacity-100 {
  opacity: 1 !important;
}
.bs .overflow-auto {
  overflow: auto !important;
}
.bs .overflow-hidden {
  overflow: hidden !important;
}
.bs .overflow-visible {
  overflow: visible !important;
}
.bs .overflow-scroll {
  overflow: scroll !important;
}
.bs .d-inline {
  display: inline !important;
}
.bs .d-inline-block {
  display: inline-block !important;
}
.bs .d-block {
  display: block !important;
}
.bs .d-grid {
  display: grid !important;
}
.bs .d-table {
  display: table !important;
}
.bs .d-table-row {
  display: table-row !important;
}
.bs .d-table-cell {
  display: table-cell !important;
}
.bs .d-flex {
  display: flex !important;
}
.bs .d-inline-flex {
  display: inline-flex !important;
}
.bs .d-none {
  display: none !important;
}
.bs .shadow {
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}
.bs .shadow-sm {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}
.bs .shadow-lg {
  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}
.bs .shadow-none {
  box-shadow: none !important;
}
.bs .position-static {
  position: static !important;
}
.bs .position-relative {
  position: relative !important;
}
.bs .position-absolute {
  position: absolute !important;
}
.bs .position-fixed {
  position: fixed !important;
}
.bs .position-sticky {
  position: sticky !important;
}
.bs .top-0 {
  top: 0 !important;
}
.bs .top-50 {
  top: 50% !important;
}
.bs .top-100 {
  top: 100% !important;
}
.bs .bottom-0 {
  bottom: 0 !important;
}
.bs .bottom-50 {
  bottom: 50% !important;
}
.bs .bottom-100 {
  bottom: 100% !important;
}
.bs .start-0 {
  left: 0 !important;
}
.bs .start-50 {
  left: 50% !important;
}
.bs .start-100 {
  left: 100% !important;
}
.bs .end-0 {
  right: 0 !important;
}
.bs .end-50 {
  right: 50% !important;
}
.bs .end-100 {
  right: 100% !important;
}
.bs .translate-middle {
  transform: translate(-50%, -50%) !important;
}
.bs .translate-middle-x {
  transform: translateX(-50%) !important;
}
.bs .translate-middle-y {
  transform: translateY(-50%) !important;
}
.bs .border {
  border: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
}
.bs .border-0 {
  border: 0 !important;
}
.bs .border-top {
  border-top: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
}
.bs .border-top-0 {
  border-top: 0 !important;
}
.bs .border-end {
  border-right: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
}
.bs .border-end-0 {
  border-right: 0 !important;
}
.bs .border-bottom {
  border-bottom: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
}
.bs .border-bottom-0 {
  border-bottom: 0 !important;
}
.bs .border-start {
  border-left: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
}
.bs .border-start-0 {
  border-left: 0 !important;
}
.bs .border-primary {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-primary-rgb), var(--bs-border-opacity)) !important;
}
.bs .border-secondary {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-secondary-rgb), var(--bs-border-opacity)) !important;
}
.bs .border-success {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-success-rgb), var(--bs-border-opacity)) !important;
}
.bs .border-info {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-info-rgb), var(--bs-border-opacity)) !important;
}
.bs .border-warning {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-warning-rgb), var(--bs-border-opacity)) !important;
}
.bs .border-danger {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-danger-rgb), var(--bs-border-opacity)) !important;
}
.bs .border-light {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-light-rgb), var(--bs-border-opacity)) !important;
}
.bs .border-dark {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-dark-rgb), var(--bs-border-opacity)) !important;
}
.bs .border-white {
  --bs-border-opacity: 1;
  border-color: rgba(var(--bs-white-rgb), var(--bs-border-opacity)) !important;
}
.bs .border-1 {
  --bs-border-width: 1px;
}
.bs .border-2 {
  --bs-border-width: 2px;
}
.bs .border-3 {
  --bs-border-width: 3px;
}
.bs .border-4 {
  --bs-border-width: 4px;
}
.bs .border-5 {
  --bs-border-width: 5px;
}
.bs .border-opacity-10 {
  --bs-border-opacity: 0.1;
}
.bs .border-opacity-25 {
  --bs-border-opacity: 0.25;
}
.bs .border-opacity-50 {
  --bs-border-opacity: 0.5;
}
.bs .border-opacity-75 {
  --bs-border-opacity: 0.75;
}
.bs .border-opacity-100 {
  --bs-border-opacity: 1;
}
.bs .w-25 {
  width: 25% !important;
}
.bs .w-50 {
  width: 50% !important;
}
.bs .w-75 {
  width: 75% !important;
}
.bs .w-100 {
  width: 100% !important;
}
.bs .w-auto {
  width: auto !important;
}
.bs .mw-100 {
  max-width: 100% !important;
}
.bs .vw-100 {
  width: 100vw !important;
}
.bs .min-vw-100 {
  min-width: 100vw !important;
}
.bs .h-25 {
  height: 25% !important;
}
.bs .h-50 {
  height: 50% !important;
}
.bs .h-75 {
  height: 75% !important;
}
.bs .h-100 {
  height: 100% !important;
}
.bs .h-auto {
  height: auto !important;
}
.bs .mh-100 {
  max-height: 100% !important;
}
.bs .vh-100 {
  height: 100vh !important;
}
.bs .min-vh-100 {
  min-height: 100vh !important;
}
.bs .flex-fill {
  flex: 1 1 auto !important;
}
.bs .flex-row {
  flex-direction: row !important;
}
.bs .flex-column {
  flex-direction: column !important;
}
.bs .flex-row-reverse {
  flex-direction: row-reverse !important;
}
.bs .flex-column-reverse {
  flex-direction: column-reverse !important;
}
.bs .flex-grow-0 {
  flex-grow: 0 !important;
}
.bs .flex-grow-1 {
  flex-grow: 1 !important;
}
.bs .flex-shrink-0 {
  flex-shrink: 0 !important;
}
.bs .flex-shrink-1 {
  flex-shrink: 1 !important;
}
.bs .flex-wrap {
  flex-wrap: wrap !important;
}
.bs .flex-nowrap {
  flex-wrap: nowrap !important;
}
.bs .flex-wrap-reverse {
  flex-wrap: wrap-reverse !important;
}
.bs .justify-content-start {
  justify-content: flex-start !important;
}
.bs .justify-content-end {
  justify-content: flex-end !important;
}
.bs .justify-content-center {
  justify-content: center !important;
}
.bs .justify-content-between {
  justify-content: space-between !important;
}
.bs .justify-content-around {
  justify-content: space-around !important;
}
.bs .justify-content-evenly {
  justify-content: space-evenly !important;
}
.bs .align-items-start {
  align-items: flex-start !important;
}
.bs .align-items-end {
  align-items: flex-end !important;
}
.bs .align-items-center {
  align-items: center !important;
}
.bs .align-items-baseline {
  align-items: baseline !important;
}
.bs .align-items-stretch {
  align-items: stretch !important;
}
.bs .align-content-start {
  align-content: flex-start !important;
}
.bs .align-content-end {
  align-content: flex-end !important;
}
.bs .align-content-center {
  align-content: center !important;
}
.bs .align-content-between {
  align-content: space-between !important;
}
.bs .align-content-around {
  align-content: space-around !important;
}
.bs .align-content-stretch {
  align-content: stretch !important;
}
.bs .align-self-auto {
  align-self: auto !important;
}
.bs .align-self-start {
  align-self: flex-start !important;
}
.bs .align-self-end {
  align-self: flex-end !important;
}
.bs .align-self-center {
  align-self: center !important;
}
.bs .align-self-baseline {
  align-self: baseline !important;
}
.bs .align-self-stretch {
  align-self: stretch !important;
}
.bs .order-first {
  order: -1 !important;
}
.bs .order-0 {
  order: 0 !important;
}
.bs .order-1 {
  order: 1 !important;
}
.bs .order-2 {
  order: 2 !important;
}
.bs .order-3 {
  order: 3 !important;
}
.bs .order-4 {
  order: 4 !important;
}
.bs .order-5 {
  order: 5 !important;
}
.bs .order-last {
  order: 6 !important;
}
.bs .m-0 {
  margin: 0 !important;
}
.bs .m-1 {
  margin: 0.25rem !important;
}
.bs .m-2 {
  margin: 0.5rem !important;
}
.bs .m-3 {
  margin: 1rem !important;
}
.bs .m-4 {
  margin: 1.5rem !important;
}
.bs .m-5 {
  margin: 3rem !important;
}
.bs .m-auto {
  margin: auto !important;
}
.bs .mx-0 {
  margin-right: 0 !important;
  margin-left: 0 !important;
}
.bs .mx-1 {
  margin-right: 0.25rem !important;
  margin-left: 0.25rem !important;
}
.bs .mx-2 {
  margin-right: 0.5rem !important;
  margin-left: 0.5rem !important;
}
.bs .mx-3 {
  margin-right: 1rem !important;
  margin-left: 1rem !important;
}
.bs .mx-4 {
  margin-right: 1.5rem !important;
  margin-left: 1.5rem !important;
}
.bs .mx-5 {
  margin-right: 3rem !important;
  margin-left: 3rem !important;
}
.bs .mx-auto {
  margin-right: auto !important;
  margin-left: auto !important;
}
.bs .my-0 {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}
.bs .my-1 {
  margin-top: 0.25rem !important;
  margin-bottom: 0.25rem !important;
}
.bs .my-2 {
  margin-top: 0.5rem !important;
  margin-bottom: 0.5rem !important;
}
.bs .my-3 {
  margin-top: 1rem !important;
  margin-bottom: 1rem !important;
}
.bs .my-4 {
  margin-top: 1.5rem !important;
  margin-bottom: 1.5rem !important;
}
.bs .my-5 {
  margin-top: 3rem !important;
  margin-bottom: 3rem !important;
}
.bs .my-auto {
  margin-top: auto !important;
  margin-bottom: auto !important;
}
.bs .mt-0 {
  margin-top: 0 !important;
}
.bs .mt-1 {
  margin-top: 0.25rem !important;
}
.bs .mt-2 {
  margin-top: 0.5rem !important;
}
.bs .mt-3 {
  margin-top: 1rem !important;
}
.bs .mt-4 {
  margin-top: 1.5rem !important;
}
.bs .mt-5 {
  margin-top: 3rem !important;
}
.bs .mt-auto {
  margin-top: auto !important;
}
.bs .me-0 {
  margin-right: 0 !important;
}
.bs .me-1 {
  margin-right: 0.25rem !important;
}
.bs .me-2 {
  margin-right: 0.5rem !important;
}
.bs .me-3 {
  margin-right: 1rem !important;
}
.bs .me-4 {
  margin-right: 1.5rem !important;
}
.bs .me-5 {
  margin-right: 3rem !important;
}
.bs .me-auto {
  margin-right: auto !important;
}
.bs .mb-0 {
  margin-bottom: 0 !important;
}
.bs .mb-1 {
  margin-bottom: 0.25rem !important;
}
.bs .mb-2 {
  margin-bottom: 0.5rem !important;
}
.bs .mb-3 {
  margin-bottom: 1rem !important;
}
.bs .mb-4 {
  margin-bottom: 1.5rem !important;
}
.bs .mb-5 {
  margin-bottom: 3rem !important;
}
.bs .mb-auto {
  margin-bottom: auto !important;
}
.bs .ms-0 {
  margin-left: 0 !important;
}
.bs .ms-1 {
  margin-left: 0.25rem !important;
}
.bs .ms-2 {
  margin-left: 0.5rem !important;
}
.bs .ms-3 {
  margin-left: 1rem !important;
}
.bs .ms-4 {
  margin-left: 1.5rem !important;
}
.bs .ms-5 {
  margin-left: 3rem !important;
}
.bs .ms-auto {
  margin-left: auto !important;
}
.bs .p-0 {
  padding: 0 !important;
}
.bs .p-1 {
  padding: 0.25rem !important;
}
.bs .p-2 {
  padding: 0.5rem !important;
}
.bs .p-3 {
  padding: 1rem !important;
}
.bs .p-4 {
  padding: 1.5rem !important;
}
.bs .p-5 {
  padding: 3rem !important;
}
.bs .px-0 {
  padding-right: 0 !important;
  padding-left: 0 !important;
}
.bs .px-1 {
  padding-right: 0.25rem !important;
  padding-left: 0.25rem !important;
}
.bs .px-2 {
  padding-right: 0.5rem !important;
  padding-left: 0.5rem !important;
}
.bs .px-3 {
  padding-right: 1rem !important;
  padding-left: 1rem !important;
}
.bs .px-4 {
  padding-right: 1.5rem !important;
  padding-left: 1.5rem !important;
}
.bs .px-5 {
  padding-right: 3rem !important;
  padding-left: 3rem !important;
}
.bs .py-0 {
  padding-top: 0 !important;
  padding-bottom: 0 !important;
}
.bs .py-1 {
  padding-top: 0.25rem !important;
  padding-bottom: 0.25rem !important;
}
.bs .py-2 {
  padding-top: 0.5rem !important;
  padding-bottom: 0.5rem !important;
}
.bs .py-3 {
  padding-top: 1rem !important;
  padding-bottom: 1rem !important;
}
.bs .py-4 {
  padding-top: 1.5rem !important;
  padding-bottom: 1.5rem !important;
}
.bs .py-5 {
  padding-top: 3rem !important;
  padding-bottom: 3rem !important;
}
.bs .pt-0 {
  padding-top: 0 !important;
}
.bs .pt-1 {
  padding-top: 0.25rem !important;
}
.bs .pt-2 {
  padding-top: 0.5rem !important;
}
.bs .pt-3 {
  padding-top: 1rem !important;
}
.bs .pt-4 {
  padding-top: 1.5rem !important;
}
.bs .pt-5 {
  padding-top: 3rem !important;
}
.bs .pe-0 {
  padding-right: 0 !important;
}
.bs .pe-1 {
  padding-right: 0.25rem !important;
}
.bs .pe-2 {
  padding-right: 0.5rem !important;
}
.bs .pe-3 {
  padding-right: 1rem !important;
}
.bs .pe-4 {
  padding-right: 1.5rem !important;
}
.bs .pe-5 {
  padding-right: 3rem !important;
}
.bs .pb-0 {
  padding-bottom: 0 !important;
}
.bs .pb-1 {
  padding-bottom: 0.25rem !important;
}
.bs .pb-2 {
  padding-bottom: 0.5rem !important;
}
.bs .pb-3 {
  padding-bottom: 1rem !important;
}
.bs .pb-4 {
  padding-bottom: 1.5rem !important;
}
.bs .pb-5 {
  padding-bottom: 3rem !important;
}
.bs .ps-0 {
  padding-left: 0 !important;
}
.bs .ps-1 {
  padding-left: 0.25rem !important;
}
.bs .ps-2 {
  padding-left: 0.5rem !important;
}
.bs .ps-3 {
  padding-left: 1rem !important;
}
.bs .ps-4 {
  padding-left: 1.5rem !important;
}
.bs .ps-5 {
  padding-left: 3rem !important;
}
.bs .gap-0 {
  gap: 0 !important;
}
.bs .gap-1 {
  gap: 0.25rem !important;
}
.bs .gap-2 {
  gap: 0.5rem !important;
}
.bs .gap-3 {
  gap: 1rem !important;
}
.bs .gap-4 {
  gap: 1.5rem !important;
}
.bs .gap-5 {
  gap: 3rem !important;
}
.bs .font-monospace {
  font-family: var(--bs-font-monospace) !important;
}
.bs .fs-1 {
  font-size: calc(1.375rem + 1.5vw) !important;
}
.bs .fs-2 {
  font-size: calc(1.325rem + 0.9vw) !important;
}
.bs .fs-3 {
  font-size: calc(1.3rem + 0.6vw) !important;
}
.bs .fs-4 {
  font-size: calc(1.275rem + 0.3vw) !important;
}
.bs .fs-5 {
  font-size: 1.25rem !important;
}
.bs .fs-6 {
  font-size: 1rem !important;
}
.bs .fst-italic {
  font-style: italic !important;
}
.bs .fst-normal {
  font-style: normal !important;
}
.bs .fw-light {
  font-weight: 300 !important;
}
.bs .fw-lighter {
  font-weight: lighter !important;
}
.bs .fw-normal {
  font-weight: 400 !important;
}
.bs .fw-bold {
  font-weight: 700 !important;
}
.bs .fw-semibold {
  font-weight: 600 !important;
}
.bs .fw-bolder {
  font-weight: bolder !important;
}
.bs .lh-1 {
  line-height: 1 !important;
}
.bs .lh-sm {
  line-height: 1.25 !important;
}
.bs .lh-base {
  line-height: 1.5 !important;
}
.bs .lh-lg {
  line-height: 2 !important;
}
.bs .text-start {
  text-align: left !important;
}
.bs .text-end {
  text-align: right !important;
}
.bs .text-center {
  text-align: center !important;
}
.bs .text-decoration-none {
  text-decoration: none !important;
}
.bs .text-decoration-underline {
  text-decoration: underline !important;
}
.bs .text-decoration-line-through {
  text-decoration: line-through !important;
}
.bs .text-lowercase {
  text-transform: lowercase !important;
}
.bs .text-uppercase {
  text-transform: uppercase !important;
}
.bs .text-capitalize {
  text-transform: capitalize !important;
}
.bs .text-wrap {
  white-space: normal !important;
}
.bs .text-nowrap {
  white-space: nowrap !important;
}
.bs .text-break {
  word-wrap: break-word !important;
  word-break: break-word !important;
}
.bs .text-primary {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-primary-rgb), var(--bs-text-opacity)) !important;
}
.bs .text-secondary {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-secondary-rgb), var(--bs-text-opacity)) !important;
}
.bs .text-success {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-success-rgb), var(--bs-text-opacity)) !important;
}
.bs .text-info {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-info-rgb), var(--bs-text-opacity)) !important;
}
.bs .text-warning {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-warning-rgb), var(--bs-text-opacity)) !important;
}
.bs .text-danger {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-danger-rgb), var(--bs-text-opacity)) !important;
}
.bs .text-light {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-light-rgb), var(--bs-text-opacity)) !important;
}
.bs .text-dark {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-dark-rgb), var(--bs-text-opacity)) !important;
}
.bs .text-black {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-black-rgb), var(--bs-text-opacity)) !important;
}
.bs .text-white {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-white-rgb), var(--bs-text-opacity)) !important;
}
.bs .text-body {
  --bs-text-opacity: 1;
  color: rgba(var(--bs-body-color-rgb), var(--bs-text-opacity)) !important;
}
.bs .text-muted {
  --bs-text-opacity: 1;
  color: #6c757d !important;
}
.bs .text-black-50 {
  --bs-text-opacity: 1;
  color: rgba(0, 0, 0, 0.5) !important;
}
.bs .text-white-50 {
  --bs-text-opacity: 1;
  color: rgba(255, 255, 255, 0.5) !important;
}
.bs .text-reset {
  --bs-text-opacity: 1;
  color: inherit !important;
}
.bs .text-opacity-25 {
  --bs-text-opacity: 0.25;
}
.bs .text-opacity-50 {
  --bs-text-opacity: 0.5;
}
.bs .text-opacity-75 {
  --bs-text-opacity: 0.75;
}
.bs .text-opacity-100 {
  --bs-text-opacity: 1;
}
.bs .bg-primary {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-primary-rgb), var(--bs-bg-opacity)) !important;
}
.bs .bg-secondary {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-secondary-rgb), var(--bs-bg-opacity)) !important;
}
.bs .bg-success {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-success-rgb), var(--bs-bg-opacity)) !important;
}
.bs .bg-info {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-info-rgb), var(--bs-bg-opacity)) !important;
}
.bs .bg-warning {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-warning-rgb), var(--bs-bg-opacity)) !important;
}
.bs .bg-danger {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-danger-rgb), var(--bs-bg-opacity)) !important;
}
.bs .bg-light {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-light-rgb), var(--bs-bg-opacity)) !important;
}
.bs .bg-dark {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-dark-rgb), var(--bs-bg-opacity)) !important;
}
.bs .bg-black {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-black-rgb), var(--bs-bg-opacity)) !important;
}
.bs .bg-white {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-white-rgb), var(--bs-bg-opacity)) !important;
}
.bs .bg-body {
  --bs-bg-opacity: 1;
  background-color: rgba(var(--bs-body-bg-rgb), var(--bs-bg-opacity)) !important;
}
.bs .bg-transparent {
  --bs-bg-opacity: 1;
  background-color: transparent !important;
}
.bs .bg-opacity-10 {
  --bs-bg-opacity: 0.1;
}
.bs .bg-opacity-25 {
  --bs-bg-opacity: 0.25;
}
.bs .bg-opacity-50 {
  --bs-bg-opacity: 0.5;
}
.bs .bg-opacity-75 {
  --bs-bg-opacity: 0.75;
}
.bs .bg-opacity-100 {
  --bs-bg-opacity: 1;
}
.bs .bg-gradient {
  background-image: var(--bs-gradient) !important;
}
.bs .user-select-all {
  -webkit-user-select: all !important;
     -moz-user-select: all !important;
          user-select: all !important;
}
.bs .user-select-auto {
  -webkit-user-select: auto !important;
     -moz-user-select: auto !important;
          user-select: auto !important;
}
.bs .user-select-none {
  -webkit-user-select: none !important;
     -moz-user-select: none !important;
          user-select: none !important;
}
.bs .pe-none {
  pointer-events: none !important;
}
.bs .pe-auto {
  pointer-events: auto !important;
}
.bs .rounded {
  border-radius: var(--bs-border-radius) !important;
}
.bs .rounded-0 {
  border-radius: 0 !important;
}
.bs .rounded-1 {
  border-radius: var(--bs-border-radius-sm) !important;
}
.bs .rounded-2 {
  border-radius: var(--bs-border-radius) !important;
}
.bs .rounded-3 {
  border-radius: var(--bs-border-radius-lg) !important;
}
.bs .rounded-4 {
  border-radius: var(--bs-border-radius-xl) !important;
}
.bs .rounded-5 {
  border-radius: var(--bs-border-radius-2xl) !important;
}
.bs .rounded-circle {
  border-radius: 50% !important;
}
.bs .rounded-pill {
  border-radius: var(--bs-border-radius-pill) !important;
}
.bs .rounded-top {
  border-top-left-radius: var(--bs-border-radius) !important;
  border-top-right-radius: var(--bs-border-radius) !important;
}
.bs .rounded-end {
  border-top-right-radius: var(--bs-border-radius) !important;
  border-bottom-right-radius: var(--bs-border-radius) !important;
}
.bs .rounded-bottom {
  border-bottom-right-radius: var(--bs-border-radius) !important;
  border-bottom-left-radius: var(--bs-border-radius) !important;
}
.bs .rounded-start {
  border-bottom-left-radius: var(--bs-border-radius) !important;
  border-top-left-radius: var(--bs-border-radius) !important;
}
.bs .visible {
  visibility: visible !important;
}
.bs .invisible {
  visibility: hidden !important;
}
@media (min-width: 576px) {
  .bs .float-sm-start {
    float: left !important;
  }
  .bs .float-sm-end {
    float: right !important;
  }
  .bs .float-sm-none {
    float: none !important;
  }
  .bs .d-sm-inline {
    display: inline !important;
  }
  .bs .d-sm-inline-block {
    display: inline-block !important;
  }
  .bs .d-sm-block {
    display: block !important;
  }
  .bs .d-sm-grid {
    display: grid !important;
  }
  .bs .d-sm-table {
    display: table !important;
  }
  .bs .d-sm-table-row {
    display: table-row !important;
  }
  .bs .d-sm-table-cell {
    display: table-cell !important;
  }
  .bs .d-sm-flex {
    display: flex !important;
  }
  .bs .d-sm-inline-flex {
    display: inline-flex !important;
  }
  .bs .d-sm-none {
    display: none !important;
  }
  .bs .flex-sm-fill {
    flex: 1 1 auto !important;
  }
  .bs .flex-sm-row {
    flex-direction: row !important;
  }
  .bs .flex-sm-column {
    flex-direction: column !important;
  }
  .bs .flex-sm-row-reverse {
    flex-direction: row-reverse !important;
  }
  .bs .flex-sm-column-reverse {
    flex-direction: column-reverse !important;
  }
  .bs .flex-sm-grow-0 {
    flex-grow: 0 !important;
  }
  .bs .flex-sm-grow-1 {
    flex-grow: 1 !important;
  }
  .bs .flex-sm-shrink-0 {
    flex-shrink: 0 !important;
  }
  .bs .flex-sm-shrink-1 {
    flex-shrink: 1 !important;
  }
  .bs .flex-sm-wrap {
    flex-wrap: wrap !important;
  }
  .bs .flex-sm-nowrap {
    flex-wrap: nowrap !important;
  }
  .bs .flex-sm-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .bs .justify-content-sm-start {
    justify-content: flex-start !important;
  }
  .bs .justify-content-sm-end {
    justify-content: flex-end !important;
  }
  .bs .justify-content-sm-center {
    justify-content: center !important;
  }
  .bs .justify-content-sm-between {
    justify-content: space-between !important;
  }
  .bs .justify-content-sm-around {
    justify-content: space-around !important;
  }
  .bs .justify-content-sm-evenly {
    justify-content: space-evenly !important;
  }
  .bs .align-items-sm-start {
    align-items: flex-start !important;
  }
  .bs .align-items-sm-end {
    align-items: flex-end !important;
  }
  .bs .align-items-sm-center {
    align-items: center !important;
  }
  .bs .align-items-sm-baseline {
    align-items: baseline !important;
  }
  .bs .align-items-sm-stretch {
    align-items: stretch !important;
  }
  .bs .align-content-sm-start {
    align-content: flex-start !important;
  }
  .bs .align-content-sm-end {
    align-content: flex-end !important;
  }
  .bs .align-content-sm-center {
    align-content: center !important;
  }
  .bs .align-content-sm-between {
    align-content: space-between !important;
  }
  .bs .align-content-sm-around {
    align-content: space-around !important;
  }
  .bs .align-content-sm-stretch {
    align-content: stretch !important;
  }
  .bs .align-self-sm-auto {
    align-self: auto !important;
  }
  .bs .align-self-sm-start {
    align-self: flex-start !important;
  }
  .bs .align-self-sm-end {
    align-self: flex-end !important;
  }
  .bs .align-self-sm-center {
    align-self: center !important;
  }
  .bs .align-self-sm-baseline {
    align-self: baseline !important;
  }
  .bs .align-self-sm-stretch {
    align-self: stretch !important;
  }
  .bs .order-sm-first {
    order: -1 !important;
  }
  .bs .order-sm-0 {
    order: 0 !important;
  }
  .bs .order-sm-1 {
    order: 1 !important;
  }
  .bs .order-sm-2 {
    order: 2 !important;
  }
  .bs .order-sm-3 {
    order: 3 !important;
  }
  .bs .order-sm-4 {
    order: 4 !important;
  }
  .bs .order-sm-5 {
    order: 5 !important;
  }
  .bs .order-sm-last {
    order: 6 !important;
  }
  .bs .m-sm-0 {
    margin: 0 !important;
  }
  .bs .m-sm-1 {
    margin: 0.25rem !important;
  }
  .bs .m-sm-2 {
    margin: 0.5rem !important;
  }
  .bs .m-sm-3 {
    margin: 1rem !important;
  }
  .bs .m-sm-4 {
    margin: 1.5rem !important;
  }
  .bs .m-sm-5 {
    margin: 3rem !important;
  }
  .bs .m-sm-auto {
    margin: auto !important;
  }
  .bs .mx-sm-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  .bs .mx-sm-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }
  .bs .mx-sm-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }
  .bs .mx-sm-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }
  .bs .mx-sm-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }
  .bs .mx-sm-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }
  .bs .mx-sm-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  .bs .my-sm-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .bs .my-sm-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }
  .bs .my-sm-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .bs .my-sm-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .bs .my-sm-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  .bs .my-sm-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
  .bs .my-sm-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .bs .mt-sm-0 {
    margin-top: 0 !important;
  }
  .bs .mt-sm-1 {
    margin-top: 0.25rem !important;
  }
  .bs .mt-sm-2 {
    margin-top: 0.5rem !important;
  }
  .bs .mt-sm-3 {
    margin-top: 1rem !important;
  }
  .bs .mt-sm-4 {
    margin-top: 1.5rem !important;
  }
  .bs .mt-sm-5 {
    margin-top: 3rem !important;
  }
  .bs .mt-sm-auto {
    margin-top: auto !important;
  }
  .bs .me-sm-0 {
    margin-right: 0 !important;
  }
  .bs .me-sm-1 {
    margin-right: 0.25rem !important;
  }
  .bs .me-sm-2 {
    margin-right: 0.5rem !important;
  }
  .bs .me-sm-3 {
    margin-right: 1rem !important;
  }
  .bs .me-sm-4 {
    margin-right: 1.5rem !important;
  }
  .bs .me-sm-5 {
    margin-right: 3rem !important;
  }
  .bs .me-sm-auto {
    margin-right: auto !important;
  }
  .bs .mb-sm-0 {
    margin-bottom: 0 !important;
  }
  .bs .mb-sm-1 {
    margin-bottom: 0.25rem !important;
  }
  .bs .mb-sm-2 {
    margin-bottom: 0.5rem !important;
  }
  .bs .mb-sm-3 {
    margin-bottom: 1rem !important;
  }
  .bs .mb-sm-4 {
    margin-bottom: 1.5rem !important;
  }
  .bs .mb-sm-5 {
    margin-bottom: 3rem !important;
  }
  .bs .mb-sm-auto {
    margin-bottom: auto !important;
  }
  .bs .ms-sm-0 {
    margin-left: 0 !important;
  }
  .bs .ms-sm-1 {
    margin-left: 0.25rem !important;
  }
  .bs .ms-sm-2 {
    margin-left: 0.5rem !important;
  }
  .bs .ms-sm-3 {
    margin-left: 1rem !important;
  }
  .bs .ms-sm-4 {
    margin-left: 1.5rem !important;
  }
  .bs .ms-sm-5 {
    margin-left: 3rem !important;
  }
  .bs .ms-sm-auto {
    margin-left: auto !important;
  }
  .bs .p-sm-0 {
    padding: 0 !important;
  }
  .bs .p-sm-1 {
    padding: 0.25rem !important;
  }
  .bs .p-sm-2 {
    padding: 0.5rem !important;
  }
  .bs .p-sm-3 {
    padding: 1rem !important;
  }
  .bs .p-sm-4 {
    padding: 1.5rem !important;
  }
  .bs .p-sm-5 {
    padding: 3rem !important;
  }
  .bs .px-sm-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  .bs .px-sm-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }
  .bs .px-sm-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
  .bs .px-sm-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }
  .bs .px-sm-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }
  .bs .px-sm-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }
  .bs .py-sm-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .bs .py-sm-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }
  .bs .py-sm-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .bs .py-sm-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  .bs .py-sm-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .bs .py-sm-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .bs .pt-sm-0 {
    padding-top: 0 !important;
  }
  .bs .pt-sm-1 {
    padding-top: 0.25rem !important;
  }
  .bs .pt-sm-2 {
    padding-top: 0.5rem !important;
  }
  .bs .pt-sm-3 {
    padding-top: 1rem !important;
  }
  .bs .pt-sm-4 {
    padding-top: 1.5rem !important;
  }
  .bs .pt-sm-5 {
    padding-top: 3rem !important;
  }
  .bs .pe-sm-0 {
    padding-right: 0 !important;
  }
  .bs .pe-sm-1 {
    padding-right: 0.25rem !important;
  }
  .bs .pe-sm-2 {
    padding-right: 0.5rem !important;
  }
  .bs .pe-sm-3 {
    padding-right: 1rem !important;
  }
  .bs .pe-sm-4 {
    padding-right: 1.5rem !important;
  }
  .bs .pe-sm-5 {
    padding-right: 3rem !important;
  }
  .bs .pb-sm-0 {
    padding-bottom: 0 !important;
  }
  .bs .pb-sm-1 {
    padding-bottom: 0.25rem !important;
  }
  .bs .pb-sm-2 {
    padding-bottom: 0.5rem !important;
  }
  .bs .pb-sm-3 {
    padding-bottom: 1rem !important;
  }
  .bs .pb-sm-4 {
    padding-bottom: 1.5rem !important;
  }
  .bs .pb-sm-5 {
    padding-bottom: 3rem !important;
  }
  .bs .ps-sm-0 {
    padding-left: 0 !important;
  }
  .bs .ps-sm-1 {
    padding-left: 0.25rem !important;
  }
  .bs .ps-sm-2 {
    padding-left: 0.5rem !important;
  }
  .bs .ps-sm-3 {
    padding-left: 1rem !important;
  }
  .bs .ps-sm-4 {
    padding-left: 1.5rem !important;
  }
  .bs .ps-sm-5 {
    padding-left: 3rem !important;
  }
  .bs .gap-sm-0 {
    gap: 0 !important;
  }
  .bs .gap-sm-1 {
    gap: 0.25rem !important;
  }
  .bs .gap-sm-2 {
    gap: 0.5rem !important;
  }
  .bs .gap-sm-3 {
    gap: 1rem !important;
  }
  .bs .gap-sm-4 {
    gap: 1.5rem !important;
  }
  .bs .gap-sm-5 {
    gap: 3rem !important;
  }
  .bs .text-sm-start {
    text-align: left !important;
  }
  .bs .text-sm-end {
    text-align: right !important;
  }
  .bs .text-sm-center {
    text-align: center !important;
  }
}
@media (min-width: 768px) {
  .bs .float-md-start {
    float: left !important;
  }
  .bs .float-md-end {
    float: right !important;
  }
  .bs .float-md-none {
    float: none !important;
  }
  .bs .d-md-inline {
    display: inline !important;
  }
  .bs .d-md-inline-block {
    display: inline-block !important;
  }
  .bs .d-md-block {
    display: block !important;
  }
  .bs .d-md-grid {
    display: grid !important;
  }
  .bs .d-md-table {
    display: table !important;
  }
  .bs .d-md-table-row {
    display: table-row !important;
  }
  .bs .d-md-table-cell {
    display: table-cell !important;
  }
  .bs .d-md-flex {
    display: flex !important;
  }
  .bs .d-md-inline-flex {
    display: inline-flex !important;
  }
  .bs .d-md-none {
    display: none !important;
  }
  .bs .flex-md-fill {
    flex: 1 1 auto !important;
  }
  .bs .flex-md-row {
    flex-direction: row !important;
  }
  .bs .flex-md-column {
    flex-direction: column !important;
  }
  .bs .flex-md-row-reverse {
    flex-direction: row-reverse !important;
  }
  .bs .flex-md-column-reverse {
    flex-direction: column-reverse !important;
  }
  .bs .flex-md-grow-0 {
    flex-grow: 0 !important;
  }
  .bs .flex-md-grow-1 {
    flex-grow: 1 !important;
  }
  .bs .flex-md-shrink-0 {
    flex-shrink: 0 !important;
  }
  .bs .flex-md-shrink-1 {
    flex-shrink: 1 !important;
  }
  .bs .flex-md-wrap {
    flex-wrap: wrap !important;
  }
  .bs .flex-md-nowrap {
    flex-wrap: nowrap !important;
  }
  .bs .flex-md-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .bs .justify-content-md-start {
    justify-content: flex-start !important;
  }
  .bs .justify-content-md-end {
    justify-content: flex-end !important;
  }
  .bs .justify-content-md-center {
    justify-content: center !important;
  }
  .bs .justify-content-md-between {
    justify-content: space-between !important;
  }
  .bs .justify-content-md-around {
    justify-content: space-around !important;
  }
  .bs .justify-content-md-evenly {
    justify-content: space-evenly !important;
  }
  .bs .align-items-md-start {
    align-items: flex-start !important;
  }
  .bs .align-items-md-end {
    align-items: flex-end !important;
  }
  .bs .align-items-md-center {
    align-items: center !important;
  }
  .bs .align-items-md-baseline {
    align-items: baseline !important;
  }
  .bs .align-items-md-stretch {
    align-items: stretch !important;
  }
  .bs .align-content-md-start {
    align-content: flex-start !important;
  }
  .bs .align-content-md-end {
    align-content: flex-end !important;
  }
  .bs .align-content-md-center {
    align-content: center !important;
  }
  .bs .align-content-md-between {
    align-content: space-between !important;
  }
  .bs .align-content-md-around {
    align-content: space-around !important;
  }
  .bs .align-content-md-stretch {
    align-content: stretch !important;
  }
  .bs .align-self-md-auto {
    align-self: auto !important;
  }
  .bs .align-self-md-start {
    align-self: flex-start !important;
  }
  .bs .align-self-md-end {
    align-self: flex-end !important;
  }
  .bs .align-self-md-center {
    align-self: center !important;
  }
  .bs .align-self-md-baseline {
    align-self: baseline !important;
  }
  .bs .align-self-md-stretch {
    align-self: stretch !important;
  }
  .bs .order-md-first {
    order: -1 !important;
  }
  .bs .order-md-0 {
    order: 0 !important;
  }
  .bs .order-md-1 {
    order: 1 !important;
  }
  .bs .order-md-2 {
    order: 2 !important;
  }
  .bs .order-md-3 {
    order: 3 !important;
  }
  .bs .order-md-4 {
    order: 4 !important;
  }
  .bs .order-md-5 {
    order: 5 !important;
  }
  .bs .order-md-last {
    order: 6 !important;
  }
  .bs .m-md-0 {
    margin: 0 !important;
  }
  .bs .m-md-1 {
    margin: 0.25rem !important;
  }
  .bs .m-md-2 {
    margin: 0.5rem !important;
  }
  .bs .m-md-3 {
    margin: 1rem !important;
  }
  .bs .m-md-4 {
    margin: 1.5rem !important;
  }
  .bs .m-md-5 {
    margin: 3rem !important;
  }
  .bs .m-md-auto {
    margin: auto !important;
  }
  .bs .mx-md-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  .bs .mx-md-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }
  .bs .mx-md-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }
  .bs .mx-md-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }
  .bs .mx-md-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }
  .bs .mx-md-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }
  .bs .mx-md-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  .bs .my-md-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .bs .my-md-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }
  .bs .my-md-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .bs .my-md-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .bs .my-md-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  .bs .my-md-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
  .bs .my-md-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .bs .mt-md-0 {
    margin-top: 0 !important;
  }
  .bs .mt-md-1 {
    margin-top: 0.25rem !important;
  }
  .bs .mt-md-2 {
    margin-top: 0.5rem !important;
  }
  .bs .mt-md-3 {
    margin-top: 1rem !important;
  }
  .bs .mt-md-4 {
    margin-top: 1.5rem !important;
  }
  .bs .mt-md-5 {
    margin-top: 3rem !important;
  }
  .bs .mt-md-auto {
    margin-top: auto !important;
  }
  .bs .me-md-0 {
    margin-right: 0 !important;
  }
  .bs .me-md-1 {
    margin-right: 0.25rem !important;
  }
  .bs .me-md-2 {
    margin-right: 0.5rem !important;
  }
  .bs .me-md-3 {
    margin-right: 1rem !important;
  }
  .bs .me-md-4 {
    margin-right: 1.5rem !important;
  }
  .bs .me-md-5 {
    margin-right: 3rem !important;
  }
  .bs .me-md-auto {
    margin-right: auto !important;
  }
  .bs .mb-md-0 {
    margin-bottom: 0 !important;
  }
  .bs .mb-md-1 {
    margin-bottom: 0.25rem !important;
  }
  .bs .mb-md-2 {
    margin-bottom: 0.5rem !important;
  }
  .bs .mb-md-3 {
    margin-bottom: 1rem !important;
  }
  .bs .mb-md-4 {
    margin-bottom: 1.5rem !important;
  }
  .bs .mb-md-5 {
    margin-bottom: 3rem !important;
  }
  .bs .mb-md-auto {
    margin-bottom: auto !important;
  }
  .bs .ms-md-0 {
    margin-left: 0 !important;
  }
  .bs .ms-md-1 {
    margin-left: 0.25rem !important;
  }
  .bs .ms-md-2 {
    margin-left: 0.5rem !important;
  }
  .bs .ms-md-3 {
    margin-left: 1rem !important;
  }
  .bs .ms-md-4 {
    margin-left: 1.5rem !important;
  }
  .bs .ms-md-5 {
    margin-left: 3rem !important;
  }
  .bs .ms-md-auto {
    margin-left: auto !important;
  }
  .bs .p-md-0 {
    padding: 0 !important;
  }
  .bs .p-md-1 {
    padding: 0.25rem !important;
  }
  .bs .p-md-2 {
    padding: 0.5rem !important;
  }
  .bs .p-md-3 {
    padding: 1rem !important;
  }
  .bs .p-md-4 {
    padding: 1.5rem !important;
  }
  .bs .p-md-5 {
    padding: 3rem !important;
  }
  .bs .px-md-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  .bs .px-md-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }
  .bs .px-md-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
  .bs .px-md-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }
  .bs .px-md-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }
  .bs .px-md-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }
  .bs .py-md-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .bs .py-md-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }
  .bs .py-md-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .bs .py-md-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  .bs .py-md-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .bs .py-md-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .bs .pt-md-0 {
    padding-top: 0 !important;
  }
  .bs .pt-md-1 {
    padding-top: 0.25rem !important;
  }
  .bs .pt-md-2 {
    padding-top: 0.5rem !important;
  }
  .bs .pt-md-3 {
    padding-top: 1rem !important;
  }
  .bs .pt-md-4 {
    padding-top: 1.5rem !important;
  }
  .bs .pt-md-5 {
    padding-top: 3rem !important;
  }
  .bs .pe-md-0 {
    padding-right: 0 !important;
  }
  .bs .pe-md-1 {
    padding-right: 0.25rem !important;
  }
  .bs .pe-md-2 {
    padding-right: 0.5rem !important;
  }
  .bs .pe-md-3 {
    padding-right: 1rem !important;
  }
  .bs .pe-md-4 {
    padding-right: 1.5rem !important;
  }
  .bs .pe-md-5 {
    padding-right: 3rem !important;
  }
  .bs .pb-md-0 {
    padding-bottom: 0 !important;
  }
  .bs .pb-md-1 {
    padding-bottom: 0.25rem !important;
  }
  .bs .pb-md-2 {
    padding-bottom: 0.5rem !important;
  }
  .bs .pb-md-3 {
    padding-bottom: 1rem !important;
  }
  .bs .pb-md-4 {
    padding-bottom: 1.5rem !important;
  }
  .bs .pb-md-5 {
    padding-bottom: 3rem !important;
  }
  .bs .ps-md-0 {
    padding-left: 0 !important;
  }
  .bs .ps-md-1 {
    padding-left: 0.25rem !important;
  }
  .bs .ps-md-2 {
    padding-left: 0.5rem !important;
  }
  .bs .ps-md-3 {
    padding-left: 1rem !important;
  }
  .bs .ps-md-4 {
    padding-left: 1.5rem !important;
  }
  .bs .ps-md-5 {
    padding-left: 3rem !important;
  }
  .bs .gap-md-0 {
    gap: 0 !important;
  }
  .bs .gap-md-1 {
    gap: 0.25rem !important;
  }
  .bs .gap-md-2 {
    gap: 0.5rem !important;
  }
  .bs .gap-md-3 {
    gap: 1rem !important;
  }
  .bs .gap-md-4 {
    gap: 1.5rem !important;
  }
  .bs .gap-md-5 {
    gap: 3rem !important;
  }
  .bs .text-md-start {
    text-align: left !important;
  }
  .bs .text-md-end {
    text-align: right !important;
  }
  .bs .text-md-center {
    text-align: center !important;
  }
}
@media (min-width: 992px) {
  .bs .float-lg-start {
    float: left !important;
  }
  .bs .float-lg-end {
    float: right !important;
  }
  .bs .float-lg-none {
    float: none !important;
  }
  .bs .d-lg-inline {
    display: inline !important;
  }
  .bs .d-lg-inline-block {
    display: inline-block !important;
  }
  .bs .d-lg-block {
    display: block !important;
  }
  .bs .d-lg-grid {
    display: grid !important;
  }
  .bs .d-lg-table {
    display: table !important;
  }
  .bs .d-lg-table-row {
    display: table-row !important;
  }
  .bs .d-lg-table-cell {
    display: table-cell !important;
  }
  .bs .d-lg-flex {
    display: flex !important;
  }
  .bs .d-lg-inline-flex {
    display: inline-flex !important;
  }
  .bs .d-lg-none {
    display: none !important;
  }
  .bs .flex-lg-fill {
    flex: 1 1 auto !important;
  }
  .bs .flex-lg-row {
    flex-direction: row !important;
  }
  .bs .flex-lg-column {
    flex-direction: column !important;
  }
  .bs .flex-lg-row-reverse {
    flex-direction: row-reverse !important;
  }
  .bs .flex-lg-column-reverse {
    flex-direction: column-reverse !important;
  }
  .bs .flex-lg-grow-0 {
    flex-grow: 0 !important;
  }
  .bs .flex-lg-grow-1 {
    flex-grow: 1 !important;
  }
  .bs .flex-lg-shrink-0 {
    flex-shrink: 0 !important;
  }
  .bs .flex-lg-shrink-1 {
    flex-shrink: 1 !important;
  }
  .bs .flex-lg-wrap {
    flex-wrap: wrap !important;
  }
  .bs .flex-lg-nowrap {
    flex-wrap: nowrap !important;
  }
  .bs .flex-lg-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .bs .justify-content-lg-start {
    justify-content: flex-start !important;
  }
  .bs .justify-content-lg-end {
    justify-content: flex-end !important;
  }
  .bs .justify-content-lg-center {
    justify-content: center !important;
  }
  .bs .justify-content-lg-between {
    justify-content: space-between !important;
  }
  .bs .justify-content-lg-around {
    justify-content: space-around !important;
  }
  .bs .justify-content-lg-evenly {
    justify-content: space-evenly !important;
  }
  .bs .align-items-lg-start {
    align-items: flex-start !important;
  }
  .bs .align-items-lg-end {
    align-items: flex-end !important;
  }
  .bs .align-items-lg-center {
    align-items: center !important;
  }
  .bs .align-items-lg-baseline {
    align-items: baseline !important;
  }
  .bs .align-items-lg-stretch {
    align-items: stretch !important;
  }
  .bs .align-content-lg-start {
    align-content: flex-start !important;
  }
  .bs .align-content-lg-end {
    align-content: flex-end !important;
  }
  .bs .align-content-lg-center {
    align-content: center !important;
  }
  .bs .align-content-lg-between {
    align-content: space-between !important;
  }
  .bs .align-content-lg-around {
    align-content: space-around !important;
  }
  .bs .align-content-lg-stretch {
    align-content: stretch !important;
  }
  .bs .align-self-lg-auto {
    align-self: auto !important;
  }
  .bs .align-self-lg-start {
    align-self: flex-start !important;
  }
  .bs .align-self-lg-end {
    align-self: flex-end !important;
  }
  .bs .align-self-lg-center {
    align-self: center !important;
  }
  .bs .align-self-lg-baseline {
    align-self: baseline !important;
  }
  .bs .align-self-lg-stretch {
    align-self: stretch !important;
  }
  .bs .order-lg-first {
    order: -1 !important;
  }
  .bs .order-lg-0 {
    order: 0 !important;
  }
  .bs .order-lg-1 {
    order: 1 !important;
  }
  .bs .order-lg-2 {
    order: 2 !important;
  }
  .bs .order-lg-3 {
    order: 3 !important;
  }
  .bs .order-lg-4 {
    order: 4 !important;
  }
  .bs .order-lg-5 {
    order: 5 !important;
  }
  .bs .order-lg-last {
    order: 6 !important;
  }
  .bs .m-lg-0 {
    margin: 0 !important;
  }
  .bs .m-lg-1 {
    margin: 0.25rem !important;
  }
  .bs .m-lg-2 {
    margin: 0.5rem !important;
  }
  .bs .m-lg-3 {
    margin: 1rem !important;
  }
  .bs .m-lg-4 {
    margin: 1.5rem !important;
  }
  .bs .m-lg-5 {
    margin: 3rem !important;
  }
  .bs .m-lg-auto {
    margin: auto !important;
  }
  .bs .mx-lg-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  .bs .mx-lg-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }
  .bs .mx-lg-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }
  .bs .mx-lg-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }
  .bs .mx-lg-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }
  .bs .mx-lg-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }
  .bs .mx-lg-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  .bs .my-lg-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .bs .my-lg-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }
  .bs .my-lg-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .bs .my-lg-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .bs .my-lg-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  .bs .my-lg-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
  .bs .my-lg-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .bs .mt-lg-0 {
    margin-top: 0 !important;
  }
  .bs .mt-lg-1 {
    margin-top: 0.25rem !important;
  }
  .bs .mt-lg-2 {
    margin-top: 0.5rem !important;
  }
  .bs .mt-lg-3 {
    margin-top: 1rem !important;
  }
  .bs .mt-lg-4 {
    margin-top: 1.5rem !important;
  }
  .bs .mt-lg-5 {
    margin-top: 3rem !important;
  }
  .bs .mt-lg-auto {
    margin-top: auto !important;
  }
  .bs .me-lg-0 {
    margin-right: 0 !important;
  }
  .bs .me-lg-1 {
    margin-right: 0.25rem !important;
  }
  .bs .me-lg-2 {
    margin-right: 0.5rem !important;
  }
  .bs .me-lg-3 {
    margin-right: 1rem !important;
  }
  .bs .me-lg-4 {
    margin-right: 1.5rem !important;
  }
  .bs .me-lg-5 {
    margin-right: 3rem !important;
  }
  .bs .me-lg-auto {
    margin-right: auto !important;
  }
  .bs .mb-lg-0 {
    margin-bottom: 0 !important;
  }
  .bs .mb-lg-1 {
    margin-bottom: 0.25rem !important;
  }
  .bs .mb-lg-2 {
    margin-bottom: 0.5rem !important;
  }
  .bs .mb-lg-3 {
    margin-bottom: 1rem !important;
  }
  .bs .mb-lg-4 {
    margin-bottom: 1.5rem !important;
  }
  .bs .mb-lg-5 {
    margin-bottom: 3rem !important;
  }
  .bs .mb-lg-auto {
    margin-bottom: auto !important;
  }
  .bs .ms-lg-0 {
    margin-left: 0 !important;
  }
  .bs .ms-lg-1 {
    margin-left: 0.25rem !important;
  }
  .bs .ms-lg-2 {
    margin-left: 0.5rem !important;
  }
  .bs .ms-lg-3 {
    margin-left: 1rem !important;
  }
  .bs .ms-lg-4 {
    margin-left: 1.5rem !important;
  }
  .bs .ms-lg-5 {
    margin-left: 3rem !important;
  }
  .bs .ms-lg-auto {
    margin-left: auto !important;
  }
  .bs .p-lg-0 {
    padding: 0 !important;
  }
  .bs .p-lg-1 {
    padding: 0.25rem !important;
  }
  .bs .p-lg-2 {
    padding: 0.5rem !important;
  }
  .bs .p-lg-3 {
    padding: 1rem !important;
  }
  .bs .p-lg-4 {
    padding: 1.5rem !important;
  }
  .bs .p-lg-5 {
    padding: 3rem !important;
  }
  .bs .px-lg-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  .bs .px-lg-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }
  .bs .px-lg-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
  .bs .px-lg-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }
  .bs .px-lg-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }
  .bs .px-lg-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }
  .bs .py-lg-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .bs .py-lg-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }
  .bs .py-lg-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .bs .py-lg-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  .bs .py-lg-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .bs .py-lg-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .bs .pt-lg-0 {
    padding-top: 0 !important;
  }
  .bs .pt-lg-1 {
    padding-top: 0.25rem !important;
  }
  .bs .pt-lg-2 {
    padding-top: 0.5rem !important;
  }
  .bs .pt-lg-3 {
    padding-top: 1rem !important;
  }
  .bs .pt-lg-4 {
    padding-top: 1.5rem !important;
  }
  .bs .pt-lg-5 {
    padding-top: 3rem !important;
  }
  .bs .pe-lg-0 {
    padding-right: 0 !important;
  }
  .bs .pe-lg-1 {
    padding-right: 0.25rem !important;
  }
  .bs .pe-lg-2 {
    padding-right: 0.5rem !important;
  }
  .bs .pe-lg-3 {
    padding-right: 1rem !important;
  }
  .bs .pe-lg-4 {
    padding-right: 1.5rem !important;
  }
  .bs .pe-lg-5 {
    padding-right: 3rem !important;
  }
  .bs .pb-lg-0 {
    padding-bottom: 0 !important;
  }
  .bs .pb-lg-1 {
    padding-bottom: 0.25rem !important;
  }
  .bs .pb-lg-2 {
    padding-bottom: 0.5rem !important;
  }
  .bs .pb-lg-3 {
    padding-bottom: 1rem !important;
  }
  .bs .pb-lg-4 {
    padding-bottom: 1.5rem !important;
  }
  .bs .pb-lg-5 {
    padding-bottom: 3rem !important;
  }
  .bs .ps-lg-0 {
    padding-left: 0 !important;
  }
  .bs .ps-lg-1 {
    padding-left: 0.25rem !important;
  }
  .bs .ps-lg-2 {
    padding-left: 0.5rem !important;
  }
  .bs .ps-lg-3 {
    padding-left: 1rem !important;
  }
  .bs .ps-lg-4 {
    padding-left: 1.5rem !important;
  }
  .bs .ps-lg-5 {
    padding-left: 3rem !important;
  }
  .bs .gap-lg-0 {
    gap: 0 !important;
  }
  .bs .gap-lg-1 {
    gap: 0.25rem !important;
  }
  .bs .gap-lg-2 {
    gap: 0.5rem !important;
  }
  .bs .gap-lg-3 {
    gap: 1rem !important;
  }
  .bs .gap-lg-4 {
    gap: 1.5rem !important;
  }
  .bs .gap-lg-5 {
    gap: 3rem !important;
  }
  .bs .text-lg-start {
    text-align: left !important;
  }
  .bs .text-lg-end {
    text-align: right !important;
  }
  .bs .text-lg-center {
    text-align: center !important;
  }
}
@media (min-width: 1200px) {
  .bs .float-xl-start {
    float: left !important;
  }
  .bs .float-xl-end {
    float: right !important;
  }
  .bs .float-xl-none {
    float: none !important;
  }
  .bs .d-xl-inline {
    display: inline !important;
  }
  .bs .d-xl-inline-block {
    display: inline-block !important;
  }
  .bs .d-xl-block {
    display: block !important;
  }
  .bs .d-xl-grid {
    display: grid !important;
  }
  .bs .d-xl-table {
    display: table !important;
  }
  .bs .d-xl-table-row {
    display: table-row !important;
  }
  .bs .d-xl-table-cell {
    display: table-cell !important;
  }
  .bs .d-xl-flex {
    display: flex !important;
  }
  .bs .d-xl-inline-flex {
    display: inline-flex !important;
  }
  .bs .d-xl-none {
    display: none !important;
  }
  .bs .flex-xl-fill {
    flex: 1 1 auto !important;
  }
  .bs .flex-xl-row {
    flex-direction: row !important;
  }
  .bs .flex-xl-column {
    flex-direction: column !important;
  }
  .bs .flex-xl-row-reverse {
    flex-direction: row-reverse !important;
  }
  .bs .flex-xl-column-reverse {
    flex-direction: column-reverse !important;
  }
  .bs .flex-xl-grow-0 {
    flex-grow: 0 !important;
  }
  .bs .flex-xl-grow-1 {
    flex-grow: 1 !important;
  }
  .bs .flex-xl-shrink-0 {
    flex-shrink: 0 !important;
  }
  .bs .flex-xl-shrink-1 {
    flex-shrink: 1 !important;
  }
  .bs .flex-xl-wrap {
    flex-wrap: wrap !important;
  }
  .bs .flex-xl-nowrap {
    flex-wrap: nowrap !important;
  }
  .bs .flex-xl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .bs .justify-content-xl-start {
    justify-content: flex-start !important;
  }
  .bs .justify-content-xl-end {
    justify-content: flex-end !important;
  }
  .bs .justify-content-xl-center {
    justify-content: center !important;
  }
  .bs .justify-content-xl-between {
    justify-content: space-between !important;
  }
  .bs .justify-content-xl-around {
    justify-content: space-around !important;
  }
  .bs .justify-content-xl-evenly {
    justify-content: space-evenly !important;
  }
  .bs .align-items-xl-start {
    align-items: flex-start !important;
  }
  .bs .align-items-xl-end {
    align-items: flex-end !important;
  }
  .bs .align-items-xl-center {
    align-items: center !important;
  }
  .bs .align-items-xl-baseline {
    align-items: baseline !important;
  }
  .bs .align-items-xl-stretch {
    align-items: stretch !important;
  }
  .bs .align-content-xl-start {
    align-content: flex-start !important;
  }
  .bs .align-content-xl-end {
    align-content: flex-end !important;
  }
  .bs .align-content-xl-center {
    align-content: center !important;
  }
  .bs .align-content-xl-between {
    align-content: space-between !important;
  }
  .bs .align-content-xl-around {
    align-content: space-around !important;
  }
  .bs .align-content-xl-stretch {
    align-content: stretch !important;
  }
  .bs .align-self-xl-auto {
    align-self: auto !important;
  }
  .bs .align-self-xl-start {
    align-self: flex-start !important;
  }
  .bs .align-self-xl-end {
    align-self: flex-end !important;
  }
  .bs .align-self-xl-center {
    align-self: center !important;
  }
  .bs .align-self-xl-baseline {
    align-self: baseline !important;
  }
  .bs .align-self-xl-stretch {
    align-self: stretch !important;
  }
  .bs .order-xl-first {
    order: -1 !important;
  }
  .bs .order-xl-0 {
    order: 0 !important;
  }
  .bs .order-xl-1 {
    order: 1 !important;
  }
  .bs .order-xl-2 {
    order: 2 !important;
  }
  .bs .order-xl-3 {
    order: 3 !important;
  }
  .bs .order-xl-4 {
    order: 4 !important;
  }
  .bs .order-xl-5 {
    order: 5 !important;
  }
  .bs .order-xl-last {
    order: 6 !important;
  }
  .bs .m-xl-0 {
    margin: 0 !important;
  }
  .bs .m-xl-1 {
    margin: 0.25rem !important;
  }
  .bs .m-xl-2 {
    margin: 0.5rem !important;
  }
  .bs .m-xl-3 {
    margin: 1rem !important;
  }
  .bs .m-xl-4 {
    margin: 1.5rem !important;
  }
  .bs .m-xl-5 {
    margin: 3rem !important;
  }
  .bs .m-xl-auto {
    margin: auto !important;
  }
  .bs .mx-xl-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  .bs .mx-xl-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }
  .bs .mx-xl-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }
  .bs .mx-xl-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }
  .bs .mx-xl-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }
  .bs .mx-xl-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }
  .bs .mx-xl-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  .bs .my-xl-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .bs .my-xl-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }
  .bs .my-xl-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .bs .my-xl-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .bs .my-xl-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  .bs .my-xl-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
  .bs .my-xl-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .bs .mt-xl-0 {
    margin-top: 0 !important;
  }
  .bs .mt-xl-1 {
    margin-top: 0.25rem !important;
  }
  .bs .mt-xl-2 {
    margin-top: 0.5rem !important;
  }
  .bs .mt-xl-3 {
    margin-top: 1rem !important;
  }
  .bs .mt-xl-4 {
    margin-top: 1.5rem !important;
  }
  .bs .mt-xl-5 {
    margin-top: 3rem !important;
  }
  .bs .mt-xl-auto {
    margin-top: auto !important;
  }
  .bs .me-xl-0 {
    margin-right: 0 !important;
  }
  .bs .me-xl-1 {
    margin-right: 0.25rem !important;
  }
  .bs .me-xl-2 {
    margin-right: 0.5rem !important;
  }
  .bs .me-xl-3 {
    margin-right: 1rem !important;
  }
  .bs .me-xl-4 {
    margin-right: 1.5rem !important;
  }
  .bs .me-xl-5 {
    margin-right: 3rem !important;
  }
  .bs .me-xl-auto {
    margin-right: auto !important;
  }
  .bs .mb-xl-0 {
    margin-bottom: 0 !important;
  }
  .bs .mb-xl-1 {
    margin-bottom: 0.25rem !important;
  }
  .bs .mb-xl-2 {
    margin-bottom: 0.5rem !important;
  }
  .bs .mb-xl-3 {
    margin-bottom: 1rem !important;
  }
  .bs .mb-xl-4 {
    margin-bottom: 1.5rem !important;
  }
  .bs .mb-xl-5 {
    margin-bottom: 3rem !important;
  }
  .bs .mb-xl-auto {
    margin-bottom: auto !important;
  }
  .bs .ms-xl-0 {
    margin-left: 0 !important;
  }
  .bs .ms-xl-1 {
    margin-left: 0.25rem !important;
  }
  .bs .ms-xl-2 {
    margin-left: 0.5rem !important;
  }
  .bs .ms-xl-3 {
    margin-left: 1rem !important;
  }
  .bs .ms-xl-4 {
    margin-left: 1.5rem !important;
  }
  .bs .ms-xl-5 {
    margin-left: 3rem !important;
  }
  .bs .ms-xl-auto {
    margin-left: auto !important;
  }
  .bs .p-xl-0 {
    padding: 0 !important;
  }
  .bs .p-xl-1 {
    padding: 0.25rem !important;
  }
  .bs .p-xl-2 {
    padding: 0.5rem !important;
  }
  .bs .p-xl-3 {
    padding: 1rem !important;
  }
  .bs .p-xl-4 {
    padding: 1.5rem !important;
  }
  .bs .p-xl-5 {
    padding: 3rem !important;
  }
  .bs .px-xl-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  .bs .px-xl-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }
  .bs .px-xl-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
  .bs .px-xl-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }
  .bs .px-xl-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }
  .bs .px-xl-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }
  .bs .py-xl-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .bs .py-xl-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }
  .bs .py-xl-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .bs .py-xl-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  .bs .py-xl-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .bs .py-xl-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .bs .pt-xl-0 {
    padding-top: 0 !important;
  }
  .bs .pt-xl-1 {
    padding-top: 0.25rem !important;
  }
  .bs .pt-xl-2 {
    padding-top: 0.5rem !important;
  }
  .bs .pt-xl-3 {
    padding-top: 1rem !important;
  }
  .bs .pt-xl-4 {
    padding-top: 1.5rem !important;
  }
  .bs .pt-xl-5 {
    padding-top: 3rem !important;
  }
  .bs .pe-xl-0 {
    padding-right: 0 !important;
  }
  .bs .pe-xl-1 {
    padding-right: 0.25rem !important;
  }
  .bs .pe-xl-2 {
    padding-right: 0.5rem !important;
  }
  .bs .pe-xl-3 {
    padding-right: 1rem !important;
  }
  .bs .pe-xl-4 {
    padding-right: 1.5rem !important;
  }
  .bs .pe-xl-5 {
    padding-right: 3rem !important;
  }
  .bs .pb-xl-0 {
    padding-bottom: 0 !important;
  }
  .bs .pb-xl-1 {
    padding-bottom: 0.25rem !important;
  }
  .bs .pb-xl-2 {
    padding-bottom: 0.5rem !important;
  }
  .bs .pb-xl-3 {
    padding-bottom: 1rem !important;
  }
  .bs .pb-xl-4 {
    padding-bottom: 1.5rem !important;
  }
  .bs .pb-xl-5 {
    padding-bottom: 3rem !important;
  }
  .bs .ps-xl-0 {
    padding-left: 0 !important;
  }
  .bs .ps-xl-1 {
    padding-left: 0.25rem !important;
  }
  .bs .ps-xl-2 {
    padding-left: 0.5rem !important;
  }
  .bs .ps-xl-3 {
    padding-left: 1rem !important;
  }
  .bs .ps-xl-4 {
    padding-left: 1.5rem !important;
  }
  .bs .ps-xl-5 {
    padding-left: 3rem !important;
  }
  .bs .gap-xl-0 {
    gap: 0 !important;
  }
  .bs .gap-xl-1 {
    gap: 0.25rem !important;
  }
  .bs .gap-xl-2 {
    gap: 0.5rem !important;
  }
  .bs .gap-xl-3 {
    gap: 1rem !important;
  }
  .bs .gap-xl-4 {
    gap: 1.5rem !important;
  }
  .bs .gap-xl-5 {
    gap: 3rem !important;
  }
  .bs .text-xl-start {
    text-align: left !important;
  }
  .bs .text-xl-end {
    text-align: right !important;
  }
  .bs .text-xl-center {
    text-align: center !important;
  }
}
@media (min-width: 1400px) {
  .bs .float-xxl-start {
    float: left !important;
  }
  .bs .float-xxl-end {
    float: right !important;
  }
  .bs .float-xxl-none {
    float: none !important;
  }
  .bs .d-xxl-inline {
    display: inline !important;
  }
  .bs .d-xxl-inline-block {
    display: inline-block !important;
  }
  .bs .d-xxl-block {
    display: block !important;
  }
  .bs .d-xxl-grid {
    display: grid !important;
  }
  .bs .d-xxl-table {
    display: table !important;
  }
  .bs .d-xxl-table-row {
    display: table-row !important;
  }
  .bs .d-xxl-table-cell {
    display: table-cell !important;
  }
  .bs .d-xxl-flex {
    display: flex !important;
  }
  .bs .d-xxl-inline-flex {
    display: inline-flex !important;
  }
  .bs .d-xxl-none {
    display: none !important;
  }
  .bs .flex-xxl-fill {
    flex: 1 1 auto !important;
  }
  .bs .flex-xxl-row {
    flex-direction: row !important;
  }
  .bs .flex-xxl-column {
    flex-direction: column !important;
  }
  .bs .flex-xxl-row-reverse {
    flex-direction: row-reverse !important;
  }
  .bs .flex-xxl-column-reverse {
    flex-direction: column-reverse !important;
  }
  .bs .flex-xxl-grow-0 {
    flex-grow: 0 !important;
  }
  .bs .flex-xxl-grow-1 {
    flex-grow: 1 !important;
  }
  .bs .flex-xxl-shrink-0 {
    flex-shrink: 0 !important;
  }
  .bs .flex-xxl-shrink-1 {
    flex-shrink: 1 !important;
  }
  .bs .flex-xxl-wrap {
    flex-wrap: wrap !important;
  }
  .bs .flex-xxl-nowrap {
    flex-wrap: nowrap !important;
  }
  .bs .flex-xxl-wrap-reverse {
    flex-wrap: wrap-reverse !important;
  }
  .bs .justify-content-xxl-start {
    justify-content: flex-start !important;
  }
  .bs .justify-content-xxl-end {
    justify-content: flex-end !important;
  }
  .bs .justify-content-xxl-center {
    justify-content: center !important;
  }
  .bs .justify-content-xxl-between {
    justify-content: space-between !important;
  }
  .bs .justify-content-xxl-around {
    justify-content: space-around !important;
  }
  .bs .justify-content-xxl-evenly {
    justify-content: space-evenly !important;
  }
  .bs .align-items-xxl-start {
    align-items: flex-start !important;
  }
  .bs .align-items-xxl-end {
    align-items: flex-end !important;
  }
  .bs .align-items-xxl-center {
    align-items: center !important;
  }
  .bs .align-items-xxl-baseline {
    align-items: baseline !important;
  }
  .bs .align-items-xxl-stretch {
    align-items: stretch !important;
  }
  .bs .align-content-xxl-start {
    align-content: flex-start !important;
  }
  .bs .align-content-xxl-end {
    align-content: flex-end !important;
  }
  .bs .align-content-xxl-center {
    align-content: center !important;
  }
  .bs .align-content-xxl-between {
    align-content: space-between !important;
  }
  .bs .align-content-xxl-around {
    align-content: space-around !important;
  }
  .bs .align-content-xxl-stretch {
    align-content: stretch !important;
  }
  .bs .align-self-xxl-auto {
    align-self: auto !important;
  }
  .bs .align-self-xxl-start {
    align-self: flex-start !important;
  }
  .bs .align-self-xxl-end {
    align-self: flex-end !important;
  }
  .bs .align-self-xxl-center {
    align-self: center !important;
  }
  .bs .align-self-xxl-baseline {
    align-self: baseline !important;
  }
  .bs .align-self-xxl-stretch {
    align-self: stretch !important;
  }
  .bs .order-xxl-first {
    order: -1 !important;
  }
  .bs .order-xxl-0 {
    order: 0 !important;
  }
  .bs .order-xxl-1 {
    order: 1 !important;
  }
  .bs .order-xxl-2 {
    order: 2 !important;
  }
  .bs .order-xxl-3 {
    order: 3 !important;
  }
  .bs .order-xxl-4 {
    order: 4 !important;
  }
  .bs .order-xxl-5 {
    order: 5 !important;
  }
  .bs .order-xxl-last {
    order: 6 !important;
  }
  .bs .m-xxl-0 {
    margin: 0 !important;
  }
  .bs .m-xxl-1 {
    margin: 0.25rem !important;
  }
  .bs .m-xxl-2 {
    margin: 0.5rem !important;
  }
  .bs .m-xxl-3 {
    margin: 1rem !important;
  }
  .bs .m-xxl-4 {
    margin: 1.5rem !important;
  }
  .bs .m-xxl-5 {
    margin: 3rem !important;
  }
  .bs .m-xxl-auto {
    margin: auto !important;
  }
  .bs .mx-xxl-0 {
    margin-right: 0 !important;
    margin-left: 0 !important;
  }
  .bs .mx-xxl-1 {
    margin-right: 0.25rem !important;
    margin-left: 0.25rem !important;
  }
  .bs .mx-xxl-2 {
    margin-right: 0.5rem !important;
    margin-left: 0.5rem !important;
  }
  .bs .mx-xxl-3 {
    margin-right: 1rem !important;
    margin-left: 1rem !important;
  }
  .bs .mx-xxl-4 {
    margin-right: 1.5rem !important;
    margin-left: 1.5rem !important;
  }
  .bs .mx-xxl-5 {
    margin-right: 3rem !important;
    margin-left: 3rem !important;
  }
  .bs .mx-xxl-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  .bs .my-xxl-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }
  .bs .my-xxl-1 {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }
  .bs .my-xxl-2 {
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
  .bs .my-xxl-3 {
    margin-top: 1rem !important;
    margin-bottom: 1rem !important;
  }
  .bs .my-xxl-4 {
    margin-top: 1.5rem !important;
    margin-bottom: 1.5rem !important;
  }
  .bs .my-xxl-5 {
    margin-top: 3rem !important;
    margin-bottom: 3rem !important;
  }
  .bs .my-xxl-auto {
    margin-top: auto !important;
    margin-bottom: auto !important;
  }
  .bs .mt-xxl-0 {
    margin-top: 0 !important;
  }
  .bs .mt-xxl-1 {
    margin-top: 0.25rem !important;
  }
  .bs .mt-xxl-2 {
    margin-top: 0.5rem !important;
  }
  .bs .mt-xxl-3 {
    margin-top: 1rem !important;
  }
  .bs .mt-xxl-4 {
    margin-top: 1.5rem !important;
  }
  .bs .mt-xxl-5 {
    margin-top: 3rem !important;
  }
  .bs .mt-xxl-auto {
    margin-top: auto !important;
  }
  .bs .me-xxl-0 {
    margin-right: 0 !important;
  }
  .bs .me-xxl-1 {
    margin-right: 0.25rem !important;
  }
  .bs .me-xxl-2 {
    margin-right: 0.5rem !important;
  }
  .bs .me-xxl-3 {
    margin-right: 1rem !important;
  }
  .bs .me-xxl-4 {
    margin-right: 1.5rem !important;
  }
  .bs .me-xxl-5 {
    margin-right: 3rem !important;
  }
  .bs .me-xxl-auto {
    margin-right: auto !important;
  }
  .bs .mb-xxl-0 {
    margin-bottom: 0 !important;
  }
  .bs .mb-xxl-1 {
    margin-bottom: 0.25rem !important;
  }
  .bs .mb-xxl-2 {
    margin-bottom: 0.5rem !important;
  }
  .bs .mb-xxl-3 {
    margin-bottom: 1rem !important;
  }
  .bs .mb-xxl-4 {
    margin-bottom: 1.5rem !important;
  }
  .bs .mb-xxl-5 {
    margin-bottom: 3rem !important;
  }
  .bs .mb-xxl-auto {
    margin-bottom: auto !important;
  }
  .bs .ms-xxl-0 {
    margin-left: 0 !important;
  }
  .bs .ms-xxl-1 {
    margin-left: 0.25rem !important;
  }
  .bs .ms-xxl-2 {
    margin-left: 0.5rem !important;
  }
  .bs .ms-xxl-3 {
    margin-left: 1rem !important;
  }
  .bs .ms-xxl-4 {
    margin-left: 1.5rem !important;
  }
  .bs .ms-xxl-5 {
    margin-left: 3rem !important;
  }
  .bs .ms-xxl-auto {
    margin-left: auto !important;
  }
  .bs .p-xxl-0 {
    padding: 0 !important;
  }
  .bs .p-xxl-1 {
    padding: 0.25rem !important;
  }
  .bs .p-xxl-2 {
    padding: 0.5rem !important;
  }
  .bs .p-xxl-3 {
    padding: 1rem !important;
  }
  .bs .p-xxl-4 {
    padding: 1.5rem !important;
  }
  .bs .p-xxl-5 {
    padding: 3rem !important;
  }
  .bs .px-xxl-0 {
    padding-right: 0 !important;
    padding-left: 0 !important;
  }
  .bs .px-xxl-1 {
    padding-right: 0.25rem !important;
    padding-left: 0.25rem !important;
  }
  .bs .px-xxl-2 {
    padding-right: 0.5rem !important;
    padding-left: 0.5rem !important;
  }
  .bs .px-xxl-3 {
    padding-right: 1rem !important;
    padding-left: 1rem !important;
  }
  .bs .px-xxl-4 {
    padding-right: 1.5rem !important;
    padding-left: 1.5rem !important;
  }
  .bs .px-xxl-5 {
    padding-right: 3rem !important;
    padding-left: 3rem !important;
  }
  .bs .py-xxl-0 {
    padding-top: 0 !important;
    padding-bottom: 0 !important;
  }
  .bs .py-xxl-1 {
    padding-top: 0.25rem !important;
    padding-bottom: 0.25rem !important;
  }
  .bs .py-xxl-2 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }
  .bs .py-xxl-3 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
  }
  .bs .py-xxl-4 {
    padding-top: 1.5rem !important;
    padding-bottom: 1.5rem !important;
  }
  .bs .py-xxl-5 {
    padding-top: 3rem !important;
    padding-bottom: 3rem !important;
  }
  .bs .pt-xxl-0 {
    padding-top: 0 !important;
  }
  .bs .pt-xxl-1 {
    padding-top: 0.25rem !important;
  }
  .bs .pt-xxl-2 {
    padding-top: 0.5rem !important;
  }
  .bs .pt-xxl-3 {
    padding-top: 1rem !important;
  }
  .bs .pt-xxl-4 {
    padding-top: 1.5rem !important;
  }
  .bs .pt-xxl-5 {
    padding-top: 3rem !important;
  }
  .bs .pe-xxl-0 {
    padding-right: 0 !important;
  }
  .bs .pe-xxl-1 {
    padding-right: 0.25rem !important;
  }
  .bs .pe-xxl-2 {
    padding-right: 0.5rem !important;
  }
  .bs .pe-xxl-3 {
    padding-right: 1rem !important;
  }
  .bs .pe-xxl-4 {
    padding-right: 1.5rem !important;
  }
  .bs .pe-xxl-5 {
    padding-right: 3rem !important;
  }
  .bs .pb-xxl-0 {
    padding-bottom: 0 !important;
  }
  .bs .pb-xxl-1 {
    padding-bottom: 0.25rem !important;
  }
  .bs .pb-xxl-2 {
    padding-bottom: 0.5rem !important;
  }
  .bs .pb-xxl-3 {
    padding-bottom: 1rem !important;
  }
  .bs .pb-xxl-4 {
    padding-bottom: 1.5rem !important;
  }
  .bs .pb-xxl-5 {
    padding-bottom: 3rem !important;
  }
  .bs .ps-xxl-0 {
    padding-left: 0 !important;
  }
  .bs .ps-xxl-1 {
    padding-left: 0.25rem !important;
  }
  .bs .ps-xxl-2 {
    padding-left: 0.5rem !important;
  }
  .bs .ps-xxl-3 {
    padding-left: 1rem !important;
  }
  .bs .ps-xxl-4 {
    padding-left: 1.5rem !important;
  }
  .bs .ps-xxl-5 {
    padding-left: 3rem !important;
  }
  .bs .gap-xxl-0 {
    gap: 0 !important;
  }
  .bs .gap-xxl-1 {
    gap: 0.25rem !important;
  }
  .bs .gap-xxl-2 {
    gap: 0.5rem !important;
  }
  .bs .gap-xxl-3 {
    gap: 1rem !important;
  }
  .bs .gap-xxl-4 {
    gap: 1.5rem !important;
  }
  .bs .gap-xxl-5 {
    gap: 3rem !important;
  }
  .bs .text-xxl-start {
    text-align: left !important;
  }
  .bs .text-xxl-end {
    text-align: right !important;
  }
  .bs .text-xxl-center {
    text-align: center !important;
  }
}
@media (min-width: 1200px) {
  .bs .fs-1 {
    font-size: 2.5rem !important;
  }
  .bs .fs-2 {
    font-size: 2rem !important;
  }
  .bs .fs-3 {
    font-size: 1.75rem !important;
  }
  .bs .fs-4 {
    font-size: 1.5rem !important;
  }
}
@media print {
  .bs .d-print-inline {
    display: inline !important;
  }
  .bs .d-print-inline-block {
    display: inline-block !important;
  }
  .bs .d-print-block {
    display: block !important;
  }
  .bs .d-print-grid {
    display: grid !important;
  }
  .bs .d-print-table {
    display: table !important;
  }
  .bs .d-print-table-row {
    display: table-row !important;
  }
  .bs .d-print-table-cell {
    display: table-cell !important;
  }
  .bs .d-print-flex {
    display: flex !important;
  }
  .bs .d-print-inline-flex {
    display: inline-flex !important;
  }
  .bs .d-print-none {
    display: none !important;
  }
}
