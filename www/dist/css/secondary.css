#validation-form-container {
  position: relative;
}
#validation-form-container #disabled-overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
}
#validation-options {
  margin-bottom: 2em;
}
.header {
  display: flex;
  justify-content: space-between;
  align-content: center;
  align-items: center;
}
#validation-options {
  margin-top: 1em;
}
#validation-options .validation-option {
  display: flex;
  align-items: flex-start;
}
#validation-options .validation-option input {
  margin-right: 1em;
}
#validation-options .validation-option label span {
  opacity: 0.5;
}
#validation-options-triggers .validation-options-trigger {
  margin-top: 1em;
  margin-bottom: 1em;
}
#validation-options-triggers label {
  margin-bottom: 1em;
}
#not-available-message {
  padding: 1em;
}
#status-container {
  margin-top: 3em;
}
.color-orange {
  color: #FF6600;
}
.message-container {
  display: flex;
  justify-content: flex-start;
}
.message-container .message-icon {
  margin-right: 1em;
}
.message-container .message-text .message-header {
  display: flex;
  font-size: 1.5em;
  align-items: flex-start;
  margin: 0;
  margin-bottom: 0.5em;
}
.message-container .message-text .message-label {
  display: flex;
  font-size: 1em;
  align-items: flex-start;
  margin: 0;
  margin-bottom: 0.5em;
}
.header img {
  height: 65px;
}
#retry-btn-container {
  margin-top: 1em;
}
#validation-options {
  margin-bottom: 1em;
}
#validation-status-container {
  padding-bottom: 1em;
  padding-top: 1em;
  margin-bottom: 1em;
}
.header {
  display: flex;
  justify-content: space-between;
  align-content: center;
  align-items: center;
}
#validation-options {
  margin-top: 1em;
}
#validation-options .validation-option {
  display: flex;
  align-items: flex-start;
}
#validation-options .validation-option input {
  margin-right: 1em;
}
#validation-options .validation-option label span {
  opacity: 0.5;
}
#validation-options-triggers {
  /*margin-top: 1em;*/
}
#validation-options-triggers .validation-options-trigger {
  margin-top: 1em;
  margin-bottom: 1em;
}
#validation-options-triggers label {
  margin-bottom: 1em;
}
#not-available-message {
  padding: 1em;
}
#status-container {
  margin-top: 3em;
}
#retry-btn-container {
  margin-top: 1em;
}
#validation-options {
  margin-bottom: 1em;
}
#validation-status-container {
  padding-bottom: 1em;
  padding-top: 1em;
  margin-bottom: 1em;
}
.header {
  display: flex;
  justify-content: space-between;
  align-content: center;
  align-items: center;
}
#validation-options {
  margin-top: 1em;
}
#validation-options .validation-option {
  display: flex;
  align-items: flex-start;
}
#validation-options .validation-option input {
  margin-right: 1em;
}
#validation-options .validation-option label span {
  opacity: 0.5;
}
#validation-options-triggers {
  /*margin-top: 1em;*/
}
#validation-options-triggers .validation-options-trigger {
  margin-top: 1em;
  margin-bottom: 1em;
}
#validation-options-triggers label {
  margin-bottom: 1em;
}
#not-available-message {
  padding: 1em;
}
#status-container {
  margin-top: 3em;
}
#validation-form-container {
  position: relative;
}
#validation-form-container #disabled-overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
}
#validation-options {
  margin-bottom: 2em;
}
.header {
  display: flex;
  justify-content: space-between;
  align-content: center;
  align-items: center;
}
#validation-options {
  margin-top: 1em;
}
#validation-options .validation-option {
  display: flex;
  align-items: flex-start;
}
#validation-options .validation-option input {
  margin-right: 1em;
}
#validation-options .validation-option label span {
  opacity: 0.5;
}
#validation-options-triggers .validation-options-trigger {
  margin-top: 1em;
  margin-bottom: 1em;
}
#validation-options-triggers label {
  margin-bottom: 1em;
}
#not-available-message {
  padding: 1em;
}
#status-container {
  margin-top: 3em;
}
.color-orange {
  color: #FF6600;
}
.w-95-center {
  width: 95%;
  margin: 2em auto;
}
.photo-col {
  max-width: 90%;
  width: auto;
  height: 250px;
  -o-object-fit: contain;
     object-fit: contain;
  margin-bottom: 1em;
}
@media (max-width: 575px) {
.photo-col {
    height: 100%;
    max-width: 100%;
}
}
#retry-btn-container {
  margin-top: 1em;
}
#validation-options {
  margin-bottom: 1em;
}
#validation-status-container {
  padding-bottom: 1em;
  padding-top: 1em;
  margin-bottom: 1em;
}
.header {
  display: flex;
  justify-content: space-between;
  align-content: center;
  align-items: center;
}
#validation-options {
  margin-top: 1em;
}
#validation-options .validation-option {
  display: flex;
  align-items: flex-start;
}
#validation-options .validation-option input {
  margin-right: 1em;
}
#validation-options .validation-option label span {
  opacity: 0.5;
}
#validation-options-triggers {
  /*margin-top: 1em;*/
}
#validation-options-triggers .validation-options-trigger {
  margin-top: 1em;
  margin-bottom: 1em;
}
#validation-options-triggers label {
  margin-bottom: 1em;
}
#not-available-message {
  padding: 1em;
}
#status-container {
  margin-top: 3em;
}
#retry-btn-container {
  margin-top: 1em;
}
#validation-options {
  margin-bottom: 1em;
}
#validation-status-container {
  padding-bottom: 1em;
  padding-top: 1em;
  margin-bottom: 1em;
}
.header {
  display: flex;
  justify-content: space-between;
  align-content: center;
  align-items: center;
}
#validation-options {
  margin-top: 1em;
}
#validation-options .validation-option {
  display: flex;
  align-items: flex-start;
}
#validation-options .validation-option input {
  margin-right: 1em;
}
#validation-options .validation-option label span {
  opacity: 0.5;
}
#validation-options-triggers {
  /*margin-top: 1em;*/
}
#validation-options-triggers .validation-options-trigger {
  margin-top: 1em;
  margin-bottom: 1em;
}
#validation-options-triggers label {
  margin-bottom: 1em;
}
#not-available-message {
  padding: 1em;
}
#status-container {
  margin-top: 3em;
}
