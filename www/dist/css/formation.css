a {
  color: var(--bs-primary);
}

h2 {
  color: black;
  font-size: 36px;
  font-weight: bold;
}

.custom-tooltip {
  --bs-tooltip-bg: var(--bs-light);
  --bs-tooltip-color: var(--bs-dark);
  --bs-tooltip-opacity: 1;
}

.tooltip .tooltip-inner {
  text-align: left;
}

.fs-small {
  font-size: 10px;
}

.border-12 {
  border-top-width: 12px !important;
}

.triangle-orange {
  position: absolute;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid var(--bs-orange);
  left: 50%;
  transform: translateX(-50%);
}

#lg-navlist thead, #lg-navlist tbody, #lg-navlist tfoot, #lg-navlist tr, #lg-navlist td, #lg-navlist th {
  width: 100%;
}

.formation-arrow {
  font-size: larger !important;
}

.nip-card-title {
  color: #2B2B2B;
  font-family: "Open Sans", sans-serif;
  letter-spacing: 0;
  font-size: 32px;
}

.nip-pending-card-title {
  color: #B2B2B2;
  font-family: "Open Sans", sans-serif;
  letter-spacing: 0;
  font-size: 20px;
}

.nip-offer-box-underline {
  background-color: #009FDA;
  padding: 0 !important;
  height: 9px !important;
}

.nip-active-card-border {
  border: 3px solid #009FDA !important;
  border-radius: 16px !important;
}

.nip-inactive-card-border {
  border: 1px solid #B2B2B2;
  border-radius: 16px;
}

.choose-button {
  min-width: 9em !important;
}

.choose-button-partners {
  width: 11em !important;
}

.nip-find-address-button {
  color: var(--bs-orange);
  font-size: 16px !important;
  font-family: "Open Sans", sans-serif;
  font-weight: bold;
  border-radius: 30px;
  border-width: 2px;
  padding: 0.4em 0.5em 0.4em 1em !important;
  --bs-btn-hover-border-color: var(--bs-orange) !important;
  animation: linear 0.5s;
}

.nip-next-step-button {
  color: var(--bs-orange);
  font-size: 18px !important;
  font-family: "Open Sans", sans-serif;
  font-weight: bold;
  border-radius: 30px;
  padding: 0.8em 1em 0.8em 2em !important;
  --bs-btn-hover-border-color: var(--bs-orange) !important;
  animation: linear 0.5s;
}
@media screen and (max-width: 998px) {
  .nip-next-step-button {
    text-wrap: wrap;
  }
}

.nip-next-step-button-larger {
  color: var(--bs-orange);
  font-size: 18px !important;
  font-family: "Open Sans", sans-serif;
  font-weight: bold;
  border-radius: 30px;
  padding: 1em 1.3em 1em 2.6em !important;
  --bs-btn-hover-border-color: var(--bs-orange) !important;
  animation: linear 0.5s;
}

.nip-btn-orange {
  background-color: var(--bs-orange) !important;
  border-color: var(--bs-orange) !important;
  color: white !important;
  font-weight: 600 !important;
  font-size: 16px;
}

.arrow {
  height: 0;
  display: inline-flex;
  position: relative;
  margin-left: 8px;
  transition: transform 0.3s ease;
}

.arrow-shaft {
  width: 25px;
  height: 2px;
  background-color: var(--bs-orange);
  position: relative;
  top: 50%;
  transform: translateY(-50%);
}

.arrow-head {
  width: 0;
  height: 0;
  border-bottom: 7px solid transparent;
  border-right: 7px solid transparent;
  border-top: 7px solid transparent;
  border-left: 7px solid var(--bs-orange);
  top: 50%;
  right: -2px;
  translate: 0 -50%;
}

.arrow-shaft-color-override-white {
  background-color: white !important;
}

.arrow-head-color-override-white {
  border-left-color: white !important;
}

.nip-find-address-button:hover .arrow {
  transform: translateX(10px);
}

.nip-next-step-button:hover .arrow {
  transform: translateX(10px);
}

.nip-next-step-button-larger:hover .arrow {
  transform: translateX(10px);
}

.nip-2b-color {
  color: #2B2B2B;
}

.nip-text-color {
  color: #2B2B2B;
  font-family: "Open Sans", sans-serif;
  font-weight: 400;
  font-size: 16px;
}

.nip-small-text-color {
  color: #2B2B2B;
  font-family: "Open Sans", sans-serif;
  font-weight: 400;
  font-size: 14px;
}

/* ICONS */
.nip-question-mark-helper {
  color: #009FDA;
  font-size: 24px;
}

.nip-search-icon {
  color: #EEAF30 !important;
  font-size: 22px;
}

.nip-info-tooltip-color {
  color: #B0BCC1;
}

.nip-link {
  color: #2680EB;
  text-decoration: underline;
  font-weight: 600;
}

.nip-link-light {
  color: #2680EB;
  text-decoration: underline;
  font-weight: 600;
}

.step-item {
  flex: 1;
}

.nav-link.nip-step-link-active {
  color: #009FDA !important;
  white-space: nowrap;
}

.nav-link.nip-step-link-inactive {
  color: #B0BCC1 !important;
  white-space: nowrap;
}

@media (max-width: 768px) {
  .nip-step-list {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}
.border.nip-step-border-active {
  border-color: #009FDA !important;
  border-radius: 4px;
  opacity: 1;
}

.border.nip-step-border-inactive {
  border-color: #C7CFD3 !important;
  border-radius: 4px;
  opacity: 1;
}

.nip-light-checkmark-color {
  color: #4E9DD5;
}

.nip-help-grey-text {
  color: #808080;
}

.nip-x-mark-close-icon-color {
  color: #EBEBEB;
}

.nip-purple-text {
  color: #0F4DBC;
}

.nip-font-inter {
  font-family: "Inter", sans-serif;
}

.flex-align-end {
  display: flex;
  justify-content: flex-end;
}

.custom-modal-width {
  max-width: 900px;
  max-height: 450px;
  width: 100%;
  height: 100%;
}

.nip-guide-card-border {
  border: 0;
  border-bottom-right-radius: 16px;
  border-top-right-radius: 16px;
  border-left: 3px solid #009FDA;
}

.nip-guide-card-bottom-border {
  border: 0;
  border-top-right-radius: 16px;
  border-top-left-radius: 16px;
  border-bottom: 3px solid #009FDA;
}

.nip-radio-icon {
  color: #009FDA;
  min-width: 25px;
  min-height: 25px;
  margin: 0;
}

.nip-completed-card-text {
  color: #0BA854;
}

.nip-checkbox {
  border: 2px solid #707070;
  border-radius: 4px;
  color: #707070;
  width: 24px;
  height: 24px;
}

.nip-checkbox-size {
  width: 24px;
  height: 24px;
}

.nip-radio-large {
  width: 32px;
  height: 32px;
}

.nip-radio-label-large {
  font-size: 18px;
}

.nip-large-text {
  font-size: 18px;
}

.nip-small-text {
  font-size: 14px;
}

.nip-phone-width {
  width: 80%;
}

.nip-phone-area-code-width {
  width: 20%;
}

.nip-division-line {
  border-top: 1px solid #CECECE;
  height: 0 !important;
}

.nip-close-modal-icon {
  width: 20px;
  height: 20px;
}

.nip-light-sky-blue {
  color: #e2f7ff;
}

.nip-light-sky-blue-bg {
  background-color: #e2f7ff;
}

.nip-disabled-input-bg {
  background-color: #d4e6fb !important;
}

.nip-alert-background-color {
  background-color: #E3F6FF;
}

.nip-red-alert-background-color {
  background-color: #F8D7DA;
}

.nip-check-icon {
  color: #2680EB;
  margin-right: 10px;
  width: 14px;
  height: 15px;
}

.nip-inner-card-board {
  border: 2px solid #D9D9D9;
  border-radius: 16px;
}

.nip-partner-logo {
  max-height: 40px;
  max-width: 100px;
}

.nip-id-check-not-verified-color {
  color: #BF0000;
}

.id-check-verified-color {
  color: #0BA854;
}

.id-check-pending-color {
  color: #EEAF30;
}

.guide-section-border {
  border-radius: 25px;
}

.steps-completed-color {
  color: #009FDA !important;
}

.open-online-tag {
  background-color: #EEAF30;
  color: white;
  border-radius: 20px;
}

.nip-business-offers-button-wrapper {
  position: absolute;
  bottom: 0;
  right: 0;
  margin-bottom: 20px;
}
@media screen and (min-width: 998px) {
  .nip-business-offers-button-wrapper {
    position: relative;
    margin-bottom: 0;
  }
}

.nip-business-offers-logo {
  max-width: 200px;
  max-height: 120px;
}

.nip-guide-component {
  position: relative;
}

.tooltip-wrapper {
  display: inline-block;
  padding: 10px;
}

.nip-bg-blue {
  background-color: #e3f6ff;
}

.nip-bg-grey {
  background-color: #e9eef4;
}

.nip-bg-grey-light {
  background-color: #f9fbfd;
}

.nip-bg-light-beige {
  background-color: #efebd9;
}

.cms-success {
  color: #dff0d8;
}

.cms-bg-success {
  background-color: #dff0d8 !important;
}

.cms-bg-light {
  background-color: #edf3f5;
}

.cms-btn-outline {
  background-color: transparent;
  border-radius: 30px;
  padding: 17.5px 40px;
  color: #fd7901;
  text-decoration: none;
  border: 2px solid #fd7901;
  font-weight: 600;
}
.cms-btn-outline:hover {
  background-color: #fff;
  color: #ffa452;
}

.alert-error {
  background-color: #E3D4D4;
  color: #BF0000;
  padding: 1rem;
}

.btn-orange {
  background-color: var(--bs-orange) !important;
  border-color: var(--bs-orange) !important;
  color: white !important;
  font-weight: 600 !important;
  font-size: 16px !important;
}

.btn-outline-orange {
  background-color: transparent;
  border: 2px solid #ff7900;
  color: #ff7900 !important;
  font-size: 14px;
  font-weight: 600;
  border-radius: 31px;
  padding: 9.5px 10px 9.5px 20px;
  text-align: center;
}

.btn-view-formation {
  display: flex;
  gap: 6px;
  justify-content: center;
  align-items: center;
  color: #fff;
  text-decoration: none !important;
  font-weight: 600 !important;
  width: 256px;
  height: 56px;
}

.border-orange {
  border-color: var(--bs-orange) !important;
}

.text-orange {
  color: var(--bs-orange) !important;
}

table {
  margin-top: 1em !important;
  margin-bottom: 2em !important;
}

.cms-blue-bottom-border {
  border-bottom: 5px solid #009fda !important;
}

.bg-cms-blue {
  background-color: #009fda !important;
}

.cms-blue-text {
  color: #009fda !important;
}

.modal-z-index {
  z-index: 10000 !important;
}

.banking-offer-img {
  width: 150px;
  height: 50px;
  margin-bottom: 10px;
}

@media (max-width: 992px) {
  .btn-view-formation {
    display: flex;
    gap: 6px;
    justify-content: center;
    align-items: center;
    color: #fff;
    text-decoration: none !important;
    width: 160px;
    height: 56px;
  }
}
.disabled-content {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: white;
  top: 0;
  filter: alpha(opacity=50); /* IE */
  opacity: 0.5; /* Safari, Opera */
  -moz-opacity: 0.5; /* FireFox */
  z-index: 1000;
}

.borderless {
  border: none !important;
}

.has-search .form-control {
  padding-left: 2.375rem;
}

.has-search .form-control-feedback {
  position: absolute;
  z-index: 2;
  display: block;
  width: 2.375rem;
  height: 2.375rem;
  line-height: 2.375rem;
  text-align: center;
  pointer-events: none;
  color: #aaa;
}

.register-office-pscs-recommended-offer #title {
  border-top-right-radius: 20px;
  border-bottom-left-radius: 20px;
  margin-top: -33px;
  background: #192a48;
}

.register-office-pscs-recommended-offer {
  border-top: 8px solid #009fda !important;
}

#sic-list thead tr {
  visibility: hidden;
  position: absolute;
  left: -9999px;
}

.sic-code:hover {
  color: #000000;
  background-color: #D0DCE0;
}

.cms-link {
  color: #2680EB !important;
}

.font-14 {
  font-size: 14px !important;
}

.font-18 {
  font-size: 18px !important;
}

.font-10 {
  font-size: 10px !important;
}

.font-36 {
  font-size: 36px !important;
}

.ptop-11 {
  padding-top: 11px !important;
}

.mtop-40 {
  margin-top: 40px !important;
}

.mbot-40 {
  margin-bottom: 40px !important;
}
