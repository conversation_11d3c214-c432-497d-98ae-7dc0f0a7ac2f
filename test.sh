#!/bin/bash

php_no_xdebug () {
    temporaryPath="$(mktemp -t php.XXXX).ini"

    # Using awk to ensure that files ending without newlines do not lead to configuration error
    php -i | grep "\.ini" | grep -o -e '\(/[a-z0-9._-]\+\)\+\.ini' | grep -v xdebug | xargs awk 'FNR==1{print ""}1' | grep -v xdebug > "$temporaryPath"

    php -n -c "$temporaryPath" "$@"
    rm -f "$temporaryPath"
}

print_incorrect() {
  echo "Incorrect syntax, use ./test.sh --help"
  exit 1
}

print_usage() {
   echo "Utility to run tests"
   echo
   echo -e "\e[1mSyntax:\e[0m ./test.sh [-A] [-B] [-C] [-c] [-d] [-E] [-J] [-h host] [-M] [-r] [-S] [-s] [-t] [-U] testname"
   echo
   echo -e "\e[1mOptions:\e[0m"
   echo "  -A     All tests (PHPSpec, PHPUnit, JS, Behat) - alias for ./test.sh -BJSU"
   echo "  -B     Behat"
   echo "  -C     Code Quality Report"
   echo "  -c     CI mode for Behat"
   echo "  -d     Docker mode for Behat"
   echo "  -E     Runs Workflow Engine"
   echo "  -J     Runs JS tests"
   echo "  -h     Host mode for Behat"
   echo "  -M     Smoke Tests"
   echo "  -r     Re-run for Behat failed tests"
   echo "  -S     PHPSpec"
   echo "  -s     Setup (node sync + migrations)"
   echo "  -t     Stop on failure for CI"
   echo "  -U     PHPUnit"
   echo
   echo -e "\e[1mRun specific PHPSpec test:\e[0m ./test.sh -S specs/TestingSpec.php"
   echo -e "\e[1mRun specific PHPUnit test:\e[0m ./test.sh -U tests/TestingTest.php"
   echo -e "\e[1mRun specific Behat on Docker:\e[0m ./test.sh -Bd features/basket.feature"
   echo -e "\e[1mRun all tests on Docker:\e[0m ./test.sh -Ad"
   echo -e "\e[1mRun code quality test:\e[0m ./test.sh -C"
}

while getopts 'ABCcdEJh:MrPSstU-' flag; do
  case "${flag}" in
    -)
    ;;
    A) PHP_SPEC='true'
       PHP_UNIT='true'
       JS='true'
       BEHAT='true' ;;
    B) BEHAT='true' ;;
    C) CODE_QUALITY='true' ;;
    c) CI='true' ;;
    d) DOCKER='true' ;;
    E) WORKFLOW_ENGINE='true' ;;
    J) JS='true' ;;
    h) HOST="${OPTARG}" ;;
    M) SMOKE='true' ;;
    P) USE_PARALLEL='true' ;;
    s) SETUP='true' ;;
    r) RERUN='true' ;;
    S) PHP_SPEC='true' ;;
    t) STOP_ON_FAILURE='--stop-on-failure' ;;
    U) PHP_UNIT='true' ;;
    *) print_incorrect ;;
  esac
done

if [[ $1 == "--help" ]]; then
    print_usage
    exit;
fi

if [ $OPTIND -eq 1 ]; then
    print_incorrect
fi

shift $(($OPTIND - 1))

if [[ $SETUP ]]; then
    echo -e "\e[92m### SETUP ###\e[0m"
    echo -e "\e[93mSyncing nodes\e[0m"
    php console/cm sync:nodes -s -e console
    echo -e "\e[93mMigrating DB\e[0m"
    php console/db migrate -e console
fi

if [[ $STOP_ON_FAILURE ]]; then
    echo -e "\e[93mCI MODE - STOP ON FAILURE\e[0m"
    set -e
fi

if [[ $PHP_SPEC ]]; then
    echo -e "\e[92m### PHP SPEC ###\e[0m"
    php -d memory_limit=512M vendor/bin/phpspec run -n $1
fi

if [[ $PHP_UNIT ]]; then
    echo -e "\e[92m### PHP UNIT ###\e[0m"
    php -d memory_limit=512M vendor/bin/phpunit $1
fi

if [[ $JS ]]; then
    echo -e "\e[92m### JS TESTS ###\e[0m"
    npm run test
    npm run lint
fi

if [[ $HOST ]]; then
    echo -e "\e[93mHOST SET TO $HOST\e[0m"
    export BEHAT_PARAMS="{\"extensions\" : {\"Behat\\\\MinkExtension\" : {\"base_url\" : \"https://$HOST\"}}}"
fi

if [[ $DOCKER ]]; then
    echo -e "\e[93mDOCKER MODE\e[0m"
    export BEHAT_PARAMS='{"extensions" : {"Behat\\MinkExtension" : {"base_url" : "https://test.cms", "selenium2" : {"wd_host" : "selenium:4444/wd/hub", "capabilities": {"extra_capabilities": { "idle-timeout": 10, "acceptInsecureCerts":true}}}}}}'
fi

if [[ $WORKFLOW_ENGINE ]]; then
    echo -e "\e[92m### WORKFLOW ENGINE ###\e[0m"
    vendor/bin/behat --config vendor/made_simple/msg-framework/workflow_engine_module/src/WorkflowEngineModule/Config/workflow_engine_dry_run.yml  --tags "~@test&&~@disabled"
fi

if [[ $BEHAT ]]; then
    echo -e "\e[92m### BEHAT TESTS ###\e[0m"
    FULL_BEHAT="vendor/bin/behat -n --tags=~@standalone --tags=~@smoke_tests"
    BEHAT_OPTIONS=""

    if [[ $CI ]]; then
        echo -e "\e[93mCI Mode activated\e[0m"
        BEHAT_OPTIONS="--tags ~@ci-incomplete --tags=~@payment"
    fi

    if [[ $RERUN ]]; then
        echo -e "\e[93mRE-RUN Enabled\e[0m"
        $FULL_BEHAT $BEHAT_OPTIONS $1 || $FULL_BEHAT $BEHAT_OPTIONS --rerun $1
    else
        $FULL_BEHAT $BEHAT_OPTIONS $1
    fi
fi

if [[ $SMOKE ]]; then
    echo -e "\e[92m### SMOKE TESTS ###\e[0m"

    echo -e "\e[93mGenerating feature files from Router\e[0m"
    php console/cm smoke_tests:generate --fromRoutes=1

    echo -e "\e[93mGenerating feature files from PageRepository\e[0m"
    php console/cm smoke_tests:generate --fromRoutes=2

    echo -e "\e[93mGenerating feature files from Dynamic pages\e[0m"
    php console/cm smoke_tests:generate --fromRoutes=3

    echo -e "\e[93mGenerating feature files from API routes\e[0m"
    php console/cm smoke_tests:generate --fromRoutes=4

    echo -e "\e[93mSetting up database\e[0m"
    php vendor/bin/phpunit tests/project/SmokeTestsModule/SetUpDatabaseCommand.php

    echo -e "\e[93mRunning SMOKE TESTS:\e[0m"

    if [[ $USE_PARALLEL ]]; then
        set -eu
        find temp/smoke_tests -name "*.feature" |
        parallel --halt soon,fail=1 -u -j 4 --keep-order --progress vendor/bin/behat
    else
        vendor/bin/behat -nv --tags=@smoke_tests
    fi
fi

if [[ $CODE_QUALITY ]]; then
  echo -e "\e[92m### CODE QUALITY ###\e[0m"
  php -d memory_limit=-1 -d max_execution_time=0 vendor/bin/phpstan analyse --configuration=./phpstan.neon --error-format=gitlab > temp/phpstan-gitlab.json
  set -e
  php -d memory_limit=512M -d max_execution_time=0 console/cm debug:code_quality
fi