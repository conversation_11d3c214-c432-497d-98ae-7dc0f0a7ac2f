{"compilerOptions": {"outDir": "./www/dist", "allowJs": true, "target": "es5", "module": "es2015", "strict": false, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "experimentalDecorators": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": false, "newLine": "lf", "baseUrl": ".", "downlevelIteration": true, "types": ["mocha"], "paths": {"@/*": ["client/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["client/*.ts", "client/**/*.ts", "client/**/*.tsx", "client/**/*.vue"], "exclude": ["node_modules"]}