<?php

namespace CMS\Proxy\__CG__\Entities;


/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class OrderItem extends \Entities\OrderItem implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }

    /**
     * {@inheritDoc}
     * @param string $name
     */
    public function & __get($name)
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__get', [$name]);
        return parent::__get($name);
    }

    /**
     * {@inheritDoc}
     * @param string $name
     * @param mixed  $value
     */
    public function __set($name, $value)
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__set', [$name, $value]);
        return parent::__set($name, $value);
    }

    /**
     * {@inheritDoc}
     * @param  string $name
     * @return boolean
     */
    public function __isset($name)
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__isset', [$name]);

        return parent::__isset($name);
    }

    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'orderItemId', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'isRefund', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'isRefunded', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'refundedOrderItem', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'isFee', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'productId', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'productTitle', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'companyName', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'companyNumber', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'qty', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'price', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'notApplyVat', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'nonVatableValue', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'subTotal', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'vat', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'additional', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'totalPrice', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'incorporationRequired', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'markup', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'dtExported', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'company', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'order', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'dtc', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'dtm', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'additionalInformation'];
        }

        return ['__isInitialized__', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'orderItemId', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'isRefund', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'isRefunded', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'refundedOrderItem', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'isFee', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'productId', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'productTitle', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'companyName', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'companyNumber', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'qty', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'price', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'notApplyVat', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'nonVatableValue', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'subTotal', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'vat', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'additional', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'totalPrice', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'incorporationRequired', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'markup', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'dtExported', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'company', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'order', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'dtc', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'dtm', '' . "\0" . 'Entities\\OrderItem' . "\0" . 'additionalInformation'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (OrderItem $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * {@inheritDoc}
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);

        parent::__clone();
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load(): void
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized(): bool
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized): void
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null): void
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer(): ?\Closure
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null): void
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner(): ?\Closure
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties(): array
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function getOrderItemId()
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getOrderItemId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOrderItemId', []);

        return parent::getOrderItemId();
    }

    /**
     * {@inheritDoc}
     */
    public function getOrder()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOrder', []);

        return parent::getOrder();
    }

    /**
     * {@inheritDoc}
     */
    public function getIsRefund()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getIsRefund', []);

        return parent::getIsRefund();
    }

    /**
     * {@inheritDoc}
     */
    public function isRefunded()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isRefunded', []);

        return parent::isRefunded();
    }

    /**
     * {@inheritDoc}
     */
    public function getRefundedOrderItem()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRefundedOrderItem', []);

        return parent::getRefundedOrderItem();
    }

    /**
     * {@inheritDoc}
     */
    public function getIsFee()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getIsFee', []);

        return parent::getIsFee();
    }

    /**
     * {@inheritDoc}
     */
    public function getProductId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProductId', []);

        return parent::getProductId();
    }

    /**
     * {@inheritDoc}
     */
    public function getProductTitle()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProductTitle', []);

        return parent::getProductTitle();
    }

    /**
     * {@inheritDoc}
     */
    public function getCompanyName()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCompanyName', []);

        return parent::getCompanyName();
    }

    /**
     * {@inheritDoc}
     */
    public function getCompanyNumber()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCompanyNumber', []);

        return parent::getCompanyNumber();
    }

    /**
     * {@inheritDoc}
     */
    public function getQty()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getQty', []);

        return parent::getQty();
    }

    /**
     * {@inheritDoc}
     */
    public function getPrice()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPrice', []);

        return parent::getPrice();
    }

    /**
     * {@inheritDoc}
     */
    public function getNotApplyVat()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNotApplyVat', []);

        return parent::getNotApplyVat();
    }

    /**
     * {@inheritDoc}
     */
    public function getNonVatableValue()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNonVatableValue', []);

        return parent::getNonVatableValue();
    }

    /**
     * {@inheritDoc}
     */
    public function getSubTotal()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSubTotal', []);

        return parent::getSubTotal();
    }

    /**
     * {@inheritDoc}
     */
    public function getVat()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getVat', []);

        return parent::getVat();
    }

    /**
     * {@inheritDoc}
     */
    public function getAdditional()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAdditional', []);

        return parent::getAdditional();
    }

    /**
     * {@inheritDoc}
     */
    public function getTotalPrice()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTotalPrice', []);

        return parent::getTotalPrice();
    }

    /**
     * {@inheritDoc}
     */
    public function getIncorporationRequired()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getIncorporationRequired', []);

        return parent::getIncorporationRequired();
    }

    /**
     * {@inheritDoc}
     */
    public function getCompany()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCompany', []);

        return parent::getCompany();
    }

    /**
     * {@inheritDoc}
     */
    public function getMarkup()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getMarkup', []);

        return parent::getMarkup();
    }

    /**
     * {@inheritDoc}
     */
    public function getDtExported()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDtExported', []);

        return parent::getDtExported();
    }

    /**
     * {@inheritDoc}
     */
    public function getDtc()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDtc', []);

        return parent::getDtc();
    }

    /**
     * {@inheritDoc}
     */
    public function getDtm()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDtm', []);

        return parent::getDtm();
    }

    /**
     * {@inheritDoc}
     */
    public function setOrder(\Entities\Order $order)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setOrder', [$order]);

        return parent::setOrder($order);
    }

    /**
     * {@inheritDoc}
     */
    public function setIsRefund($isRefund)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIsRefund', [$isRefund]);

        return parent::setIsRefund($isRefund);
    }

    /**
     * {@inheritDoc}
     */
    public function setIsRefunded($isRefunded)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIsRefunded', [$isRefunded]);

        return parent::setIsRefunded($isRefunded);
    }

    /**
     * {@inheritDoc}
     */
    public function setRefundedOrderItem(\Entities\OrderItem $refundedOrderItem)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setRefundedOrderItem', [$refundedOrderItem]);

        return parent::setRefundedOrderItem($refundedOrderItem);
    }

    /**
     * {@inheritDoc}
     */
    public function setIsFee($isFee)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIsFee', [$isFee]);

        return parent::setIsFee($isFee);
    }

    /**
     * {@inheritDoc}
     */
    public function setProductId($productId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setProductId', [$productId]);

        return parent::setProductId($productId);
    }

    /**
     * {@inheritDoc}
     */
    public function setProductTitle($productTitle)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setProductTitle', [$productTitle]);

        return parent::setProductTitle($productTitle);
    }

    /**
     * {@inheritDoc}
     */
    public function setQty($qty)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setQty', [$qty]);

        return parent::setQty($qty);
    }

    /**
     * {@inheritDoc}
     */
    public function setPrice($price)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPrice', [$price]);

        return parent::setPrice($price);
    }

    /**
     * {@inheritDoc}
     */
    public function setNotApplyVat($notApplyVat)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNotApplyVat', [$notApplyVat]);

        return parent::setNotApplyVat($notApplyVat);
    }

    /**
     * {@inheritDoc}
     */
    public function setNonVatableValue($nonVatableValue)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNonVatableValue', [$nonVatableValue]);

        return parent::setNonVatableValue($nonVatableValue);
    }

    /**
     * {@inheritDoc}
     */
    public function setSubTotal($subTotal)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setSubTotal', [$subTotal]);

        return parent::setSubTotal($subTotal);
    }

    /**
     * {@inheritDoc}
     */
    public function setVat($vat)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setVat', [$vat]);

        return parent::setVat($vat);
    }

    /**
     * {@inheritDoc}
     */
    public function setAdditional($additional)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAdditional', [$additional]);

        return parent::setAdditional($additional);
    }

    /**
     * {@inheritDoc}
     */
    public function setTotalPrice($totalPrice)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTotalPrice', [$totalPrice]);

        return parent::setTotalPrice($totalPrice);
    }

    /**
     * {@inheritDoc}
     */
    public function setIncorporationRequired($incorporationRequired)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIncorporationRequired', [$incorporationRequired]);

        return parent::setIncorporationRequired($incorporationRequired);
    }

    /**
     * {@inheritDoc}
     */
    public function setCompany(\Entities\Company $company)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCompany', [$company]);

        return parent::setCompany($company);
    }

    /**
     * {@inheritDoc}
     */
    public function setMarkup($markup)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setMarkup', [$markup]);

        return parent::setMarkup($markup);
    }

    /**
     * {@inheritDoc}
     */
    public function setDtExported(\DateTime $dtExported = NULL)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDtExported', [$dtExported]);

        return parent::setDtExported($dtExported);
    }

    /**
     * {@inheritDoc}
     */
    public function setDtc(\DateTime $dtc)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDtc', [$dtc]);

        return parent::setDtc($dtc);
    }

    /**
     * {@inheritDoc}
     */
    public function setDtm(\DateTime $dtm)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDtm', [$dtm]);

        return parent::setDtm($dtm);
    }

    /**
     * {@inheritDoc}
     */
    public function setProduct(\Models\Products\Product $product)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setProduct', [$product]);

        return parent::setProduct($product);
    }

    /**
     * {@inheritDoc}
     */
    public function getProductTitleAndTotalPrice()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProductTitleAndTotalPrice', []);

        return parent::getProductTitleAndTotalPrice();
    }

    /**
     * {@inheritDoc}
     */
    public function getAdditionalInformation(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAdditionalInformation', []);

        return parent::getAdditionalInformation();
    }

    /**
     * {@inheritDoc}
     */
    public function setAdditionalInformation(array $additionalInformation): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAdditionalInformation', [$additionalInformation]);

        parent::setAdditionalInformation($additionalInformation);
    }

    /**
     * {@inheritDoc}
     */
    public function __call($name, $args)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, '__call', [$name, $args]);

        return parent::__call($name, $args);
    }

    /**
     * {@inheritDoc}
     */
    public function __unset($name)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, '__unset', [$name]);

        return parent::__unset($name);
    }

}
