<?php

namespace CMS\Proxy\__CG__\UserModule\Entities;


/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class AdminUser extends \UserModule\Entities\AdminUser implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'UserModule\\Entities\\AdminUser' . "\0" . 'id', '' . "\0" . 'UserModule\\Entities\\AdminUser' . "\0" . 'statusId', '' . "\0" . 'UserModule\\Entities\\AdminUser' . "\0" . 'loginName', '' . "\0" . 'UserModule\\Entities\\AdminUser' . "\0" . 'password', '' . "\0" . 'UserModule\\Entities\\AdminUser' . "\0" . 'firstName', '' . "\0" . 'UserModule\\Entities\\AdminUser' . "\0" . 'lastName', '' . "\0" . 'UserModule\\Entities\\AdminUser' . "\0" . 'email', '' . "\0" . 'UserModule\\Entities\\AdminUser' . "\0" . 'phonePaymentAccess', '' . "\0" . 'UserModule\\Entities\\AdminUser' . "\0" . 'idAccess', '' . "\0" . 'UserModule\\Entities\\AdminUser' . "\0" . 'dtc', '' . "\0" . 'UserModule\\Entities\\AdminUser' . "\0" . 'dtm', '' . "\0" . 'UserModule\\Entities\\AdminUser' . "\0" . 'role', '' . "\0" . 'UserModule\\Entities\\AdminUser' . "\0" . 'author', '' . "\0" . 'UserModule\\Entities\\AdminUser' . "\0" . 'editor'];
        }

        return ['__isInitialized__', '' . "\0" . 'UserModule\\Entities\\AdminUser' . "\0" . 'id', '' . "\0" . 'UserModule\\Entities\\AdminUser' . "\0" . 'statusId', '' . "\0" . 'UserModule\\Entities\\AdminUser' . "\0" . 'loginName', '' . "\0" . 'UserModule\\Entities\\AdminUser' . "\0" . 'password', '' . "\0" . 'UserModule\\Entities\\AdminUser' . "\0" . 'firstName', '' . "\0" . 'UserModule\\Entities\\AdminUser' . "\0" . 'lastName', '' . "\0" . 'UserModule\\Entities\\AdminUser' . "\0" . 'email', '' . "\0" . 'UserModule\\Entities\\AdminUser' . "\0" . 'phonePaymentAccess', '' . "\0" . 'UserModule\\Entities\\AdminUser' . "\0" . 'idAccess', '' . "\0" . 'UserModule\\Entities\\AdminUser' . "\0" . 'dtc', '' . "\0" . 'UserModule\\Entities\\AdminUser' . "\0" . 'dtm', '' . "\0" . 'UserModule\\Entities\\AdminUser' . "\0" . 'role', '' . "\0" . 'UserModule\\Entities\\AdminUser' . "\0" . 'author', '' . "\0" . 'UserModule\\Entities\\AdminUser' . "\0" . 'editor'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (AdminUser $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load(): void
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized(): bool
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized): void
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null): void
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer(): ?\Closure
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null): void
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner(): ?\Closure
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties(): array
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getId(): int
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function getStatusId(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getStatusId', []);

        return parent::getStatusId();
    }

    /**
     * {@inheritDoc}
     */
    public function getLoginName(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLoginName', []);

        return parent::getLoginName();
    }

    /**
     * {@inheritDoc}
     */
    public function getPassword(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPassword', []);

        return parent::getPassword();
    }

    /**
     * {@inheritDoc}
     */
    public function getFirstName(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getFirstName', []);

        return parent::getFirstName();
    }

    /**
     * {@inheritDoc}
     */
    public function getLastName(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLastName', []);

        return parent::getLastName();
    }

    /**
     * {@inheritDoc}
     */
    public function getEmail(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getEmail', []);

        return parent::getEmail();
    }

    /**
     * {@inheritDoc}
     */
    public function getDtc(): \DateTime
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDtc', []);

        return parent::getDtc();
    }

    /**
     * {@inheritDoc}
     */
    public function getDtm(): \DateTime
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDtm', []);

        return parent::getDtm();
    }

    /**
     * {@inheritDoc}
     */
    public function getRole(): \UserModule\Entities\AdminRole
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRole', []);

        return parent::getRole();
    }

    /**
     * {@inheritDoc}
     */
    public function getAuthor(): \UserModule\Entities\AdminUser
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAuthor', []);

        return parent::getAuthor();
    }

    /**
     * {@inheritDoc}
     */
    public function getEditor(): \UserModule\Entities\AdminUser
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getEditor', []);

        return parent::getEditor();
    }

    /**
     * {@inheritDoc}
     */
    public function getRoleKey(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRoleKey', []);

        return parent::getRoleKey();
    }

    /**
     * {@inheritDoc}
     */
    public function isAdmin(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isAdmin', []);

        return parent::isAdmin();
    }

    /**
     * {@inheritDoc}
     */
    public function isStaff(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isStaff', []);

        return parent::isStaff();
    }

    /**
     * {@inheritDoc}
     */
    public function isAllowedTo(string $name): ?bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isAllowedTo', [$name]);

        return parent::isAllowedTo($name);
    }

}
