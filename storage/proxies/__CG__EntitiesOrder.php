<?php

namespace CMS\Proxy\__CG__\Entities;


/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class Order extends \Entities\Order implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }

    /**
     * {@inheritDoc}
     * @param string $name
     */
    public function & __get($name)
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__get', [$name]);
        return parent::__get($name);
    }

    /**
     * {@inheritDoc}
     * @param string $name
     * @param mixed  $value
     */
    public function __set($name, $value)
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__set', [$name, $value]);
        return parent::__set($name, $value);
    }

    /**
     * {@inheritDoc}
     * @param  string $name
     * @return boolean
     */
    public function __isset($name)
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__isset', [$name]);

        return parent::__isset($name);
    }

    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'Entities\\Order' . "\0" . 'orderId', '' . "\0" . 'Entities\\Order' . "\0" . 'realSubTotal', '' . "\0" . 'Entities\\Order' . "\0" . 'subTotal', '' . "\0" . 'Entities\\Order' . "\0" . 'discount', '' . "\0" . 'Entities\\Order' . "\0" . 'vat', '' . "\0" . 'Entities\\Order' . "\0" . 'credit', '' . "\0" . 'Entities\\Order' . "\0" . 'total', '' . "\0" . 'Entities\\Order' . "\0" . 'customerName', '' . "\0" . 'Entities\\Order' . "\0" . 'customerAddress', '' . "\0" . 'Entities\\Order' . "\0" . 'customerPhone', '' . "\0" . 'Entities\\Order' . "\0" . 'customerEmail', '' . "\0" . 'Entities\\Order' . "\0" . 'voucherId', '' . "\0" . 'Entities\\Order' . "\0" . 'voucherName', '' . "\0" . 'Entities\\Order' . "\0" . 'description', '' . "\0" . 'Entities\\Order' . "\0" . 'statusId', '' . "\0" . 'Entities\\Order' . "\0" . 'isRefunded', '' . "\0" . 'Entities\\Order' . "\0" . 'refundValue', '' . "\0" . 'Entities\\Order' . "\0" . 'refundCreditValue', '' . "\0" . 'Entities\\Order' . "\0" . 'refundCustomerSupportLogin', '' . "\0" . 'Entities\\Order' . "\0" . 'paymentMediumId', '' . "\0" . 'Entities\\Order' . "\0" . 'agent', '' . "\0" . 'Entities\\Order' . "\0" . 'dtc', '' . "\0" . 'Entities\\Order' . "\0" . 'dtm', '' . "\0" . 'Entities\\Order' . "\0" . 'services', '' . "\0" . 'Entities\\Order' . "\0" . 'items', '' . "\0" . 'Entities\\Order' . "\0" . 'customer', '' . "\0" . 'Entities\\Order' . "\0" . 'transactions'];
        }

        return ['__isInitialized__', '' . "\0" . 'Entities\\Order' . "\0" . 'orderId', '' . "\0" . 'Entities\\Order' . "\0" . 'realSubTotal', '' . "\0" . 'Entities\\Order' . "\0" . 'subTotal', '' . "\0" . 'Entities\\Order' . "\0" . 'discount', '' . "\0" . 'Entities\\Order' . "\0" . 'vat', '' . "\0" . 'Entities\\Order' . "\0" . 'credit', '' . "\0" . 'Entities\\Order' . "\0" . 'total', '' . "\0" . 'Entities\\Order' . "\0" . 'customerName', '' . "\0" . 'Entities\\Order' . "\0" . 'customerAddress', '' . "\0" . 'Entities\\Order' . "\0" . 'customerPhone', '' . "\0" . 'Entities\\Order' . "\0" . 'customerEmail', '' . "\0" . 'Entities\\Order' . "\0" . 'voucherId', '' . "\0" . 'Entities\\Order' . "\0" . 'voucherName', '' . "\0" . 'Entities\\Order' . "\0" . 'description', '' . "\0" . 'Entities\\Order' . "\0" . 'statusId', '' . "\0" . 'Entities\\Order' . "\0" . 'isRefunded', '' . "\0" . 'Entities\\Order' . "\0" . 'refundValue', '' . "\0" . 'Entities\\Order' . "\0" . 'refundCreditValue', '' . "\0" . 'Entities\\Order' . "\0" . 'refundCustomerSupportLogin', '' . "\0" . 'Entities\\Order' . "\0" . 'paymentMediumId', '' . "\0" . 'Entities\\Order' . "\0" . 'agent', '' . "\0" . 'Entities\\Order' . "\0" . 'dtc', '' . "\0" . 'Entities\\Order' . "\0" . 'dtm', '' . "\0" . 'Entities\\Order' . "\0" . 'services', '' . "\0" . 'Entities\\Order' . "\0" . 'items', '' . "\0" . 'Entities\\Order' . "\0" . 'customer', '' . "\0" . 'Entities\\Order' . "\0" . 'transactions'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (Order $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load(): void
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized(): bool
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized): void
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null): void
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer(): ?\Closure
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null): void
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner(): ?\Closure
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties(): array
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function cloneObject()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'cloneObject', []);

        return parent::cloneObject();
    }

    /**
     * {@inheritDoc}
     */
    public function getId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function getItems()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getItems', []);

        return parent::getItems();
    }

    /**
     * {@inheritDoc}
     */
    public function addItem(\Entities\OrderItem $item)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addItem', [$item]);

        return parent::addItem($item);
    }

    /**
     * {@inheritDoc}
     */
    public function setItems(\Doctrine\Common\Collections\ArrayCollection $items): \Entities\Order
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setItems', [$items]);

        return parent::setItems($items);
    }

    /**
     * {@inheritDoc}
     */
    public function getOrderId()
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getOrderId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOrderId', []);

        return parent::getOrderId();
    }

    /**
     * {@inheritDoc}
     */
    public function getRealSubTotal()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRealSubTotal', []);

        return parent::getRealSubTotal();
    }

    /**
     * {@inheritDoc}
     */
    public function setRealSubTotal($realSubTotal): \Entities\Order
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setRealSubTotal', [$realSubTotal]);

        return parent::setRealSubTotal($realSubTotal);
    }

    /**
     * {@inheritDoc}
     */
    public function getSubTotal()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSubTotal', []);

        return parent::getSubTotal();
    }

    /**
     * {@inheritDoc}
     */
    public function setSubTotal($subTotal): \Entities\Order
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setSubTotal', [$subTotal]);

        return parent::setSubTotal($subTotal);
    }

    /**
     * {@inheritDoc}
     */
    public function getDiscount()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDiscount', []);

        return parent::getDiscount();
    }

    /**
     * {@inheritDoc}
     */
    public function setDiscount($discount): \Entities\Order
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDiscount', [$discount]);

        return parent::setDiscount($discount);
    }

    /**
     * {@inheritDoc}
     */
    public function getVat()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getVat', []);

        return parent::getVat();
    }

    /**
     * {@inheritDoc}
     */
    public function setVat($vat): \Entities\Order
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setVat', [$vat]);

        return parent::setVat($vat);
    }

    /**
     * {@inheritDoc}
     */
    public function getCredit()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCredit', []);

        return parent::getCredit();
    }

    /**
     * {@inheritDoc}
     */
    public function setCredit($credit): \Entities\Order
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCredit', [$credit]);

        return parent::setCredit($credit);
    }

    /**
     * {@inheritDoc}
     */
    public function getTotal()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTotal', []);

        return parent::getTotal();
    }

    /**
     * {@inheritDoc}
     */
    public function setTotal($total): \Entities\Order
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTotal', [$total]);

        return parent::setTotal($total);
    }

    /**
     * {@inheritDoc}
     */
    public function getCustomerName()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCustomerName', []);

        return parent::getCustomerName();
    }

    /**
     * {@inheritDoc}
     */
    public function setCustomerName($customerName): \Entities\Order
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCustomerName', [$customerName]);

        return parent::setCustomerName($customerName);
    }

    /**
     * {@inheritDoc}
     */
    public function getCustomerAddress()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCustomerAddress', []);

        return parent::getCustomerAddress();
    }

    /**
     * {@inheritDoc}
     */
    public function setCustomerAddress($customerAddress): \Entities\Order
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCustomerAddress', [$customerAddress]);

        return parent::setCustomerAddress($customerAddress);
    }

    /**
     * {@inheritDoc}
     */
    public function getCustomerPhone()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCustomerPhone', []);

        return parent::getCustomerPhone();
    }

    /**
     * {@inheritDoc}
     */
    public function setCustomerPhone($customerPhone): \Entities\Order
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCustomerPhone', [$customerPhone]);

        return parent::setCustomerPhone($customerPhone);
    }

    /**
     * {@inheritDoc}
     */
    public function getCustomerEmail()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCustomerEmail', []);

        return parent::getCustomerEmail();
    }

    /**
     * {@inheritDoc}
     */
    public function setCustomerEmail($customerEmail): \Entities\Order
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCustomerEmail', [$customerEmail]);

        return parent::setCustomerEmail($customerEmail);
    }

    /**
     * {@inheritDoc}
     */
    public function getVoucherId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getVoucherId', []);

        return parent::getVoucherId();
    }

    /**
     * {@inheritDoc}
     */
    public function setVoucherId($voucherId): \Entities\Order
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setVoucherId', [$voucherId]);

        return parent::setVoucherId($voucherId);
    }

    /**
     * {@inheritDoc}
     */
    public function getVoucherName()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getVoucherName', []);

        return parent::getVoucherName();
    }

    /**
     * {@inheritDoc}
     */
    public function setVoucherName($voucherName): \Entities\Order
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setVoucherName', [$voucherName]);

        return parent::setVoucherName($voucherName);
    }

    /**
     * {@inheritDoc}
     */
    public function getDescription()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDescription', []);

        return parent::getDescription();
    }

    /**
     * {@inheritDoc}
     */
    public function setDescription($description): \Entities\Order
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDescription', [$description]);

        return parent::setDescription($description);
    }

    /**
     * {@inheritDoc}
     */
    public function getStatusId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getStatusId', []);

        return parent::getStatusId();
    }

    /**
     * {@inheritDoc}
     */
    public function setStatusId($statusId): \Entities\Order
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setStatusId', [$statusId]);

        return parent::setStatusId($statusId);
    }

    /**
     * {@inheritDoc}
     */
    public function isRefunded()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isRefunded', []);

        return parent::isRefunded();
    }

    /**
     * {@inheritDoc}
     */
    public function setIsRefunded($isRefunded): \Entities\Order
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIsRefunded', [$isRefunded]);

        return parent::setIsRefunded($isRefunded);
    }

    /**
     * {@inheritDoc}
     */
    public function getRefundValue()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRefundValue', []);

        return parent::getRefundValue();
    }

    /**
     * {@inheritDoc}
     */
    public function setRefundValue($refundValue): \Entities\Order
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setRefundValue', [$refundValue]);

        return parent::setRefundValue($refundValue);
    }

    /**
     * {@inheritDoc}
     */
    public function getRefundCreditValue()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRefundCreditValue', []);

        return parent::getRefundCreditValue();
    }

    /**
     * {@inheritDoc}
     */
    public function setRefundCreditValue($refundCreditValue): \Entities\Order
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setRefundCreditValue', [$refundCreditValue]);

        return parent::setRefundCreditValue($refundCreditValue);
    }

    /**
     * {@inheritDoc}
     */
    public function getRefundCustomerSupportLogin()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRefundCustomerSupportLogin', []);

        return parent::getRefundCustomerSupportLogin();
    }

    /**
     * {@inheritDoc}
     */
    public function setRefundCustomerSupportLogin($refundCustomerSupportLogin): \Entities\Order
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setRefundCustomerSupportLogin', [$refundCustomerSupportLogin]);

        return parent::setRefundCustomerSupportLogin($refundCustomerSupportLogin);
    }

    /**
     * {@inheritDoc}
     */
    public function getPaymentMediumId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPaymentMediumId', []);

        return parent::getPaymentMediumId();
    }

    /**
     * {@inheritDoc}
     */
    public function setPaymentMediumId($paymentMediumId): \Entities\Order
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPaymentMediumId', [$paymentMediumId]);

        return parent::setPaymentMediumId($paymentMediumId);
    }

    /**
     * {@inheritDoc}
     */
    public function getAgent()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAgent', []);

        return parent::getAgent();
    }

    /**
     * {@inheritDoc}
     */
    public function setAgent(\Entities\User $agent): \Entities\Order
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAgent', [$agent]);

        return parent::setAgent($agent);
    }

    /**
     * {@inheritDoc}
     */
    public function getCustomer()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCustomer', []);

        return parent::getCustomer();
    }

    /**
     * {@inheritDoc}
     */
    public function setCustomer(\Entities\Customer $customer): \Entities\Order
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCustomer', [$customer]);

        return parent::setCustomer($customer);
    }

    /**
     * {@inheritDoc}
     */
    public function setCustomerDetails(\Entities\Customer $customer): \Entities\Order
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCustomerDetails', [$customer]);

        return parent::setCustomerDetails($customer);
    }

    /**
     * {@inheritDoc}
     */
    public function getTransaction()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTransaction', []);

        return parent::getTransaction();
    }

    /**
     * {@inheritDoc}
     */
    public function setTransaction(\Entities\Transaction $transaction): \Entities\Order
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTransaction', [$transaction]);

        return parent::setTransaction($transaction);
    }

    /**
     * {@inheritDoc}
     */
    public function getDtc()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDtc', []);

        return parent::getDtc();
    }

    /**
     * {@inheritDoc}
     */
    public function setDtc(\DateTime $dtc): \Entities\Order
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDtc', [$dtc]);

        return parent::setDtc($dtc);
    }

    /**
     * {@inheritDoc}
     */
    public function getDtm()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDtm', []);

        return parent::getDtm();
    }

    /**
     * {@inheritDoc}
     */
    public function setDtm(\DateTime $dtm): \Entities\Order
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDtm', [$dtm]);

        return parent::setDtm($dtm);
    }

    /**
     * {@inheritDoc}
     */
    public function getPaymentMedium()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPaymentMedium', []);

        return parent::getPaymentMedium();
    }

    /**
     * {@inheritDoc}
     */
    public function getFeefoData()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getFeefoData', []);

        return parent::getFeefoData();
    }

    /**
     * {@inheritDoc}
     */
    public function isPaidBySagePay()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isPaidBySagePay', []);

        return parent::isPaidBySagePay();
    }

    /**
     * {@inheritDoc}
     */
    public function isPaidBySagePayToken()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isPaidBySagePayToken', []);

        return parent::isPaidBySagePayToken();
    }

    /**
     * {@inheritDoc}
     */
    public function getRenewableServices()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRenewableServices', []);

        return parent::getRenewableServices();
    }

    /**
     * {@inheritDoc}
     */
    public function containsRenewableServices()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'containsRenewableServices', []);

        return parent::containsRenewableServices();
    }

    /**
     * {@inheritDoc}
     */
    public function hasVoucher()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasVoucher', []);

        return parent::hasVoucher();
    }

    /**
     * {@inheritDoc}
     */
    public function hasCredit()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasCredit', []);

        return parent::hasCredit();
    }

    /**
     * {@inheritDoc}
     */
    public function isEqual(\Entities\Order $order)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isEqual', [$order]);

        return parent::isEqual($order);
    }

    /**
     * {@inheritDoc}
     */
    public function getDistinctProductCount()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDistinctProductCount', []);

        return parent::getDistinctProductCount();
    }

    /**
     * {@inheritDoc}
     */
    public function isPhoneOrder()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isPhoneOrder', []);

        return parent::isPhoneOrder();
    }

    /**
     * {@inheritDoc}
     */
    public function isComplimentaryOrder()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isComplimentaryOrder', []);

        return parent::isComplimentaryOrder();
    }

    /**
     * {@inheritDoc}
     */
    public function setPrice(\BasketModule\ValueObject\Price $price): \Entities\Order
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPrice', [$price]);

        return parent::setPrice($price);
    }

    /**
     * {@inheritDoc}
     */
    public function addService(\Entities\Service $service)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addService', [$service]);

        return parent::addService($service);
    }

    /**
     * {@inheritDoc}
     */
    public function getVatNumber()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getVatNumber', []);

        return parent::getVatNumber();
    }

    /**
     * {@inheritDoc}
     */
    public function getStatusName(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getStatusName', []);

        return parent::getStatusName();
    }

    /**
     * {@inheritDoc}
     */
    public function isAutoRenewal(\Entities\Service $service): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isAutoRenewal', [$service]);

        return parent::isAutoRenewal($service);
    }

    /**
     * {@inheritDoc}
     */
    public function getServices()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getServices', []);

        return parent::getServices();
    }

    /**
     * {@inheritDoc}
     */
    public function getTransactions()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTransactions', []);

        return parent::getTransactions();
    }

    /**
     * {@inheritDoc}
     */
    public function hasSuccessfulTransaction(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasSuccessfulTransaction', []);

        return parent::hasSuccessfulTransaction();
    }

    /**
     * {@inheritDoc}
     */
    public function __call($name, $args)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, '__call', [$name, $args]);

        return parent::__call($name, $args);
    }

    /**
     * {@inheritDoc}
     */
    public function __unset($name)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, '__unset', [$name]);

        return parent::__unset($name);
    }

}
