<?php

namespace CMS\Proxy\__CG__\IdModule\Entities;


/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class IdValidation extends \IdModule\Entities\IdValidation implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'id', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'entityId', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'pass', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'diligenceLevelName', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'checkName', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'checkType', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'nullified', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'originalData', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'verifiedData', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'dtc', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'company', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'customer', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'verification', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'event', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'reference'];
        }

        return ['__isInitialized__', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'id', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'entityId', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'pass', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'diligenceLevelName', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'checkName', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'checkType', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'nullified', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'originalData', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'verifiedData', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'dtc', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'company', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'customer', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'verification', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'event', '' . "\0" . 'IdModule\\Entities\\IdValidation' . "\0" . 'reference'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (IdValidation $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load(): void
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized(): bool
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized): void
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null): void
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer(): ?\Closure
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null): void
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner(): ?\Closure
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties(): array
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getId(): ?int
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function getEntityId(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getEntityId', []);

        return parent::getEntityId();
    }

    /**
     * {@inheritDoc}
     */
    public function isPass(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isPass', []);

        return parent::isPass();
    }

    /**
     * {@inheritDoc}
     */
    public function getDiligenceLevelName(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDiligenceLevelName', []);

        return parent::getDiligenceLevelName();
    }

    /**
     * {@inheritDoc}
     */
    public function getCheckName(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCheckName', []);

        return parent::getCheckName();
    }

    /**
     * {@inheritDoc}
     */
    public function getOriginalData(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOriginalData', []);

        return parent::getOriginalData();
    }

    /**
     * {@inheritDoc}
     */
    public function getVerifiedData(): ?array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getVerifiedData', []);

        return parent::getVerifiedData();
    }

    /**
     * {@inheritDoc}
     */
    public function getDtc(): \DateTime
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDtc', []);

        return parent::getDtc();
    }

    /**
     * {@inheritDoc}
     */
    public function isPassedWithData(\IdModule\Domain\IdData $idData): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isPassedWithData', [$idData]);

        return parent::isPassedWithData($idData);
    }

    /**
     * {@inheritDoc}
     */
    public function isMatchingData(\IdModule\Domain\IdData $idData): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isMatchingData', [$idData]);

        return parent::isMatchingData($idData);
    }

    /**
     * {@inheritDoc}
     */
    public function getNotMatchingDataDiff(\IdModule\Domain\IdData $idData): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNotMatchingDataDiff', [$idData]);

        return parent::getNotMatchingDataDiff($idData);
    }

    /**
     * {@inheritDoc}
     */
    public function getVerification(): ?\IdModule\Entities\Id3GlobalVerification
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getVerification', []);

        return parent::getVerification();
    }

    /**
     * {@inheritDoc}
     */
    public function getCustomer(): ?\UserModule\Contracts\ICustomer
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCustomer', []);

        return parent::getCustomer();
    }

    /**
     * {@inheritDoc}
     */
    public function getCompany(): ?\CompanyModule\ICompany
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCompany', []);

        return parent::getCompany();
    }

    /**
     * {@inheritDoc}
     */
    public function getCheckType(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCheckType', []);

        return parent::getCheckType();
    }

    /**
     * {@inheritDoc}
     */
    public function isNullified(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isNullified', []);

        return parent::isNullified();
    }

    /**
     * {@inheritDoc}
     */
    public function markAsNullified()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'markAsNullified', []);

        return parent::markAsNullified();
    }

    /**
     * {@inheritDoc}
     */
    public function setDiligenceLevelName(string $diligenceLevelName)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDiligenceLevelName', [$diligenceLevelName]);

        return parent::setDiligenceLevelName($diligenceLevelName);
    }

    /**
     * {@inheritDoc}
     */
    public function setCheckType(string $checkType)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCheckType', [$checkType]);

        return parent::setCheckType($checkType);
    }

    /**
     * {@inheritDoc}
     */
    public function getEvent(): ?\IdModule\Entities\Events\Validation
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getEvent', []);

        return parent::getEvent();
    }

    /**
     * {@inheritDoc}
     */
    public function belongToCustomer(\UserModule\Contracts\ICustomer $customer): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'belongToCustomer', [$customer]);

        return parent::belongToCustomer($customer);
    }

    /**
     * {@inheritDoc}
     */
    public function getReference(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getReference', []);

        return parent::getReference();
    }

    /**
     * {@inheritDoc}
     */
    public function setCompany(\CompanyModule\ICompany $company): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCompany', [$company]);

        parent::setCompany($company);
    }

    /**
     * {@inheritDoc}
     */
    public function setCustomer(\UserModule\Contracts\ICustomer $customer): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCustomer', [$customer]);

        parent::setCustomer($customer);
    }

}
