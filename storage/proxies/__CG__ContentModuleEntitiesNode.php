<?php

namespace CMS\Proxy\__CG__\ContentModule\Entities;


/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class Node extends \ContentModule\Entities\Node implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }







    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'id', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'name', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'statusId', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'deleted', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'frontController', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'adminController', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'level', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'order', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'dtShow', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'dtSensitive', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'dtStart', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'dtEnd', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'dtc', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'dtm', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'parent', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'author', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'editor', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'page', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'properties'];
        }

        return ['__isInitialized__', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'id', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'name', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'statusId', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'deleted', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'frontController', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'adminController', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'level', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'order', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'dtShow', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'dtSensitive', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'dtStart', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'dtEnd', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'dtc', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'dtm', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'parent', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'author', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'editor', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'page', '' . "\0" . 'ContentModule\\Entities\\Node' . "\0" . 'properties'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (Node $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load(): void
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized(): bool
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized): void
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null): void
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer(): ?\Closure
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null): void
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner(): ?\Closure
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties(): array
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getId(): int
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function getName(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getName', []);

        return parent::getName();
    }

    /**
     * {@inheritDoc}
     */
    public function getStatusId(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getStatusId', []);

        return parent::getStatusId();
    }

    /**
     * {@inheritDoc}
     */
    public function isDeleted(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isDeleted', []);

        return parent::isDeleted();
    }

    /**
     * {@inheritDoc}
     */
    public function getLevel(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLevel', []);

        return parent::getLevel();
    }

    /**
     * {@inheritDoc}
     */
    public function getOrder(): int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOrder', []);

        return parent::getOrder();
    }

    /**
     * {@inheritDoc}
     */
    public function getDtShow(): ?\DateTime
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDtShow', []);

        return parent::getDtShow();
    }

    /**
     * {@inheritDoc}
     */
    public function isDtSensitive(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isDtSensitive', []);

        return parent::isDtSensitive();
    }

    /**
     * {@inheritDoc}
     */
    public function getDtStart(): ?\DateTime
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDtStart', []);

        return parent::getDtStart();
    }

    /**
     * {@inheritDoc}
     */
    public function getDtEnd(): ?\DateTime
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDtEnd', []);

        return parent::getDtEnd();
    }

    /**
     * {@inheritDoc}
     */
    public function getDtc(): \DateTime
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDtc', []);

        return parent::getDtc();
    }

    /**
     * {@inheritDoc}
     */
    public function getDtm(): \DateTime
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDtm', []);

        return parent::getDtm();
    }

    /**
     * {@inheritDoc}
     */
    public function getParent(): ?\ContentModule\Entities\Node
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getParent', []);

        return parent::getParent();
    }

    /**
     * {@inheritDoc}
     */
    public function getAuthor(): \UserModule\Entities\AdminUser
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAuthor', []);

        return parent::getAuthor();
    }

    /**
     * {@inheritDoc}
     */
    public function getEditor(): \UserModule\Entities\AdminUser
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getEditor', []);

        return parent::getEditor();
    }

    /**
     * {@inheritDoc}
     */
    public function getPage(): \ContentModule\Entities\Page
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPage', []);

        return parent::getPage();
    }

    /**
     * {@inheritDoc}
     */
    public function getProperties(): \Doctrine\Common\Collections\Collection
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProperties', []);

        return parent::getProperties();
    }

    /**
     * {@inheritDoc}
     */
    public function getProperty(string $name): ?\ContentModule\Entities\Property
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProperty', [$name]);

        return parent::getProperty($name);
    }

    /**
     * {@inheritDoc}
     */
    public function getPropertyValue(string $name)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPropertyValue', [$name]);

        return parent::getPropertyValue($name);
    }

    /**
     * {@inheritDoc}
     */
    public function addProperty(\ContentModule\Entities\Property $property): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addProperty', [$property]);

        parent::addProperty($property);
    }

    /**
     * {@inheritDoc}
     */
    public function markPublished()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'markPublished', []);

        return parent::markPublished();
    }

    /**
     * {@inheritDoc}
     */
    public function getFrontController(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getFrontController', []);

        return parent::getFrontController();
    }

    /**
     * {@inheritDoc}
     */
    public function getAdminController(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAdminController', []);

        return parent::getAdminController();
    }

}
