<?php

use Phinx\Migration\AbstractMigration;

class CountriesInPartnerServicesInfoFromIntegerToIsoAlpha2Code extends AbstractMigration
{
    private const TABLE = 'business_services_partner_information';

    public function up()
    {
        foreach ($this->getCountries() as $row) {
            if ($isoAlpha2 = CountryMap::mapIntTo2Letter((int)$row[0])) {
                $this->execute($this->createUpdateQuery($isoAlpha2, $row[0]));
            }
        }
    }

    public function down()
    {
        foreach ($this->getCountries() as $row) {
            if ($msgCountryInt = CountryMap::map2LetterToInt($row[0])) {
                $this->execute($this->createUpdateQuery($msgCountryInt, $row[0]));
            }
        }
    }

    private function createUpdateQuery(string $newValue, string $oldValue): string
    {
        return sprintf('UPDATE %s SET country="%s" WHERE country="%s"', self::TABLE, $newValue, $oldValue);
    }

    private function getCountries()
    {
        return $this->query(
            sprintf('SELECT DISTINCT country FROM %s WHERE country IS NOT NULL AND country<>""', self::TABLE)
        )->fetchAll();
    }
}
