<?php

use Phinx\Migration\AbstractMigration;
use MigrationModule\Helpers\NodeMigrationHelper;

class AddNonVatableValueToConfirmationStatementDormantCompany extends AbstractMigration
{
    const PRODUCT_NAME = '1740';
    const NON_VATABLE_COLUMN='nonVatableValue';

    public function up()
    {
        $migrationHelper = new NodeMigrationHelper($this);
        $nodeId = $migrationHelper->getExistingNodeId(self::PRODUCT_NAME);
        if (empty($nodeId)) throw new Exception(sprintf('Node with name %s does not exist!', self::PRODUCT_NAME));
        $migrationHelper->updateProperty($nodeId, self::NON_VATABLE_COLUMN, '34');
    }

    public function down()
    {
        $migrationHelper = new NodeMigrationHelper($this);
        $nodeId = $migrationHelper->getExistingNodeId(self::PRODUCT_NAME);
        if (empty($nodeId)) throw new Exception(sprintf('Node with name %s does not exist!', self::PRODUCT_NAME));
        $migrationHelper->updateProperty($nodeId, self::NON_VATABLE_COLUMN, '');
    }
}
