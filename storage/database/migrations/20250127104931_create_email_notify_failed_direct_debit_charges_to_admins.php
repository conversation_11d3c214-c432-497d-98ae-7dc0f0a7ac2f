<?php

use OmnipayModule\Emailers\DirectDebitEmailer;
use Phinx\Migration\AbstractMigration;
use MigrationModule\Dto\Node;
use MigrationModule\Dto\Page;
use MigrationModule\Dto\Property;
use MigrationModule\Helpers\NodeMigrationHelper;

class CreateEmailNotifyFailedDirectDebitChargesToAdmins extends AbstractMigration
{
    public function up(): void
    {
        if (!is_null($this->getNodeId())) {
            return;
        }

        $page = new Page('Failed Direct Debit Charges Notification to Admins Email');

        $properties = [
            new Property('from', '<EMAIL>'),
            new Property('fromName', 'Companies Made Simple'),
            new Property('subject', 'Failed Direct Debit Charges'),
            new Property('tag1', 'direct debit'),
            new Property('tag2', 'omnipay'),
            new Property('tag3', 'payment'),
            new Property('tag4', 'payments'),
            new Property('templateName', DirectDebitEmailer::NOTIFY_FAILED_DIRECT_DEBIT_CHARGES_TO_ADMINS)
        ];

        $node = new Node(
            $page,
            $properties,
            DirectDebitEmailer::NOTIFY_FAILED_DIRECT_DEBIT_CHARGES_TO_ADMINS,
            137,
            null,
            'EmailAdminControler',
            290
        );

        $helper = new NodeMigrationHelper($this);
        $helper->create($node);
    }

    public function down(): void
    {
        $nodeId = $this->getNodeId();
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_NODES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_PAGES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`nodeId` = '%s');", TBL_PROPERTIES, $nodeId));
    }

    private function getNodeId(): ?string
    {
        $node = $this->fetchRow(sprintf("SELECT `node_id` FROM `%s` WHERE `name`='%s'", TBL_NODES, DirectDebitEmailer::NOTIFY_FAILED_DIRECT_DEBIT_CHARGES_TO_ADMINS));
        return $node['node_id'] ?? null;
    }
}
