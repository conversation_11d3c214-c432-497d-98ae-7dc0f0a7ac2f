<?php

declare(strict_types=1);

use Phinx\Migration\AbstractMigration;

class AddMailboxTypeEnumValuesToCms2ServicesSettingsServiceTypeColumn extends AbstractMigration
{
    public function up(): void
    {
        $this->execute(<<<SQL
            ALTER TABLE `cms2_service_settings` 
            MODIFY COLUMN `serviceTypeId` ENUM(
                'PACKAGE_PRIVACY',
                'PACKAGE_COMPREHENSIVE_ULTIMATE',
                'PACKAGE_BY_GUARANTEE',
                'PACKAGE_SOLE_TRADER_PLUS',
                'RESERVE_COMPANY_NAME',
                'DORMANT_COMPANY_ACCOUNTS',
                'APOSTILLED_DOCUMENTS',
                'CERTIFICATE_OF_GOOD_STANDING',
                'REGISTERED_OFFICE',
                'SERVICE_ADDRESS',
                'NOMINEE',
                'ANNUAL_RETURN',
                'PSC_ONLINE_REGISTER',
                'BUND<PERSON>_PSC_ONLINE_REGISTER_CONFIRMATION_STATEMENT',
                'CONFIRMATION_STATEMENT',
                'FRAUD_PROTECTION',
                'PACKAGE_COMPREHENSIVE',
                'PACKAGE_ULTIMATE',
                'SECRETARIAL_SERVICE',
                'MAIL_FORWARDING',
                'COMPANY_MONITORING',
                'MAILBOX_STANDARD',
                'MAILBOX_PREMIUM'
            ) NOT NULL;
        SQL);
    }

    public function down(): void
    {
        $this->execute(<<<SQL
            ALTER TABLE `cms2_service_settings` 
            MODIFY COLUMN `serviceTypeId` ENUM(
                'PACKAGE_PRIVACY',
                'PACKAGE_COMPREHENSIVE_ULTIMATE',
                'PACKAGE_BY_GUARANTEE',
                'PACKAGE_SOLE_TRADER_PLUS',
                'RESERVE_COMPANY_NAME',
                'DORMANT_COMPANY_ACCOUNTS',
                'APOSTILLED_DOCUMENTS',
                'CERTIFICATE_OF_GOOD_STANDING',
                'REGISTERED_OFFICE',
                'SERVICE_ADDRESS',
                'NOMINEE',
                'ANNUAL_RETURN',
                'PSC_ONLINE_REGISTER',
                'BUNDLE_PSC_ONLINE_REGISTER_CONFIRMATION_STATEMENT',
                'CONFIRMATION_STATEMENT',
                'FRAUD_PROTECTION',
                'PACKAGE_COMPREHENSIVE',
                'PACKAGE_ULTIMATE',
                'SECRETARIAL_SERVICE',
                'MAIL_FORWARDING',
                'COMPANY_MONITORING'
            ) NOT NULL;
        SQL);
    }
}