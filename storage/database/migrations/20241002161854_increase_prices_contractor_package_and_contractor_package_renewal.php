<?php

use MigrationModule\Helpers\NodeMigrationHelper;
use Models\Products\Package;
use Phinx\Migration\AbstractMigration;

final class IncreasePricesContractorPackageAndContractorPackageRenewal extends AbstractMigration
{
    public function up(): void
    {
        $packages = [
            Package::PACKAGE_CONTRACTOR_RENEWAL => ['89.99', '79.99'],
            Package::PACKAGE_CONTRACTOR => ['69.99','69.99'],
        ];
        foreach ($packages as $productName => $data) {
            $this->updateProductPrices($productName, $data);
        }
    }
    public function down(): void
    {
        $packages = [
            Package::PACKAGE_CONTRACTOR_RENEWAL => ['79.99', '79.99'],
            Package::PACKAGE_CONTRACTOR => ['59.99','59.99'],
        ];
        foreach ($packages as $productName => $oldPrices) {
            $this->updateProductPrices($productName, $oldPrices);
        }
    }

    private function updateProductPrices(string $productName, array $prices): void
    {
        $migrationHelper = new NodeMigrationHelper($this);
        [$price, $wholesalePrice] = $prices;

        $nodeId = $migrationHelper->getExistingNodeId($productName);
        if (empty($nodeId)) {
            throw new Exception(sprintf('Node with name %s does not exist!', $productName));
        }

        $migrationHelper->updateProperty($nodeId, 'price', $price);
        $migrationHelper->updateProperty($nodeId, 'associatedPrice', $price);
        $migrationHelper->updateProperty($nodeId, 'productValue', $price);
        $migrationHelper->updateProperty($nodeId, 'offerPrice', $price);
        $migrationHelper->updateProperty($nodeId, 'wholesalePrice', $wholesalePrice);
    }
}
