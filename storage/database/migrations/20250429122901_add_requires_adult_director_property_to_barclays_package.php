<?php

declare(strict_types=1);

use MigrationModule\Helpers\NodeMigrationHelper;
use Models\Products\Package;
use Phinx\Migration\AbstractMigration;

class AddRequiresAdultDirectorPropertyToBarclaysPackage extends AbstractMigration
{
    private const REQUIRES_ADULT_DIRECTOR_PROPERTY_NAME = 'requiresAdultDirector';

    public function up(): void
    {
        $nodeId = $this->getBarclaysPackageNodeId();

        if (empty($nodeId)) {
            return;
        }

        $this->query(
            sprintf(
                'INSERT INTO %s (nodeId, name, value, authorId, editorId, dtc, dtm) VALUES
                (%d, "%s", "%s", %d, %d, NOW(), NOW())',
                TBL_PROPERTIES,
                $nodeId, self::REQUIRES_ADULT_DIRECTOR_PROPERTY_NAME, '1', 8, 8,
            )
        );
    }

    public function down(): void
    {
        $nodeId = $this->getBarclaysPackageNodeId();

        if (empty($nodeId)) {
            return;
        }

        $this->query(
            sprintf(
                'DELETE FROM %s WHERE nodeId = %d AND name IN ("%s")',
                TBL_PROPERTIES,
                $nodeId,
                self::REQUIRES_ADULT_DIRECTOR_PROPERTY_NAME,
            )
        );
    }

    private function getBarclaysPackageNodeId(): ?int
    {
        $helper = new NodeMigrationHelper($this);

        return $helper->getExistingNodeId(Package::LTD_COMPANY_FORMATION_WITH_BARCLAYS_REFERRAL_PACKAGE);
    }
}
