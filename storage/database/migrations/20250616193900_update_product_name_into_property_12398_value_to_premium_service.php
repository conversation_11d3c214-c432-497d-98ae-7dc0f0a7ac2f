<?php

declare(strict_types=1);

use Models\Products\Product;
use Phinx\Migration\AbstractMigration;

final class UpdateProductNameIntoProperty12398ValueToPremiumService extends AbstractMigration
{
	const TEMPLATE_CONTENT = <<<HTML
	<div><b>%s</b></div>
	<div><b><br />
	</b></div>
	<div>Thank you for purchasing the %s.</div>
	<div>&#160;</div>
	<div>
	<div><strong>Please complete the following Dissolution Form via this link:</strong></div>
	<div>&#160;</div>
	<div>https://goo.gl/forms/tUhdDuoPvkSLiFC02</div>
	<div>&#160;</div>
	<div>
	<div><strong>NB: PLEASE DO NOT RESIGN DIRECTOR/S.</strong>&#160;At least one director must be appointed to sign the DS01 form. If there is more than one director in the company, please provide the email addresses of all directors. The dissolution request must be sent to and approved by all individuals.</div>
	</div>
	<div>&#160;</div>
	<div>Our standard procedure is to prepare the form online for electronic signatures by all directors. Please therefore make sure you have all the relevant email addresses for each director and that they are 100%% correct. This is particularly important as electronic dissolution applications cannot be amended if the email address is incorrect, nor can reminders be sent once the initial request is sent to the directors. Please note this is a Companies House limitation, not ours.&#160;</div>
	</div>
	<div><b><br />
	</b></div>
	<div>You will then receive a <strong>‘Request to close’ </strong>email from Companies House within 24 hours of purchase (excluding weekends and Bank Holidays). To approve the request, click on the <strong>'Click here to approve the application'</strong> and you will be prompted to sign the electronic DS01 form. Simply click on the yellow 'Sign' Box to sign the document.&#160;</div>
	<div>&#160;</div>
	<div>Once signed, your Dissolution request will be submitted to Companies House and you will receive a Submission Reference Number. Companies House will email us within 2 working days to confirm acceptance or rejection of the submission.&#160;</div>
	<div>&#160;</div>
	<div><strong>3 Months Registered Office Service:</strong>&#160;This will only be added once all director/s have signed / approved the dissolution application.</div>
	HTML;
	
	const ORIGINAL_NAME = 'Company Dissolution Express Service';
	const NEW_NAME = 'Company Dissolution Premium Service';
	
	public function up(): void
	{
		$value = sprintf(self::TEMPLATE_CONTENT, self::NEW_NAME, self::NEW_NAME);
		
		$selectedProperty = $this->fetchRow(sprintf(
			"SELECT propertyId FROM cms2_properties WHERE value LIKE '%%%s%%' AND nodeId = %d",
			self::ORIGINAL_NAME,
			Product::PRODUCT_COMPANY_DISSOLUTION_EXPRESS
		));

		if ($selectedProperty) {
			$this->execute(sprintf(
				"UPDATE cms2_properties SET value = '%s' WHERE propertyId = %d",
				addslashes($value),
				$selectedProperty['propertyId']
			));
		}
	}
	
	public function down(): void
	{
		$value = sprintf(self::TEMPLATE_CONTENT, self::ORIGINAL_NAME, self::ORIGINAL_NAME);

		$selectedProperty = $this->fetchRow(sprintf(
			"SELECT propertyId FROM cms2_properties WHERE value LIKE '%%%s%%' AND nodeId = %d",
			self::NEW_NAME,
			Product::PRODUCT_COMPANY_DISSOLUTION_EXPRESS
		));

		if ($selectedProperty) {
			$this->execute(sprintf(
				"UPDATE cms2_properties SET value = '%s' WHERE propertyId = %d",
				addslashes($value),
				$selectedProperty['propertyId']
			));
		}
	}
}
