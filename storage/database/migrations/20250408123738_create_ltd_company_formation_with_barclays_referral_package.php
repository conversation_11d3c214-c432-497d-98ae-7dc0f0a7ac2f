<?php

declare(strict_types=1);

use MigrationModule\Dto\Node;
use MigrationModule\Dto\Page;
use MigrationModule\Dto\Property;
use MigrationModule\Helpers\NodeMigrationHelper;
use Models\Products\Package;
use Phinx\Migration\AbstractMigration;

class CreateLtdCompanyFormationWithBarclaysReferralPackage extends AbstractMigration
{
    private const PRICE = '15.00';

    public function up(): void
    {
        if (!empty($this->getLtdCompanyFormationWithBarclaysReferralPackageNodeId())) {
            return;
        }

        $page = new Page('LTD Company Formation with Barclays Referral');

        $properties = [
            new Property('associatedPrice', self::PRICE),
            new Property('offerPrice', self::PRICE),
            new Property('price', self::PRICE),
            new Property('productValue', self::PRICE),
            new Property('specialPrice', self::PRICE),
            new Property('wholesalePrice', self::PRICE),
            new Property('emailText', $this->getEmailText()),
            new Property('upgradeDescription', $this->getUpgradeDescription()),
            new Property('upgradeText', $this->getUpgradeText()),
            new Property('associatedImageId', '1018'),
            new Property('associatedProducts1', '1867,1796'),
            new Property('associatedProducts2', '782,322,324,321,589,475,443,165,301,302,331,299,298,325,569'),
            new Property('associatedProducts3', '1850,1867,870,1705,331'),
            new Property('availableForPayByPhone', 'COMPANY_FORMATION'),
            new Property('bankingEnabled', '1'),
            new Property('bankingOptions', 'BARCLAYS'),
            new Property('bankingRequired', '1'),
            new Property('basketText', 'UK Private Company Limited by Shares. After purchase you will need to enter your company details and submit it for incorporation.'),
            new Property('blacklistedEquivalentProducts', '1313,379,436,1175,1430,1598,400,401'),
            new Property('businessServicesEnabled', '1'),
            new Property('businessServicesOptions', '1,3,4'),
            new Property('cashBackAmount', '0'),
            new Property('disabledLeftColumn', '1'),
            new Property('disabledRightColumn', '1'),
            new Property('isAutoRenewalAllowed', '0'),
            new Property('isFeefoEnabled', '1'),
            new Property('isIdCheckRequired', '1'),
            new Property('isInitialProduct', '1'),
            new Property('nonVatableValue', '0'),
            new Property('offerOfTheMonthProductId', '475'),
            new Property('onlyOurCompanies', '1'),
            new Property('registrationReviewEnabled', '1'),
            new Property('removableFromBasket', '1'),
            new Property('requiredCompanyNumber', '0'),
            new Property('requiredCorePackage', '0'),
            new Property('requiredIncorporatedCompanyNumber', '0'),
            new Property('typeId', 'BYSHR'),
            new Property('upgradeProductId', '1315'),
            new Property('voServiceDurationInMonths', '0'),
        ];

        $helper = $this->getHelper();

        $node = new Node(
            $page,
            $properties,
            Package::LTD_COMPANY_FORMATION_WITH_BARCLAYS_REFERRAL_PACKAGE,
            $helper->getExistingNodeId(CreatePartnerPackagesParentFolder::FOLDER_NAME),
            'DefaultControler',
            'PackageAdminControler',
            10
        );

        $helper->create($node);
    }

    public function down(): void
    {
        $nodeId = $this->getLtdCompanyFormationWithBarclaysReferralPackageNodeId();

        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_NODES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_PAGES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`nodeId` = '%s');", TBL_PROPERTIES, $nodeId));
    }

    private function getEmailText(): string
    {
        return <<<EOD
<p>Thank you for purchasing the LTD Company Formation with Barclays Referral.</p>
<ul>
    <li><strong>Please note that your application is not yet complete. You still need to appoint your director(s) and shareholder(s) for the company and assign a registered office.</strong></li>
</ul>
<p>Please log in to your account to complete your application (if you have not done so already) at <a href="http://www.companiesmadesimple.com/login.html">http://www.companiesmadesimple.com/login.html</a></p>
<p><strong>Exclusive Certificate Upgrade Offer - £7.99</strong><br />
The LTD Company Formation Package you purchased does <u>not</u> include the printed certificate of incorporation or share certificates. These documents are usually required by banks to open an account, so we are offering you an exclusive Certificate Upgrade Offer for just £7.99 +VAT. More Info: <a href="http://www.companiesmadesimple.com/certificate-upgrade-offer.html">http://www.companiesmadesimple.com/certificate-upgrade-offer.html</a></p>
EOD;
    }

    private function getUpgradeDescription(): string
    {
        return <<<EOD
<p>&#160;</p>
EOD;
    }

    private function getUpgradeText(): string
    {
        return '';
    }

    private function getLtdCompanyFormationWithBarclaysReferralPackageNodeId(): ?int
    {
        return $this->getHelper()->getExistingNodeId(Package::LTD_COMPANY_FORMATION_WITH_BARCLAYS_REFERRAL_PACKAGE);
    }

    private function getHelper(): NodeMigrationHelper
    {
        return new NodeMigrationHelper($this);
    }
}
