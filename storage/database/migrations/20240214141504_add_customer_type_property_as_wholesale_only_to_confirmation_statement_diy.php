<?php

use Phinx\Migration\AbstractMigration;
use Models\Products\BasketProduct;

class AddCustomerTypePropertyAsWholesaleOnlyToConfirmationStatementDIY extends AbstractMigration
{
    private CONST CONFIRMATION_STATEMENT_DIY_NODE_ID = 666;
    private CONST CUSTOMER_TYPE_PROPERTY_NAME = 'customerType';

    public function up()
    {
        $this->query(
            sprintf(
                'INSERT INTO %s (nodeId, name, value, authorId, editorId, dtc, dtm) VALUES (%d, "%s", "%s", %d, %d, NOW(), NOW())',
                TBL_PROPERTIES,
                self::CONFIRMATION_STATEMENT_DIY_NODE_ID,
                self::CUSTOMER_TYPE_PROPERTY_NAME,
                BasketProduct::CUSTOMER_TYPE_WHOLESALE_ONLY,
                8,
                8
            )
        );
    }

    public function down()
    {
        $this->query(
            sprintf(
                'DELETE FROM %s WHERE name = "%s" AND nodeId = %d',
                TBL_PROPERTIES,
                self::CUSTOMER_TYPE_PROPERTY_NAME,
                self::CONFIRMATION_STATEMENT_DIY_NODE_ID
            )
        );
    }
}
