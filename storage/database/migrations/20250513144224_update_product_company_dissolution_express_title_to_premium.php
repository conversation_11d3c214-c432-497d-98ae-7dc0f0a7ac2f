<?php

declare(strict_types=1);

use Models\Products\Product;
use Phinx\Migration\AbstractMigration;

final class UpdateProductCompanyDissolutionExpressTitleToPremium extends AbstractMigration
{
	const ORIGINAL_NAME = 'Company Dissolution - Express Service';
	const NEW_NAME = 'Company Dissolution - Premium Service';
	
	public function up(): void
	{
		$this->execute(sprintf("UPDATE cms2_pages p SET p.title = '%s' WHERE p.node_id = %d", self::NEW_NAME, Product::PRODUCT_COMPANY_DISSOLUTION_EXPRESS));
	}
	
	public function down(): void
	{
		$this->execute(sprintf("UPDATE cms2_pages p SET p.title = '%s' WHERE p.node_id = %d", self::ORIGINAL_NAME, Product::PRODUCT_COMPANY_DISSOLUTION_EXPRESS));
	}
}
