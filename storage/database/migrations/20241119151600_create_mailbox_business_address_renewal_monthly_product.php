<?php

use MigrationModule\Dto\Node;
use MigrationModule\Dto\Page;
use MigrationModule\Dto\Property;
use MigrationModule\Helpers\NodeMigrationHelper;
use Phinx\Migration\AbstractMigration;

class CreateMailboxBusinessAddressRenewalMonthlyProduct extends AbstractMigration
{
    public const PRODUCT_NAME = 'mailbox_business_address_renewal_monthly';

    public function up()
    {
        $helper = new NodeMigrationHelper($this);

        if ($nodeId = $helper->getExistingNodeId(self::PRODUCT_NAME)) {
            return $nodeId;
        }

        $page = new Page('Mailbox Business Address Renewal (Monthly)');

        $properties = [
            new Property('address1', '20'),
            new Property('address2', 'Wenlock Road'),
            new Property('address3', ''),
            new Property('additional', ''),
            new Property('associatedDescription', "The registered office is the official address of a limited company and must be a physical location in the UK. Our service is ideal if you want to protect your residential address from being shown on the public register or if you're an overseas customer who needs a UK address. NB. This is not a full mail forwarding service. Only mail from Companies House, HMRC and other Government bodies will be forwarded (this mail is forwarded to you free of charge)"),
            new Property('associatedIconClass', 'fa-building'),
            new Property('associatedImageId', '1187'),
            new Property('associatedPrice', '29.99'),
            new Property('associatedProducts3', ''),
            new Property('associatedText', "Use our prestigious N1 London address as your company's registered office and keep your residential address off the public register."),
            new Property('availableForPayByPhone', 'NOT_AVAILABLE'),
            new Property('bankingEnabled', ''),
            new Property('bankingOptions', ''),
            new Property('bankingRequired', ''),
            new Property('basketText', ''),
            new Property('blacklistedContainedProducts', ''),
            new Property('blacklistedEquivalentProducts', ''),
            new Property('cashBackAmount', '0'),
            new Property('conditionedById', ''),
            new Property('countryId', 'GB-ENG'),
            new Property('county', ''),
            new Property('customAssociatedIcon', ''),
            new Property('duration', '+1 month'),
            new Property('emailCmsFileAttachments', '4'),
            new Property('emailText', $this->getEmailText()),
            new Property('isAutoRenewalAllowed', '1'),
            new Property('isFeefoEnabled', ''),
            new Property('isIdCheckRequired', '1'),
            new Property('isInitialProduct', ''),
            new Property('isRenewalProduct', '1'),
            new Property('isVoServiceEligible', ''),
            new Property('lockCompany', ''),
            new Property('markUp', ''),
            new Property('maxQuantityOne', ''),
            new Property('nonVatableDescription', ''),
            new Property('nonVatableValue', ''),
            new Property('notApplyVat', ''),
            new Property('offerPrice', '29.99'),
            new Property('offerProductId', ''),
            new Property('onlyOne', '0'),
            new Property('onlyOneItem', ''),
            new Property('onlyOurCompanies', '0'),
            new Property('optionalRenewalProductId', ''),
            new Property('postcode', 'N1 7GU'),
            new Property('price', '29.99'),
            new Property('printedCertificateOptionEnabled', ''),
            new Property('productValue', '29.99'),
            new Property('registrationReviewEnabled', ''),
            new Property('removableFromBasket', '1'),
            new Property('removeFromBasketConfirmation', ''),
            new Property('renewalDtStartBehavior', ''),
            new Property('requiredCompanyNumber', '1'),
            new Property('requiredCorePackage', ''),
            new Property('requiredIncorporatedCompanyNumber', '0'),
            new Property('responsibleEmails', ''),
            new Property('sageNominalCode', ''),
            new Property('saveToCompany', '0'),
            new Property('serviceTypeId', 'REGISTERED_OFFICE'),
            new Property('showInMyServicesPage', ''),
            new Property('specialPrice', '29.99'),
            new Property('toolkitOfferTypes', ''),
            new Property('town', 'London'),
            new Property('upgradeDescription', $this->getUpgradeDescription()),
            new Property('upgradeImageId', '425'),
            new Property('voServiceDurationInMonths', '0'),
            new Property('wholesalePrice', '29.99'),
        ];

        $node = new Node(
            $page,
            $properties,
            self::PRODUCT_NAME,
            1862,
            'DefaultControler',
            'ProductAdminControler',
            80
        );
        $node->setExcludeFromSitemap(false);
        $helper = new NodeMigrationHelper($this);
        $helper->create($node);

        $nodeId = $helper->getExistingNodeId(self::PRODUCT_NAME);
        $helper->createProperties($nodeId, [new Property('renewalProductId', $nodeId)]);
    }

    public function down()
    {
        $helper = new NodeMigrationHelper($this);
        $nodeId = $helper->getExistingNodeId(self::PRODUCT_NAME);

        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_NODES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_PAGES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`nodeId` = '%s');", TBL_PROPERTIES, $nodeId));
    }

    private function getEmailText(): string
    {
        return <<<EOD
This is a 'Mailbox Business Address Renewal (Monthly)' placeholder for the email text 
EOD;
    }

    private function getUpgradeDescription(): string
    {
        return <<<EOD
This is a 'Mailbox Business Address Renewal (Monthly)' placeholder for the upgrade description text
EOD;
    }
}
