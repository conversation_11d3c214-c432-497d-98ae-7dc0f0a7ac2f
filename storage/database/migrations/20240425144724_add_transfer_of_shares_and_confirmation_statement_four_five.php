<?php

use MigrationModule\Dto\Node;
use MigrationModule\Dto\Page;
use MigrationModule\Dto\Property;
use MigrationModule\Helpers\NodeMigrationHelper;
use Phinx\Migration\AbstractMigration;

class AddTransferOfSharesAndConfirmationStatementFourFive extends AbstractMigration
{
    private CONST NAME = 'transfer_of_shares_confirmation_statement_four_five';

    public function up()
    {
        if ($this->getNodeId() !== null) {
            return;
        }

        $page = new Page('Transfer of Shares & Confirmation Statement (4-5 Shareholders)');

        $properties = [
            new Property('price', '99.99'),
            new Property('productValue', '99.99'),
            new Property('requiredCompanyNumber', '1'),
            new Property('onlyOurCompanies', '0'),
            new Property('saveToCompany', ''),
            new Property('onlyOneItem', '0'),
            new Property('maxQuantityOne', '1'),
            new Property('conditionedById', ''),
            new Property('responsibleEmails', ''),
            new Property('notApplyVat', '0'),
            new Property('nonVatableValue', '34'),
            new Property('emailText', $this->getEmailText()),
            new Property('associatedPrice', '99.99'),
            new Property('basketText', ''),
            new Property('additional', ''),
            new Property('wholesalePrice', '99.99'),
            new Property('lockCompany', ''),
            new Property('requiredIncorporatedCompanyNumber', '0'),
            new Property('upgradeDescription', $this->getUpgradeDescription()),
            new Property('upgradeImageId', ''),
            new Property('markUp', ''),
            new Property('associatedImageId', ''),
            new Property('associatedText', ''),
            new Property('emailCmsFileAttachments', ''),
            new Property('isFeefoEnabled', ''),
            new Property('isAutoRenewalAllowed', '0'),
            new Property('blacklistedEquivalentProducts', ''),
            new Property('blacklistedContainedProducts', ''),
            new Property('serviceTypeId', ''),
            new Property('duration', ''),
            new Property('renewalProductId', ''),
            new Property('isVoServiceEligible', ''),
            new Property('voServiceDurationInMonths', '0'),
            new Property('isIdCheckRequired', '0'),
            new Property('associatedDescription', ''),
            new Property('associatedIconClass', ''),
            new Property('bankingEnabled', '0'),
            new Property('bankingOptions', ''),
            new Property('bankingRequired', '0'),
            new Property('cashBackAmount', '0'),
            new Property('removableFromBasket', '1'),
            new Property('removeFromBasketConfirmation', ''),
            new Property('availableForPayByPhone', 'OTHER_PRODUCTS'),
            new Property('offerPrice', '99.99'),
            new Property('toolkitOfferTypes', ''),
            new Property('associatedProducts3', ''),
            new Property('offerProductId', ''),
            new Property('isInitialProduct', ''),
            new Property('isRenewalProduct', ''),
            new Property('businessServicesEnabled', '0'),
            new Property('businessServicesOptions', ''),
            new Property('sageNominalCode', ''),
            new Property('printedCertificateOptionEnabled', ''),
            new Property('nonVatableDescription', ''),
            new Property('registrationReviewEnabled', ''),
            new Property('specialPrice', '0'),
        ];

        $node = new Node(
            $page,
            $properties,
            $this::NAME,
            310,
            'DefaultControler',
            'ProductAdminControler',
            440
        );

        $helper = new NodeMigrationHelper($this);
        $helper->create($node);
    }

    public function down()
    {
        $nodeId = $this->getNodeId();
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_NODES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_PAGES, $nodeId));
        $this->execute(sprintf("DELETE FROM `%s` WHERE (`nodeId` = '%s');", TBL_PROPERTIES, $nodeId));
    }

    private function getEmailText(): string
    {
        return <<<EOD
            <p><strong>Transfer of Shares & Confirmation Statement (4-5 Shareholders) </strong></p>
            <p>Thank you for ordering the Transfer of Shares and Confirmation Statement service.</p>
            <p>In order for us to process your order, please complete the following form: <a href="https://bit.ly/Made-Simple-Transfer-of-Shares-4-5-Shareholders">https://bit.ly/Made-Simple-Transfer-of-Shares-4-5-Shareholders</a></p>
            <p>Once completed, our Company Secretarial Team will prepare your documents within 48 working hours (excluding bank holidays and weekends).</p>
            <p>The share documents will be uploaded to your account available for you to view and download.</p>
            <p>We will also prepare the Confirmation Statement to update the changes to shareholding at Companies House. Once prepared, we will email you and request
             that you review the Statement before submitting to Companies House.</p>
EOD;
    }

    private function getUpgradeDescription(): string
    {
        return <<<EOD
            <p>&#160;</p>
EOD;
    }

    private function getNodeId()
    {
        $node = $this->fetchRow(sprintf("SELECT node_id FROM %s WHERE `name`='%s'", TBL_NODES, $this::NAME));
        return $node['node_id'] ?? null;
    }
}
