<?php

use Phinx\Migration\AbstractMigration;

class RollbackUpdateGuaranteedSameDayFormationProductDescription extends AbstractMigration
{
    const NODE_ID = 589;
    const AUTHOR_ID = 8;
    const ATTRIBUTE_TO_CHANGE = 'associatedText';

    public function up()
    {
        $this->execute(
            sprintf(
                "UPDATE %s SET `value` = '%s', `authorId` = %d WHERE name = '%s' AND `nodeId` = %d",
                TBL_PROPERTIES,
                addslashes($this->getNewAssociatedText()),
                self::AUTHOR_ID,
                self::ATTRIBUTE_TO_CHANGE,
                self::NODE_ID
            )
        );
    }

    public function down()
    {
        $this->execute(
            sprintf(
                "UPDATE %s SET `value` = '%s' WHERE name = '%s' AND `nodeId` = %d",
                TBL_PROPERTIES,
                addslashes($this->getOldAssociatedText()),
                self::ATTRIBUTE_TO_CHANGE,
                self::NODE_ID
            )
        );
    }

    private function getOldAssociatedText(): string
    {
        return <<<EOD
Jump the processing queue at Companies House when you submit incorporation details before 11am (Monday-Friday, excluding Bank Holidays).
EOD;
    }

    private function getNewAssociatedText(): string
    {
        return <<<EOD
Jump the processing queue at Companies House when you submit incorporation details before 2pm (Monday-Friday, excluding Bank Holidays).
EOD;
    }
}
