<?php

use Phinx\Migration\AbstractMigration;

class UpdateQuotaMailForwardingProductsToRequireACompany extends AbstractMigration
{
    public function up()
    {
        $quotaMailForwardingRecurrenceTier1 = $this->getNodeId(QuotaMailForwardingRecurrenceTier1::PRODUCT_NAME);
        $quotaMailForwardingRecurrenceTier2 = $this->getNodeId(QuotaMailForwardingRecurrenceTier2::PRODUCT_NAME);
        $quotaMailForwardingRecurrenceTier3 = $this->getNodeId(QuotaMailForwardingRecurrenceTier3::PRODUCT_NAME);
        $quotaMailForwardingRenewalRecurrenceTier1 = $this->getNodeId(QuotaMailForwardingRenewalRecurrenceTier1::PRODUCT_NAME);
        $quotaMailForwardingRenewalRecurrenceTier2 = $this->getNodeId(QuotaMailForwardingRenewalRecurrenceTier2::PRODUCT_NAME);
        $quotaMailForwardingRenewalRecurrenceTier3 = $this->getNodeId(QuotaMailForwardingRenewalRecurrenceTier3::PRODUCT_NAME);

        $baseUpdateSql = "INSERT INTO cms2_properties (nodeId, name, value, authorId, editorId, dtc, dtm) VALUES (%s, 'requiredCompanyNumber', 1, 8, 8, NOW(), NOW())";

        $this->query(sprintf($baseUpdateSql, $quotaMailForwardingRecurrenceTier1));
        $this->query(sprintf($baseUpdateSql, $quotaMailForwardingRecurrenceTier2));
        $this->query(sprintf($baseUpdateSql, $quotaMailForwardingRecurrenceTier3));
        $this->query(sprintf($baseUpdateSql, $quotaMailForwardingRenewalRecurrenceTier1));
        $this->query(sprintf($baseUpdateSql, $quotaMailForwardingRenewalRecurrenceTier2));
        $this->query(sprintf($baseUpdateSql, $quotaMailForwardingRenewalRecurrenceTier3));
    }

    public function down()
    {
        $quotaMailForwardingRecurrenceTier1 = $this->getNodeId(QuotaMailForwardingRecurrenceTier1::PRODUCT_NAME);
        $quotaMailForwardingRecurrenceTier2 = $this->getNodeId(QuotaMailForwardingRecurrenceTier2::PRODUCT_NAME);
        $quotaMailForwardingRecurrenceTier3 = $this->getNodeId(QuotaMailForwardingRecurrenceTier3::PRODUCT_NAME);
        $quotaMailForwardingRenewalRecurrenceTier1 = $this->getNodeId(QuotaMailForwardingRenewalRecurrenceTier1::PRODUCT_NAME);
        $quotaMailForwardingRenewalRecurrenceTier2 = $this->getNodeId(QuotaMailForwardingRenewalRecurrenceTier2::PRODUCT_NAME);
        $quotaMailForwardingRenewalRecurrenceTier3 = $this->getNodeId(QuotaMailForwardingRenewalRecurrenceTier3::PRODUCT_NAME);

        $this->query("DELETE FROM cms2_properties WHERE name in ('requiredCompanyNumber') 
            and nodeId in ({$quotaMailForwardingRecurrenceTier1},{$quotaMailForwardingRecurrenceTier2},{$quotaMailForwardingRecurrenceTier3},{$quotaMailForwardingRenewalRecurrenceTier1},{$quotaMailForwardingRenewalRecurrenceTier2},{$quotaMailForwardingRenewalRecurrenceTier3})");
    }

    private function getNodeId($productName)
    {
        $node = $this->fetchRow(sprintf("SELECT node_id FROM %s WHERE `name`='%s'", TBL_NODES, $productName));
        return $node['node_id'] ?? null;
    }
}
