<?php

use Phinx\Migration\AbstractMigration;

class IncorporationMemberPrescribedParticularsLengthIncrease extends AbstractMigration
{
    public function up()
    {
        $this->execute("ALTER TABLE `ch_incorporation_member` CHANGE `prescribed_particulars` `prescribed_particulars` varchar(4000);");
    }

    public function down()
    {
        $this->execute("ALTER TABLE `ch_incorporation_member` <PERSON>ANGE `prescribed_particulars` `prescribed_particulars` varchar(255);");
    }
}
