<?php

use Phinx\Migration\AbstractMigration;

class AddNotificationsEnabledColumnToChCompanyTrackingsTable extends AbstractMigration
{
    const COLUMN_NOTIFICATIONS_ENABLED = 'notificationsEnabled';
    const TBL_COMPANY_TRACKINGS = 'ch_company_trackings';

    public function up()
    {
        $this->table(self::TBL_COMPANY_TRACKINGS)
            ->addColumn(
                self::COLUMN_NOTIFICATIONS_ENABLED,
                'boolean',
                array('after' => 'serviceTypeId', 'null' => FALSE, 'default' => 1)
            )
            ->update();
    }

    public function down()
    {
        $this->execute(
            sprintf(
                "ALTER TABLE `%s` DROP `%s`",
                self::TBL_COMPANY_TRACKINGS, self::COLUMN_NOTIFICATIONS_ENABLED
            )
        );
    }
}