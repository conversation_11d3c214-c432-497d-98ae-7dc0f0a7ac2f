<?php

use Phinx\Migration\AbstractMigration;

class UpdateApostilledDocumentsAndCertificateOfGoodStandingTitle extends AbstractMigration
{
    private const APOSTILLED_DOCUMENTS_AND_CERTIFICATE_OF_GOOD_STANDING_NORMAL_NODE_ID = 1748;

    /**
     * Migrate Up.
     */
    public function up()
    {
        $this->query(
            sprintf("UPDATE cms2_pages SET title = 'Apostille Documents & Certificate of Good Standing Apostille Bundle' WHERE node_id = %s;",
                self::APOSTILLED_DOCUMENTS_AND_CERTIFICATE_OF_GOOD_STANDING_NORMAL_NODE_ID
            )
        );
    }

    /**
     * Migrate Down.
     */
    public function down()
    {
        $this->query(
            sprintf("UPDATE cms2_pages SET title = 'Apostille Documents & Certificate of Good Standing Apostille Bundle (Normal)' WHERE node_id = %s;",
                self::APOSTILLED_DOCUMENTS_AND_CERTIFICATE_OF_GOOD_STANDING_NORMAL_NODE_ID
            )
        );
    }
}
