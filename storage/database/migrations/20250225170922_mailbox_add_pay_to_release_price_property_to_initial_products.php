<?php

use MigrationModule\Dto\Property;
use MigrationModule\Helpers\NodeMigrationHelper;
use Models\Products\Product;
use Phinx\Migration\AbstractMigration;

class MailboxAddPayToReleasePricePropertyToInitialProducts extends AbstractMigration
{
    public function up()
    {
        $helper = new NodeMigrationHelper($this);

        foreach (Product::PRODUCTS_THAT_HAVE_MAILBOX_SETTINGS as $productName) {
            $nodeId = $helper->getExistingNodeId((string) $productName);

            $properties = [
                new Property('mailbox-pay-to-release-fee-post-item', '2.5'),
                new Property('mailbox-pay-to-release-fee-parcel', '2.5'),
            ];

            $helper->createProperties($nodeId, $properties);
        }
    }

    public function down()
    {
        $helper = new NodeMigrationHelper($this);

        $nodeIds = [];
        foreach (Product::PRODUCTS_THAT_HAVE_MAILBOX_SETTINGS as $productName) {
            $nodeIds[] = $helper->getExistingNodeId((string) $productName);
        }

        $this->execute(
            sprintf(
                "DELETE FROM `%s` WHERE (`nodeId` IN (%s)) AND (`name` IN (%s));",
                TBL_PROPERTIES,
                implode(',', $nodeIds),
                implode(',', ['\'mailbox-pay-to-release-fee-post-item\'', '\'mailbox-pay-to-release-fee-parcel\''])
            )
        );
    }
}