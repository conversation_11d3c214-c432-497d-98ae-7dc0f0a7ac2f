<?php

declare(strict_types=1);

use MailScanModule\Providers\MailboxEmailNameProvider;
use MigrationModule\Dto\Node;
use MigrationModule\Dto\Page;
use MigrationModule\Dto\Property;
use MigrationModule\Helpers\NodeMigrationHelper;
use Phinx\Migration\AbstractMigration;

class AddExtraRemindersMailboxEmailsNamesIntoNodesTable extends AbstractMigration
{
    public const MAILBOX_EMAILS = [
        MailboxEmailNameProvider::MAIL_RELEASED_COLLECT . '-reminder',
        MailboxEmailNameProvider::MAIL_WAITING_PAYMENT_SCAN . '-reminder',
    ];

    public function up()
    {
        $helper = new NodeMigrationHelper($this);
        $mailboxEmailsFolderId = $helper->getExistingNodeId(CreateMailboxEmailsFolder::FOLDER_NAME);
        $order = 0;

        foreach (self::MAILBOX_EMAILS as $mailboxEmailName) {
            $page = new Page($this->convertToTitleCase($mailboxEmailName));
            $properties = [
                new Property('from', '<EMAIL>'),
                new Property('fromName', 'Companies Made Simple'),
                new Property('tag1', 'mailbox-email'),
                new Property('tag2', $mailboxEmailName),
                new Property('templateName', $mailboxEmailName),
            ];

            $order = $order + 10;
            $node = new Node(
                $page,
                $properties,
                $mailboxEmailName,
                $mailboxEmailsFolderId,
                null,
                'EmailAdminControler',
                $order
            );

            $helper->create($node);
        }
    }

    public function down()
    {
        $helper = new NodeMigrationHelper($this);

        foreach (self::MAILBOX_EMAILS as $mailboxEmailName) {
            $nodeId = $helper->getExistingNodeId($mailboxEmailName);
            $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_NODES, $nodeId));
            $this->execute(sprintf("DELETE FROM `%s` WHERE (`node_id` = '%s');", TBL_PAGES, $nodeId));
            $this->execute(sprintf("DELETE FROM `%s` WHERE (`nodeId` = '%s');", TBL_PROPERTIES, $nodeId));
        }
    }

    private function convertToTitleCase(string $string): string
    {
        $parts = explode('-', $string);

        $convertedParts = array_map(function ($part) {
            return ucfirst(mb_strtolower($part));
        }, $parts);

        return implode(' ', $convertedParts);
    }
}
