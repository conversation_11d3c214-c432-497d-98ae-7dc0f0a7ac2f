SET sql_mode = '';
CREATE TABLE IF NOT EXISTS `brookson_one_leads` (
  `brooksonOneLeadId` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `fullName` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `companyName` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `email` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `phone` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `workSector` enum('public','private') CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `consent` tinyint(1) NOT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`brooksonOneLeadId`),
  KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `business_services_partner_information` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `lead_id` int(11) NOT NULL,
  `title` varchar(255) DEFAULT NULL,
  `first_name` varchar(255) DEFAULT NULL,
  `last_name` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `name_number` varchar(255) DEFAULT NULL,
  `street` varchar(255) DEFAULT NULL,
  `locality` varchar(255) DEFAULT NULL,
  `postcode` varchar(255) DEFAULT NULL,
  `town` varchar(255) DEFAULT NULL,
  `county` varchar(255) DEFAULT NULL,
  `country` varchar(255) DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  `dob` date DEFAULT NULL,
  `middle_name` varchar(255) DEFAULT NULL,
  `existing_client` tinyint(1) DEFAULT NULL,
  `pin_sentry_access` tinyint(1) DEFAULT NULL,
  `online_signup` tinyint(1) DEFAULT NULL,
  `phone_alternative` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=152089 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `ch_address_change` (
  `form_submission_id` int(6) unsigned zerofill NOT NULL,
  `reject_description` mediumtext CHARACTER SET utf8 COLLATE utf8_unicode_ci,
  `premise` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `street` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `thoroughfare` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `post_town` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `county` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `postcode` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `care_of_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `po_box` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `accept_appropriate_office_address_statement` tinyint(1) DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`form_submission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_annual_return` (
  `form_submission_id` int(6) unsigned zerofill NOT NULL,
  `reject_description` varchar(250) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `company_category` enum('PLC','BYSHR','BYGUAR','BYSHREXUNDSEC60','BYGUAREXUNDSEC60','UNLCWSHRCAP','UNLCWOSHRCAP','LLP') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `trading` tinyint(1) DEFAULT NULL,
  `dtr5` tinyint(1) DEFAULT '0',
  `made_up_date` date DEFAULT NULL,
  `registered_email_address` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `is_lawful_purpose_statement` tinyint(1) DEFAULT NULL,
  `sic_code_type` enum('code','text') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_code` varchar(250) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `premise` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `street` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `thoroughfare` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `post_town` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `county` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT 'GBR',
  `postcode` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `care_of_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `po_box` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_premise` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_street` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_thoroughfare` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_post_town` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_county` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_country` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_postcode` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_care_of_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_po_box` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_records` varchar(250) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `members_list` enum('NONE','FULL','NOCHANGE','FULLPLC') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `regulated_markets` tinyint(1) DEFAULT NULL,
  `no_psc_reason` enum('NO_INDIVIDUAL_OR_ENTITY_WITH_SIGNFICANT_CONTROL') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `pscs_pre_added` tinyint(1) DEFAULT NULL,
  `before_changes` mediumtext CHARACTER SET utf8 COLLATE utf8_unicode_ci,
  PRIMARY KEY (`form_submission_id`),
  KEY `form_submission_id` (`form_submission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_annual_return_officer` (
  `annual_return_officer_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `form_submission_id` int(6) unsigned zerofill NOT NULL,
  `type` enum('DIR','SEC','MEM','PSC') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `corporate` tinyint(1) DEFAULT NULL,
  `designated_ind` tinyint(1) DEFAULT NULL,
  `title` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `forename` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `middle_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `surname` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `corporate_name` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `premise` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `street` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `thoroughfare` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `post_town` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `county` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `postcode` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `care_of_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `po_box` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `same_as_reg_office` tinyint(1) DEFAULT NULL,
  `previous_names` varchar(250) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `dob` date DEFAULT NULL,
  `nationality` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `occupation` varchar(35) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country_of_residence` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `identification_type` enum('EEA','NonEEA','PSC','UK','NonUK') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `place_registered` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `registration_number` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `law_governed` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `legal_form` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `ownership_of_shares` enum('OWNERSHIPOFSHARES_25TO50PERCENT','OWNERSHIPOFSHARES_50TO75PERCENT','OWNERSHIPOFSHARES_75TO100PERCENT','OWNERSHIPOFSHARES_25TO50PERCENT_AS_FIRM','OWNERSHIPOFSHARES_50TO75PERCENT_AS_FIRM','OWNERSHIPOFSHARES_75TO100PERCENT_AS_FIRM','OWNERSHIPOFSHARES_25TO50PERCENT_AS_TRUST','OWNERSHIPOFSHARES_50TO75PERCENT_AS_TRUST','OWNERSHIPOFSHARES_75TO100PERCENT_AS_TRUST','RIGHTTOSHARESURPLUSASSETS_25TO50PERCENT','RIGHTTOSHARESURPLUSASSETS_50TO75PERCENT','RIGHTTOSHARESURPLUSASSETS_75TO100PERCENT','RIGHTTOSHARESURPLUSASSETS_25TO50PERCENT_AS_FIRM','RIGHTTOSHARESURPLUSASSETS_50TO75PERCENT_AS_FIRM','RIGHTTOSHARESURPLUSASSETS_75TO100PERCENT_AS_FIRM','RIGHTTOSHARESURPLUSASSETS_25TO50PERCENT_AS_TRUST','RIGHTTOSHARESURPLUSASSETS_50TO75PERCENT_AS_TRUST','RIGHTTOSHARESURPLUSASSETS_75TO100PERCENT_AS_TRUST') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `ownership_of_voting_rights` enum('VOTINGRIGHTS_25TO50PERCENT','VOTINGRIGHTS_50TO75PERCENT','VOTINGRIGHTS_75TO100PERCENT','VOTINGRIGHTS_25TO50PERCENT_AS_FIRM','VOTINGRIGHTS_50TO75PERCENT_AS_FIRM','VOTINGRIGHTS_75TO100PERCENT_AS_FIRM','VOTINGRIGHTS_25TO50PERCENT_AS_TRUST','VOTINGRIGHTS_50TO75PERCENT_AS_TRUST','VOTINGRIGHTS_75TO100PERCENT_AS_TRUST') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `right_to_appoint_and_remove_directors` enum('RIGHTTOAPPOINTANDREMOVEDIRECTORS','RIGHTTOAPPOINTANDREMOVEDIRECTORS_AS_FIRM','RIGHTTOAPPOINTANDREMOVEDIRECTORS_AS_TRUST','RIGHTTOAPPOINTANDREMOVEMEMBERS','RIGHTTOAPPOINTANDREMOVEMEMBERS_AS_FIRM','RIGHTTOAPPOINTANDREMOVEMEMBERS_AS_TRUST') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `significant_influence_or_control` enum('SIGINFLUENCECONTROL','SIGINFLUENCECONTROL_AS_FIRM','SIGINFLUENCECONTROL_AS_TRUST') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country_or_state` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `eea_not_synced` tinyint(1) DEFAULT '0',
  `eea_complete` tinyint(1) DEFAULT '1',
  `country_not_synced` tinyint(1) DEFAULT '0',
  `country_of_residence_not_synced` tinyint(1) DEFAULT '0',
  `residential_premise` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_street` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_thoroughfare` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_post_town` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_county` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_country` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_postcode` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_secure_address_ind` tinyint(1) NOT NULL DEFAULT '0',
  `consentToAct` tinyint(1) NOT NULL DEFAULT '1',
  `existing_officer` tinyint(1) NOT NULL DEFAULT '0',
  `cessation_date` date DEFAULT NULL,
  `before_changes` mediumtext CHARACTER SET utf8 COLLATE utf8_unicode_ci,
  `date_of_change` date DEFAULT NULL,
  `notification_date` date DEFAULT NULL,
  `psc_statement_notification` enum('PSC_EXISTS_BUT_NOT_IDENTIFIED','PSC_DETAILS_NOT_CONFIRMED','PSC_CONTACTED_BUT_NO_RESPONSE','RESTRICTIONS_NOTICE_ISSUED_TO_PSC') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`annual_return_officer_id`),
  KEY `form_submission_id` (`form_submission_id`)
) ENGINE=InnoDB AUTO_INCREMENT=651102 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_annual_return_shareholder` (
  `annual_return_shareholder_id` int(11) NOT NULL AUTO_INCREMENT,
  `annual_return_shareholding_id` int(11) NOT NULL,
  `forename` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `middle_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `surname` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `premise` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `street` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `thoroughfare` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `post_town` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `county` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `postcode` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `isRemoved` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1 - Disabled address',
  PRIMARY KEY (`annual_return_shareholder_id`),
  KEY `annual_return_shareholding_id` (`annual_return_shareholding_id`)
) ENGINE=InnoDB AUTO_INCREMENT=433384 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_annual_return_shareholding` (
  `annual_return_shareholding_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `form_submission_id` int(6) unsigned zerofill NOT NULL,
  `share_class` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `number_held` varchar(250) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sync` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`annual_return_shareholding_id`),
  KEY `form_submission_id` (`form_submission_id`)
) ENGINE=InnoDB AUTO_INCREMENT=431576 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_annual_return_shares` (
  `annual_return_shares_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `form_submission_id` int(6) unsigned zerofill NOT NULL,
  `share_class` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `prescribed_particulars` mediumtext CHARACTER SET utf8 COLLATE utf8_unicode_ci,
  `num_shares` varchar(11) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `amount_paid` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `amount_unpaid` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `currency` char(3) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `aggregate_nom_value` varchar(19) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sync_paid` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `paid` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `total_amount_unpaid` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`annual_return_shares_id`),
  KEY `form_submission_id` (`form_submission_id`)
) ENGINE=InnoDB AUTO_INCREMENT=287810 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_annual_return_transfer` (
  `annual_return_transfer_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `annual_return_shareholding_id` int(10) unsigned NOT NULL,
  `date_of_transfer` date DEFAULT NULL,
  `shares_transfered` varchar(250) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`annual_return_transfer_id`),
  KEY `annual_return_shareholding_id` (`annual_return_shareholding_id`)
) ENGINE=InnoDB AUTO_INCREMENT=11834 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_barclays` (
  `barclays_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `company_id` int(10) unsigned NOT NULL,
  `test` tinyint(1) NOT NULL,
  `date_sent` datetime DEFAULT NULL,
  `success` tinyint(1) DEFAULT NULL,
  `response_code` smallint(6) DEFAULT NULL,
  `response_description` mediumtext CHARACTER SET utf8 COLLATE utf8_unicode_ci,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`barclays_id`),
  KEY `cmpany_id` (`company_id`)
) ENGINE=InnoDB AUTO_INCREMENT=250433 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_barclays_soletrader` (
  `barclaysId` int(11) NOT NULL AUTO_INCREMENT,
  `barclaysSoletraderId` int(11) NOT NULL,
  `test` tinyint(1) NOT NULL,
  `dateSent` datetime DEFAULT NULL,
  `success` tinyint(1) DEFAULT NULL,
  `responseCode` smallint(6) DEFAULT NULL,
  `responseDescription` mediumtext CHARACTER SET utf8 COLLATE utf8_unicode_ci,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`barclaysId`),
  KEY `barclaysSoletraderId` (`barclaysSoletraderId`)
) ENGINE=InnoDB AUTO_INCREMENT=1388 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_card_one` (
  `card_one_id` int(11) NOT NULL AUTO_INCREMENT,
  `company_id` int(11) NOT NULL,
  `date_sent` datetime NOT NULL,
  `success` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`card_one_id`),
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2967 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_change_accounting_reference_date` (
  `form_submission_id` int(11) NOT NULL,
  `reject_description` mediumtext CHARACTER SET utf8 COLLATE utf8_unicode_ci,
  `date` date DEFAULT NULL,
  `fiveYearExtensionDetails` tinyint(1) DEFAULT NULL,
  `extensionReason` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `extensionAuthorisedCode` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`form_submission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_change_company_name` (
  `form_submission_id` int(11) NOT NULL,
  `reject_description` mediumtext CHARACTER SET utf8 COLLATE utf8_unicode_ci,
  `sameDay` tinyint(4) DEFAULT NULL,
  `meetingDate` date NOT NULL,
  `methodOfChange` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `noticeGiven` tinyint(1) NOT NULL,
  `newCompanyName` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  PRIMARY KEY (`form_submission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_company_capital` (
  `company_capital_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `company_id` int(10) unsigned NOT NULL,
  `total_issued` varchar(18) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `currency` char(3) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `total_aggregate_value` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `total_amount_unpaid` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`company_capital_id`),
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB AUTO_INCREMENT=84526206 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_company_capital_shares` (
  `company_capital_shares_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `company_capital_id` int(10) unsigned NOT NULL,
  `share_class` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `prescribed_particulars` mediumtext CHARACTER SET utf8 COLLATE utf8_unicode_ci,
  `num_shares` varchar(11) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `amount_paid` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `amount_unpaid` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `nominal_value` varchar(19) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`company_capital_shares_id`),
  KEY `company_capital_id` (`company_capital_id`)
) ENGINE=InnoDB AUTO_INCREMENT=90979947 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_company_data_request` (
  `company_data_request_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `customer_id` int(10) unsigned NOT NULL,
  `envelope_id` int(10) unsigned DEFAULT NULL,
  `company_number` char(8) NOT NULL,
  `authentication_code` char(6) NOT NULL,
  PRIMARY KEY (`company_data_request_id`),
  KEY `customer_id` (`customer_id`),
  KEY `envelope_id` (`envelope_id`)
) ENGINE=InnoDB AUTO_INCREMENT=92901017 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `ch_company_documents` (
  `companyDocumentId` int(11) NOT NULL AUTO_INCREMENT,
  `companyId` int(11) NOT NULL,
  `typeId` enum('OTHER') CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `fileTitle` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `ext` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `size` int(11) NOT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`companyDocumentId`),
  KEY `companyId` (`companyId`)
) ENGINE=InnoDB AUTO_INCREMENT=12233 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_company_incorporation` (
  `form_submission_id` int(6) unsigned zerofill NOT NULL,
  `msg_address` tinyint(1) NOT NULL DEFAULT '0',
  `type` enum('PLC','BYSHR','BYGUAR','BYGUAREXEMPT','LLP') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `reject_reference` char(8) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'only firs reject reference',
  `reject_description` mediumtext CHARACTER SET utf8 COLLATE utf8_unicode_ci COMMENT 'this is text because it''s all reject descriptions separated by semicolon',
  `same_day` tinyint(1) NOT NULL DEFAULT '0',
  `country_of_incorporation` enum('EW','SC','WA','NI') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `premise` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `street` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `thoroughfare` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `post_town` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `county` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country` enum('GB-ENG','GB-WLS','GB-SCT','GB-NIR','GBR','UNDEF') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `postcode` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `care_of_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `po_box` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `articles` enum('BYSHRMODEL','BYGUARMODEL','PLCMODEL','BYSHAREAMEND','BYGUARAMEND','PLCAMEND','BESPOKE') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `registered_email_address` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `is_lawful_purpose_statement` tinyint(1) DEFAULT NULL,
  `restricted_articles` tinyint(1) DEFAULT NULL,
  `same_name` tinyint(1) DEFAULT NULL,
  `name_authorisation` tinyint(1) DEFAULT NULL,
  `no_psc_reason` enum('NO_INDIVIDUAL_OR_ENTITY_WITH_SIGNFICANT_CONTROL') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sicCode1` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sicCode2` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sicCode3` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sicCode4` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `preAddedPscs` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`form_submission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_company_managers` (
  `company_manager_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `entity_id` varchar(255) NOT NULL,
  `company_id` int(11) NOT NULL,
  `existing_officer` tinyint(1) NOT NULL,
  `corporate` tinyint(1) NOT NULL,
  `title` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `forename` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `middle_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `surname` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `corporate_name` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `identification_type` enum('EEA','NonEEA','PSC','UK','NonUK') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `place_registered` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `registration_number` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `law_governed` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `legal_form` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country_or_state` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `dob` date DEFAULT NULL,
  `nationality` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `occupation` varchar(35) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country_of_residence` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `premise` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `street` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `thoroughfare` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `post_town` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `county` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `postcode` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_premise` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_street` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_thoroughfare` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_post_town` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_county` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_postcode` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_country` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `deleted` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`company_manager_id`),
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB AUTO_INCREMENT=79234 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `ch_company_member` (
  `company_member_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `company_id` int(10) unsigned NOT NULL,
  `type` enum('DIR','SEC','SUB','PSC') CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `corporate` tinyint(1) NOT NULL,
  `discriminator` enum('DIR_0','DIR_1','SUB_0','SUB_1','SEC_0','SEC_1','PSC_0','PSC_1','PSC_2') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `designated_ind` tinyint(1) DEFAULT NULL,
  `title` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `forename` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `middle_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `surname` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `corporate_name` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `place_registered` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `registration_number` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `law_governed` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `legal_form` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country_or_state` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `premise` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `street` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `thoroughfare` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `post_town` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `county` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `postcode` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `care_of_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `po_box` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `dob` date DEFAULT NULL,
  `nationality` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `occupation` varchar(35) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country_of_residence` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_premise` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_street` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_thoroughfare` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_post_town` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_county` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_country` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_postcode` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_secure_address_ind` tinyint(1) DEFAULT NULL,
  `appointment_date` date DEFAULT NULL,
  `resignation_date` date DEFAULT NULL,
  `share_class` varchar(30) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `num_shares` int(11) DEFAULT NULL,
  `ownership_of_shares` enum('OWNERSHIPOFSHARES_25TO50PERCENT','OWNERSHIPOFSHARES_50TO75PERCENT','OWNERSHIPOFSHARES_75TO100PERCENT','OWNERSHIPOFSHARES_25TO50PERCENT_AS_FIRM','OWNERSHIPOFSHARES_50TO75PERCENT_AS_FIRM','OWNERSHIPOFSHARES_75TO100PERCENT_AS_FIRM','OWNERSHIPOFSHARES_25TO50PERCENT_AS_TRUST','OWNERSHIPOFSHARES_50TO75PERCENT_AS_TRUST','OWNERSHIPOFSHARES_75TO100PERCENT_AS_TRUST','RIGHTTOSHARESURPLUSASSETS_25TO50PERCENT','RIGHTTOSHARESURPLUSASSETS_50TO75PERCENT','RIGHTTOSHARESURPLUSASSETS_75TO100PERCENT','RIGHTTOSHARESURPLUSASSETS_25TO50PERCENT_AS_FIRM','RIGHTTOSHARESURPLUSASSETS_50TO75PERCENT_AS_FIRM','RIGHTTOSHARESURPLUSASSETS_75TO100PERCENT_AS_FIRM','RIGHTTOSHARESURPLUSASSETS_25TO50PERCENT_AS_TRUST','RIGHTTOSHARESURPLUSASSETS_50TO75PERCENT_AS_TRUST','RIGHTTOSHARESURPLUSASSETS_75TO100PERCENT_AS_TRUST') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `ownership_of_voting_rights` enum('VOTINGRIGHTS_25TO50PERCENT','VOTINGRIGHTS_50TO75PERCENT','VOTINGRIGHTS_75TO100PERCENT','VOTINGRIGHTS_25TO50PERCENT_AS_FIRM','VOTINGRIGHTS_50TO75PERCENT_AS_FIRM','VOTINGRIGHTS_75TO100PERCENT_AS_FIRM','VOTINGRIGHTS_25TO50PERCENT_AS_TRUST','VOTINGRIGHTS_50TO75PERCENT_AS_TRUST','VOTINGRIGHTS_75TO100PERCENT_AS_TRUST') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `right_to_appoint_and_remove_directors` enum('RIGHTTOAPPOINTANDREMOVEDIRECTORS','RIGHTTOAPPOINTANDREMOVEDIRECTORS_AS_FIRM','RIGHTTOAPPOINTANDREMOVEDIRECTORS_AS_TRUST','RIGHTTOAPPOINTANDREMOVEMEMBERS','RIGHTTOAPPOINTANDREMOVEMEMBERS_AS_FIRM','RIGHTTOAPPOINTANDREMOVEMEMBERS_AS_TRUST') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `significant_influence_or_control` enum('SIGINFLUENCECONTROL','SIGINFLUENCECONTROL_AS_FIRM','SIGINFLUENCECONTROL_AS_TRUST') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `psc_statement_notification` enum('PSC_EXISTS_BUT_NOT_IDENTIFIED','PSC_DETAILS_NOT_CONFIRMED','PSC_CONTACTED_BUT_NO_RESPONSE','RESTRICTIONS_NOTICE_ISSUED_TO_PSC') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `notification_date` date DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`company_member_id`),
  KEY `company_id` (`company_id`),
  KEY `type` (`type`,`corporate`),
  KEY `discriminator` (`discriminator`)
) ENGINE=InnoDB AUTO_INCREMENT=********* DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_company_monitoring` (
  `companyMonitoredId` int(11) NOT NULL AUTO_INCREMENT,
  `customerId` int(11) NOT NULL,
  `companyNumber` varchar(8) NOT NULL,
  `companyName` varchar(160) NOT NULL,
  `companyStatus` varchar(255) NOT NULL,
  `incorporationDate` date NOT NULL,
  `accountsNextDueDate` date DEFAULT NULL,
  `returnsNextDueDate` date DEFAULT NULL,
  `dtm` datetime NOT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`companyMonitoredId`),
  KEY `customerId` (`customerId`)
) ENGINE=InnoDB AUTO_INCREMENT=12821 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `ch_company_trackings` (
  `companyTrackingsId` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `serviceId` int(11) NOT NULL,
  `customerId` int(11) NOT NULL,
  `companyNumber` varchar(20) NOT NULL,
  `serviceTypeId` enum('PACKAGE_PRIVACY','PACKAGE_COMPREHENSIVE_ULTIMATE','PACKAGE_BY_GUARANTEE','PACKAGE_SOLE_TRADER_PLUS','RESERVE_COMPANY_NAME','DORMANT_COMPANY_ACCOUNTS','APOSTILLED_DOCUMENTS','CERTIFICATE_OF_GOOD_STANDING','REGISTERED_OFFICE','SERVICE_ADDRESS','NOMINEE','ANNUAL_RETURN','PSC_ONLINE_REGISTER','BUNDLE_PSC_ONLINE_REGISTER_CONFIRMATION_STATEMENT','CONFIRMATION_STATEMENT','FRAUD_PROTECTION','PACKAGE_COMPREHENSIVE','PACKAGE_ULTIMATE','SECRETARIAL_SERVICE','MAIL_FORWARDING','COMPANY_MONITORING') DEFAULT NULL,
  `notificationsEnabled` tinyint(1) NOT NULL DEFAULT '1',
  `lastChecked` datetime DEFAULT NULL,
  `dtExpires` datetime DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`companyTrackingsId`),
  UNIQUE KEY `serviceId` (`serviceId`)
) ENGINE=InnoDB AUTO_INCREMENT=42957 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `ch_dividend` (
  `dividend_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `company_id` int(10) unsigned NOT NULL,
  `type` enum('ORDINARY','PREFERENCE') CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT 'ORDINARY',
  `directors_meeting` date DEFAULT NULL,
  `end_of_acc_period` date DEFAULT NULL,
  `record_date` date DEFAULT NULL,
  `tax_rate` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `dividend` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `declared_as` enum('NET','GROSS') CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT 'NET',
  `period` enum('FINAL','INTERIM') CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT 'INTERIM',
  `paid_date` date DEFAULT NULL,
  `officer_name` varchar(150) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `officer_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`dividend_id`),
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB AUTO_INCREMENT=16950 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_dividend_share` (
  `dividend_id` int(10) unsigned NOT NULL,
  `share_class` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `currency` char(3) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `share_value` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`dividend_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_dividend_shareholder` (
  `shareholder_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `dividend_id` int(10) unsigned NOT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `premise` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `street` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `thoroughfare` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `post_town` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `county` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `postcode` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `shares` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`shareholder_id`),
  KEY `dividend_id` (`dividend_id`)
) ENGINE=InnoDB AUTO_INCREMENT=24497 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_document` (
  `document_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `form_submission_id` int(6) unsigned zerofill NOT NULL,
  `category` enum('MEMARTS','SUPPNAMEAUTH','SUPPEXISTNAME','ACCOUNTS') CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `filename` varchar(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `file_path` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `custom` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`document_id`),
  KEY `form_submission_id` (`form_submission_id`)
) ENGINE=InnoDB AUTO_INCREMENT=960831 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_envelope` (
  `envelope_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `form_submission_id` int(6) unsigned zerofill DEFAULT NULL,
  `class` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `gateway_test` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'indicates if the submission is test submission',
  `date_sent` datetime DEFAULT NULL,
  `success` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'indicates if the envelope was sent successfuly',
  `error_raised_by` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `error_number` mediumint(8) unsigned DEFAULT NULL,
  `error_type` enum('fatal','recoverable','business','warining') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `error_text` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `error_location` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`envelope_id`),
  KEY `form_submission_id` (`form_submission_id`)
) ENGINE=InnoDB AUTO_INCREMENT=********* DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_filing_history` (
  `filingHistoryId` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `companyNumber` varchar(20) NOT NULL,
  `totalCount` int(11) NOT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`filingHistoryId`)
) ENGINE=InnoDB AUTO_INCREMENT=88029 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `ch_form_submission` (
  `form_submission_id` int(6) unsigned zerofill NOT NULL AUTO_INCREMENT,
  `company_id` int(10) unsigned NOT NULL,
  `form_identifier` varchar(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `language` enum('EN','CY') CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT 'EN',
  `response` enum('ERROR','ACCEPT','REJECT','PENDING','PARKED','INTERNAL_FAILURE','WITHHOLD','DELETED') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `reject_reference` char(8) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `examiner_telephone` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `examiner_comment` mediumtext CHARACTER SET utf8 COLLATE utf8_unicode_ci,
  `date_cancelled` datetime DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  `toResend` tinyint(1) NOT NULL,
  `submitter` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`form_submission_id`),
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2109866 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_form_submission_error` (
  `form_submission_error_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `form_submission_id` int(6) unsigned zerofill NOT NULL,
  `reject_code` int(10) unsigned NOT NULL,
  `description` mediumtext CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `instance_number` varchar(12) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`form_submission_error_id`),
  KEY `form_submission_id` (`form_submission_id`)
) ENGINE=InnoDB AUTO_INCREMENT=183040 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_get_submission_status` (
  `get_submission_status_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `envelope_id` int(10) unsigned DEFAULT NULL,
  `presenter_id` varchar(50) NOT NULL,
  `submission_number` int(6) unsigned zerofill DEFAULT NULL,
  `company_number` char(8) DEFAULT NULL,
  PRIMARY KEY (`get_submission_status_id`)
) ENGINE=InnoDB AUTO_INCREMENT=23553516 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `ch_hsbc` (
  `hsbc_id` int(11) NOT NULL AUTO_INCREMENT,
  `company_id` int(11) NOT NULL,
  `date_sent` datetime NOT NULL,
  `success` tinyint(1) DEFAULT '0',
  PRIMARY KEY (`hsbc_id`),
  KEY `company_id` (`company_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2886 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_incorporation_capital` (
  `incorporation_capital_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `form_submission_id` int(6) unsigned zerofill NOT NULL,
  `currency` char(3) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `num_shares` varchar(11) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `share_class` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `prescribed_particulars` mediumtext CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `amount_paid` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `amount_unpaid` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `aggregate_nom_value` varchar(19) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  PRIMARY KEY (`incorporation_capital_id`),
  KEY `form_submission_id` (`form_submission_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3834 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_incorporation_member` (
  `incorporation_member_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `form_submission_id` int(6) unsigned zerofill NOT NULL,
  `nominee` tinyint(1) DEFAULT '0',
  `type` enum('DIR','SEC','SUB','PSC') CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `corporate` tinyint(1) NOT NULL DEFAULT '0',
  `discriminator` enum('DIR_0','DIR_1','SUB_0','SUB_1','SEC_0','SEC_1','PSC_0','PSC_1','PSC_2') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `title` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `forename` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `middle_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `surname` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `corporate_name` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `authentication` varchar(45) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `consentToAct` tinyint(1) NOT NULL DEFAULT '1',
  `isManager` tinyint(1) NOT NULL DEFAULT '0',
  `premise` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `street` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `thoroughfare` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `post_town` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `county` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `postcode` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `care_of_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `po_box` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `dob` date DEFAULT NULL,
  `nationality` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `occupation` varchar(35) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country_of_residence` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_premise` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_street` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_thoroughfare` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_post_town` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_county` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_country` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_postcode` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_secure_address_ind` tinyint(1) DEFAULT NULL,
  `identification_type` enum('EEA','NonEEA','PSC','UK','NonUK') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `place_registered` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `registration_number` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `law_governed` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `legal_form` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country_or_state` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `share_class` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `prescribed_particulars` varchar(4000) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `num_shares` varchar(11) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `amount_paid` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `amount_unpaid` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `currency` char(3) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `share_value` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `nomineeType` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `ownership_of_shares` enum('OWNERSHIPOFSHARES_25TO50PERCENT','OWNERSHIPOFSHARES_50TO75PERCENT','OWNERSHIPOFSHARES_75TO100PERCENT','OWNERSHIPOFSHARES_25TO50PERCENT_AS_FIRM','OWNERSHIPOFSHARES_50TO75PERCENT_AS_FIRM','OWNERSHIPOFSHARES_75TO100PERCENT_AS_FIRM','OWNERSHIPOFSHARES_25TO50PERCENT_AS_TRUST','OWNERSHIPOFSHARES_50TO75PERCENT_AS_TRUST','OWNERSHIPOFSHARES_75TO100PERCENT_AS_TRUST','RIGHTTOSHARESURPLUSASSETS_25TO50PERCENT','RIGHTTOSHARESURPLUSASSETS_50TO75PERCENT','RIGHTTOSHARESURPLUSASSETS_75TO100PERCENT','RIGHTTOSHARESURPLUSASSETS_25TO50PERCENT_AS_FIRM','RIGHTTOSHARESURPLUSASSETS_50TO75PERCENT_AS_FIRM','RIGHTTOSHARESURPLUSASSETS_75TO100PERCENT_AS_FIRM','RIGHTTOSHARESURPLUSASSETS_25TO50PERCENT_AS_TRUST','RIGHTTOSHARESURPLUSASSETS_50TO75PERCENT_AS_TRUST','RIGHTTOSHARESURPLUSASSETS_75TO100PERCENT_AS_TRUST') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `ownership_of_voting_rights` enum('VOTINGRIGHTS_25TO50PERCENT','VOTINGRIGHTS_50TO75PERCENT','VOTINGRIGHTS_75TO100PERCENT','VOTINGRIGHTS_25TO50PERCENT_AS_FIRM','VOTINGRIGHTS_50TO75PERCENT_AS_FIRM','VOTINGRIGHTS_75TO100PERCENT_AS_FIRM','VOTINGRIGHTS_25TO50PERCENT_AS_TRUST','VOTINGRIGHTS_50TO75PERCENT_AS_TRUST','VOTINGRIGHTS_75TO100PERCENT_AS_TRUST') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `right_to_appoint_and_remove_directors` enum('RIGHTTOAPPOINTANDREMOVEDIRECTORS','RIGHTTOAPPOINTANDREMOVEDIRECTORS_AS_FIRM','RIGHTTOAPPOINTANDREMOVEDIRECTORS_AS_TRUST','RIGHTTOAPPOINTANDREMOVEMEMBERS','RIGHTTOAPPOINTANDREMOVEMEMBERS_AS_FIRM','RIGHTTOAPPOINTANDREMOVEMEMBERS_AS_TRUST') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `significant_influence_or_control` enum('SIGINFLUENCECONTROL','SIGINFLUENCECONTROL_AS_FIRM','SIGINFLUENCECONTROL_AS_TRUST') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`incorporation_member_id`),
  KEY `form_submission_id` (`form_submission_id`),
  KEY `type` (`type`,`corporate`),
  KEY `discriminator` (`discriminator`)
) ENGINE=InnoDB AUTO_INCREMENT=3171140 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_officer_appointment` (
  `form_submission_id` int(6) unsigned zerofill NOT NULL,
  `reject_description` mediumtext CHARACTER SET utf8 COLLATE utf8_unicode_ci,
  `type` enum('DIR','SEC') CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `corporate` tinyint(1) NOT NULL DEFAULT '0',
  `designated_ind` tinyint(1) DEFAULT NULL,
  `appointment_date` date NOT NULL,
  `title` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `forename` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `middle_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `surname` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `corporate_name` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `authentication` varchar(45) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `consentToAct` tinyint(1) NOT NULL DEFAULT '1',
  `premise` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `street` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `thoroughfare` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `post_town` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `county` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `postcode` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `care_of_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `po_box` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `dob` date DEFAULT NULL,
  `nationality` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `occupation` varchar(35) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country_of_residence` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_premise` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_street` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_thoroughfare` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_post_town` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_county` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_country` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_postcode` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `residential_secure_address_ind` tinyint(1) DEFAULT NULL,
  `identification_type` enum('EEA','NonEEA','UK','NonUK') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `place_registered` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `registration_number` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `law_governed` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `legal_form` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country_or_state` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`form_submission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_officer_change` (
  `form_submission_id` int(6) unsigned zerofill NOT NULL,
  `reject_description` mediumtext CHARACTER SET utf8 COLLATE utf8_unicode_ci,
  `type` enum('DIR','SEC') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `corporate` tinyint(1) DEFAULT '0',
  `designated_ind` tinyint(1) DEFAULT NULL,
  `authentication` varchar(45) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `consentToAct` tinyint(1) NOT NULL DEFAULT '1',
  `corporate_name` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `title` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `forename` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `middle_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `surname` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `dob` date DEFAULT NULL,
  `new_title` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_forename` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_middle_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_surname` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_corporate_name` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_premise` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_street` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_thoroughfare` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_post_town` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_county` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_country` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_postcode` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_care_of_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_po_box` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_residential_premise` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_residential_street` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_residential_thoroughfare` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_residential_post_town` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_residential_county` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_residential_country` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_residential_postcode` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_residential_secure_address_ind` tinyint(1) DEFAULT NULL,
  `new_nationality` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_country_of_residence` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_occupation` varchar(35) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_identification_type` enum('EEA','NonEEA','UK','NonUK') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_place_registered` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_registration_number` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_law_governed` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `new_legal_form` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`form_submission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_officer_resignation` (
  `form_submission_id` int(6) unsigned zerofill NOT NULL,
  `reject_description` mediumtext CHARACTER SET utf8 COLLATE utf8_unicode_ci,
  `type` enum('DIR','SEC') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `corporate` tinyint(1) DEFAULT NULL,
  `corporate_name` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `surname` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `forename` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `middle_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `dob` date DEFAULT NULL,
  `resign_date` date DEFAULT NULL,
  `company_type` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`form_submission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_payment_periods_request` (
  `paymentPeriodsRequestId` int(11) NOT NULL AUTO_INCREMENT,
  `customerId` int(11) NOT NULL,
  `envelopeId` int(11) NOT NULL,
  `companyNumber` varchar(8) NOT NULL,
  `authenticationCode` varchar(6) NOT NULL,
  PRIMARY KEY (`paymentPeriodsRequestId`),
  KEY `customerId` (`customerId`),
  KEY `envelopeId` (`envelopeId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `ch_psc_cessation` (
  `form_submission_id` int(10) unsigned NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `ch_psc_change_details` (
  `form_submission_id` int(10) unsigned NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `ch_psc_notification` (
  `form_submission_id` int(10) unsigned NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `ch_psc_statement_notification` (
  `form_submission_id` int(10) unsigned NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `ch_psc_statement_withdrawal` (
  `form_submission_id` int(10) unsigned NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `ch_regus` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `customerId` int(11) NOT NULL,
  `lastName` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `firstName` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `email` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `phone` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `dialCode` int(11) NOT NULL,
  `send` tinyint(1) DEFAULT '0',
  `dtm` datetime NOT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=460 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_reminder` (
  `company_number` char(8) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `accounts_due_date` datetime DEFAULT NULL,
  `returns_due_date` datetime DEFAULT NULL,
  `accounts_last_sent_date` datetime DEFAULT NULL,
  `returns_last_sent_date` datetime DEFAULT NULL,
  `email_address` varchar(250) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `active` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`company_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_return_allotment` (
  `return_allotment_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `form_submission_id` int(6) unsigned zerofill NOT NULL,
  `share_class` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `num_shares` varchar(11) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `amount_paid` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `amount_unpaid` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `currency` char(3) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `share_value` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `consideration` mediumtext CHARACTER SET utf8 COLLATE utf8_unicode_ci,
  PRIMARY KEY (`return_allotment_id`),
  KEY `form_submission_id` (`form_submission_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_return_of_allotment_shares` (
  `form_submission_id` int(6) unsigned zerofill NOT NULL,
  `reject_description` mediumtext CHARACTER SET utf8 COLLATE utf8_unicode_ci,
  `start_period` date DEFAULT NULL,
  `end_period` date DEFAULT NULL,
  PRIMARY KEY (`form_submission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_return_shares` (
  `return_shares_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `form_submission_id` int(6) unsigned zerofill NOT NULL,
  `share_class` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `prescribed_particulars` mediumtext CHARACTER SET utf8 COLLATE utf8_unicode_ci,
  `num_shares` varchar(11) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `amount_paid` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `amount_unpaid` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `currency` char(3) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `aggregate_nom_value` varchar(19) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `allotment` varchar(11) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT '0',
  PRIMARY KEY (`return_shares_id`),
  KEY `form_submission_id` (`form_submission_id`)
) ENGINE=InnoDB AUTO_INCREMENT=57552 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_submission_queue` (
  `submissionQueueId` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `formSubmissionId` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `companyId` int(10) unsigned NOT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`submissionQueueId`),
  KEY `companyId` (`companyId`),
  KEY `formSubmissionId` (`formSubmissionId`)
) ENGINE=InnoDB AUTO_INCREMENT=110833 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_tide_leads` (
  `tideLeadId` int(11) NOT NULL AUTO_INCREMENT,
  `companyId` int(11) NOT NULL,
  `request` text NOT NULL,
  `response` text,
  `error` varchar(255) DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`tideLeadId`),
  KEY `tideLeadId` (`tideLeadId`),
  KEY `companyId` (`companyId`)
) ENGINE=InnoDB AUTO_INCREMENT=31313 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `ch_tsb_leads` (
  `tsbLeadId` int(11) NOT NULL AUTO_INCREMENT,
  `companyId` int(11) NOT NULL,
  `accountOpened` tinyint(1) NOT NULL DEFAULT '0',
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`tsbLeadId`),
  KEY `companyId` (`companyId`)
) ENGINE=InnoDB AUTO_INCREMENT=13597 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_affiliate_products` (
  `affiliateProductId` int(11) NOT NULL AUTO_INCREMENT,
  `affiliateId` int(11) NOT NULL,
  `productId` int(11) DEFAULT NULL,
  `markUp` float(10,2) NOT NULL DEFAULT '0.00',
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`affiliateProductId`),
  KEY `affiliateId` (`affiliateId`),
  KEY `productId` (`productId`)
) ENGINE=InnoDB AUTO_INCREMENT=2459 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_affiliates` (
  `affiliateId` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `statusId` tinyint(3) unsigned NOT NULL,
  `hash` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `companyName` varchar(255) DEFAULT NULL,
  `name` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `password` varchar(32) DEFAULT NULL,
  `lastName` varchar(255) DEFAULT NULL,
  `firstName` varchar(255) DEFAULT NULL,
  `email` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  `phone` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  `web` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `packagesIds` text,
  `companyColour` char(6) NOT NULL,
  `textColour` char(6) NOT NULL,
  `servicesIds` varchar(255) DEFAULT NULL,
  `googleAnalyticsTrackingTag` varchar(255) DEFAULT NULL,
  `textbox` text,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`affiliateId`),
  UNIQUE KEY `hash` (`hash`)
) ENGINE=InnoDB AUTO_INCREMENT=804 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_annual_return_service` (
  `annualReturnServiceId` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `statusId` enum('draft','completed','rejected_by_user','pending','error','rejected') NOT NULL DEFAULT 'draft',
  `companyId` int(10) unsigned NOT NULL,
  `customerId` int(10) unsigned NOT NULL,
  `productId` int(10) unsigned NOT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`annualReturnServiceId`),
  KEY `companyId` (`companyId`),
  KEY `customerId` (`customerId`)
) ENGINE=InnoDB AUTO_INCREMENT=147404 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_answers` (
  `answerId` int(11) NOT NULL AUTO_INCREMENT,
  `text` varchar(255) DEFAULT NULL,
  `additionalText` text,
  `customerId` int(11) NOT NULL,
  `dtc` datetime NOT NULL,
  `tradingStart` varchar(255) DEFAULT NULL,
  `companyId` int(11) DEFAULT NULL,
  `dtm` datetime DEFAULT NULL,
  PRIMARY KEY (`answerId`),
  KEY `customerId` (`customerId`)
) ENGINE=InnoDB AUTO_INCREMENT=235286 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_barclays_soletrader` (
  `barclaysSoletraderId` int(11) NOT NULL AUTO_INCREMENT,
  `companyName` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `establishedDate` date NOT NULL,
  `preferredContactDate` date NOT NULL,
  `email` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `titleId` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `firstName` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `lastName` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `address1` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `address2` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `address3` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `city` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `county` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `postcode` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `countryId` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `phone` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `additionalPhone` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `existingClient` int(11) DEFAULT NULL,
  `pinSentryAccess` int(11) DEFAULT NULL,
  `onlineSignUp` int(11) DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`barclaysSoletraderId`)
) ENGINE=InnoDB AUTO_INCREMENT=1388 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `cms2_cashback` (
  `cashbackId` int(11) NOT NULL AUTO_INCREMENT,
  `statusId` enum('IMPORTED','ELIGIBLE','PAID','OUTDATED') NOT NULL,
  `groupId` varchar(255) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  `companyId` int(11) NOT NULL,
  `customerId` int(11) NOT NULL,
  `packageTypeId` enum('RETAIL','WHOLESALE') CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  `cashbackTypeId` enum('BANK','CREDITS') DEFAULT NULL,
  `bankTypeId` enum('BARCLAYS','HSBC','TSB') CHARACTER SET latin1 COLLATE latin1_swedish_ci NOT NULL,
  `sortCode` varchar(10) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  `accountNumber` varchar(20) CHARACTER SET latin1 COLLATE latin1_swedish_ci DEFAULT NULL,
  `amount` int(11) DEFAULT NULL,
  `dateLeadSent` datetime DEFAULT NULL,
  `dateAccountOpened` datetime DEFAULT NULL,
  `datePaid` datetime DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  `isArchived` tinyint(1) DEFAULT NULL,
  PRIMARY KEY (`cashbackId`),
  KEY `customerId` (`customerId`),
  KEY `companyId` (`companyId`)
) ENGINE=InnoDB AUTO_INCREMENT=82425 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_company_customer` (
  `companyCustomerId` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `companyId` int(10) unsigned NOT NULL,
  `wholesalerId` int(10) unsigned NOT NULL,
  `bankTypeId` enum('BARCLAYS','TSB','CARD_ONE','HSBC','TIDE','CASHPLUS','BDG') NOT NULL,
  `preferredContactDate` date DEFAULT NULL,
  `email` varchar(255) NOT NULL,
  `titleId` varchar(255) DEFAULT NULL,
  `firstName` varchar(255) NOT NULL,
  `lastName` varchar(255) NOT NULL,
  `dateOfBirth` date DEFAULT NULL,
  `address1` varchar(255) NOT NULL,
  `address2` varchar(255) DEFAULT NULL,
  `address3` varchar(255) NOT NULL,
  `city` varchar(255) NOT NULL,
  `county` varchar(255) DEFAULT NULL,
  `postcode` varchar(255) NOT NULL,
  `countryId` varchar(255) NOT NULL,
  `phone` varchar(255) NOT NULL,
  `additionalPhone` varchar(255) DEFAULT NULL,
  `consent` tinyint(1) DEFAULT '0',
  `existingClient` int(11) DEFAULT NULL,
  `pinSentryAccess` int(11) DEFAULT NULL,
  `onlineSignUp` int(11) DEFAULT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`companyCustomerId`),
  KEY `companyId` (`companyId`),
  KEY `bankTypeId` (`bankTypeId`)
) ENGINE=InnoDB AUTO_INCREMENT=******** DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_company_settings` (
  `companySettingId` int(11) NOT NULL AUTO_INCREMENT,
  `settingKey` varchar(255) NOT NULL,
  `settingValue` text,
  `companyId` int(11) NOT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`companySettingId`),
  KEY `settingKey` (`settingKey`),
  KEY `companyId` (`companyId`)
) ENGINE=InnoDB AUTO_INCREMENT=1048971 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_company_toolkit_offers` (
  `companyToolkitOfferId` int(11) NOT NULL AUTO_INCREMENT,
  `companyId` int(11) NOT NULL,
  `toolkitOfferId` int(11) NOT NULL,
  `selected` tinyint(1) NOT NULL,
  `dateClaimed` datetime DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  `options` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`companyToolkitOfferId`),
  KEY `companyId` (`companyId`)
) ENGINE=InnoDB AUTO_INCREMENT=2305348 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_credits_added` (
  `customerId` int(11) DEFAULT NULL,
  `amount` int(11) NOT NULL DEFAULT '0',
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  KEY `customerId` (`customerId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_customer_addresses` (
  `customerAddressId` int(11) NOT NULL AUTO_INCREMENT,
  `typeId` enum('INVOICE') NOT NULL,
  `customerId` int(10) unsigned NOT NULL,
  `recipientName` varchar(255) DEFAULT NULL,
  `address1` varchar(255) NOT NULL,
  `address2` varchar(255) DEFAULT NULL,
  `city` varchar(255) NOT NULL,
  `postcode` varchar(255) NOT NULL,
  `countryId` int(10) unsigned NOT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`customerAddressId`),
  UNIQUE KEY `idx_typeId_customerId` (`typeId`,`customerId`)
) ENGINE=InnoDB AUTO_INCREMENT=10319 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_customer_auth_tokens` (
  `customerAuthTokenId` int(11) NOT NULL AUTO_INCREMENT,
  `typeId` enum('FORGOTTEN_PASSWORD','ONE_TIME_PASSWORD') NOT NULL,
  `customerId` int(10) unsigned NOT NULL,
  `token` varchar(255) NOT NULL,
  `validUntil` date NOT NULL,
  `usedOn` datetime DEFAULT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`customerAuthTokenId`)
) ENGINE=InnoDB AUTO_INCREMENT=6430123 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_customer_business_info` (
  `businessInfoId` int(11) NOT NULL AUTO_INCREMENT,
  `type` enum('PROFESSIONAL_COMPANY_FORMATION','NOT_PROFESSIONAL_COMPANY_FORMATION') CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `number` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `isPartOfRegulatedBody` tinyint(1) DEFAULT NULL,
  `regulatedBy` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `regulationNumber` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `customerId` int(10) unsigned NOT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`businessInfoId`),
  KEY `type` (`type`),
  KEY `customerId` (`customerId`)
) ENGINE=InnoDB AUTO_INCREMENT=151249 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `cms2_customer_files` (
  `customerFileId` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `fileKey` varchar(255) NOT NULL,
  `type` varchar(255) NOT NULL,
  `ext` varchar(20) NOT NULL,
  `customerId` int(11) NOT NULL,
  PRIMARY KEY (`customerFileId`),
  KEY `type` (`customerId`,`type`),
  KEY `fileKey` (`fileKey`)
) ENGINE=InnoDB AUTO_INCREMENT=26301 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_customer_log` (
  `customerActionLoggerid` int(11) NOT NULL AUTO_INCREMENT,
  `customerId` int(11) NOT NULL,
  `actionId` enum('LOG_IN','LOG_OUT','DETAILS_CHANGED','CASHBACK_PREF_CREATED','CASHBACK_PREF_CHANGED','STAFF_LOGGED_IN','LOG_IN_OTP','LOG_IN_FIREBASE','LOG_IN_FIREBASE_SOCIAL') COLLATE utf8_unicode_ci NOT NULL,
  `ip` varchar(50) COLLATE utf8_unicode_ci DEFAULT NULL,
  `context` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`customerActionLoggerid`),
  KEY `customerId` (`customerId`)
) ENGINE=InnoDB AUTO_INCREMENT=5255266 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `cms2_customer_notes` (
  `customerNoteId` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `customerId` int(10) unsigned NOT NULL,
  `userId` int(10) unsigned NOT NULL,
  `note` text NOT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`customerNoteId`)
) ENGINE=InnoDB AUTO_INCREMENT=7361 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_customer_reviews` (
  `customerReviewId` int(11) NOT NULL AUTO_INCREMENT,
  `respondentId` bigint(20) NOT NULL,
  `statusId` enum('unapproved','approved','unallowed') NOT NULL DEFAULT 'unapproved',
  `formationProcess` int(11) DEFAULT NULL,
  `communicationOurProducts` int(11) DEFAULT NULL,
  `customerService` int(11) DEFAULT NULL,
  `providingValueForMoney` int(11) DEFAULT NULL,
  `respondingPromptlyToQueries` int(11) DEFAULT NULL,
  `rateQualityOfProducts` int(11) DEFAULT NULL,
  `recomendOurServices` int(11) DEFAULT NULL,
  `suggestions` text,
  `comment` text,
  `allowed` tinyint(1) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `companyName` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`customerReviewId`),
  UNIQUE KEY `respondentId` (`respondentId`)
) ENGINE=InnoDB AUTO_INCREMENT=20291 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_deleted_companies` (
  `deletedCompanyId` int(11) NOT NULL AUTO_INCREMENT,
  `customerId` int(11) NOT NULL,
  `companyId` int(11) NOT NULL,
  `customerName` varchar(255) NOT NULL,
  `companyNumber` varchar(20) NOT NULL,
  `companyName` tinytext NOT NULL,
  `rejectMessage` text NOT NULL,
  `deletedDate` date NOT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`deletedCompanyId`),
  KEY `customerId` (`customerId`)
) ENGINE=InnoDB AUTO_INCREMENT=97862 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_email_guard_log` (
  `emailGuardLogId` int(11) NOT NULL AUTO_INCREMENT,
  `emailHash` varchar(255) NOT NULL,
  `createdAt` datetime NOT NULL,
  `emailFrom` varchar(255) NOT NULL,
  `emailTo` varchar(255) NOT NULL,
  `emailSubject` text NOT NULL,
  PRIMARY KEY (`emailGuardLogId`)
) ENGINE=InnoDB AUTO_INCREMENT=95769 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_event_details` (
  `eventDetailId` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `eventId` int(11) NOT NULL,
  `eventDetailKey` varchar(255) NOT NULL,
  `eventDetailValue` varchar(255) NOT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`eventDetailId`)
) ENGINE=InnoDB AUTO_INCREMENT=1993198 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_events` (
  `eventId` int(11) NOT NULL AUTO_INCREMENT,
  `eventKey` varchar(255) NOT NULL,
  `objectId` varchar(36) NOT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`eventId`),
  KEY `eventKey` (`eventKey`,`objectId`)
) ENGINE=InnoDB AUTO_INCREMENT=5370599 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_feedbacks` (
  `feedbackId` int(11) NOT NULL AUTO_INCREMENT,
  `text` mediumtext CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `rating` enum('Positive','Neutral','Negative') CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `nodeId` int(11) NOT NULL,
  `pageTitle` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `url` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`feedbackId`)
) ENGINE=InnoDB AUTO_INCREMENT=3523 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `cms2_feefo` (
  `feefoId` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `orderId` int(10) unsigned NOT NULL,
  `statusId` enum('WAITING','SENT','ELIGIBLE') DEFAULT NULL,
  `dateSent` datetime DEFAULT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`feefoId`),
  KEY `statusId` (`statusId`)
) ENGINE=InnoDB AUTO_INCREMENT=514518 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_files` (
  `fileId` int(11) NOT NULL AUTO_INCREMENT,
  `isActive` tinyint(4) NOT NULL,
  `isDeleted` tinyint(4) DEFAULT '0',
  `isImg` tinyint(4) DEFAULT NULL,
  `ord` int(11) NOT NULL,
  `nodeId` int(11) NOT NULL,
  `name` varchar(80) NOT NULL,
  `description` varchar(255) DEFAULT NULL,
  `ext` varchar(10) NOT NULL,
  `size` int(11) NOT NULL DEFAULT '0',
  `width` int(11) DEFAULT NULL,
  `height` int(11) DEFAULT NULL,
  `authorId` int(11) NOT NULL DEFAULT '0',
  `editorId` int(11) NOT NULL DEFAULT '0',
  `dtc` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `dtm` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`fileId`)
) ENGINE=InnoDB AUTO_INCREMENT=1541 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_fraud_protection_queue` (
  `fraudProtectionQueueId` int(11) NOT NULL AUTO_INCREMENT,
  `companyId` int(11) NOT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`fraudProtectionQueueId`)
) ENGINE=InnoDB AUTO_INCREMENT=216494 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_id_checks` (
  `idCheckId` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `points` smallint(6) NOT NULL,
  `documentTypeId` enum('PASSPORT','LICENSE','EUROPEAN_CARD','INTERNATIONAL_PASSPORT') NOT NULL,
  `response` text,
  `customerId` int(10) unsigned NOT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`idCheckId`),
  KEY `customerId` (`customerId`)
) ENGINE=InnoDB AUTO_INCREMENT=49456 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_inline_payment_log` (
  `id` varchar(255) NOT NULL,
  `type` varchar(255) NOT NULL,
  `companyId` int(11) NOT NULL,
  `customerId` int(11) NOT NULL,
  `productId` int(11) NOT NULL,
  `tokenId` int(11) DEFAULT NULL,
  `orderId` int(11) DEFAULT NULL,
  `amount` varchar(255) DEFAULT NULL,
  `url` varchar(255) NOT NULL,
  `layout` varchar(255) DEFAULT NULL,
  `ctaLabel` varchar(255) DEFAULT NULL,
  `dtc` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_journey_customer_products` (
  `journeyCustomerProductId` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `journeyCustomerId` int(10) unsigned NOT NULL,
  `productId` int(10) unsigned NOT NULL,
  `productName` varchar(255) NOT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`journeyCustomerProductId`),
  KEY `journeyCustomerId` (`journeyCustomerId`)
) ENGINE=InnoDB AUTO_INCREMENT=283789 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_journey_customers` (
  `journeyCustomerId` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `fullName` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(255) NOT NULL,
  `postcode` varchar(10) DEFAULT NULL,
  `companyName` varchar(255) DEFAULT NULL,
  `customerId` int(10) unsigned DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`journeyCustomerId`),
  KEY `customerId` (`customerId`)
) ENGINE=InnoDB AUTO_INCREMENT=92388 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_journey_products_questions` (
  `questionId` int(11) NOT NULL AUTO_INCREMENT,
  `question` varchar(500) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `answer` varchar(500) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `journeyCustomerProductId` int(11) NOT NULL,
  PRIMARY KEY (`questionId`),
  KEY `journeyCustomerProductId` (`journeyCustomerProductId`)
) ENGINE=InnoDB AUTO_INCREMENT=20509 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `cms2_languages` (
  `pk` char(2) NOT NULL,
  `name` varchar(20) NOT NULL,
  `is_active` tinyint(4) NOT NULL DEFAULT '0',
  `ord` int(11) NOT NULL DEFAULT '0',
  PRIMARY KEY (`pk`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_mailgun_events` (
  `mailgunEventId` int(11) NOT NULL AUTO_INCREMENT,
  `status` varchar(255) NOT NULL,
  `emailTemplateId` varchar(255) DEFAULT NULL,
  `recipient` varchar(255) NOT NULL,
  `customerId` int(11) DEFAULT NULL,
  `eventBody` text NOT NULL,
  `createdAt` datetime NOT NULL,
  `campaign` varchar(255) DEFAULT NULL,
  `scenario` varchar(255) DEFAULT NULL,
  `site` varchar(255) DEFAULT NULL,
  `temporaryId` varchar(255) NOT NULL,
  PRIMARY KEY (`mailgunEventId`),
  KEY `emailTemplateId` (`emailTemplateId`),
  KEY `customerId` (`customerId`),
  KEY `createdAt` (`createdAt`)
) ENGINE=InnoDB AUTO_INCREMENT=2903880 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_mr_site_codes` (
  `mrSiteCodeId` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `orderId` int(11) DEFAULT NULL,
  `productId` int(11) NOT NULL,
  `code` varchar(255) NOT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`mrSiteCodeId`),
  UNIQUE KEY `code` (`code`),
  KEY `orderId` (`orderId`)
) ENGINE=InnoDB AUTO_INCREMENT=340 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_nodes` (
  `node_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(160) DEFAULT NULL,
  `status_id` tinyint(4) NOT NULL DEFAULT '0',
  `is_deleted` tinyint(4) NOT NULL,
  `parent_id` int(11) NOT NULL DEFAULT '0',
  `front_controler` varchar(240) DEFAULT NULL,
  `admin_controler` varchar(240) NOT NULL,
  `level` int(10) unsigned NOT NULL DEFAULT '0',
  `ord` int(10) unsigned NOT NULL DEFAULT '0',
  `author_id` int(10) unsigned NOT NULL DEFAULT '0',
  `editor_id` int(10) unsigned NOT NULL DEFAULT '0',
  `dt_show` datetime DEFAULT NULL,
  `is_dt_sensitive` tinyint(3) unsigned DEFAULT '0',
  `dt_start` datetime DEFAULT NULL,
  `dt_end` datetime DEFAULT NULL,
  `excludeFromSitemap` tinyint(1) DEFAULT NULL,
  `dtc` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `dtm` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`node_id`),
  UNIQUE KEY `name` (`name`),
  KEY `is_deleted` (`is_deleted`),
  KEY `ord` (`ord`)
) ENGINE=InnoDB AUTO_INCREMENT=1919 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_offer_customer_details` (
  `customerDetailsId` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `firstName` varchar(255) NOT NULL,
  `lastName` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `postcode` varchar(255) DEFAULT NULL,
  `companyName` varchar(255) DEFAULT NULL,
  `insuranceReturnMonth` varchar(20) DEFAULT NULL,
  `productId` int(10) unsigned NOT NULL,
  `customerId` int(10) unsigned DEFAULT NULL,
  `companyId` int(11) DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`customerDetailsId`),
  KEY `productId` (`productId`,`customerId`),
  KEY `companyId` (`companyId`)
) ENGINE=InnoDB AUTO_INCREMENT=51091 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_orders` (
  `orderId` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `customerId` int(10) unsigned NOT NULL,
  `realSubtotal` float(7,2) NOT NULL DEFAULT '0.00',
  `subtotal` float(7,2) NOT NULL COMMENT 'after discount is applied',
  `discount` float(7,2) NOT NULL DEFAULT '0.00',
  `vat` float(7,2) NOT NULL,
  `credit` float(7,2) NOT NULL DEFAULT '0.00',
  `total` float(7,2) NOT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  `customerName` varchar(255) DEFAULT NULL,
  `customerAddress` varchar(255) DEFAULT NULL,
  `customerPhone` varchar(255) DEFAULT NULL,
  `customerEmail` varchar(255) DEFAULT NULL,
  `voucherId` int(11) DEFAULT NULL,
  `voucherName` varchar(255) DEFAULT NULL,
  `description` text,
  `statusId` tinyint(3) unsigned NOT NULL,
  `isRefunded` tinyint(3) unsigned DEFAULT '0',
  `refundValue` float(7,2) DEFAULT NULL,
  `refundCreditValue` float(7,2) DEFAULT NULL,
  `refundCustomerSupportLogin` varchar(30) DEFAULT NULL,
  `paymentMediumId` enum('ONSITE','PHONE','AUTO_RENEWAL','COMPLIMENTARY','NON_STANDARD','TRANSFERRED','CLAIMED') NOT NULL,
  `agentId` int(11) DEFAULT NULL,
  PRIMARY KEY (`orderId`),
  KEY `customerId` (`customerId`)
) ENGINE=InnoDB AUTO_INCREMENT=1736929 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_orders_items` (
  `orderItemId` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `orderId` int(10) unsigned NOT NULL,
  `isRefund` tinyint(3) unsigned DEFAULT '0',
  `isRefunded` tinyint(3) unsigned DEFAULT NULL,
  `refundedOrderItemId` int(11) DEFAULT NULL,
  `isFee` tinyint(1) NOT NULL DEFAULT '0',
  `productId` int(10) unsigned DEFAULT NULL,
  `productTitle` varchar(255) NOT NULL,
  `companyName` varchar(255) DEFAULT NULL,
  `companyNumber` varchar(255) DEFAULT NULL,
  `qty` smallint(6) NOT NULL,
  `price` float(7,2) NOT NULL,
  `notApplyVat` tinyint(3) unsigned NOT NULL,
  `nonVatableValue` float(7,2) unsigned NOT NULL,
  `subTotal` float(7,2) unsigned NOT NULL,
  `vat` float(7,2) unsigned NOT NULL,
  `additional` varchar(255) DEFAULT NULL,
  `totalPrice` float(7,2) NOT NULL,
  `incorporationRequired` tinyint(1) NOT NULL DEFAULT '0',
  `companyId` int(11) DEFAULT NULL,
  `markUp` float(7,2) DEFAULT '0.00',
  `dtExported` datetime DEFAULT NULL,
  `additionalInformation` json DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`orderItemId`),
  KEY `orderId_idx` (`orderId`),
  KEY `companyId` (`companyId`),
  KEY `productId` (`productId`),
  KEY `refundedOrderItemId` (`refundedOrderItemId`)
) ENGINE=InnoDB AUTO_INCREMENT=3062238 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_pages` (
  `page_id` int(11) NOT NULL AUTO_INCREMENT,
  `node_id` int(10) unsigned NOT NULL,
  `language_pk` char(2) NOT NULL,
  `title` varchar(255) NOT NULL,
  `alternative_title` varchar(240) DEFAULT NULL,
  `seo_title` varchar(240) DEFAULT NULL,
  `friendly_url` varchar(240) NOT NULL,
  `keywords` varchar(255) DEFAULT NULL,
  `description` text,
  `abstract` text,
  `text` mediumtext,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`page_id`),
  UNIQUE KEY `friendly_url` (`friendly_url`),
  KEY `node_id` (`node_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1949 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_payment_error_logs` (
  `errorId` int(11) NOT NULL AUTO_INCREMENT,
  `paymentTypeId` enum('SAGE','PAYPAL') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `errorTypeId` enum('RESUBMIT','EMPTY_RESPONSE','FAILED','CURL_ERROR') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `vendorTXCode` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `email` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `address1` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `address2` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `postcode` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country` varchar(30) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `basketPrice` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `basketItems` mediumtext CHARACTER SET utf8 COLLATE utf8_unicode_ci,
  `paymentSystem` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `errorMessage` mediumtext CHARACTER SET utf8 COLLATE utf8_unicode_ci,
  `auth3D` int(11) DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`errorId`)
) ENGINE=InnoDB AUTO_INCREMENT=39545 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `cms2_payment_form_logs` (
  `paymentFormId` int(11) NOT NULL AUTO_INCREMENT,
  `customerId` int(11) NOT NULL,
  `email` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `cardHolder` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `cardNumber` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `cardType` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `expiryDate` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `securityCode` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `address1` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `town` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `postCode` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `country` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `termscond` varchar(250) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`paymentFormId`)
) ENGINE=InnoDB AUTO_INCREMENT=63732 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `cms2_pearl_tracking_lead` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `firstName` varchar(255) NOT NULL,
  `lastName` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(255) NOT NULL,
  `postcode` varchar(255) NOT NULL,
  `companyName` varchar(255) NOT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1544 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_properties` (
  `propertyId` int(11) NOT NULL AUTO_INCREMENT,
  `nodeId` int(11) NOT NULL,
  `name` varchar(240) NOT NULL,
  `value` text NOT NULL,
  `authorId` int(11) NOT NULL,
  `editorId` int(11) NOT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`propertyId`),
  KEY `nodeId` (`nodeId`)
) ENGINE=InnoDB AUTO_INCREMENT=18459 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_register_pscs` (
  `entryId` int(11) NOT NULL AUTO_INCREMENT,
  `companyId` int(11) NOT NULL,
  `entryType` enum('PERSON_PSC','CORPORATE_PSC','LEGAL_PERSON_PSC','COMPANY_STATEMENT') NOT NULL,
  `companyStatement` enum('NO_INDIVIDUAL_OR_ENTITY_WITH_SIGNFICANT_CONTROL','STEPS_TO_FIND_PSC_NOT_YET_COMPLETED') DEFAULT NULL,
  `pscStatementNotification` enum('PSC_EXISTS_BUT_NOT_IDENTIFIED','PSC_DETAILS_NOT_CONFIRMED','PSC_CONTACTED_BUT_NO_RESPONSE','RESTRICTIONS_NOTICE_ISSUED_TO_PSC') DEFAULT NULL,
  `title` varchar(255) DEFAULT NULL,
  `forename` varchar(255) DEFAULT NULL,
  `middleName` varchar(255) DEFAULT NULL,
  `surname` varchar(255) DEFAULT NULL,
  `corporateName` varchar(255) DEFAULT NULL,
  `legalPersonName` varchar(255) DEFAULT NULL,
  `premise` varchar(255) DEFAULT NULL,
  `street` varchar(255) DEFAULT NULL,
  `thoroughfare` varchar(255) DEFAULT NULL,
  `postTown` varchar(255) DEFAULT NULL,
  `county` varchar(255) DEFAULT NULL,
  `country` varchar(255) DEFAULT NULL,
  `postcode` varchar(255) DEFAULT NULL,
  `dob` varchar(255) DEFAULT NULL,
  `nationality` varchar(255) DEFAULT NULL,
  `countryOfResidence` varchar(255) DEFAULT NULL,
  `residentialPremise` varchar(255) DEFAULT NULL,
  `residentialStreet` varchar(255) DEFAULT NULL,
  `residentialThoroughfare` varchar(255) DEFAULT NULL,
  `residentialPostTown` varchar(255) DEFAULT NULL,
  `residentialCounty` varchar(255) DEFAULT NULL,
  `residentialCountry` varchar(255) DEFAULT NULL,
  `residentialPostcode` varchar(255) DEFAULT NULL,
  `residentialSecureAddressInd` varchar(255) DEFAULT NULL,
  `placeRegistered` varchar(255) DEFAULT NULL,
  `registrationNumber` varchar(255) DEFAULT NULL,
  `lawGoverned` varchar(255) DEFAULT NULL,
  `legalForm` varchar(255) DEFAULT NULL,
  `countryOrState` varchar(255) DEFAULT NULL,
  `ownershipOfShares` enum('OWNERSHIPOFSHARES_25TO50PERCENT','OWNERSHIPOFSHARES_50TO75PERCENT','OWNERSHIPOFSHARES_75TO100PERCENT','OWNERSHIPOFSHARES_25TO50PERCENT_AS_FIRM','OWNERSHIPOFSHARES_50TO75PERCENT_AS_FIRM','OWNERSHIPOFSHARES_75TO100PERCENT_AS_FIRM','OWNERSHIPOFSHARES_25TO50PERCENT_AS_TRUST','OWNERSHIPOFSHARES_50TO75PERCENT_AS_TRUST','OWNERSHIPOFSHARES_75TO100PERCENT_AS_TRUST','RIGHTTOSHARESURPLUSASSETS_25TO50PERCENT','RIGHTTOSHARESURPLUSASSETS_50TO75PERCENT','RIGHTTOSHARESURPLUSASSETS_75TO100PERCENT','RIGHTTOSHARESURPLUSASSETS_25TO50PERCENT_AS_FIRM','RIGHTTOSHARESURPLUSASSETS_50TO75PERCENT_AS_FIRM','RIGHTTOSHARESURPLUSASSETS_75TO100PERCENT_AS_FIRM','RIGHTTOSHARESURPLUSASSETS_25TO50PERCENT_AS_TRUST','RIGHTTOSHARESURPLUSASSETS_50TO75PERCENT_AS_TRUST','RIGHTTOSHARESURPLUSASSETS_75TO100PERCENT_AS_TRUST') DEFAULT NULL,
  `ownershipOfVotingRights` enum('VOTINGRIGHTS_25TO50PERCENT','VOTINGRIGHTS_50TO75PERCENT','VOTINGRIGHTS_75TO100PERCENT','VOTINGRIGHTS_25TO50PERCENT_AS_FIRM','VOTINGRIGHTS_50TO75PERCENT_AS_FIRM','VOTINGRIGHTS_75TO100PERCENT_AS_FIRM','VOTINGRIGHTS_25TO50PERCENT_AS_TRUST','VOTINGRIGHTS_50TO75PERCENT_AS_TRUST','VOTINGRIGHTS_75TO100PERCENT_AS_TRUST') DEFAULT NULL,
  `rightToAppointAndRemoveDirectors` enum('RIGHTTOAPPOINTANDREMOVEDIRECTORS','RIGHTTOAPPOINTANDREMOVEDIRECTORS_AS_FIRM','RIGHTTOAPPOINTANDREMOVEDIRECTORS_AS_TRUST','RIGHTTOAPPOINTANDREMOVEMEMBERS','RIGHTTOAPPOINTANDREMOVEMEMBERS_AS_FIRM','RIGHTTOAPPOINTANDREMOVEMEMBERS_AS_TRUST') DEFAULT NULL,
  `significantInfluenceOrControl` enum('SIGINFLUENCECONTROL','SIGINFLUENCECONTROL_AS_FIRM','SIGINFLUENCECONTROL_AS_TRUST') DEFAULT NULL,
  `dateEntry` date DEFAULT NULL,
  `dateCancelled` date DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`entryId`)
) ENGINE=InnoDB AUTO_INCREMENT=39997 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_reserved_words` (
  `reservedWordId` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `typeId` int(11) NOT NULL,
  `word` varchar(255) NOT NULL,
  `description` text,
  `fileId` int(10) unsigned DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`reservedWordId`)
) ENGINE=InnoDB AUTO_INCREMENT=419 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_service_settings` (
  `serviceSettingId` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `serviceTypeId` enum('PACKAGE_PRIVACY','PACKAGE_COMPREHENSIVE_ULTIMATE','PACKAGE_BY_GUARANTEE','PACKAGE_SOLE_TRADER_PLUS','RESERVE_COMPANY_NAME','DORMANT_COMPANY_ACCOUNTS','APOSTILLED_DOCUMENTS','CERTIFICATE_OF_GOOD_STANDING','REGISTERED_OFFICE','SERVICE_ADDRESS','NOMINEE','ANNUAL_RETURN','PSC_ONLINE_REGISTER','BUNDLE_PSC_ONLINE_REGISTER_CONFIRMATION_STATEMENT','CONFIRMATION_STATEMENT','FRAUD_PROTECTION','PACKAGE_COMPREHENSIVE','PACKAGE_ULTIMATE','SECRETARIAL_SERVICE','MAIL_FORWARDING','COMPANY_MONITORING') NOT NULL,
  `companyId` int(10) unsigned NOT NULL,
  `isAutoRenewalEnabled` tinyint(1) NOT NULL,
  `renewalTokenId` int(11) DEFAULT NULL,
  `emailReminders` tinyint(1) NOT NULL DEFAULT '1',
  `cancellationReason` varchar(255) DEFAULT NULL,
  `cancellationCustomReason` varchar(255) DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`serviceSettingId`),
  KEY `serviceTypeId` (`serviceTypeId`),
  KEY `companyId` (`companyId`)
) ENGINE=InnoDB AUTO_INCREMENT=444369 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_services` (
  `serviceId` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `parentId` int(10) unsigned DEFAULT NULL,
  `serviceTypeId` enum('PACKAGE_PRIVACY','PACKAGE_COMPREHENSIVE_ULTIMATE','PACKAGE_BY_GUARANTEE','PACKAGE_SOLE_TRADER_PLUS','RESERVE_COMPANY_NAME','DORMANT_COMPANY_ACCOUNTS','APOSTILLED_DOCUMENTS','CERTIFICATE_OF_GOOD_STANDING','REGISTERED_OFFICE','SERVICE_ADDRESS','NOMINEE','ANNUAL_RETURN','PSC_ONLINE_REGISTER','BUNDLE_PSC_ONLINE_REGISTER_CONFIRMATION_STATEMENT','CONFIRMATION_STATEMENT','FRAUD_PROTECTION','PACKAGE_COMPREHENSIVE','PACKAGE_ULTIMATE','SECRETARIAL_SERVICE','MAIL_FORWARDING','COMPANY_MONITORING') NOT NULL,
  `productId` int(10) unsigned NOT NULL,
  `orderId` int(10) unsigned NOT NULL,
  `orderItemId` int(11) DEFAULT NULL,
  `companyId` int(10) unsigned NOT NULL,
  `renewalProductId` int(10) unsigned DEFAULT NULL,
  `serviceName` varchar(255) DEFAULT NULL,
  `stateId` enum('ENABLED','DISABLED','UPGRADED','DOWNGRADED') NOT NULL,
  `initialDuration` varchar(255) DEFAULT NULL,
  `initialDtStart` date DEFAULT NULL,
  `dtStart` date DEFAULT NULL,
  `dtExpires` date DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`serviceId`),
  KEY `parentId` (`parentId`),
  KEY `serviceTypeId` (`serviceTypeId`),
  KEY `orderId` (`orderId`),
  KEY `companyId` (`companyId`),
  KEY `dtStart` (`dtStart`),
  KEY `dtExpires` (`dtExpires`)
) ENGINE=InnoDB AUTO_INCREMENT=1569561 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_settings` (
  `settings_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `key` varchar(255) DEFAULT NULL,
  `value` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`settings_id`),
  KEY `key` (`key`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_signups` (
  `signupId` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `email` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `typeId` enum('STARTBUSINESS','SAVVYSURVEY') CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`signupId`)
) ENGINE=InnoDB AUTO_INCREMENT=9912 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `cms2_static_texts` (
  `static_text_id` int(11) NOT NULL AUTO_INCREMENT,
  `node_id` int(11) NOT NULL,
  `name` text NOT NULL,
  `author_id` int(11) NOT NULL DEFAULT '0',
  `editor_id` int(11) NOT NULL DEFAULT '0',
  `dtc` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `dtm` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`static_text_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_static_texts_langs` (
  `texts_languages_id` int(11) NOT NULL AUTO_INCREMENT,
  `texts_id` int(11) NOT NULL,
  `language_pk` char(2) NOT NULL,
  `text` text NOT NULL,
  `author_id` int(11) NOT NULL,
  `editor_id` int(11) NOT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`texts_languages_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_tax_assist_lead_tracking` (
  `leadId` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `type` enum('ACCOUNTS_PAGE','SOLETRADER_PAGE','GENERAL_PAGE') DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `postcode` varchar(255) NOT NULL,
  `phone` varchar(255) NOT NULL,
  `companyName` varchar(255) NOT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`leadId`)
) ENGINE=InnoDB AUTO_INCREMENT=12870 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_token_logs` (
  `tokenStatusId` int(11) NOT NULL AUTO_INCREMENT,
  `tokenStatus` enum('INCOMPLETE','ERROR','COMPLETE','REMOVED','REMOVED_ERROR','UPDATED') COLLATE utf8_unicode_ci NOT NULL,
  `token` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `cardNumber` varchar(4) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `name` varchar(200) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `address` mediumtext CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `email` varchar(120) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`tokenStatusId`)
) ENGINE=InnoDB AUTO_INCREMENT=800343 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `cms2_tokens` (
  `tokenId` int(11) NOT NULL AUTO_INCREMENT,
  `customerId` int(11) NOT NULL,
  `identifier` varchar(255) NOT NULL,
  `acsTransId` varchar(255) DEFAULT NULL,
  `dsTransId` varchar(255) DEFAULT NULL,
  `schemeTraceID` varchar(255) DEFAULT NULL,
  `cardNumber` varchar(4) NOT NULL,
  `cardHolder` varchar(50) DEFAULT NULL,
  `cardType` varchar(15) DEFAULT NULL,
  `cardExpiryDate` date DEFAULT NULL,
  `billingFirstnames` varchar(20) NOT NULL,
  `billingSurname` varchar(20) NOT NULL,
  `billingAddress1` varchar(100) NOT NULL,
  `billingAddress2` varchar(100) DEFAULT NULL,
  `BillingAddress3` varchar(50) DEFAULT NULL,
  `billingCity` varchar(40) NOT NULL,
  `billingPostCode` varchar(10) NOT NULL,
  `billingCountry` varchar(2) NOT NULL,
  `billingState` varchar(2) DEFAULT NULL,
  `billingPhone` varchar(20) DEFAULT NULL,
  `deliveryFirstnames` varchar(20) DEFAULT NULL,
  `deliverySurname` varchar(20) DEFAULT NULL,
  `deliveryAddress1` varchar(100) NOT NULL,
  `deliveryAddress2` varchar(100) DEFAULT NULL,
  `DeliveryAddress3` varchar(50) DEFAULT NULL,
  `deliveryCity` varchar(40) NOT NULL,
  `deliveryPostCode` varchar(10) NOT NULL,
  `deliveryCountry` varchar(2) NOT NULL,
  `deliveryState` varchar(2) DEFAULT NULL,
  `deliveryPhone` varchar(20) DEFAULT NULL,
  `isCurrent` tinyint(1) NOT NULL DEFAULT '0',
  `sageStatus` enum('ACTIVE','DELETED') NOT NULL DEFAULT 'ACTIVE',
  `description` varchar(255) DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`tokenId`),
  KEY `customerId` (`customerId`),
  KEY `cardNumber` (`cardNumber`),
  KEY `cardExpiryDate` (`cardExpiryDate`),
  KEY `identifier` (`identifier`)
) ENGINE=InnoDB AUTO_INCREMENT=436260 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_tokens_backup` (
  `tokenId` int(11) NOT NULL AUTO_INCREMENT,
  `customerId` int(11) NOT NULL,
  `identifier` varchar(255) NOT NULL,
  `cardNumber` varchar(4) NOT NULL,
  `cardHolder` varchar(50) DEFAULT NULL,
  `cardType` varchar(15) DEFAULT NULL,
  `cardExpiryDate` date DEFAULT NULL,
  `billingFirstnames` varchar(20) NOT NULL,
  `billingSurname` varchar(20) NOT NULL,
  `billingAddress1` varchar(100) NOT NULL,
  `billingAddress2` varchar(100) DEFAULT NULL,
  `billingCity` varchar(40) NOT NULL,
  `billingPostCode` varchar(10) NOT NULL,
  `billingCountry` varchar(2) NOT NULL,
  `billingState` varchar(2) DEFAULT NULL,
  `billingPhone` varchar(20) DEFAULT NULL,
  `deliveryFirstnames` varchar(20) DEFAULT NULL,
  `deliverySurname` varchar(20) DEFAULT NULL,
  `deliveryAddress1` varchar(100) NOT NULL,
  `deliveryAddress2` varchar(100) DEFAULT NULL,
  `deliveryCity` varchar(40) NOT NULL,
  `deliveryPostCode` varchar(10) NOT NULL,
  `deliveryCountry` varchar(2) NOT NULL,
  `deliveryState` varchar(2) DEFAULT NULL,
  `deliveryPhone` varchar(20) DEFAULT NULL,
  `isCurrent` tinyint(1) NOT NULL DEFAULT '0',
  `sageStatus` enum('ACTIVE','DELETED') NOT NULL DEFAULT 'ACTIVE',
  `description` varchar(255) DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`tokenId`),
  KEY `customerId` (`customerId`),
  KEY `cardNumber` (`cardNumber`),
  KEY `cardExpiryDate` (`cardExpiryDate`),
  KEY `identifier` (`identifier`)
) ENGINE=InnoDB AUTO_INCREMENT=124555 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_toolkit_offers` (
  `toolkitOfferId` int(11) NOT NULL AUTO_INCREMENT,
  `type` varchar(255) NOT NULL,
  `icon_selected` varchar(255) NOT NULL,
  `icon_unselected` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `legend` varchar(255) DEFAULT NULL,
  `description` text NOT NULL,
  `dateValidFrom` date DEFAULT NULL,
  `dateValidTo` date DEFAULT NULL,
  PRIMARY KEY (`toolkitOfferId`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_transactions` (
  `transactionId` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `customerId` int(10) unsigned NOT NULL,
  `statusId` enum('SUCCEEDED','FAILED','PROCESSING') NOT NULL,
  `typeId` int(10) unsigned NOT NULL,
  `cardHolder` varchar(255) DEFAULT NULL,
  `cardNumber` varchar(255) DEFAULT NULL,
  `cardType` varchar(255) DEFAULT NULL,
  `requestType` enum('PAYMENT','AUTHENTICATE','AUTHORIZE','REPEAT') DEFAULT NULL,
  `tokenId` int(11) DEFAULT NULL,
  `orderId` int(10) unsigned DEFAULT NULL,
  `orderCode` varchar(255) DEFAULT NULL,
  `error` varchar(255) DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `details` text COMMENT 'serialized response data',
  `request` text,
  `vendorTXCode` varchar(255) DEFAULT NULL,
  `vpsAuthCode` varchar(255) DEFAULT NULL,
  `securityKey` varchar(10) DEFAULT NULL,
  `parentTransactionId` int(11) DEFAULT NULL,
  PRIMARY KEY (`transactionId`),
  KEY `orderId` (`orderId`),
  KEY `customerId` (`customerId`),
  KEY `cardHolder` (`cardHolder`(4)),
  KEY `statusId` (`statusId`),
  KEY `typeId` (`typeId`),
  KEY `transactions_dtc_desc` (`dtc`),
  KEY `parentTransactionId` (`parentTransactionId`),
  KEY `tokenId` (`tokenId`)
) ENGINE=InnoDB AUTO_INCREMENT=1941743 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_upsell_queue` (
  `upsellQueueId` int(11) NOT NULL AUTO_INCREMENT,
  `companyId` int(11) NOT NULL,
  `productId` int(11) NOT NULL,
  `orderId` int(11) NOT NULL,
  `tokenId` int(11) DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`upsellQueueId`)
) ENGINE=InnoDB AUTO_INCREMENT=25802 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_users` (
  `user_id` int(11) NOT NULL AUTO_INCREMENT,
  `status_id` tinyint(4) NOT NULL DEFAULT '0',
  `login` varchar(100) NOT NULL,
  `password` varchar(32) NOT NULL,
  `first_name` varchar(100) NOT NULL,
  `last_name` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `role_id` int(11) NOT NULL DEFAULT '0',
  `language_pk` char(2) DEFAULT NULL,
  `author_id` int(11) NOT NULL DEFAULT '0',
  `editor_id` int(11) NOT NULL DEFAULT '0',
  `hasPhonePaymentAccess` tinyint(1) NOT NULL DEFAULT '0',
  `hasIdAccess` tinyint(1) NOT NULL DEFAULT '0',
  `dtc` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `dtm` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `login_2` (`login`),
  FULLTEXT KEY `login` (`login`)
) ENGINE=InnoDB AUTO_INCREMENT=279 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_users_logs` (
  `user_role_id` int(11) NOT NULL AUTO_INCREMENT,
  `successful` tinyint(4) NOT NULL DEFAULT '0',
  `user_id` int(10) unsigned NOT NULL,
  `bad_password` varchar(100) NOT NULL,
  `ip` varchar(12) NOT NULL,
  `browser` varchar(255) NOT NULL,
  `dtc` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`user_role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_users_roles` (
  `user_role_id` int(11) NOT NULL AUTO_INCREMENT,
  `parent_id` int(10) unsigned DEFAULT NULL,
  `key` varchar(20) NOT NULL,
  `title` varchar(80) NOT NULL,
  `author_id` int(11) NOT NULL DEFAULT '0',
  `editor_id` int(11) NOT NULL DEFAULT '0',
  `dtc` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `dtm` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  PRIMARY KEY (`user_role_id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_vo_services_queue` (
  `voServiceQueueId` int(11) NOT NULL AUTO_INCREMENT,
  `customerId` int(11) NOT NULL,
  `companyId` int(11) NOT NULL,
  `productId` int(11) NOT NULL,
  `productName` varchar(255) NOT NULL,
  `orderItemId` int(11) NOT NULL,
  `durationInMonths` int(11) NOT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`voServiceQueueId`)
) ENGINE=InnoDB AUTO_INCREMENT=34252 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_vouchers` (
  `voucherId` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) NOT NULL,
  `voucherCode` varchar(255) NOT NULL,
  `typeId` enum('PERCENT','AMOUNT') NOT NULL DEFAULT 'AMOUNT',
  `typeValue` int(10) unsigned NOT NULL,
  `minSpend` int(10) unsigned DEFAULT NULL,
  `usageLimit` int(10) unsigned DEFAULT NULL,
  `used` int(10) unsigned DEFAULT '0',
  `dtExpiry` date DEFAULT NULL,
  `dtc` datetime DEFAULT NULL,
  `dtm` datetime DEFAULT NULL,
  PRIMARY KEY (`voucherId`),
  UNIQUE KEY `key_UNIQUE` (`voucherCode`(10))
) ENGINE=InnoDB AUTO_INCREMENT=22628 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `companies_profile_data` (
  `companyId` int(11) DEFAULT NULL,
  `latest_net_assets` bigint(20) DEFAULT NULL,
  `latest_net_assets_date` date DEFAULT NULL,
  `latest_credit_rating` int(11) DEFAULT NULL,
  `latest_credit_rating_date` date DEFAULT NULL,
  `last_updated` datetime DEFAULT NULL,
  UNIQUE KEY `companyId` (`companyId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `entities_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `action` varchar(8) NOT NULL,
  `logged_at` datetime NOT NULL,
  `version` int(11) NOT NULL,
  `object_id` varchar(64) DEFAULT NULL,
  `object_class` varchar(255) NOT NULL,
  `data` longtext,
  `username` varchar(255) DEFAULT NULL,
  `userId` int(10) unsigned DEFAULT NULL,
  `customerId` int(10) unsigned DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `log_class_lookup_idx` (`object_class`),
  KEY `log_date_lookup_idx` (`logged_at`),
  KEY `log_user_lookup_idx` (`username`),
  KEY `log_object_id` (`object_id`)
) ENGINE=InnoDB AUTO_INCREMENT=87388640 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `id_company_status_events` (
  `companyStatusEventId` int(11) NOT NULL AUTO_INCREMENT,
  `companyId` int(11) NOT NULL,
  `isValid` tinyint(1) NOT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`companyStatusEventId`),
  KEY `companyId` (`companyId`),
  KEY `dtc` (`dtc`)
) ENGINE=InnoDB AUTO_INCREMENT=1147926 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `kofax_matching_companies` (
  `id` varbinary(255) NOT NULL,
  `companyName` varchar(255) NOT NULL,
  `cmsId` varchar(255) DEFAULT NULL COMMENT 'cms company number',
  `voId` varchar(255) DEFAULT NULL COMMENT 'vo service lp number',
  PRIMARY KEY (`id`),
  KEY `cmsId` (`cmsId`),
  KEY `voId` (`voId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `kofax_post_items_path` (
  `postItemId` int(11) NOT NULL,
  `postItemPath` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  UNIQUE KEY `postItemId_2` (`postItemId`),
  KEY `postItemId` (`postItemId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `lookup_business_services_offer_ids` (
  `offerId` int(11) DEFAULT NULL,
  `offer` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  UNIQUE KEY `offerId` (`offerId`),
  UNIQUE KEY `offer` (`offer`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `lookup_countries` (
  `countryId` int(11) DEFAULT NULL,
  `country` varchar(250) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  UNIQUE KEY `countryId` (`countryId`),
  UNIQUE KEY `country` (`country`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `lookup_how_heard` (
  `howHeardId` int(11) DEFAULT NULL,
  `howHeardDescription` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  UNIQUE KEY `howHeardId` (`howHeardId`),
  UNIQUE KEY `howHeardDescription` (`howHeardDescription`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `lookup_industries` (
  `industryId` int(11) DEFAULT NULL,
  `industryDescription` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  UNIQUE KEY `industryId` (`industryId`),
  UNIQUE KEY `industryDescription` (`industryDescription`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `lookup_sic_codes` (
  `sicCode` int(11) DEFAULT NULL,
  `sicDescription` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sicSection` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  UNIQUE KEY `sicCode` (`sicCode`),
  UNIQUE KEY `sicDescription` (`sicDescription`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `lookup_titles` (
  `titleId` int(11) DEFAULT NULL,
  `title` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  UNIQUE KEY `titleId` (`titleId`),
  UNIQUE KEY `title` (`title`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `lookup_transaction_types` (
  `typeId` int(11) DEFAULT NULL,
  `type` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  UNIQUE KEY `typeId` (`typeId`),
  UNIQUE KEY `type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `mailroom_events` (
  `id` char(36) NOT NULL,
  `type` char(255) NOT NULL,
  `lpOrCompanyNumber` char(255) NOT NULL,
  `site` char(255) NOT NULL,
  `companyName` char(255) NOT NULL,
  `serviceType` char(255) NOT NULL,
  `operator` char(255) NOT NULL,
  `dateScanned` datetime NOT NULL,
  `isImported` tinyint(4) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `partner_cashplus_cache_products` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `tCsReferenceNumber` int(11) NOT NULL,
  `url` varchar(512) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `partner_cashplus_leads` (
  `id` char(36) NOT NULL,
  `lead_id` int(11) NOT NULL,
  `product_id` int(11) NOT NULL,
  `tcs_reference_number` int(11) NOT NULL,
  `business_name_on_card` varchar(255) NOT NULL,
  `date_sent` datetime DEFAULT NULL,
  `request_body` text,
  `response_code` int(11) DEFAULT NULL,
  `response_body` text,
  `message` text,
  `reference_number` int(11) DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  `number_of_employees` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `phinx_log` (
  `version` bigint(20) NOT NULL,
  `migration_name` varchar(100) DEFAULT NULL,
  `start_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `end_time` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `remove_services_table` (
  `serviceId` int(10) unsigned NOT NULL DEFAULT '0',
  `serviceTypeId` enum('PACKAGE_PRIVACY','PACKAGE_COMPREHENSIVE_ULTIMATE','PACKAGE_BY_GUARANTEE','PACKAGE_SOLE_TRADER_PLUS','RESERVE_COMPANY_NAME','DORMANT_COMPANY_ACCOUNTS','APOSTILLED_DOCUMENTS','CERTIFICATE_OF_GOOD_STANDING','REGISTERED_OFFICE','SERVICE_ADDRESS','NOMINEE','ANNUAL_RETURN') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `companyId` int(10) unsigned NOT NULL,
  `parentId` int(10) unsigned DEFAULT NULL,
  `dtStart` date DEFAULT NULL,
  `dtExpires` date DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
CREATE TABLE IF NOT EXISTS `temp_lpl_sync` (
  `CoServId` int(11) NOT NULL,
  `CustomerId` int(11) DEFAULT NULL,
  `CompanyId` int(11) DEFAULT NULL,
  `OrderId` int(11) DEFAULT NULL,
  `OrderItemId` int(11) DEFAULT NULL,
  `ProductId` int(11) DEFAULT NULL,
  `PaidDate` datetime NOT NULL,
  `StartDate` datetime NOT NULL,
  `ExpiryDate` datetime NOT NULL,
  `Del` tinyint(4) NOT NULL,
  KEY `OrderItemId` (`OrderItemId`),
  KEY `CustomerId` (`CustomerId`),
  KEY `ProductId` (`ProductId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `temp_lpl_update_dates` (
  `CoServId` int(11) DEFAULT NULL,
  `CustomerId` int(11) DEFAULT NULL,
  `CompanyId` int(11) DEFAULT NULL,
  `OrderId` int(11) DEFAULT NULL,
  `OrderItemId` int(11) DEFAULT NULL,
  `ProductId` int(11) DEFAULT NULL,
  `StartDate` datetime NOT NULL,
  `ExpiryDate` datetime NOT NULL,
  KEY `OrderItemId` (`OrderItemId`),
  KEY `ProductId` (`ProductId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_email_logs` (
  `emailLogId` int(11) NOT NULL AUTO_INCREMENT,
  `sent` tinyint(1) DEFAULT NULL,
  `uniqueId` varchar(255) DEFAULT NULL,
  `customerId` int(11) DEFAULT NULL,
  `emailName` varchar(255) DEFAULT NULL,
  `emailNodeId` int(11) DEFAULT NULL,
  `emailFrom` varchar(255) DEFAULT NULL,
  `emailTo` varchar(255) DEFAULT NULL,
  `emailSubject` varchar(255) DEFAULT NULL,
  `emailBody` text,
  `attachment` tinyint(1) NOT NULL DEFAULT '0',
  `attachmentData` text,
  `gateway` varchar(255) DEFAULT NULL,
  `additionalData` text,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`emailLogId`),
  KEY `customerId` (`customerId`),
  KEY `emailNodeId` (`emailNodeId`),
  KEY `emailTo` (`emailTo`),
  KEY `emailName` (`emailName`),
  KEY `uniqueId` (`uniqueId`)
) ENGINE=InnoDB AUTO_INCREMENT=11289119 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_customers` (
  `customerId` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `statusId` int(10) unsigned NOT NULL,
  `roleId` varchar(100) NOT NULL,
  `tagId` enum('RETAIL','COSEC','AFFILIATE','ICAEW','ICAEWWHOLESALE','GENERALWHOLESALE','TAXASSISTWHOLESALE') DEFAULT 'RETAIL',
  `icaewId` varchar(200) DEFAULT NULL,
  `myDetailsQuestion` tinyint(1) DEFAULT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `migrated` tinyint(1) DEFAULT NULL,
  `titleId` int(10) unsigned DEFAULT NULL,
  `firstName` varchar(255) DEFAULT NULL,
  `middleName` varchar(255) DEFAULT NULL,
  `lastName` varchar(255) DEFAULT NULL,
  `dateOfBirth` date DEFAULT NULL,
  `companyName` varchar(255) DEFAULT NULL,
  `address1` varchar(255) DEFAULT NULL,
  `address2` varchar(255) DEFAULT NULL,
  `address3` varchar(255) DEFAULT NULL,
  `city` varchar(255) DEFAULT NULL,
  `county` varchar(255) DEFAULT NULL,
  `postcode` varchar(20) DEFAULT NULL,
  `countryId` int(11) DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `additionalPhone` varchar(14) DEFAULT NULL,
  `isSubscribed` int(10) unsigned DEFAULT NULL,
  `howHeardId` int(10) unsigned DEFAULT NULL,
  `industryId` int(11) DEFAULT NULL,
  `credit` float(7,2) unsigned DEFAULT NULL,
  `affiliateId` int(10) unsigned DEFAULT NULL,
  `activeCompanies` tinyint(1) DEFAULT NULL,
  `monitoredActiveCompanies` tinyint(1) DEFAULT NULL,
  `cashbackType` enum('BANK','CREDITS') DEFAULT NULL,
  `accountNumber` varchar(255) DEFAULT NULL,
  `sortCode` varchar(255) DEFAULT NULL,
  `isIdCheckRequired` tinyint(1) NOT NULL DEFAULT '0',
  `dtIdValidated` datetime DEFAULT NULL,
  `isPartOfTheIdCheck` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'part of the new id check system',
  `canBelongToCustomerOnlyId` tinyint(1) NOT NULL DEFAULT '0',
  `mailboxAttachmentsEnabled` int(11) NOT NULL DEFAULT '1',
  `partOfJumio` tinyint(1) DEFAULT NULL,
  `partOfCredas` tinyint(1) DEFAULT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`customerId`),
  UNIQUE KEY `email` (`email`),
  KEY `affiliateId` (`affiliateId`),
  KEY `firstName` (`firstName`(4)),
  KEY `firstName_2` (`firstName`(4)),
  KEY `statusId` (`statusId`),
  KEY `tagId` (`tagId`),
  KEY `email_2` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=531104 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_register_members` (
  `registerMemberId` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `address` longtext CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  `companyId` int(11) NOT NULL,
  `dateEntry` date DEFAULT NULL,
  `dateCeased` date DEFAULT NULL,
  PRIMARY KEY (`registerMemberId`),
  KEY `IDX_82D9C2D2480E723` (`companyId`)
) ENGINE=InnoDB AUTO_INCREMENT=9884 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `cms2_email_logs` (
  `emailLogId` int(11) NOT NULL AUTO_INCREMENT,
  `sent` tinyint(1) DEFAULT NULL,
  `uniqueId` varchar(255) DEFAULT NULL,
  `customerId` int(11) DEFAULT NULL,
  `emailName` varchar(255) DEFAULT NULL,
  `emailNodeId` int(11) DEFAULT NULL,
  `emailFrom` varchar(255) DEFAULT NULL,
  `emailTo` varchar(255) DEFAULT NULL,
  `emailSubject` varchar(255) DEFAULT NULL,
  `emailBody` text,
  `attachment` tinyint(1) NOT NULL DEFAULT '0',
  `attachmentData` text,
  `gateway` varchar(255) DEFAULT NULL,
  `additionalData` text,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`emailLogId`),
  KEY `customerId` (`customerId`),
  KEY `emailNodeId` (`emailNodeId`),
  KEY `emailTo` (`emailTo`),
  KEY `emailName` (`emailName`),
  KEY `uniqueId` (`uniqueId`)
) ENGINE=InnoDB AUTO_INCREMENT=11289119 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `ch_company` (
  `company_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `customer_id` int(10) unsigned NOT NULL,
  `product_id` int(10) unsigned DEFAULT NULL,
  `is_certificate_printed` tinyint(1) NOT NULL DEFAULT '0',
  `is_bronze_cover_letter_printed` tinyint(1) NOT NULL DEFAULT '0',
  `is_ma_printed` tinyint(1) DEFAULT '0',
  `is_ma_cover_letter_printed` tinyint(1) DEFAULT '0',
  `company_name` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `company_number` char(16) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `authentication_code` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `incorporation_date` date DEFAULT NULL,
  `dissolution_date` date DEFAULT NULL,
  `company_category` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `jurisdiction` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `made_up_date` date DEFAULT NULL,
  `premise` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `street` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `thoroughfare` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `post_town` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `county` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country` enum('GB-ENG','GB-WLS','GB-SCT','GB-NIR','GBR','UNDEF') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `postcode` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `care_of_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `po_box` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_premise` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_street` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_thoroughfare` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_post_town` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_county` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_country` enum('GB-ENG','GB-WLS','GB-SCT','GB-NIR','GBR','UNDEF') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_postcode` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_care_of_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_po_box` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_code1` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_code2` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_code3` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_code4` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_description` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `company_status` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country_of_origin` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `accounts_ref_date` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `accounts_overdue` tinyint(1) DEFAULT NULL,
  `accounts_next_period_start_date` date DEFAULT NULL,
  `accounts_next_period_end_date` date DEFAULT NULL,
  `accounts_next_due_date` date DEFAULT NULL,
  `accounts_last_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `accounts_last_period_start_date` date DEFAULT NULL,
  `accounts_last_made_up_date` date DEFAULT NULL,
  `returns_next_made_up_date` date DEFAULT NULL,
  `returns_next_due_date` date DEFAULT NULL,
  `returns_last_made_up_date` date DEFAULT NULL,
  `returns_overdue` tinyint(1) DEFAULT NULL,
  `dca_id` int(10) unsigned DEFAULT NULL,
  `order_id` int(10) unsigned DEFAULT NULL,
  `registered_office_id` int(10) unsigned DEFAULT NULL,
  `service_address_id` int(11) DEFAULT NULL,
  `nominee_director_id` int(10) unsigned DEFAULT NULL,
  `nominee_secretary_id` int(10) unsigned DEFAULT NULL,
  `nominee_subscriber_id` int(10) unsigned DEFAULT NULL,
  `annual_return_id` int(10) unsigned DEFAULT NULL,
  `change_name_id` int(10) unsigned DEFAULT NULL,
  `ereminder_id` tinyint(1) DEFAULT '0',
  `document_date` date DEFAULT NULL COMMENT 'this is received from get document request',
  `document_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'this is received from get document request',
  `accepted_date` date DEFAULT NULL,
  `cash_back_amount` int(11) NOT NULL DEFAULT '0',
  `no_psc_reason` enum('NO_INDIVIDUAL_OR_ENTITY_WITH_SIGNFICANT_CONTROL','STEPS_TO_FIND_PSC_NOT_YET_COMPLETED') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `registered_email_address` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `locked` tinyint(1) NOT NULL DEFAULT '0',
  `hidden` tinyint(1) NOT NULL DEFAULT '0',
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `etag` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `dtLastSynced` datetime DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`company_id`),
  KEY `order_id_idx` (`order_id`),
  KEY `customer_id` (`customer_id`),
  KEY `company_number` (`company_number`(8)),
  KEY `company_name` (`company_name`(6))
) ENGINE=InnoDB AUTO_INCREMENT=1131463 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `id_events` (
  `idEvent` int(11) NOT NULL AUTO_INCREMENT,
  `entityId` varchar(255) NOT NULL,
  `typeId` varchar(255) NOT NULL,
  `actionBy` varchar(255) NOT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`idEvent`),
  KEY `entityId` (`entityId`)
) ENGINE=InnoDB AUTO_INCREMENT=1938608 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_email_logs` (
  `emailLogId` int(11) NOT NULL AUTO_INCREMENT,
  `sent` tinyint(1) DEFAULT NULL,
  `uniqueId` varchar(255) DEFAULT NULL,
  `customerId` int(11) DEFAULT NULL,
  `emailName` varchar(255) DEFAULT NULL,
  `emailNodeId` int(11) DEFAULT NULL,
  `emailFrom` varchar(255) DEFAULT NULL,
  `emailTo` varchar(255) DEFAULT NULL,
  `emailSubject` varchar(255) DEFAULT NULL,
  `emailBody` text,
  `attachment` tinyint(1) NOT NULL DEFAULT '0',
  `attachmentData` text,
  `gateway` varchar(255) DEFAULT NULL,
  `additionalData` text,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`emailLogId`),
  KEY `customerId` (`customerId`),
  KEY `emailNodeId` (`emailNodeId`),
  KEY `emailTo` (`emailTo`),
  KEY `emailName` (`emailName`),
  KEY `uniqueId` (`uniqueId`)
) ENGINE=InnoDB AUTO_INCREMENT=11289119 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `id_events` (
  `idEvent` int(11) NOT NULL AUTO_INCREMENT,
  `entityId` varchar(255) NOT NULL,
  `typeId` varchar(255) NOT NULL,
  `actionBy` varchar(255) NOT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`idEvent`),
  KEY `entityId` (`entityId`)
) ENGINE=InnoDB AUTO_INCREMENT=1938608 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `id_events` (
  `idEvent` int(11) NOT NULL AUTO_INCREMENT,
  `entityId` varchar(255) NOT NULL,
  `typeId` varchar(255) NOT NULL,
  `actionBy` varchar(255) NOT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`idEvent`),
  KEY `entityId` (`entityId`)
) ENGINE=InnoDB AUTO_INCREMENT=1938608 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `id_events` (
  `idEvent` int(11) NOT NULL AUTO_INCREMENT,
  `entityId` varchar(255) NOT NULL,
  `typeId` varchar(255) NOT NULL,
  `actionBy` varchar(255) NOT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`idEvent`),
  KEY `entityId` (`entityId`)
) ENGINE=InnoDB AUTO_INCREMENT=1938608 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `id_3global_verifications` (
  `dataId` varchar(255) NOT NULL,
  `score` int(11) NOT NULL,
  `authenticationId` varchar(255) NOT NULL,
  `customerReference` varchar(255) NOT NULL,
  `resultCodes` mediumtext,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`dataId`,`dtc`),
  KEY `dataId` (`dataId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_customers` (
  `customerId` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `statusId` int(10) unsigned NOT NULL,
  `roleId` varchar(100) NOT NULL,
  `tagId` enum('RETAIL','COSEC','AFFILIATE','ICAEW','ICAEWWHOLESALE','GENERALWHOLESALE','TAXASSISTWHOLESALE') DEFAULT 'RETAIL',
  `icaewId` varchar(200) DEFAULT NULL,
  `myDetailsQuestion` tinyint(1) DEFAULT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `migrated` tinyint(1) DEFAULT NULL,
  `titleId` int(10) unsigned DEFAULT NULL,
  `firstName` varchar(255) DEFAULT NULL,
  `middleName` varchar(255) DEFAULT NULL,
  `lastName` varchar(255) DEFAULT NULL,
  `dateOfBirth` date DEFAULT NULL,
  `companyName` varchar(255) DEFAULT NULL,
  `address1` varchar(255) DEFAULT NULL,
  `address2` varchar(255) DEFAULT NULL,
  `address3` varchar(255) DEFAULT NULL,
  `city` varchar(255) DEFAULT NULL,
  `county` varchar(255) DEFAULT NULL,
  `postcode` varchar(20) DEFAULT NULL,
  `countryId` int(11) DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `additionalPhone` varchar(14) DEFAULT NULL,
  `isSubscribed` int(10) unsigned DEFAULT NULL,
  `howHeardId` int(10) unsigned DEFAULT NULL,
  `industryId` int(11) DEFAULT NULL,
  `credit` float(7,2) unsigned DEFAULT NULL,
  `affiliateId` int(10) unsigned DEFAULT NULL,
  `activeCompanies` tinyint(1) DEFAULT NULL,
  `monitoredActiveCompanies` tinyint(1) DEFAULT NULL,
  `cashbackType` enum('BANK','CREDITS') DEFAULT NULL,
  `accountNumber` varchar(255) DEFAULT NULL,
  `sortCode` varchar(255) DEFAULT NULL,
  `isIdCheckRequired` tinyint(1) NOT NULL DEFAULT '0',
  `dtIdValidated` datetime DEFAULT NULL,
  `isPartOfTheIdCheck` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'part of the new id check system',
  `canBelongToCustomerOnlyId` tinyint(1) NOT NULL DEFAULT '0',
  `mailboxAttachmentsEnabled` int(11) NOT NULL DEFAULT '1',
  `partOfJumio` tinyint(1) DEFAULT NULL,
  `partOfCredas` tinyint(1) DEFAULT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`customerId`),
  UNIQUE KEY `email` (`email`),
  KEY `affiliateId` (`affiliateId`),
  KEY `firstName` (`firstName`(4)),
  KEY `firstName_2` (`firstName`(4)),
  KEY `statusId` (`statusId`),
  KEY `tagId` (`tagId`),
  KEY `email_2` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=531104 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `kofax_post_items` (
  `postItemId` int(11) NOT NULL AUTO_INCREMENT,
  `companyName` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `typeId` enum('HMRC','OTHER','COMPANIES_HOUSE') CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `statusId` enum('RELEASED','NOT_RELEASED') CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL DEFAULT 'NOT_RELEASED',
  `companyNumber` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `lpNumber` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `batchId` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `batchName` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `agent` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`postItemId`),
  KEY `companyNumber` (`companyNumber`),
  KEY `dtc` (`dtc`),
  FULLTEXT KEY `companyName` (`companyName`)
) ENGINE=InnoDB AUTO_INCREMENT=1796848490 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `cms2_email_logs` (
  `emailLogId` int(11) NOT NULL AUTO_INCREMENT,
  `sent` tinyint(1) DEFAULT NULL,
  `uniqueId` varchar(255) DEFAULT NULL,
  `customerId` int(11) DEFAULT NULL,
  `emailName` varchar(255) DEFAULT NULL,
  `emailNodeId` int(11) DEFAULT NULL,
  `emailFrom` varchar(255) DEFAULT NULL,
  `emailTo` varchar(255) DEFAULT NULL,
  `emailSubject` varchar(255) DEFAULT NULL,
  `emailBody` text,
  `attachment` tinyint(1) NOT NULL DEFAULT '0',
  `attachmentData` text,
  `gateway` varchar(255) DEFAULT NULL,
  `additionalData` text,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`emailLogId`),
  KEY `customerId` (`customerId`),
  KEY `emailNodeId` (`emailNodeId`),
  KEY `emailTo` (`emailTo`),
  KEY `emailName` (`emailName`),
  KEY `uniqueId` (`uniqueId`)
) ENGINE=InnoDB AUTO_INCREMENT=11289119 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `ch_company` (
  `company_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `customer_id` int(10) unsigned NOT NULL,
  `product_id` int(10) unsigned DEFAULT NULL,
  `is_certificate_printed` tinyint(1) NOT NULL DEFAULT '0',
  `is_bronze_cover_letter_printed` tinyint(1) NOT NULL DEFAULT '0',
  `is_ma_printed` tinyint(1) DEFAULT '0',
  `is_ma_cover_letter_printed` tinyint(1) DEFAULT '0',
  `company_name` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `company_number` char(16) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `authentication_code` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `incorporation_date` date DEFAULT NULL,
  `dissolution_date` date DEFAULT NULL,
  `company_category` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `jurisdiction` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `made_up_date` date DEFAULT NULL,
  `premise` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `street` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `thoroughfare` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `post_town` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `county` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country` enum('GB-ENG','GB-WLS','GB-SCT','GB-NIR','GBR','UNDEF') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `postcode` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `care_of_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `po_box` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_premise` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_street` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_thoroughfare` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_post_town` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_county` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_country` enum('GB-ENG','GB-WLS','GB-SCT','GB-NIR','GBR','UNDEF') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_postcode` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_care_of_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_po_box` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_code1` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_code2` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_code3` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_code4` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_description` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `company_status` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country_of_origin` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `accounts_ref_date` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `accounts_overdue` tinyint(1) DEFAULT NULL,
  `accounts_next_period_start_date` date DEFAULT NULL,
  `accounts_next_period_end_date` date DEFAULT NULL,
  `accounts_next_due_date` date DEFAULT NULL,
  `accounts_last_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `accounts_last_period_start_date` date DEFAULT NULL,
  `accounts_last_made_up_date` date DEFAULT NULL,
  `returns_next_made_up_date` date DEFAULT NULL,
  `returns_next_due_date` date DEFAULT NULL,
  `returns_last_made_up_date` date DEFAULT NULL,
  `returns_overdue` tinyint(1) DEFAULT NULL,
  `dca_id` int(10) unsigned DEFAULT NULL,
  `order_id` int(10) unsigned DEFAULT NULL,
  `registered_office_id` int(10) unsigned DEFAULT NULL,
  `service_address_id` int(11) DEFAULT NULL,
  `nominee_director_id` int(10) unsigned DEFAULT NULL,
  `nominee_secretary_id` int(10) unsigned DEFAULT NULL,
  `nominee_subscriber_id` int(10) unsigned DEFAULT NULL,
  `annual_return_id` int(10) unsigned DEFAULT NULL,
  `change_name_id` int(10) unsigned DEFAULT NULL,
  `ereminder_id` tinyint(1) DEFAULT '0',
  `document_date` date DEFAULT NULL COMMENT 'this is received from get document request',
  `document_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'this is received from get document request',
  `accepted_date` date DEFAULT NULL,
  `cash_back_amount` int(11) NOT NULL DEFAULT '0',
  `no_psc_reason` enum('NO_INDIVIDUAL_OR_ENTITY_WITH_SIGNFICANT_CONTROL','STEPS_TO_FIND_PSC_NOT_YET_COMPLETED') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `registered_email_address` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `locked` tinyint(1) NOT NULL DEFAULT '0',
  `hidden` tinyint(1) NOT NULL DEFAULT '0',
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `etag` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `dtLastSynced` datetime DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`company_id`),
  KEY `order_id_idx` (`order_id`),
  KEY `customer_id` (`customer_id`),
  KEY `company_number` (`company_number`(8)),
  KEY `company_name` (`company_name`(6))
) ENGINE=InnoDB AUTO_INCREMENT=1131463 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_company` (
  `company_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `customer_id` int(10) unsigned NOT NULL,
  `product_id` int(10) unsigned DEFAULT NULL,
  `is_certificate_printed` tinyint(1) NOT NULL DEFAULT '0',
  `is_bronze_cover_letter_printed` tinyint(1) NOT NULL DEFAULT '0',
  `is_ma_printed` tinyint(1) DEFAULT '0',
  `is_ma_cover_letter_printed` tinyint(1) DEFAULT '0',
  `company_name` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `company_number` char(16) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `authentication_code` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `incorporation_date` date DEFAULT NULL,
  `dissolution_date` date DEFAULT NULL,
  `company_category` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `jurisdiction` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `made_up_date` date DEFAULT NULL,
  `premise` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `street` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `thoroughfare` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `post_town` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `county` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country` enum('GB-ENG','GB-WLS','GB-SCT','GB-NIR','GBR','UNDEF') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `postcode` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `care_of_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `po_box` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_premise` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_street` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_thoroughfare` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_post_town` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_county` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_country` enum('GB-ENG','GB-WLS','GB-SCT','GB-NIR','GBR','UNDEF') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_postcode` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_care_of_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_po_box` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_code1` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_code2` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_code3` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_code4` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_description` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `company_status` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country_of_origin` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `accounts_ref_date` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `accounts_overdue` tinyint(1) DEFAULT NULL,
  `accounts_next_period_start_date` date DEFAULT NULL,
  `accounts_next_period_end_date` date DEFAULT NULL,
  `accounts_next_due_date` date DEFAULT NULL,
  `accounts_last_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `accounts_last_period_start_date` date DEFAULT NULL,
  `accounts_last_made_up_date` date DEFAULT NULL,
  `returns_next_made_up_date` date DEFAULT NULL,
  `returns_next_due_date` date DEFAULT NULL,
  `returns_last_made_up_date` date DEFAULT NULL,
  `returns_overdue` tinyint(1) DEFAULT NULL,
  `dca_id` int(10) unsigned DEFAULT NULL,
  `order_id` int(10) unsigned DEFAULT NULL,
  `registered_office_id` int(10) unsigned DEFAULT NULL,
  `service_address_id` int(11) DEFAULT NULL,
  `nominee_director_id` int(10) unsigned DEFAULT NULL,
  `nominee_secretary_id` int(10) unsigned DEFAULT NULL,
  `nominee_subscriber_id` int(10) unsigned DEFAULT NULL,
  `annual_return_id` int(10) unsigned DEFAULT NULL,
  `change_name_id` int(10) unsigned DEFAULT NULL,
  `ereminder_id` tinyint(1) DEFAULT '0',
  `document_date` date DEFAULT NULL COMMENT 'this is received from get document request',
  `document_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'this is received from get document request',
  `accepted_date` date DEFAULT NULL,
  `cash_back_amount` int(11) NOT NULL DEFAULT '0',
  `no_psc_reason` enum('NO_INDIVIDUAL_OR_ENTITY_WITH_SIGNFICANT_CONTROL','STEPS_TO_FIND_PSC_NOT_YET_COMPLETED') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `registered_email_address` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `locked` tinyint(1) NOT NULL DEFAULT '0',
  `hidden` tinyint(1) NOT NULL DEFAULT '0',
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `etag` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `dtLastSynced` datetime DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`company_id`),
  KEY `order_id_idx` (`order_id`),
  KEY `customer_id` (`customer_id`),
  KEY `company_number` (`company_number`(8)),
  KEY `company_name` (`company_name`(6))
) ENGINE=InnoDB AUTO_INCREMENT=1131463 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `cms2_customers` (
  `customerId` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `statusId` int(10) unsigned NOT NULL,
  `roleId` varchar(100) NOT NULL,
  `tagId` enum('RETAIL','COSEC','AFFILIATE','ICAEW','ICAEWWHOLESALE','GENERALWHOLESALE','TAXASSISTWHOLESALE') DEFAULT 'RETAIL',
  `icaewId` varchar(200) DEFAULT NULL,
  `myDetailsQuestion` tinyint(1) DEFAULT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `migrated` tinyint(1) DEFAULT NULL,
  `titleId` int(10) unsigned DEFAULT NULL,
  `firstName` varchar(255) DEFAULT NULL,
  `middleName` varchar(255) DEFAULT NULL,
  `lastName` varchar(255) DEFAULT NULL,
  `dateOfBirth` date DEFAULT NULL,
  `companyName` varchar(255) DEFAULT NULL,
  `address1` varchar(255) DEFAULT NULL,
  `address2` varchar(255) DEFAULT NULL,
  `address3` varchar(255) DEFAULT NULL,
  `city` varchar(255) DEFAULT NULL,
  `county` varchar(255) DEFAULT NULL,
  `postcode` varchar(20) DEFAULT NULL,
  `countryId` int(11) DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `additionalPhone` varchar(14) DEFAULT NULL,
  `isSubscribed` int(10) unsigned DEFAULT NULL,
  `howHeardId` int(10) unsigned DEFAULT NULL,
  `industryId` int(11) DEFAULT NULL,
  `credit` float(7,2) unsigned DEFAULT NULL,
  `affiliateId` int(10) unsigned DEFAULT NULL,
  `activeCompanies` tinyint(1) DEFAULT NULL,
  `monitoredActiveCompanies` tinyint(1) DEFAULT NULL,
  `cashbackType` enum('BANK','CREDITS') DEFAULT NULL,
  `accountNumber` varchar(255) DEFAULT NULL,
  `sortCode` varchar(255) DEFAULT NULL,
  `isIdCheckRequired` tinyint(1) NOT NULL DEFAULT '0',
  `dtIdValidated` datetime DEFAULT NULL,
  `isPartOfTheIdCheck` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'part of the new id check system',
  `canBelongToCustomerOnlyId` tinyint(1) NOT NULL DEFAULT '0',
  `mailboxAttachmentsEnabled` int(11) NOT NULL DEFAULT '1',
  `partOfJumio` tinyint(1) DEFAULT NULL,
  `partOfCredas` tinyint(1) DEFAULT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`customerId`),
  UNIQUE KEY `email` (`email`),
  KEY `affiliateId` (`affiliateId`),
  KEY `firstName` (`firstName`(4)),
  KEY `firstName_2` (`firstName`(4)),
  KEY `statusId` (`statusId`),
  KEY `tagId` (`tagId`),
  KEY `email_2` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=531104 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `business_services_lead` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `company_id` int(11) NOT NULL,
  `offer_id` int(11) NOT NULL,
  `valid` tinyint(1) NOT NULL,
  `lastError` varchar(255) DEFAULT NULL,
  `lastErrorOn` datetime DEFAULT NULL,
  `processed` tinyint(1) NOT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `company_id` (`company_id`),
  KEY `offer_id` (`offer_id`,`processed`)
) ENGINE=InnoDB AUTO_INCREMENT=277618 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `business_services_lead` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `company_id` int(11) NOT NULL,
  `offer_id` int(11) NOT NULL,
  `valid` tinyint(1) NOT NULL,
  `lastError` varchar(255) DEFAULT NULL,
  `lastErrorOn` datetime DEFAULT NULL,
  `processed` tinyint(1) NOT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `company_id` (`company_id`),
  KEY `offer_id` (`offer_id`,`processed`)
) ENGINE=InnoDB AUTO_INCREMENT=277618 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_email_logs` (
  `emailLogId` int(11) NOT NULL AUTO_INCREMENT,
  `sent` tinyint(1) DEFAULT NULL,
  `uniqueId` varchar(255) DEFAULT NULL,
  `customerId` int(11) DEFAULT NULL,
  `emailName` varchar(255) DEFAULT NULL,
  `emailNodeId` int(11) DEFAULT NULL,
  `emailFrom` varchar(255) DEFAULT NULL,
  `emailTo` varchar(255) DEFAULT NULL,
  `emailSubject` varchar(255) DEFAULT NULL,
  `emailBody` text,
  `attachment` tinyint(1) NOT NULL DEFAULT '0',
  `attachmentData` text,
  `gateway` varchar(255) DEFAULT NULL,
  `additionalData` text,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`emailLogId`),
  KEY `customerId` (`customerId`),
  KEY `emailNodeId` (`emailNodeId`),
  KEY `emailTo` (`emailTo`),
  KEY `emailName` (`emailName`),
  KEY `uniqueId` (`uniqueId`)
) ENGINE=InnoDB AUTO_INCREMENT=11289119 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_customers` (
  `customerId` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `statusId` int(10) unsigned NOT NULL,
  `roleId` varchar(100) NOT NULL,
  `tagId` enum('RETAIL','COSEC','AFFILIATE','ICAEW','ICAEWWHOLESALE','GENERALWHOLESALE','TAXASSISTWHOLESALE') DEFAULT 'RETAIL',
  `icaewId` varchar(200) DEFAULT NULL,
  `myDetailsQuestion` tinyint(1) DEFAULT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `migrated` tinyint(1) DEFAULT NULL,
  `titleId` int(10) unsigned DEFAULT NULL,
  `firstName` varchar(255) DEFAULT NULL,
  `middleName` varchar(255) DEFAULT NULL,
  `lastName` varchar(255) DEFAULT NULL,
  `dateOfBirth` date DEFAULT NULL,
  `companyName` varchar(255) DEFAULT NULL,
  `address1` varchar(255) DEFAULT NULL,
  `address2` varchar(255) DEFAULT NULL,
  `address3` varchar(255) DEFAULT NULL,
  `city` varchar(255) DEFAULT NULL,
  `county` varchar(255) DEFAULT NULL,
  `postcode` varchar(20) DEFAULT NULL,
  `countryId` int(11) DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `additionalPhone` varchar(14) DEFAULT NULL,
  `isSubscribed` int(10) unsigned DEFAULT NULL,
  `howHeardId` int(10) unsigned DEFAULT NULL,
  `industryId` int(11) DEFAULT NULL,
  `credit` float(7,2) unsigned DEFAULT NULL,
  `affiliateId` int(10) unsigned DEFAULT NULL,
  `activeCompanies` tinyint(1) DEFAULT NULL,
  `monitoredActiveCompanies` tinyint(1) DEFAULT NULL,
  `cashbackType` enum('BANK','CREDITS') DEFAULT NULL,
  `accountNumber` varchar(255) DEFAULT NULL,
  `sortCode` varchar(255) DEFAULT NULL,
  `isIdCheckRequired` tinyint(1) NOT NULL DEFAULT '0',
  `dtIdValidated` datetime DEFAULT NULL,
  `isPartOfTheIdCheck` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'part of the new id check system',
  `canBelongToCustomerOnlyId` tinyint(1) NOT NULL DEFAULT '0',
  `mailboxAttachmentsEnabled` int(11) NOT NULL DEFAULT '1',
  `partOfJumio` tinyint(1) DEFAULT NULL,
  `partOfCredas` tinyint(1) DEFAULT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`customerId`),
  UNIQUE KEY `email` (`email`),
  KEY `affiliateId` (`affiliateId`),
  KEY `firstName` (`firstName`(4)),
  KEY `firstName_2` (`firstName`(4)),
  KEY `statusId` (`statusId`),
  KEY `tagId` (`tagId`),
  KEY `email_2` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=531104 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `ch_company` (
  `company_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `customer_id` int(10) unsigned NOT NULL,
  `product_id` int(10) unsigned DEFAULT NULL,
  `is_certificate_printed` tinyint(1) NOT NULL DEFAULT '0',
  `is_bronze_cover_letter_printed` tinyint(1) NOT NULL DEFAULT '0',
  `is_ma_printed` tinyint(1) DEFAULT '0',
  `is_ma_cover_letter_printed` tinyint(1) DEFAULT '0',
  `company_name` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `company_number` char(16) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `authentication_code` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `incorporation_date` date DEFAULT NULL,
  `dissolution_date` date DEFAULT NULL,
  `company_category` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `jurisdiction` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `made_up_date` date DEFAULT NULL,
  `premise` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `street` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `thoroughfare` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `post_town` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `county` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country` enum('GB-ENG','GB-WLS','GB-SCT','GB-NIR','GBR','UNDEF') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `postcode` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `care_of_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `po_box` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_premise` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_street` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_thoroughfare` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_post_town` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_county` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_country` enum('GB-ENG','GB-WLS','GB-SCT','GB-NIR','GBR','UNDEF') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_postcode` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_care_of_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_po_box` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_code1` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_code2` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_code3` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_code4` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_description` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `company_status` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country_of_origin` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `accounts_ref_date` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `accounts_overdue` tinyint(1) DEFAULT NULL,
  `accounts_next_period_start_date` date DEFAULT NULL,
  `accounts_next_period_end_date` date DEFAULT NULL,
  `accounts_next_due_date` date DEFAULT NULL,
  `accounts_last_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `accounts_last_period_start_date` date DEFAULT NULL,
  `accounts_last_made_up_date` date DEFAULT NULL,
  `returns_next_made_up_date` date DEFAULT NULL,
  `returns_next_due_date` date DEFAULT NULL,
  `returns_last_made_up_date` date DEFAULT NULL,
  `returns_overdue` tinyint(1) DEFAULT NULL,
  `dca_id` int(10) unsigned DEFAULT NULL,
  `order_id` int(10) unsigned DEFAULT NULL,
  `registered_office_id` int(10) unsigned DEFAULT NULL,
  `service_address_id` int(11) DEFAULT NULL,
  `nominee_director_id` int(10) unsigned DEFAULT NULL,
  `nominee_secretary_id` int(10) unsigned DEFAULT NULL,
  `nominee_subscriber_id` int(10) unsigned DEFAULT NULL,
  `annual_return_id` int(10) unsigned DEFAULT NULL,
  `change_name_id` int(10) unsigned DEFAULT NULL,
  `ereminder_id` tinyint(1) DEFAULT '0',
  `document_date` date DEFAULT NULL COMMENT 'this is received from get document request',
  `document_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'this is received from get document request',
  `accepted_date` date DEFAULT NULL,
  `cash_back_amount` int(11) NOT NULL DEFAULT '0',
  `no_psc_reason` enum('NO_INDIVIDUAL_OR_ENTITY_WITH_SIGNFICANT_CONTROL','STEPS_TO_FIND_PSC_NOT_YET_COMPLETED') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `registered_email_address` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `locked` tinyint(1) NOT NULL DEFAULT '0',
  `hidden` tinyint(1) NOT NULL DEFAULT '0',
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `etag` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `dtLastSynced` datetime DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`company_id`),
  KEY `order_id_idx` (`order_id`),
  KEY `customer_id` (`customer_id`),
  KEY `company_number` (`company_number`(8)),
  KEY `company_name` (`company_name`(6))
) ENGINE=InnoDB AUTO_INCREMENT=1131463 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `cms2_customers` (
  `customerId` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `statusId` int(10) unsigned NOT NULL,
  `roleId` varchar(100) NOT NULL,
  `tagId` enum('RETAIL','COSEC','AFFILIATE','ICAEW','ICAEWWHOLESALE','GENERALWHOLESALE','TAXASSISTWHOLESALE') DEFAULT 'RETAIL',
  `icaewId` varchar(200) DEFAULT NULL,
  `myDetailsQuestion` tinyint(1) DEFAULT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `migrated` tinyint(1) DEFAULT NULL,
  `titleId` int(10) unsigned DEFAULT NULL,
  `firstName` varchar(255) DEFAULT NULL,
  `middleName` varchar(255) DEFAULT NULL,
  `lastName` varchar(255) DEFAULT NULL,
  `dateOfBirth` date DEFAULT NULL,
  `companyName` varchar(255) DEFAULT NULL,
  `address1` varchar(255) DEFAULT NULL,
  `address2` varchar(255) DEFAULT NULL,
  `address3` varchar(255) DEFAULT NULL,
  `city` varchar(255) DEFAULT NULL,
  `county` varchar(255) DEFAULT NULL,
  `postcode` varchar(20) DEFAULT NULL,
  `countryId` int(11) DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `additionalPhone` varchar(14) DEFAULT NULL,
  `isSubscribed` int(10) unsigned DEFAULT NULL,
  `howHeardId` int(10) unsigned DEFAULT NULL,
  `industryId` int(11) DEFAULT NULL,
  `credit` float(7,2) unsigned DEFAULT NULL,
  `affiliateId` int(10) unsigned DEFAULT NULL,
  `activeCompanies` tinyint(1) DEFAULT NULL,
  `monitoredActiveCompanies` tinyint(1) DEFAULT NULL,
  `cashbackType` enum('BANK','CREDITS') DEFAULT NULL,
  `accountNumber` varchar(255) DEFAULT NULL,
  `sortCode` varchar(255) DEFAULT NULL,
  `isIdCheckRequired` tinyint(1) NOT NULL DEFAULT '0',
  `dtIdValidated` datetime DEFAULT NULL,
  `isPartOfTheIdCheck` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'part of the new id check system',
  `canBelongToCustomerOnlyId` tinyint(1) NOT NULL DEFAULT '0',
  `mailboxAttachmentsEnabled` int(11) NOT NULL DEFAULT '1',
  `partOfJumio` tinyint(1) DEFAULT NULL,
  `partOfCredas` tinyint(1) DEFAULT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`customerId`),
  UNIQUE KEY `email` (`email`),
  KEY `affiliateId` (`affiliateId`),
  KEY `firstName` (`firstName`(4)),
  KEY `firstName_2` (`firstName`(4)),
  KEY `statusId` (`statusId`),
  KEY `tagId` (`tagId`),
  KEY `email_2` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=531104 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `ch_company` (
  `company_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `customer_id` int(10) unsigned NOT NULL,
  `product_id` int(10) unsigned DEFAULT NULL,
  `is_certificate_printed` tinyint(1) NOT NULL DEFAULT '0',
  `is_bronze_cover_letter_printed` tinyint(1) NOT NULL DEFAULT '0',
  `is_ma_printed` tinyint(1) DEFAULT '0',
  `is_ma_cover_letter_printed` tinyint(1) DEFAULT '0',
  `company_name` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `company_number` char(16) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `authentication_code` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `incorporation_date` date DEFAULT NULL,
  `dissolution_date` date DEFAULT NULL,
  `company_category` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `jurisdiction` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `made_up_date` date DEFAULT NULL,
  `premise` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `street` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `thoroughfare` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `post_town` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `county` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country` enum('GB-ENG','GB-WLS','GB-SCT','GB-NIR','GBR','UNDEF') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `postcode` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `care_of_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `po_box` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_premise` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_street` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_thoroughfare` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_post_town` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_county` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_country` enum('GB-ENG','GB-WLS','GB-SCT','GB-NIR','GBR','UNDEF') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_postcode` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_care_of_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_po_box` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_code1` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_code2` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_code3` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_code4` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_description` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `company_status` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country_of_origin` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `accounts_ref_date` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `accounts_overdue` tinyint(1) DEFAULT NULL,
  `accounts_next_period_start_date` date DEFAULT NULL,
  `accounts_next_period_end_date` date DEFAULT NULL,
  `accounts_next_due_date` date DEFAULT NULL,
  `accounts_last_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `accounts_last_period_start_date` date DEFAULT NULL,
  `accounts_last_made_up_date` date DEFAULT NULL,
  `returns_next_made_up_date` date DEFAULT NULL,
  `returns_next_due_date` date DEFAULT NULL,
  `returns_last_made_up_date` date DEFAULT NULL,
  `returns_overdue` tinyint(1) DEFAULT NULL,
  `dca_id` int(10) unsigned DEFAULT NULL,
  `order_id` int(10) unsigned DEFAULT NULL,
  `registered_office_id` int(10) unsigned DEFAULT NULL,
  `service_address_id` int(11) DEFAULT NULL,
  `nominee_director_id` int(10) unsigned DEFAULT NULL,
  `nominee_secretary_id` int(10) unsigned DEFAULT NULL,
  `nominee_subscriber_id` int(10) unsigned DEFAULT NULL,
  `annual_return_id` int(10) unsigned DEFAULT NULL,
  `change_name_id` int(10) unsigned DEFAULT NULL,
  `ereminder_id` tinyint(1) DEFAULT '0',
  `document_date` date DEFAULT NULL COMMENT 'this is received from get document request',
  `document_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'this is received from get document request',
  `accepted_date` date DEFAULT NULL,
  `cash_back_amount` int(11) NOT NULL DEFAULT '0',
  `no_psc_reason` enum('NO_INDIVIDUAL_OR_ENTITY_WITH_SIGNFICANT_CONTROL','STEPS_TO_FIND_PSC_NOT_YET_COMPLETED') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `registered_email_address` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `locked` tinyint(1) NOT NULL DEFAULT '0',
  `hidden` tinyint(1) NOT NULL DEFAULT '0',
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `etag` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `dtLastSynced` datetime DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`company_id`),
  KEY `order_id_idx` (`order_id`),
  KEY `customer_id` (`customer_id`),
  KEY `company_number` (`company_number`(8)),
  KEY `company_name` (`company_name`(6))
) ENGINE=InnoDB AUTO_INCREMENT=1131463 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `ch_company` (
  `company_id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `customer_id` int(10) unsigned NOT NULL,
  `product_id` int(10) unsigned DEFAULT NULL,
  `is_certificate_printed` tinyint(1) NOT NULL DEFAULT '0',
  `is_bronze_cover_letter_printed` tinyint(1) NOT NULL DEFAULT '0',
  `is_ma_printed` tinyint(1) DEFAULT '0',
  `is_ma_cover_letter_printed` tinyint(1) DEFAULT '0',
  `company_name` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `company_number` char(16) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `authentication_code` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `incorporation_date` date DEFAULT NULL,
  `dissolution_date` date DEFAULT NULL,
  `company_category` varchar(160) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `jurisdiction` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `made_up_date` date DEFAULT NULL,
  `premise` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `street` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `thoroughfare` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `post_town` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `county` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country` enum('GB-ENG','GB-WLS','GB-SCT','GB-NIR','GBR','UNDEF') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `postcode` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `care_of_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `po_box` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_premise` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_street` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_thoroughfare` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_post_town` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_county` varchar(50) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_country` enum('GB-ENG','GB-WLS','GB-SCT','GB-NIR','GBR','UNDEF') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_postcode` varchar(15) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_care_of_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sail_po_box` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_code1` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_code2` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_code3` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_code4` char(6) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `sic_description` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `company_status` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `country_of_origin` varchar(20) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `accounts_ref_date` varchar(10) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `accounts_overdue` tinyint(1) DEFAULT NULL,
  `accounts_next_period_start_date` date DEFAULT NULL,
  `accounts_next_period_end_date` date DEFAULT NULL,
  `accounts_next_due_date` date DEFAULT NULL,
  `accounts_last_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `accounts_last_period_start_date` date DEFAULT NULL,
  `accounts_last_made_up_date` date DEFAULT NULL,
  `returns_next_made_up_date` date DEFAULT NULL,
  `returns_next_due_date` date DEFAULT NULL,
  `returns_last_made_up_date` date DEFAULT NULL,
  `returns_overdue` tinyint(1) DEFAULT NULL,
  `dca_id` int(10) unsigned DEFAULT NULL,
  `order_id` int(10) unsigned DEFAULT NULL,
  `registered_office_id` int(10) unsigned DEFAULT NULL,
  `service_address_id` int(11) DEFAULT NULL,
  `nominee_director_id` int(10) unsigned DEFAULT NULL,
  `nominee_secretary_id` int(10) unsigned DEFAULT NULL,
  `nominee_subscriber_id` int(10) unsigned DEFAULT NULL,
  `annual_return_id` int(10) unsigned DEFAULT NULL,
  `change_name_id` int(10) unsigned DEFAULT NULL,
  `ereminder_id` tinyint(1) DEFAULT '0',
  `document_date` date DEFAULT NULL COMMENT 'this is received from get document request',
  `document_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'this is received from get document request',
  `accepted_date` date DEFAULT NULL,
  `cash_back_amount` int(11) NOT NULL DEFAULT '0',
  `no_psc_reason` enum('NO_INDIVIDUAL_OR_ENTITY_WITH_SIGNFICANT_CONTROL','STEPS_TO_FIND_PSC_NOT_YET_COMPLETED') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `registered_email_address` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `locked` tinyint(1) NOT NULL DEFAULT '0',
  `hidden` tinyint(1) NOT NULL DEFAULT '0',
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `etag` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `dtLastSynced` datetime DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`company_id`),
  KEY `order_id_idx` (`order_id`),
  KEY `customer_id` (`customer_id`),
  KEY `company_number` (`company_number`(8)),
  KEY `company_name` (`company_name`(6))
) ENGINE=InnoDB AUTO_INCREMENT=1131463 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `cms2_customers` (
  `customerId` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `statusId` int(10) unsigned NOT NULL,
  `roleId` varchar(100) NOT NULL,
  `tagId` enum('RETAIL','COSEC','AFFILIATE','ICAEW','ICAEWWHOLESALE','GENERALWHOLESALE','TAXASSISTWHOLESALE') DEFAULT 'RETAIL',
  `icaewId` varchar(200) DEFAULT NULL,
  `myDetailsQuestion` tinyint(1) DEFAULT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `migrated` tinyint(1) DEFAULT NULL,
  `titleId` int(10) unsigned DEFAULT NULL,
  `firstName` varchar(255) DEFAULT NULL,
  `middleName` varchar(255) DEFAULT NULL,
  `lastName` varchar(255) DEFAULT NULL,
  `dateOfBirth` date DEFAULT NULL,
  `companyName` varchar(255) DEFAULT NULL,
  `address1` varchar(255) DEFAULT NULL,
  `address2` varchar(255) DEFAULT NULL,
  `address3` varchar(255) DEFAULT NULL,
  `city` varchar(255) DEFAULT NULL,
  `county` varchar(255) DEFAULT NULL,
  `postcode` varchar(20) DEFAULT NULL,
  `countryId` int(11) DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `additionalPhone` varchar(14) DEFAULT NULL,
  `isSubscribed` int(10) unsigned DEFAULT NULL,
  `howHeardId` int(10) unsigned DEFAULT NULL,
  `industryId` int(11) DEFAULT NULL,
  `credit` float(7,2) unsigned DEFAULT NULL,
  `affiliateId` int(10) unsigned DEFAULT NULL,
  `activeCompanies` tinyint(1) DEFAULT NULL,
  `monitoredActiveCompanies` tinyint(1) DEFAULT NULL,
  `cashbackType` enum('BANK','CREDITS') DEFAULT NULL,
  `accountNumber` varchar(255) DEFAULT NULL,
  `sortCode` varchar(255) DEFAULT NULL,
  `isIdCheckRequired` tinyint(1) NOT NULL DEFAULT '0',
  `dtIdValidated` datetime DEFAULT NULL,
  `isPartOfTheIdCheck` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'part of the new id check system',
  `canBelongToCustomerOnlyId` tinyint(1) NOT NULL DEFAULT '0',
  `mailboxAttachmentsEnabled` int(11) NOT NULL DEFAULT '1',
  `partOfJumio` tinyint(1) DEFAULT NULL,
  `partOfCredas` tinyint(1) DEFAULT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`customerId`),
  UNIQUE KEY `email` (`email`),
  KEY `affiliateId` (`affiliateId`),
  KEY `firstName` (`firstName`(4)),
  KEY `firstName_2` (`firstName`(4)),
  KEY `statusId` (`statusId`),
  KEY `tagId` (`tagId`),
  KEY `email_2` (`email`)
) ENGINE=InnoDB AUTO_INCREMENT=531104 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `bdg_leads` (
  `bdgLeadId` char(36) NOT NULL,
  `leadId` int(11) NOT NULL,
  `advertId` varchar(255) NOT NULL,
  `advertiserName` varchar(255) NOT NULL,
  `followUpType` varchar(255) NOT NULL,
  `followUpValue` varchar(255) NOT NULL,
  `termsAgreed` tinyint(1) NOT NULL,
  `fscsAgreed` tinyint(1) NOT NULL DEFAULT '1',
  `referral` varchar(255) DEFAULT NULL,
  `preferredContactTime` varchar(255) DEFAULT NULL,
  `nameOnCard` varchar(20) DEFAULT NULL,
  `externalId` varchar(255) DEFAULT NULL,
  `dateSent` datetime DEFAULT NULL,
  `request` text,
  `responseCode` int(11) DEFAULT NULL,
  `response` text,
  `amount` float(10,2) DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`bdgLeadId`),
  KEY `leadId` (`leadId`),
  CONSTRAINT `bdg_leads_ibfk_1` FOREIGN KEY (`leadId`) REFERENCES `business_services_lead` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `bdg_leads` (
  `bdgLeadId` char(36) NOT NULL,
  `leadId` int(11) NOT NULL,
  `advertId` varchar(255) NOT NULL,
  `advertiserName` varchar(255) NOT NULL,
  `followUpType` varchar(255) NOT NULL,
  `followUpValue` varchar(255) NOT NULL,
  `termsAgreed` tinyint(1) NOT NULL,
  `fscsAgreed` tinyint(1) NOT NULL DEFAULT '1',
  `referral` varchar(255) DEFAULT NULL,
  `preferredContactTime` varchar(255) DEFAULT NULL,
  `nameOnCard` varchar(20) DEFAULT NULL,
  `externalId` varchar(255) DEFAULT NULL,
  `dateSent` datetime DEFAULT NULL,
  `request` text,
  `responseCode` int(11) DEFAULT NULL,
  `response` text,
  `amount` float(10,2) DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`bdgLeadId`),
  KEY `leadId` (`leadId`),
  CONSTRAINT `bdg_leads_ibfk_1` FOREIGN KEY (`leadId`) REFERENCES `business_services_lead` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_register_share_classes` (
  `registerShareClassId` int(11) NOT NULL AUTO_INCREMENT,
  `classType` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `currencyIso` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `price` double NOT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  `registerMemberId` int(11) DEFAULT NULL,
  PRIMARY KEY (`registerShareClassId`),
  KEY `IDX_9FC5FF205D61E3DF` (`registerMemberId`),
  CONSTRAINT `FK_9FC5FF205D61E3DF` FOREIGN KEY (`registerMemberId`) REFERENCES `cms2_register_members` (`registerMemberId`)
) ENGINE=InnoDB AUTO_INCREMENT=4792 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `cms2_register_share_classes` (
  `registerShareClassId` int(11) NOT NULL AUTO_INCREMENT,
  `classType` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `currencyIso` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `price` double NOT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  `registerMemberId` int(11) DEFAULT NULL,
  PRIMARY KEY (`registerShareClassId`),
  KEY `IDX_9FC5FF205D61E3DF` (`registerMemberId`),
  CONSTRAINT `FK_9FC5FF205D61E3DF` FOREIGN KEY (`registerMemberId`) REFERENCES `cms2_register_members` (`registerMemberId`)
) ENGINE=InnoDB AUTO_INCREMENT=4792 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `id_jumio_events` (
  `eventId` varchar(255) NOT NULL,
  `typeId` varchar(255) NOT NULL,
  `jumioReference` varchar(255) DEFAULT NULL,
  `validationType` enum('ID_PHOTO','ID_ADDRESS') DEFAULT NULL,
  `entityId` varchar(255) DEFAULT NULL,
  `customerId` int(10) unsigned DEFAULT NULL,
  `companyId` int(10) unsigned DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  `parentId` varchar(255) DEFAULT NULL,
  `errorCode` varchar(255) DEFAULT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`eventId`),
  KEY `jumioReference` (`jumioReference`),
  KEY `entityId` (`entityId`),
  KEY `companyId` (`companyId`),
  KEY `customerId` (`customerId`),
  KEY `parentId` (`parentId`),
  CONSTRAINT `id_jumio_events_ibfk_1` FOREIGN KEY (`companyId`) REFERENCES `ch_company` (`company_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `id_jumio_events_ibfk_2` FOREIGN KEY (`customerId`) REFERENCES `cms2_customers` (`customerId`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `id_jumio_events_ibfk_3` FOREIGN KEY (`parentId`) REFERENCES `id_jumio_events` (`eventId`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `id_validations` (
  `validationId` int(11) NOT NULL AUTO_INCREMENT,
  `entityId` varchar(255) NOT NULL,
  `pass` tinyint(1) NOT NULL,
  `diligenceLevelName` varchar(255) NOT NULL,
  `checkName` varchar(255) NOT NULL,
  `checkType` varchar(255) NOT NULL,
  `nullified` tinyint(1) NOT NULL,
  `originalData` text NOT NULL,
  `verifiedData` text,
  `customerId` int(10) unsigned DEFAULT NULL,
  `companyId` int(11) DEFAULT NULL,
  `reference` varchar(255) DEFAULT NULL,
  `id3GlobalVerificationDataId` varchar(255) DEFAULT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`validationId`),
  KEY `entityId` (`entityId`,`checkName`),
  KEY `companyId` (`companyId`),
  KEY `id3GlobalVerificationDataId` (`id3GlobalVerificationDataId`),
  KEY `customerId` (`customerId`),
  KEY `reference` (`reference`),
  CONSTRAINT `id_validations_ibfk_1` FOREIGN KEY (`id3GlobalVerificationDataId`) REFERENCES `id_3global_verifications` (`dataId`),
  CONSTRAINT `id_validations_ibfk_2` FOREIGN KEY (`customerId`) REFERENCES `cms2_customers` (`customerId`)
) ENGINE=InnoDB AUTO_INCREMENT=1781330 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `id_validations` (
  `validationId` int(11) NOT NULL AUTO_INCREMENT,
  `entityId` varchar(255) NOT NULL,
  `pass` tinyint(1) NOT NULL,
  `diligenceLevelName` varchar(255) NOT NULL,
  `checkName` varchar(255) NOT NULL,
  `checkType` varchar(255) NOT NULL,
  `nullified` tinyint(1) NOT NULL,
  `originalData` text NOT NULL,
  `verifiedData` text,
  `customerId` int(10) unsigned DEFAULT NULL,
  `companyId` int(11) DEFAULT NULL,
  `reference` varchar(255) DEFAULT NULL,
  `id3GlobalVerificationDataId` varchar(255) DEFAULT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`validationId`),
  KEY `entityId` (`entityId`,`checkName`),
  KEY `companyId` (`companyId`),
  KEY `id3GlobalVerificationDataId` (`id3GlobalVerificationDataId`),
  KEY `customerId` (`customerId`),
  KEY `reference` (`reference`),
  CONSTRAINT `id_validations_ibfk_1` FOREIGN KEY (`id3GlobalVerificationDataId`) REFERENCES `id_3global_verifications` (`dataId`),
  CONSTRAINT `id_validations_ibfk_2` FOREIGN KEY (`customerId`) REFERENCES `cms2_customers` (`customerId`)
) ENGINE=InnoDB AUTO_INCREMENT=1781330 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `id_validations` (
  `validationId` int(11) NOT NULL AUTO_INCREMENT,
  `entityId` varchar(255) NOT NULL,
  `pass` tinyint(1) NOT NULL,
  `diligenceLevelName` varchar(255) NOT NULL,
  `checkName` varchar(255) NOT NULL,
  `checkType` varchar(255) NOT NULL,
  `nullified` tinyint(1) NOT NULL,
  `originalData` text NOT NULL,
  `verifiedData` text,
  `customerId` int(10) unsigned DEFAULT NULL,
  `companyId` int(11) DEFAULT NULL,
  `reference` varchar(255) DEFAULT NULL,
  `id3GlobalVerificationDataId` varchar(255) DEFAULT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`validationId`),
  KEY `entityId` (`entityId`,`checkName`),
  KEY `companyId` (`companyId`),
  KEY `id3GlobalVerificationDataId` (`id3GlobalVerificationDataId`),
  KEY `customerId` (`customerId`),
  KEY `reference` (`reference`),
  CONSTRAINT `id_validations_ibfk_1` FOREIGN KEY (`id3GlobalVerificationDataId`) REFERENCES `id_3global_verifications` (`dataId`),
  CONSTRAINT `id_validations_ibfk_2` FOREIGN KEY (`customerId`) REFERENCES `cms2_customers` (`customerId`)
) ENGINE=InnoDB AUTO_INCREMENT=1781330 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `id_validations` (
  `validationId` int(11) NOT NULL AUTO_INCREMENT,
  `entityId` varchar(255) NOT NULL,
  `pass` tinyint(1) NOT NULL,
  `diligenceLevelName` varchar(255) NOT NULL,
  `checkName` varchar(255) NOT NULL,
  `checkType` varchar(255) NOT NULL,
  `nullified` tinyint(1) NOT NULL,
  `originalData` text NOT NULL,
  `verifiedData` text,
  `customerId` int(10) unsigned DEFAULT NULL,
  `companyId` int(11) DEFAULT NULL,
  `reference` varchar(255) DEFAULT NULL,
  `id3GlobalVerificationDataId` varchar(255) DEFAULT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`validationId`),
  KEY `entityId` (`entityId`,`checkName`),
  KEY `companyId` (`companyId`),
  KEY `id3GlobalVerificationDataId` (`id3GlobalVerificationDataId`),
  KEY `customerId` (`customerId`),
  KEY `reference` (`reference`),
  CONSTRAINT `id_validations_ibfk_1` FOREIGN KEY (`id3GlobalVerificationDataId`) REFERENCES `id_3global_verifications` (`dataId`),
  CONSTRAINT `id_validations_ibfk_2` FOREIGN KEY (`customerId`) REFERENCES `cms2_customers` (`customerId`)
) ENGINE=InnoDB AUTO_INCREMENT=1781330 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `bdg_leads` (
  `bdgLeadId` char(36) NOT NULL,
  `leadId` int(11) NOT NULL,
  `advertId` varchar(255) NOT NULL,
  `advertiserName` varchar(255) NOT NULL,
  `followUpType` varchar(255) NOT NULL,
  `followUpValue` varchar(255) NOT NULL,
  `termsAgreed` tinyint(1) NOT NULL,
  `fscsAgreed` tinyint(1) NOT NULL DEFAULT '1',
  `referral` varchar(255) DEFAULT NULL,
  `preferredContactTime` varchar(255) DEFAULT NULL,
  `nameOnCard` varchar(20) DEFAULT NULL,
  `externalId` varchar(255) DEFAULT NULL,
  `dateSent` datetime DEFAULT NULL,
  `request` text,
  `responseCode` int(11) DEFAULT NULL,
  `response` text,
  `amount` float(10,2) DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`bdgLeadId`),
  KEY `leadId` (`leadId`),
  CONSTRAINT `bdg_leads_ibfk_1` FOREIGN KEY (`leadId`) REFERENCES `business_services_lead` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `id_jumio_events` (
  `eventId` varchar(255) NOT NULL,
  `typeId` varchar(255) NOT NULL,
  `jumioReference` varchar(255) DEFAULT NULL,
  `validationType` enum('ID_PHOTO','ID_ADDRESS') DEFAULT NULL,
  `entityId` varchar(255) DEFAULT NULL,
  `customerId` int(10) unsigned DEFAULT NULL,
  `companyId` int(10) unsigned DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  `parentId` varchar(255) DEFAULT NULL,
  `errorCode` varchar(255) DEFAULT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`eventId`),
  KEY `jumioReference` (`jumioReference`),
  KEY `entityId` (`entityId`),
  KEY `companyId` (`companyId`),
  KEY `customerId` (`customerId`),
  KEY `parentId` (`parentId`),
  CONSTRAINT `id_jumio_events_ibfk_1` FOREIGN KEY (`companyId`) REFERENCES `ch_company` (`company_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `id_jumio_events_ibfk_2` FOREIGN KEY (`customerId`) REFERENCES `cms2_customers` (`customerId`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `id_jumio_events_ibfk_3` FOREIGN KEY (`parentId`) REFERENCES `id_jumio_events` (`eventId`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `id_jumio_events` (
  `eventId` varchar(255) NOT NULL,
  `typeId` varchar(255) NOT NULL,
  `jumioReference` varchar(255) DEFAULT NULL,
  `validationType` enum('ID_PHOTO','ID_ADDRESS') DEFAULT NULL,
  `entityId` varchar(255) DEFAULT NULL,
  `customerId` int(10) unsigned DEFAULT NULL,
  `companyId` int(10) unsigned DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  `parentId` varchar(255) DEFAULT NULL,
  `errorCode` varchar(255) DEFAULT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`eventId`),
  KEY `jumioReference` (`jumioReference`),
  KEY `entityId` (`entityId`),
  KEY `companyId` (`companyId`),
  KEY `customerId` (`customerId`),
  KEY `parentId` (`parentId`),
  CONSTRAINT `id_jumio_events_ibfk_1` FOREIGN KEY (`companyId`) REFERENCES `ch_company` (`company_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `id_jumio_events_ibfk_2` FOREIGN KEY (`customerId`) REFERENCES `cms2_customers` (`customerId`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `id_jumio_events_ibfk_3` FOREIGN KEY (`parentId`) REFERENCES `id_jumio_events` (`eventId`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `id_jumio_events` (
  `eventId` varchar(255) NOT NULL,
  `typeId` varchar(255) NOT NULL,
  `jumioReference` varchar(255) DEFAULT NULL,
  `validationType` enum('ID_PHOTO','ID_ADDRESS') DEFAULT NULL,
  `entityId` varchar(255) DEFAULT NULL,
  `customerId` int(10) unsigned DEFAULT NULL,
  `companyId` int(10) unsigned DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  `parentId` varchar(255) DEFAULT NULL,
  `errorCode` varchar(255) DEFAULT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`eventId`),
  KEY `jumioReference` (`jumioReference`),
  KEY `entityId` (`entityId`),
  KEY `companyId` (`companyId`),
  KEY `customerId` (`customerId`),
  KEY `parentId` (`parentId`),
  CONSTRAINT `id_jumio_events_ibfk_1` FOREIGN KEY (`companyId`) REFERENCES `ch_company` (`company_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `id_jumio_events_ibfk_2` FOREIGN KEY (`customerId`) REFERENCES `cms2_customers` (`customerId`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `id_jumio_events_ibfk_3` FOREIGN KEY (`parentId`) REFERENCES `id_jumio_events` (`eventId`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `bdg_lead_contact_rules` (
  `bdgLeadContactRuleId` char(36) NOT NULL,
  `bdgLeadId` varchar(255) NOT NULL,
  `type` varchar(255) NOT NULL,
  `label` varchar(255) NOT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`bdgLeadContactRuleId`),
  KEY `bdgLeadId` (`bdgLeadId`),
  CONSTRAINT `bdg_lead_contact_rules_ibfk_1` FOREIGN KEY (`bdgLeadId`) REFERENCES `bdg_leads` (`bdgLeadId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `bdg_lead_rules` (
  `bdgLeadRuleId` char(36) NOT NULL,
  `bdgLeadId` varchar(255) NOT NULL,
  `label` varchar(255) NOT NULL,
  `type` varchar(255) NOT NULL,
  `description` varchar(255) NOT NULL,
  `required` tinyint(1) NOT NULL,
  `value` varchar(255) DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`bdgLeadRuleId`),
  KEY `bdgLeadId` (`bdgLeadId`),
  CONSTRAINT `bdg_lead_rules_ibfk_1` FOREIGN KEY (`bdgLeadId`) REFERENCES `bdg_leads` (`bdgLeadId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_customer_events` (
  `customerEventId` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `typeId` enum('EMAIL_SENT','DELETED','INVALID_EMAIL') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `customerId` int(11) NOT NULL,
  `emailLogId` int(11) DEFAULT NULL,
  `actionBy` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`customerEventId`),
  KEY `typeId` (`typeId`),
  KEY `customerId` (`customerId`),
  KEY `emailLogId` (`emailLogId`),
  CONSTRAINT `cms2_customer_events_ibfk_1` FOREIGN KEY (`emailLogId`) REFERENCES `cms2_email_logs` (`emailLogId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `cms2_customer_settings` (
  `id` varchar(255) NOT NULL,
  `customerId` int(10) unsigned NOT NULL,
  `settingKey` varchar(255) NOT NULL,
  `settingValue` json NOT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `id` (`id`),
  KEY `customerId` (`customerId`),
  CONSTRAINT `cms2_customer_settings_ibfk_1` FOREIGN KEY (`customerId`) REFERENCES `cms2_customers` (`customerId`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `cms2_register_share_class_events` (
  `shareClassEventId` int(11) NOT NULL AUTO_INCREMENT,
  `dtEntry` datetime NOT NULL,
  `typeId` enum('ALLOTMENT','TRANSFER') CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `quantity` int(11) NOT NULL,
  `certificateNumber` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `acquired` int(11) NOT NULL,
  `disposed` int(11) NOT NULL,
  `balance` int(11) NOT NULL,
  `pricePerShare` double NOT NULL,
  `totalAmount` double NOT NULL,
  `notes` longtext CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  `registerShareClassId` int(11) DEFAULT NULL,
  PRIMARY KEY (`shareClassEventId`),
  KEY `IDX_DBBB6F0CD06683FA` (`registerShareClassId`),
  CONSTRAINT `FK_DBBB6F0CD06683FA` FOREIGN KEY (`registerShareClassId`) REFERENCES `cms2_register_share_classes` (`registerShareClassId`)
) ENGINE=InnoDB AUTO_INCREMENT=5901 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `company_events` (
  `companyEventId` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `typeId` enum('EMAIL_SENT','TRANSFERRED') CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `companyId` int(10) unsigned NOT NULL,
  `emailLogId` int(11) DEFAULT NULL,
  `toCompanyId` int(10) unsigned DEFAULT NULL,
  `actionBy` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`companyEventId`),
  KEY `typeId` (`typeId`),
  KEY `companyId` (`companyId`),
  KEY `emailLogId` (`emailLogId`),
  KEY `dtc` (`dtc`),
  KEY `toCompanyId` (`toCompanyId`),
  CONSTRAINT `company_events_ibfk_1` FOREIGN KEY (`emailLogId`) REFERENCES `cms2_email_logs` (`emailLogId`),
  CONSTRAINT `company_events_ibfk_2` FOREIGN KEY (`toCompanyId`) REFERENCES `ch_company` (`company_id`),
  CONSTRAINT `company_events_ibfk_3` FOREIGN KEY (`companyId`) REFERENCES `ch_company` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `company_events` (
  `companyEventId` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `typeId` enum('EMAIL_SENT','TRANSFERRED') CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL,
  `companyId` int(10) unsigned NOT NULL,
  `emailLogId` int(11) DEFAULT NULL,
  `toCompanyId` int(10) unsigned DEFAULT NULL,
  `actionBy` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`companyEventId`),
  KEY `typeId` (`typeId`),
  KEY `companyId` (`companyId`),
  KEY `emailLogId` (`emailLogId`),
  KEY `dtc` (`dtc`),
  KEY `toCompanyId` (`toCompanyId`),
  CONSTRAINT `company_events_ibfk_1` FOREIGN KEY (`emailLogId`) REFERENCES `cms2_email_logs` (`emailLogId`),
  CONSTRAINT `company_events_ibfk_2` FOREIGN KEY (`toCompanyId`) REFERENCES `ch_company` (`company_id`),
  CONSTRAINT `company_events_ibfk_3` FOREIGN KEY (`companyId`) REFERENCES `ch_company` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `id_documents` (
  `idDocument` int(11) NOT NULL AUTO_INCREMENT,
  `idEvent` int(11) NOT NULL,
  `filename` varchar(255) NOT NULL,
  `customerId` int(11) NOT NULL,
  PRIMARY KEY (`idDocument`),
  KEY `customerId` (`customerId`),
  KEY `idEvent` (`idEvent`),
  CONSTRAINT `id_documents_ibfk_1` FOREIGN KEY (`idEvent`) REFERENCES `id_events` (`idEvent`)
) ENGINE=InnoDB AUTO_INCREMENT=137830 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `id_email_logs` (
  `idEmailLogId` int(11) NOT NULL AUTO_INCREMENT,
  `emailLogId` int(11) NOT NULL,
  `actionBy` varchar(255) NOT NULL,
  `customerId` int(11) DEFAULT NULL,
  `companyId` int(11) DEFAULT NULL,
  PRIMARY KEY (`idEmailLogId`),
  KEY `customerId` (`customerId`),
  KEY `companyId` (`companyId`),
  KEY `emailLogId` (`emailLogId`),
  CONSTRAINT `id_email_logs_ibfk_1` FOREIGN KEY (`emailLogId`) REFERENCES `cms2_email_logs` (`emailLogId`)
) ENGINE=InnoDB AUTO_INCREMENT=430457 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `id_jumio_events_additional_information` (
  `id` varchar(255) NOT NULL,
  `eventId` varchar(255) NOT NULL,
  `providedKey` varchar(255) NOT NULL,
  `providedValue` json NOT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `eventId` (`eventId`),
  CONSTRAINT `id_jumio_events_additional_information_ibfk_1` FOREIGN KEY (`eventId`) REFERENCES `id_jumio_events` (`eventId`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `id_notes` (
  `idEvent` int(11) NOT NULL,
  `note` text NOT NULL,
  KEY `idEvent` (`idEvent`),
  CONSTRAINT `id_notes_ibfk_1` FOREIGN KEY (`idEvent`) REFERENCES `id_events` (`idEvent`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `id_nullification_events` (
  `idEvent` int(11) NOT NULL,
  `validationId` int(11) NOT NULL,
  `message` text NOT NULL,
  KEY `idEvent` (`idEvent`),
  KEY `validationId` (`validationId`),
  CONSTRAINT `id_nullification_events_ibfk_1` FOREIGN KEY (`idEvent`) REFERENCES `id_events` (`idEvent`),
  CONSTRAINT `id_nullification_events_ibfk_2` FOREIGN KEY (`validationId`) REFERENCES `id_validations` (`validationId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `id_nullification_events` (
  `idEvent` int(11) NOT NULL,
  `validationId` int(11) NOT NULL,
  `message` text NOT NULL,
  KEY `idEvent` (`idEvent`),
  KEY `validationId` (`validationId`),
  CONSTRAINT `id_nullification_events_ibfk_1` FOREIGN KEY (`idEvent`) REFERENCES `id_events` (`idEvent`),
  CONSTRAINT `id_nullification_events_ibfk_2` FOREIGN KEY (`validationId`) REFERENCES `id_validations` (`validationId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `id_validation_events` (
  `idEvent` int(11) NOT NULL,
  `validationId` int(11) NOT NULL,
  `message` text,
  `previousValidationId` int(11) DEFAULT NULL,
  `notifyCustomer` tinyint(1) NOT NULL DEFAULT '0',
  KEY `idEvent` (`idEvent`),
  KEY `validationId` (`validationId`),
  KEY `previousValidationId` (`previousValidationId`),
  CONSTRAINT `id_validation_events_ibfk_1` FOREIGN KEY (`idEvent`) REFERENCES `id_events` (`idEvent`),
  CONSTRAINT `id_validation_events_ibfk_2` FOREIGN KEY (`validationId`) REFERENCES `id_validations` (`validationId`),
  CONSTRAINT `id_validation_events_ibfk_3` FOREIGN KEY (`previousValidationId`) REFERENCES `id_validations` (`validationId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `id_validation_events` (
  `idEvent` int(11) NOT NULL,
  `validationId` int(11) NOT NULL,
  `message` text,
  `previousValidationId` int(11) DEFAULT NULL,
  `notifyCustomer` tinyint(1) NOT NULL DEFAULT '0',
  KEY `idEvent` (`idEvent`),
  KEY `validationId` (`validationId`),
  KEY `previousValidationId` (`previousValidationId`),
  CONSTRAINT `id_validation_events_ibfk_1` FOREIGN KEY (`idEvent`) REFERENCES `id_events` (`idEvent`),
  CONSTRAINT `id_validation_events_ibfk_2` FOREIGN KEY (`validationId`) REFERENCES `id_validations` (`validationId`),
  CONSTRAINT `id_validation_events_ibfk_3` FOREIGN KEY (`previousValidationId`) REFERENCES `id_validations` (`validationId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `kofax_post_emails_sent` (
  `postItemId` int(11) NOT NULL,
  `emailLogId` int(11) NOT NULL,
  KEY `postItemId` (`postItemId`),
  KEY `emailLogId` (`emailLogId`),
  CONSTRAINT `kofax_post_emails_sent_ibfk_1` FOREIGN KEY (`postItemId`) REFERENCES `kofax_post_items` (`postItemId`),
  CONSTRAINT `kofax_post_emails_sent_ibfk_2` FOREIGN KEY (`emailLogId`) REFERENCES `cms2_email_logs` (`emailLogId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `kofax_post_emails_sent` (
  `postItemId` int(11) NOT NULL,
  `emailLogId` int(11) NOT NULL,
  KEY `postItemId` (`postItemId`),
  KEY `emailLogId` (`emailLogId`),
  CONSTRAINT `kofax_post_emails_sent_ibfk_1` FOREIGN KEY (`postItemId`) REFERENCES `kofax_post_items` (`postItemId`),
  CONSTRAINT `kofax_post_emails_sent_ibfk_2` FOREIGN KEY (`emailLogId`) REFERENCES `cms2_email_logs` (`emailLogId`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `mp_clevernumbers_leads` (
  `leadId` char(36) NOT NULL,
  `companyId` int(10) unsigned NOT NULL,
  `businessPhoneOption` enum('YES','NO','HAS_BUSINESS_NUMBER') NOT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`leadId`),
  KEY `companyId` (`companyId`),
  CONSTRAINT `mp_clevernumbers_leads_ibfk_1` FOREIGN KEY (`companyId`) REFERENCES `ch_company` (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `namesco_vouchers` (
  `namescoVoucherId` varchar(255) NOT NULL,
  `code` varchar(255) NOT NULL,
  `companyId` int(10) unsigned DEFAULT NULL,
  `dateSent` datetime DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`namescoVoucherId`),
  UNIQUE KEY `code` (`code`),
  KEY `companyId` (`companyId`),
  CONSTRAINT `namesco_vouchers_ibfk_1` FOREIGN KEY (`companyId`) REFERENCES `ch_company` (`company_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `order_data` (
  `id` varchar(255) NOT NULL,
  `md` varchar(255) DEFAULT NULL,
  `customer_id` int(10) unsigned DEFAULT NULL,
  `amount` float NOT NULL,
  `data` json NOT NULL,
  `deleted` datetime DEFAULT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `md` (`md`,`customer_id`),
  KEY `customer_id` (`customer_id`),
  CONSTRAINT `order_data_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `cms2_customers` (`customerId`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `business_services_lead_events` (
  `leadEventId` int(11) NOT NULL AUTO_INCREMENT,
  `typeId` enum('COMPANY_RESOLVED','EMAIL_SENT','EXPORTED','NO_LONGER_AVAILABLE','TEMPORARY_REJECTED') COLLATE utf8_unicode_ci NOT NULL,
  `eventKey` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `leadId` int(11) NOT NULL,
  `emailLogId` int(11) DEFAULT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`leadEventId`),
  KEY `typeId` (`typeId`),
  KEY `leadId` (`leadId`),
  KEY `emailLogId` (`emailLogId`),
  CONSTRAINT `business_services_lead_events_ibfk_1` FOREIGN KEY (`leadId`) REFERENCES `business_services_lead` (`id`),
  CONSTRAINT `business_services_lead_events_ibfk_2` FOREIGN KEY (`emailLogId`) REFERENCES `cms2_email_logs` (`emailLogId`)
) ENGINE=InnoDB AUTO_INCREMENT=13753 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `business_services_lead_events` (
  `leadEventId` int(11) NOT NULL AUTO_INCREMENT,
  `typeId` enum('COMPANY_RESOLVED','EMAIL_SENT','EXPORTED','NO_LONGER_AVAILABLE','TEMPORARY_REJECTED') COLLATE utf8_unicode_ci NOT NULL,
  `eventKey` varchar(255) CHARACTER SET utf8 COLLATE utf8_unicode_ci DEFAULT NULL,
  `leadId` int(11) NOT NULL,
  `emailLogId` int(11) DEFAULT NULL,
  `dtc` datetime NOT NULL,
  PRIMARY KEY (`leadEventId`),
  KEY `typeId` (`typeId`),
  KEY `leadId` (`leadId`),
  KEY `emailLogId` (`emailLogId`),
  CONSTRAINT `business_services_lead_events_ibfk_1` FOREIGN KEY (`leadId`) REFERENCES `business_services_lead` (`id`),
  CONSTRAINT `business_services_lead_events_ibfk_2` FOREIGN KEY (`emailLogId`) REFERENCES `cms2_email_logs` (`emailLogId`)
) ENGINE=InnoDB AUTO_INCREMENT=13753 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
CREATE TABLE IF NOT EXISTS `csms_reports` (
  `id` varchar(255) NOT NULL,
  `customer_id` int(10) unsigned NOT NULL,
  `order_id` int(11) DEFAULT NULL,
  `order_item_id` int(11) DEFAULT NULL,
  `company_report_id` int(11) NOT NULL,
  `company_name` varchar(255) NOT NULL,
  `company_number` varchar(255) NOT NULL,
  `criteria` json NOT NULL,
  `downloaded` tinyint(1) NOT NULL DEFAULT '0',
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `id` (`id`),
  KEY `customer_id` (`customer_id`),
  KEY `order_id` (`order_id`),
  KEY `order_item_id` (`order_item_id`),
  CONSTRAINT `csms_reports_ibfk_1` FOREIGN KEY (`customer_id`) REFERENCES `cms2_customers` (`customerId`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `mp_clevernumbers_leads_details` (
  `mpCleverNumbersLeadDetailsId` int(11) NOT NULL AUTO_INCREMENT,
  `companyId` int(11) unsigned DEFAULT NULL,
  `cleverNumber` varchar(255) NOT NULL,
  `targetNumber` varchar(255) NOT NULL,
  `cmsPaymentMethod` varchar(255) DEFAULT NULL,
  `paymentStatus` varchar(255) DEFAULT NULL,
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  PRIMARY KEY (`mpCleverNumbersLeadDetailsId`),
  KEY `companyId` (`companyId`),
  CONSTRAINT `mp_clevernumbers_leads_details_ibfk_1` FOREIGN KEY (`companyId`) REFERENCES `ch_company` (`company_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB AUTO_INCREMENT=9107 DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `id_credas_processes` (
  `processId` varchar(255) NOT NULL,
  `wholesaleClient` tinyint(1) NOT NULL,
  `journeyId` varchar(255) NOT NULL,
  `credasReference` varchar(255) NOT NULL,
  `credasEntityReference` varchar(255) NOT NULL,
  `entityId` varchar(255) DEFAULT NULL,
  `diligenceLevel` varchar(255) DEFAULT NULL,
  `emailAddress` varchar(255) DEFAULT NULL,
  `customerId` int(10) unsigned DEFAULT NULL,
  `companyId` int(10) unsigned DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  `validationsSaved` tinyint(1) NOT NULL DEFAULT '0',
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`processId`),
  KEY `credasReference` (`credasReference`),
  KEY `entityId` (`entityId`),
  KEY `companyId` (`companyId`),
  KEY `customerId` (`customerId`),
  CONSTRAINT `id_credas_processes_ibfk_1` FOREIGN KEY (`companyId`) REFERENCES `ch_company` (`company_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `id_credas_processes_ibfk_2` FOREIGN KEY (`customerId`) REFERENCES `cms2_customers` (`customerId`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `id_credas_processes` (
  `processId` varchar(255) NOT NULL,
  `wholesaleClient` tinyint(1) NOT NULL,
  `journeyId` varchar(255) NOT NULL,
  `credasReference` varchar(255) NOT NULL,
  `credasEntityReference` varchar(255) NOT NULL,
  `entityId` varchar(255) DEFAULT NULL,
  `diligenceLevel` varchar(255) DEFAULT NULL,
  `emailAddress` varchar(255) DEFAULT NULL,
  `customerId` int(10) unsigned DEFAULT NULL,
  `companyId` int(10) unsigned DEFAULT NULL,
  `status` varchar(255) DEFAULT NULL,
  `validationsSaved` tinyint(1) NOT NULL DEFAULT '0',
  `dtc` datetime NOT NULL,
  `dtm` datetime NOT NULL,
  `deleted` tinyint(1) NOT NULL DEFAULT '0',
  PRIMARY KEY (`processId`),
  KEY `credasReference` (`credasReference`),
  KEY `entityId` (`entityId`),
  KEY `companyId` (`companyId`),
  KEY `customerId` (`customerId`),
  CONSTRAINT `id_credas_processes_ibfk_1` FOREIGN KEY (`companyId`) REFERENCES `ch_company` (`company_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `id_credas_processes_ibfk_2` FOREIGN KEY (`customerId`) REFERENCES `cms2_customers` (`customerId`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE OR REPLACE VIEW `view_products` AS select `n`.`node_id` AS `productId`,`p`.`title` AS `productName`,`n`.`parent_id` AS `categoryId`,`pp`.`title` AS `categoryName`,`n`.`dtc` AS `dtc`,`n`.`dtm` AS `dtm` from ((`cms2_nodes` `n` join `cms2_pages` `p` on((`p`.`node_id` = `n`.`node_id`))) join `cms2_pages` `pp` on((`pp`.`node_id` = `n`.`parent_id`))) where exists(select 1 from `cms2_orders_items` where (`cms2_orders_items`.`productId` = `n`.`node_id`));
CREATE OR REPLACE VIEW `view_incorporation_member` AS select `ch_incorporation_member`.`incorporation_member_id` AS `incorporation_member_id`,`ch_incorporation_member`.`form_submission_id` AS `form_submission_id`,`ch_incorporation_member`.`nominee` AS `nominee`,`ch_incorporation_member`.`type` AS `type`,`ch_incorporation_member`.`corporate` AS `corporate`,`ch_incorporation_member`.`title` AS `title`,`ch_incorporation_member`.`forename` AS `forename`,`ch_incorporation_member`.`middle_name` AS `middle_name`,`ch_incorporation_member`.`surname` AS `surname`,`ch_incorporation_member`.`corporate_name` AS `corporate_name`,`ch_incorporation_member`.`authentication` AS `authentication`,`ch_incorporation_member`.`consentToAct` AS `consentToAct`,`ch_incorporation_member`.`premise` AS `premise`,`ch_incorporation_member`.`street` AS `street`,`ch_incorporation_member`.`thoroughfare` AS `thoroughfare`,`ch_incorporation_member`.`post_town` AS `post_town`,`ch_incorporation_member`.`county` AS `county`,`ch_incorporation_member`.`country` AS `country`,`ch_incorporation_member`.`postcode` AS `postcode`,`ch_incorporation_member`.`care_of_name` AS `care_of_name`,`ch_incorporation_member`.`po_box` AS `po_box`,`ch_incorporation_member`.`dob` AS `dob`,`ch_incorporation_member`.`nationality` AS `nationality`,`ch_incorporation_member`.`occupation` AS `occupation`,`ch_incorporation_member`.`country_of_residence` AS `country_of_residence`,`ch_incorporation_member`.`residential_premise` AS `residential_premise`,`ch_incorporation_member`.`residential_street` AS `residential_street`,`ch_incorporation_member`.`residential_thoroughfare` AS `residential_thoroughfare`,`ch_incorporation_member`.`residential_post_town` AS `residential_post_town`,`ch_incorporation_member`.`residential_county` AS `residential_county`,`ch_incorporation_member`.`residential_country` AS `residential_country`,`ch_incorporation_member`.`residential_postcode` AS `residential_postcode`,`ch_incorporation_member`.`residential_secure_address_ind` AS `residential_secure_address_ind`,`ch_incorporation_member`.`identification_type` AS `identification_type`,`ch_incorporation_member`.`place_registered` AS `place_registered`,`ch_incorporation_member`.`registration_number` AS `registration_number`,`ch_incorporation_member`.`law_governed` AS `law_governed`,`ch_incorporation_member`.`legal_form` AS `legal_form`,`ch_incorporation_member`.`country_or_state` AS `country_or_state`,`ch_incorporation_member`.`share_class` AS `share_class`,`ch_incorporation_member`.`prescribed_particulars` AS `prescribed_particulars`,`ch_incorporation_member`.`num_shares` AS `num_shares`,`ch_incorporation_member`.`amount_paid` AS `amount_paid`,`ch_incorporation_member`.`amount_unpaid` AS `amount_unpaid`,`ch_incorporation_member`.`currency` AS `currency`,`ch_incorporation_member`.`share_value` AS `share_value`,`ch_incorporation_member`.`nomineeType` AS `nomineeType`,`ch_incorporation_member`.`ownership_of_shares` AS `ownership_of_shares`,`ch_incorporation_member`.`ownership_of_voting_rights` AS `ownership_of_voting_rights`,`ch_incorporation_member`.`right_to_appoint_and_remove_directors` AS `right_to_appoint_and_remove_directors`,`ch_incorporation_member`.`significant_influence_or_control` AS `significant_influence_or_control`,`ch_incorporation_member`.`dtc` AS `dtc`,`ch_incorporation_member`.`dtm` AS `dtm`,concat(`ch_incorporation_member`.`type`,'_',`ch_incorporation_member`.`corporate`) AS `discriminator` from `ch_incorporation_member`;
CREATE OR REPLACE VIEW `view_company_member` AS select `ch_company_member`.`company_member_id` AS `company_member_id`,`ch_company_member`.`company_id` AS `company_id`,`ch_company_member`.`type` AS `type`,`ch_company_member`.`corporate` AS `corporate`,`ch_company_member`.`designated_ind` AS `designated_ind`,`ch_company_member`.`title` AS `title`,`ch_company_member`.`forename` AS `forename`,`ch_company_member`.`middle_name` AS `middle_name`,`ch_company_member`.`surname` AS `surname`,`ch_company_member`.`corporate_name` AS `corporate_name`,`ch_company_member`.`place_registered` AS `place_registered`,`ch_company_member`.`registration_number` AS `registration_number`,`ch_company_member`.`law_governed` AS `law_governed`,`ch_company_member`.`legal_form` AS `legal_form`,`ch_company_member`.`country_or_state` AS `country_or_state`,`ch_company_member`.`premise` AS `premise`,`ch_company_member`.`street` AS `street`,`ch_company_member`.`thoroughfare` AS `thoroughfare`,`ch_company_member`.`post_town` AS `post_town`,`ch_company_member`.`county` AS `county`,`ch_company_member`.`country` AS `country`,`ch_company_member`.`postcode` AS `postcode`,`ch_company_member`.`care_of_name` AS `care_of_name`,`ch_company_member`.`po_box` AS `po_box`,`ch_company_member`.`dob` AS `dob`,`ch_company_member`.`nationality` AS `nationality`,`ch_company_member`.`occupation` AS `occupation`,`ch_company_member`.`country_of_residence` AS `country_of_residence`,`ch_company_member`.`residential_premise` AS `residential_premise`,`ch_company_member`.`residential_street` AS `residential_street`,`ch_company_member`.`residential_thoroughfare` AS `residential_thoroughfare`,`ch_company_member`.`residential_post_town` AS `residential_post_town`,`ch_company_member`.`residential_county` AS `residential_county`,`ch_company_member`.`residential_country` AS `residential_country`,`ch_company_member`.`residential_postcode` AS `residential_postcode`,`ch_company_member`.`residential_secure_address_ind` AS `residential_secure_address_ind`,`ch_company_member`.`appointment_date` AS `appointment_date`,`ch_company_member`.`resignation_date` AS `resignation_date`,`ch_company_member`.`share_class` AS `share_class`,`ch_company_member`.`num_shares` AS `num_shares`,`ch_company_member`.`ownership_of_shares` AS `ownership_of_shares`,`ch_company_member`.`ownership_of_voting_rights` AS `ownership_of_voting_rights`,`ch_company_member`.`right_to_appoint_and_remove_directors` AS `right_to_appoint_and_remove_directors`,`ch_company_member`.`significant_influence_or_control` AS `significant_influence_or_control`,`ch_company_member`.`psc_statement_notification` AS `psc_statement_notification`,`ch_company_member`.`notification_date` AS `notification_date`,`ch_company_member`.`dtc` AS `dtc`,`ch_company_member`.`dtm` AS `dtm`,concat(`ch_company_member`.`type`,'_',`ch_company_member`.`corporate`) AS `discriminator` from `ch_company_member`;
CREATE OR REPLACE VIEW `toolkit_offers_selected` AS select `cu`.`firstName` AS `firstName`,`cu`.`lastName` AS `lastName`,`cu`.`email` AS `email`,`cu`.`phone` AS `phone`,`co`.`company_name` AS `company_name`,`tk`.`companyId` AS `companyId`,`co`.`sic_code1` AS `sic_code1`,`co`.`sic_code2` AS `sic_code2`,`co`.`sic_code3` AS `sic_code3`,`co`.`sic_code4` AS `sic_code4`,cast(`tk`.`dtc` as date) AS `lead_date`,`cto`.`type` AS `offer` from (((`cms2_company_toolkit_offers` `tk` join `ch_company` `co` on((`tk`.`companyId` = `co`.`company_id`))) join `cms2_toolkit_offers` `cto` on((`tk`.`toolkitOfferId` = `cto`.`toolkitOfferId`))) join `cms2_customers` `cu` on((`co`.`customer_id` = `cu`.`customerId`))) where (`tk`.`selected` = 1);
CREATE OR REPLACE VIEW `toolkit_offers` AS select `cto`.`companyToolkitOfferId` AS `companyToolkitOfferId`,`cto`.`companyId` AS `companyId`,`cto`.`toolkitOfferId` AS `toolkitOfferId`,`cto`.`selected` AS `selected`,`cto`.`dateClaimed` AS `dateClaimed`,`cto`.`dtc` AS `dtc`,`cto`.`dtm` AS `dtm`,`cto`.`options` AS `options`,`tof`.`type` AS `offer`,`tof`.`name` AS `offerDetails` from (`cms2_company_toolkit_offers` `cto` left join `cms2_toolkit_offers` `tof` on((`tof`.`toolkitOfferId` = `cto`.`toolkitOfferId`))) group by `cto`.`companyId`,`cto`.`toolkitOfferId`;
CREATE OR REPLACE VIEW `tax_assist_leads` AS select `ta`.`email` AS `email`,`ta`.`type` AS `type`,`ta`.`dtc` AS `dtc` from `cms2_tax_assist_lead_tracking` `ta` union select `tk`.`email` AS `email`,'EXCLUSIVE_OFFERS' AS `type`,`tk`.`dtc` AS `dtc` from `cms2_offer_customer_details` `tk` where (`tk`.`productId` = 544) union select `bsp`.`email` AS `email`,'BUSINESS_SERVICES' AS `type`,`bs`.`dtm` AS `dtc` from (`business_services_lead` `bs` left join `business_services_partner_information` `bsp` on((`bsp`.`lead_id` = `bs`.`id`))) where ((`bs`.`offer_id` = 10) and (`bs`.`processed` = 1)) union select `cu`.`email` AS `email`,'TOOLKIT' AS `type`,`tk`.`dateClaimed` AS `dtc` from ((`cms2_company_toolkit_offers` `tk` left join `ch_company` `co` on((`co`.`company_id` = `tk`.`companyId`))) left join `cms2_customers` `cu` on((`cu`.`customerId` = `co`.`customer_id`))) where ((`tk`.`toolkitOfferId` = 4) and (`tk`.`selected` = 1) and (`tk`.`dateClaimed` is not null));
CREATE OR REPLACE VIEW `services_valid_expiry` AS select `s`.`companyId` AS `companyId`,`s`.`serviceTypeId` AS `serviceTypeId`,`s`.`dtStart` AS `dtStart`,max(`s`.`dtExpires`) AS `dtExpires` from `cms2_services` `s` where (`s`.`parentId` is null) group by `s`.`companyId`,`s`.`serviceTypeId`;
CREATE OR REPLACE VIEW `service_cancellations` AS select `e`.`eventId` AS `eventId`,`e`.`eventKey` AS `eventKey`,`e`.`objectId` AS `objectId`,`e`.`dtc` AS `dtc`,`ss`.`serviceTypeId` AS `serviceTypeId` from (`cms2_events` `e` left join `cms2_service_settings` `ss` on((`ss`.`serviceSettingId` = `e`.`objectId`))) where (`e`.`eventKey` = 'events.service.cancelled');
CREATE OR REPLACE VIEW `order_items_sales` AS select `oi`.`orderItemId` AS `orderitemid`,`oi`.`orderId` AS `orderId`,`oi`.`isRefunded` AS `isRefunded`,`oi`.`productId` AS `productId`,`oi`.`productTitle` AS `productTitle`,`oi`.`companyName` AS `companyName`,`oi`.`companyNumber` AS `companyNumber`,`oi`.`qty` AS `qty`,`oi`.`price` AS `price`,`oi`.`notApplyVat` AS `notApplyVat`,`oi`.`nonVatableValue` AS `nonVatablevalue`,`oi`.`subTotal` AS `subTotal`,`oi`.`vat` AS `vat`,`oi`.`additional` AS `additional`,`oi`.`totalPrice` AS `totalPrice`,`oi`.`incorporationRequired` AS `incorporationrequired`,`oi`.`companyId` AS `companyid`,`oi`.`markUp` AS `markup`,`oi`.`dtExported` AS `dtExported`,`o`.`dtc` AS `dtc`,`oi`.`dtm` AS `dtm` from (`cms2_orders_items` `oi` join `cms2_orders` `o` on((`o`.`orderId` = `oi`.`orderId`))) where (((`oi`.`isRefund` is null) or (`oi`.`isRefund` = 0)) and (`oi`.`productId` not in (0,609,615,272,1025,1240,1350)) and (`oi`.`qty` > 0) and (`oi`.`price` > 0));
CREATE OR REPLACE VIEW `mp_leads` AS select `cu`.`firstName` AS `firstname`,`cu`.`lastName` AS `lastname`,`cu`.`email` AS `email`,`cu`.`phone` AS `phone`,`cu`.`isSubscribed` AS `issubscribed`,`co`.`company_name` AS `company_name`,`co`.`company_number` AS `company_number`,`co`.`incorporation_date` AS `incorporation_date`,`vp`.`productName` AS `productName`,`co`.`sic_code1` AS `sic_code1`,`co`.`company_category` AS `company_category`,`co`.`street` AS `street`,`co`.`post_town` AS `post_town`,`co`.`postcode` AS `postcode`,`co`.`company_status` AS `company_status`,`dir`.`forename` AS `forename`,`dir`.`surname` AS `surname`,`dir`.`corporate_name` AS `corporate_name`,`dir`.`type` AS `type`,`dir`.`dob` AS `dob`,`dir_ct`.`directors` AS `NumberDirectors`,`lastorder`.`total` AS `total`,`lastorder`.`customerName` AS `customerName`,`lastorder`.`dtc` AS `dtc`,`co`.`incorporation_date` AS `MPChosen`,`co_ct`.`NumberCompanies` AS `NumberCompanies`,********* AS `newchannel`,********* AS `newleadsource`,4 AS `crmtypecode`,'Enquiry Made Simple Number - Clever Numbers' AS `Subject`,9 AS `ownertype`,'{8e7ef4d3-8846-ea11-a812-000d3a2276dc}' AS `owner`,concat('Product=',`vp`.`productName`,'--','Order Total=',`lastorder`.`total`,'--','Company Number=',`co_ct`.`NumberCompanies`) AS `taskdescription`,`bank`.`bankTypeId` AS `MSGBank`,`a`.`tradingStart` AS `tradingStart`,`a`.`text` AS `bestDescribes`,ifnull(`mpc`.`businessPhoneOption`,'NO') AS `businessPhoneOption` from (((((((((`ch_company` `co` left join `cms2_customers` `cu` on((`cu`.`customerId` = `co`.`customer_id`))) left join `view_products` `vp` on((`vp`.`productId` = `co`.`product_id`))) left join `mp_clevernumbers_leads` `mpc` on((`mpc`.`companyId` = `co`.`company_id`))) left join (select `cm`.`company_id` AS `company_id`,`cm`.`forename` AS `forename`,`cm`.`surname` AS `surname`,`cm`.`corporate_name` AS `corporate_name`,`cm`.`type` AS `type`,`cm`.`dob` AS `dob` from `ch_company_member` `cm` where ((`cm`.`discriminator` = 'DIR_0') and (`cm`.`dob` > '1900-01-01') and `cm`.`company_id` in (select `co`.`company_id` from (`ch_company` `co` left join `cms2_customers` `cu` on((`cu`.`customerId` = `co`.`customer_id`))) where ((`co`.`product_id` in (1313,1315,1316,1317,1694)) and (`co`.`incorporation_date` > (curdate() - interval 20 day)) and (`co`.`deleted` = 0) and (`co`.`hidden` = 0) and (`co`.`locked` = 0)))) group by `cm`.`company_id`) `dir` on((`dir`.`company_id` = `co`.`company_id`))) left join (select `cm`.`company_id` AS `company_id`,count(0) AS `directors` from `ch_company_member` `cm` where ((`cm`.`type` = 'DIR') and (`cm`.`dob` > '1900-01-01') and `cm`.`company_id` in (select `co`.`company_id` from (`ch_company` `co` left join `cms2_customers` `cu` on((`cu`.`customerId` = `co`.`customer_id`))) where ((`co`.`product_id` in (1313,1315,1316,1317,1694)) and (`co`.`incorporation_date` > (curdate() - interval 20 day)) and (`co`.`deleted` = 0) and (`co`.`hidden` = 0) and (`co`.`locked` = 0)))) group by `cm`.`company_id`) `dir_ct` on((`dir_ct`.`company_id` = `co`.`company_id`))) left join (select `o`.`customerId` AS `customerId`,`o`.`total` AS `total`,`o`.`customerName` AS `customerName`,`o`.`dtc` AS `dtc` from `cms2_orders` `o` where `o`.`orderId` in (select max(`co`.`orderId`) from (`cms2_orders` `co` join (select `co`.`customer_id` AS `customer_id` from (`ch_company` `co` left join `cms2_customers` `cu` on((`cu`.`customerId` = `co`.`customer_id`))) where ((`co`.`product_id` in (1313,1315,1316,1317,1694)) and (`co`.`incorporation_date` > (curdate() - interval 20 day)) and (`co`.`deleted` = 0) and (`co`.`hidden` = 0) and (`co`.`locked` = 0))) `cu` on((`co`.`customerId` = `cu`.`customer_id`))) group by `co`.`customerId`)) `lastorder` on((`lastorder`.`customerId` = `co`.`customer_id`))) left join (select `co`.`customer_id` AS `customer_id`,count(0) AS `NumberCompanies` from `ch_company` `co` where `co`.`customer_id` in (select `co`.`customer_id` from (`ch_company` `co` left join `cms2_customers` `cu` on((`cu`.`customerId` = `co`.`customer_id`))) where ((`co`.`product_id` in (1313,1315,1316,1317,1694)) and (`co`.`incorporation_date` > (curdate() - interval 20 day)) and (`co`.`deleted` = 0) and (`co`.`hidden` = 0) and (`co`.`locked` = 0))) group by `co`.`customer_id`) `co_ct` on((`co_ct`.`customer_id` = `co`.`customer_id`))) left join (select `cc`.`companyId` AS `companyId`,`cc`.`bankTypeId` AS `bankTypeId` from `cms2_company_customer` `cc` where `cc`.`companyId` in (select `co`.`company_id` from (`ch_company` `co` left join `cms2_customers` `cu` on((`cu`.`customerId` = `co`.`customer_id`))) where ((`co`.`product_id` in (1313,1315,1316,1317,1694)) and (`co`.`incorporation_date` > (curdate() - interval 20 day)) and (`co`.`deleted` = 0) and (`co`.`hidden` = 0) and (`co`.`locked` = 0))) group by `cc`.`companyId`) `bank` on((`bank`.`companyId` = `co`.`company_id`))) left join `cms2_answers` `a` on((`a`.`customerId` = `co`.`customer_id`))) where ((`co`.`product_id` in (1313,1315,1316,1317,1694)) and (`co`.`incorporation_date` > (curdate() - interval 20 day)) and (`co`.`deleted` = 0) and (`co`.`hidden` = 0) and (`co`.`locked` = 0) and `co`.`company_id` in (select `co`.`company_id` from (`business_services_lead` `l` join `ch_company` `co` on((`co`.`company_id` = `l`.`company_id`))) where ((`l`.`offer_id` = 6) and (`co`.`incorporation_date` > (curdate() - interval 20 day)))) is false and `co`.`customer_id` in (select `above5_customers`.`customer_id` from (select count(0) AS `ct`,`co`.`customer_id` AS `customer_id` from (`ch_company` `co` join `cms2_customers` `cu` on((`cu`.`customerId` = `co`.`customer_id`))) where (`co`.`dtc` > (curdate() - interval 1 year)) group by `co`.`customer_id` having (`ct` > 5)) `above5_customers`) is false) union select `cu`.`firstName` AS `firstname`,`cu`.`lastName` AS `lastname`,`cu`.`email` AS `email`,`cu`.`phone` AS `phone`,`cu`.`isSubscribed` AS `issubscribed`,`co`.`company_name` AS `company_name`,`co`.`company_number` AS `company_number`,`co`.`incorporation_date` AS `incorporation_date`,`vp`.`productName` AS `productName`,`co`.`sic_code1` AS `sic_code1`,`co`.`company_category` AS `company_category`,`co`.`street` AS `street`,`co`.`post_town` AS `post_town`,`co`.`postcode` AS `postcode`,`co`.`company_status` AS `company_status`,`dir`.`forename` AS `forename`,`dir`.`surname` AS `surname`,`dir`.`corporate_name` AS `corporate_name`,`dir`.`type` AS `type`,`dir`.`dob` AS `dob`,`dir_ct`.`directors` AS `NumberDirectors`,`lastorder`.`total` AS `total`,`lastorder`.`customerName` AS `customerName`,`lastorder`.`dtc` AS `dtc`,`co`.`incorporation_date` AS `MPChosen`,`co_ct`.`NumberCompanies` AS `NumberCompanies`,********* AS `newchannel`,1 AS `newleadsource`,4 AS `crmtypecode`,'Enquiry Made Simple Number - PPS' AS `Subject`,9 AS `ownertype`,'{8e7ef4d3-8846-ea11-a812-000d3a2276dc}' AS `owner`,concat('Product=',`vp`.`productName`,'--','Order Total=',`lastorder`.`total`,'--','Company Number=',`co_ct`.`NumberCompanies`) AS `taskdescription`,`bank`.`bankTypeId` AS `MSGBank`,`a`.`tradingStart` AS `tradingStart`,`a`.`text` AS `bestDescribes`,ifnull(`mpc`.`businessPhoneOption`,'NO') AS `businessPhoneOption` from ((((((((((`business_services_lead` `l` join `ch_company` `co` on((`co`.`company_id` = `l`.`company_id`))) left join `cms2_customers` `cu` on((`cu`.`customerId` = `co`.`customer_id`))) left join `view_products` `vp` on((`vp`.`productId` = `co`.`product_id`))) left join `mp_clevernumbers_leads` `mpc` on((`mpc`.`companyId` = `co`.`company_id`))) left join (select `cm`.`company_id` AS `company_id`,`cm`.`forename` AS `forename`,`cm`.`surname` AS `surname`,`cm`.`corporate_name` AS `corporate_name`,`cm`.`type` AS `type`,`cm`.`dob` AS `dob` from `ch_company_member` `cm` where ((`cm`.`discriminator` = 'DIR_0') and (`cm`.`dob` > '1900-01-01') and `cm`.`company_id` in (select `c`.`company_id` from (`business_services_lead` `l` join `ch_company` `c` on((`c`.`company_id` = `l`.`company_id`))) where ((`l`.`offer_id` = 6) and (`c`.`incorporation_date` > (curdate() - interval 20 day))))) group by `cm`.`company_id`) `dir` on((`dir`.`company_id` = `co`.`company_id`))) left join (select `cm`.`company_id` AS `company_id`,count(0) AS `directors` from `ch_company_member` `cm` where ((`cm`.`type` = 'DIR') and (`cm`.`dob` > '1900-01-01') and `cm`.`company_id` in (select `c`.`company_id` from (`business_services_lead` `l` join `ch_company` `c` on((`c`.`company_id` = `l`.`company_id`))) where ((`l`.`offer_id` = 6) and (`c`.`incorporation_date` > (curdate() - interval 20 day))))) group by `cm`.`company_id`) `dir_ct` on((`dir_ct`.`company_id` = `co`.`company_id`))) left join (select `o`.`customerId` AS `customerId`,`o`.`total` AS `total`,`o`.`customerName` AS `customerName`,`o`.`dtc` AS `dtc` from `cms2_orders` `o` where `o`.`orderId` in (select max(`co`.`orderId`) from (`cms2_orders` `co` join (select `c`.`customer_id` AS `customer_id` from (`business_services_lead` `l` join `ch_company` `c` on((`c`.`company_id` = `l`.`company_id`))) where ((`l`.`offer_id` = 6) and (`c`.`incorporation_date` > (curdate() - interval 20 day)))) `cu` on((`co`.`customerId` = `cu`.`customer_id`))) group by `co`.`customerId`)) `lastorder` on((`lastorder`.`customerId` = `co`.`customer_id`))) left join (select `co`.`customer_id` AS `customer_id`,count(0) AS `NumberCompanies` from `ch_company` `co` where `co`.`customer_id` in (select `c`.`customer_id` from (`business_services_lead` `l` join `ch_company` `c` on((`c`.`company_id` = `l`.`company_id`))) where ((`l`.`offer_id` = 6) and (`c`.`incorporation_date` > (curdate() - interval 20 day)))) group by `co`.`customer_id`) `co_ct` on((`co_ct`.`customer_id` = `co`.`customer_id`))) left join (select `cc`.`companyId` AS `companyId`,`cc`.`bankTypeId` AS `bankTypeId` from `cms2_company_customer` `cc` where `cc`.`companyId` in (select `c`.`company_id` from (`business_services_lead` `l` join `ch_company` `c` on((`c`.`company_id` = `l`.`company_id`))) where ((`l`.`offer_id` = 6) and (`c`.`incorporation_date` > (curdate() - interval 20 day)))) group by `cc`.`companyId`) `bank` on((`bank`.`companyId` = `co`.`company_id`))) left join `cms2_answers` `a` on((`a`.`customerId` = `co`.`customer_id`))) where ((`l`.`offer_id` = 6) and (`co`.`incorporation_date` > (curdate() - interval 20 day))) union select (`o`.`firstName` collate utf8_general_ci) AS `o.firstname COLLATE utf8_general_ci`,(`o`.`lastName` collate utf8_general_ci) AS `o.lastname COLLATE utf8_general_ci`,(`o`.`email` collate utf8_general_ci) AS `o.email COLLATE utf8_general_ci`,(`o`.`phone` collate utf8_general_ci) AS `o.phone COLLATE utf8_general_ci`,`cu`.`isSubscribed` AS `issubscribed`,(`o`.`companyName` collate utf8_general_ci) AS `company_name`,`co`.`company_number` AS `company_number`,`co`.`incorporation_date` AS `incorporation_date`,`vp`.`productName` AS `productName`,`co`.`sic_code1` AS `sic_code1`,`co`.`company_category` AS `company_category`,`co`.`street` AS `street`,`co`.`post_town` AS `post_town`,(`o`.`postcode` collate utf8_general_ci) AS `o.postcode COLLATE utf8_general_ci`,`co`.`company_status` AS `company_status`,`dir`.`forename` AS `forename`,`dir`.`surname` AS `surname`,`dir`.`corporate_name` AS `corporate_name`,`dir`.`type` AS `type`,`dir`.`dob` AS `dob`,`dir_ct`.`directors` AS `NumberDirectors`,`lastorder`.`total` AS `total`,`lastorder`.`customerName` AS `customerName`,`lastorder`.`dtc` AS `dtc`,`co`.`incorporation_date` AS `MPChosen`,`co_ct`.`NumberCompanies` AS `NumberCompanies`,********* AS `newchannel`,********* AS `newleadsource`,4 AS `crmtypecode`,'Applications submitted' AS `Subject`,9 AS `ownertype`,'{8e7ef4d3-8846-ea11-a812-000d3a2276dc}' AS `owner`,concat('Product=',`vp`.`productName`,'--','Order Total=',`lastorder`.`total`,'--','Company Number=',`co_ct`.`NumberCompanies`) AS `taskdescription`,`bank`.`bankTypeId` AS `MSGBank`,`a`.`tradingStart` AS `tradingStart`,`a`.`text` AS `bestDescribes`,ifnull(`mpc`.`businessPhoneOption`,'NO') AS `businessPhoneOption` from (((((((((((`cms2_offer_customer_details` `o` left join `cms2_nodes` `n` on((`n`.`node_id` = `o`.`productId`))) left join `cms2_customers` `cu` on((`cu`.`customerId` = `o`.`customerId`))) left join `ch_company` `co` on((`co`.`company_id` = `o`.`companyId`))) left join `mp_clevernumbers_leads` `mpc` on((`mpc`.`companyId` = `o`.`companyId`))) left join (select `cm`.`company_id` AS `company_id`,`cm`.`forename` AS `forename`,`cm`.`surname` AS `surname`,`cm`.`corporate_name` AS `corporate_name`,`cm`.`type` AS `type`,`cm`.`dob` AS `dob` from `ch_company_member` `cm` where ((`cm`.`discriminator` = 'DIR_0') and (`cm`.`dob` > '1900-01-01') and `cm`.`company_id` in (select `c`.`company_id` from ((`cms2_offer_customer_details` `o` join `ch_company` `c` on((`c`.`company_id` = `o`.`companyId`))) left join `cms2_nodes` `n` on((`n`.`node_id` = `o`.`productId`))) where ((`n`.`name` = 'product_moneypenny') and (`c`.`incorporation_date` > (curdate() - interval 20 day))))) group by `cm`.`company_id`) `dir` on((`dir`.`company_id` = `o`.`companyId`))) left join (select `cm`.`company_id` AS `company_id`,count(0) AS `directors` from `ch_company_member` `cm` where ((`cm`.`type` = 'DIR') and (`cm`.`dob` > '1900-01-01') and `cm`.`company_id` in (select `c`.`company_id` from ((`cms2_offer_customer_details` `o` join `ch_company` `c` on((`c`.`company_id` = `o`.`companyId`))) left join `cms2_nodes` `n` on((`n`.`node_id` = `o`.`productId`))) where ((`n`.`name` = 'product_moneypenny') and (`c`.`incorporation_date` > (curdate() - interval 20 day))))) group by `cm`.`company_id`) `dir_ct` on((`dir_ct`.`company_id` = `co`.`company_id`))) left join (select `o`.`customerId` AS `customerId`,`o`.`total` AS `total`,`o`.`customerName` AS `customerName`,`o`.`dtc` AS `dtc` from `cms2_orders` `o` where `o`.`orderId` in (select max(`co`.`orderId`) from (`cms2_orders` `co` join (select `o`.`customerId` AS `customerId` from ((`cms2_offer_customer_details` `o` join `ch_company` `c` on((`c`.`company_id` = `o`.`companyId`))) left join `cms2_nodes` `n` on((`n`.`node_id` = `o`.`productId`))) where ((`n`.`name` = 'product_moneypenny') and (`c`.`incorporation_date` > (curdate() - interval 20 day)))) `cu` on((`co`.`customerId` = `cu`.`customerId`))) group by `co`.`customerId`)) `lastorder` on((`lastorder`.`customerId` = `co`.`customer_id`))) left join (select `co`.`customer_id` AS `customer_id`,count(0) AS `NumberCompanies` from `ch_company` `co` where `co`.`customer_id` in (select `o`.`customerId` from ((`cms2_offer_customer_details` `o` join `ch_company` `c` on((`c`.`company_id` = `o`.`companyId`))) left join `cms2_nodes` `n` on((`n`.`node_id` = `o`.`productId`))) where ((`n`.`name` = 'product_moneypenny') and (`c`.`incorporation_date` > (curdate() - interval 20 day)))) group by `co`.`customer_id`) `co_ct` on((`co_ct`.`customer_id` = `co`.`customer_id`))) left join (select `cc`.`companyId` AS `companyId`,`cc`.`bankTypeId` AS `bankTypeId` from `cms2_company_customer` `cc` where `cc`.`companyId` in (select `o`.`customerId` from ((`cms2_offer_customer_details` `o` join `ch_company` `c` on((`c`.`company_id` = `o`.`companyId`))) left join `cms2_nodes` `n` on((`n`.`node_id` = `o`.`productId`))) where ((`n`.`name` = 'product_moneypenny') and (`c`.`incorporation_date` > (curdate() - interval 20 day)))) group by `cc`.`companyId`) `bank` on((`bank`.`companyId` = `co`.`company_id`))) left join `cms2_answers` `a` on((`a`.`customerId` = `co`.`customer_id`))) left join `view_products` `vp` on((`vp`.`productId` = `o`.`productId`))) where ((`n`.`name` = 'product_moneypenny') and (`co`.`incorporation_date` > (curdate() - interval 20 day)));
CREATE OR REPLACE VIEW `leads_worldpay` AS select `tk`.`email` AS `email`,'EXCLUSIVE_OFFERS' AS `type`,`tk`.`dtc` AS `dtc` from `cms2_offer_customer_details` `tk` where (`tk`.`productId` = 1458) union select `bsp`.`email` AS `email`,'BUSINESS_SERVICES' AS `type`,`bs`.`dtm` AS `dtc` from (`business_services_lead` `bs` left join `business_services_partner_information` `bsp` on((`bsp`.`lead_id` = `bs`.`id`))) where ((`bs`.`offer_id` = 11) and (`bs`.`processed` = 1));
CREATE OR REPLACE VIEW `leads_insurance_takeup` AS select `bs`.`dateLead` AS `dateLead`,`co`.`companies` AS `companies`,`bs`.`leads` AS `leads` from ((select cast(`bs`.`dtm` as date) AS `dateLead`,count(`bs`.`id`) AS `leads` from `business_services_lead` `bs` where ((`bs`.`offer_id` in (1,2)) and (`bs`.`processed` = 1) and (`bs`.`dtm` >= '2017-11-28')) group by cast(`bs`.`dtm` as date)) `bs` join (select cast(`c`.`incorporation_date` as date) AS `dateIncorp`,count(`c`.`company_id`) AS `companies` from `ch_company` `c` where ((`c`.`product_id` in (1313,1314,1315,1316,1317,1175,379,436)) and (`c`.`incorporation_date` >= '2017-11-28')) group by cast(`c`.`incorporation_date` as date)) `co` on((`co`.`dateIncorp` = `bs`.`dateLead`)));
CREATE OR REPLACE VIEW `leads_business_services_offers` AS select `bs`.`dateLead` AS `dateLead`,`co`.`companies` AS `companies`,`bs`.`insuranceLeads` AS `insuranceLeads`,`bs`.`kashflowLeads` AS `kashflowLeads` from ((select cast(`bs`.`dtm` as date) AS `dateLead`,sum(if((`bs`.`offer_id` < 3),1,0)) AS `insuranceLeads`,sum(if((`bs`.`offer_id` = 3),1,0)) AS `kashflowLeads` from `business_services_lead` `bs` where ((`bs`.`processed` = 1) and (`bs`.`dtm` >= '2017-11-28')) group by cast(`bs`.`dtm` as date)) `bs` join (select cast(`c`.`incorporation_date` as date) AS `dateIncorp`,count(`c`.`company_id`) AS `companies` from `ch_company` `c` where ((`c`.`product_id` in (1313,1314,1315,1316,1317,1175,379,436)) and (`c`.`incorporation_date` >= '2017-11-28')) group by cast(`c`.`incorporation_date` as date)) `co` on((`co`.`dateIncorp` = `bs`.`dateLead`)));
CREATE OR REPLACE VIEW `financials` AS select `o`.`dtc` AS `dtc`,`oi`.`productId` AS `ProductId`,`v`.`productName` AS `ProductName`,`oi`.`orderId` AS `OrderId`,`oi`.`orderItemId` AS `OrderItemId`,`o`.`total` AS `OrderTotal`,`o`.`subtotal` AS `OrderSubtotal`,`o`.`vat` AS `OrderVAT`,`oi`.`qty` AS `qty`,`oi`.`totalPrice` AS `ItemTotal`,`oi`.`subTotal` AS `ItemSubtotal`,`oi`.`vat` AS `ItemVAT`,`o`.`voucherName` AS `voucherName`,`oi`.`isRefunded` AS `isRefunded`,`oi`.`isRefund` AS `isRefund`,`c`.`email` AS `email`,`t`.`typeId` AS `TransactionType` from ((((`cms2_customers` `c` join `cms2_orders` `o` on((`c`.`customerId` = `o`.`customerId`))) join `cms2_orders_items` `oi` on((`o`.`orderId` = `oi`.`orderId`))) join `cms2_transactions` `t` on((`t`.`orderId` = `o`.`orderId`))) join `view_products` `v` on((`v`.`productId` = `oi`.`productId`))) where ((`o`.`dtc` >= '2021-10-01') and (`o`.`dtc` < '2021-11-01'));
CREATE OR REPLACE VIEW `customers` AS select `cu`.`customerId` AS `customerId`,`cu`.`statusId` AS `statusId`,`cu`.`roleId` AS `roleId`,`cu`.`tagId` AS `tagId`,`cu`.`icaewId` AS `icaewId`,`cu`.`myDetailsQuestion` AS `myDetailsQuestion`,`cu`.`email` AS `email`,`cu`.`password` AS `password`,`cu`.`titleId` AS `titleId`,`cu`.`firstName` AS `firstName`,`cu`.`middleName` AS `middleName`,`cu`.`lastName` AS `lastName`,`cu`.`dateOfBirth` AS `dateOfBirth`,`cu`.`companyName` AS `companyName`,`cu`.`address1` AS `address1`,`cu`.`address2` AS `address2`,`cu`.`address3` AS `address3`,`cu`.`city` AS `city`,`cu`.`county` AS `county`,`cu`.`postcode` AS `postcode`,`cu`.`countryId` AS `countryId`,`cu`.`phone` AS `phone`,`cu`.`additionalPhone` AS `additionalPhone`,`cu`.`isSubscribed` AS `isSubscribed`,`cu`.`howHeardId` AS `howHeardId`,`cu`.`industryId` AS `industryId`,`cu`.`credit` AS `credit`,`cu`.`affiliateId` AS `affiliateId`,`cu`.`activeCompanies` AS `activeCompanies`,`cu`.`monitoredActiveCompanies` AS `monitoredActiveCompanies`,`cu`.`cashbackType` AS `cashbackType`,`cu`.`accountNumber` AS `accountNumber`,`cu`.`sortCode` AS `sortCode`,`cu`.`isIdCheckRequired` AS `isIdCheckRequired`,`cu`.`dtIdValidated` AS `dtIdValidated`,`cu`.`isPartOfTheIdCheck` AS `isPartOfTheIdCheck`,`cu`.`canBelongToCustomerOnlyId` AS `canBelongToCustomerOnlyId`,`cu`.`mailboxAttachmentsEnabled` AS `mailboxAttachmentsEnabled`,`cu`.`deleted` AS `deleted`,`cu`.`dtc` AS `dtc`,`cu`.`dtm` AS `dtm`,`lt`.`title` AS `title` from (`cms2_customers` `cu` left join `lookup_titles` `lt` on((`lt`.`titleId` = `cu`.`titleId`)));
CREATE OR REPLACE VIEW `crm_services` AS select `s`.`serviceId` AS `serviceId`,`s`.`companyId` AS `companyId`,`s`.`serviceTypeId` AS `serviceTypeId`,`s`.`dtStart` AS `dtStart`,max(`s`.`dtExpires`) AS `dtExpires`,max(`s`.`dtm`) AS `dateUpdated` from `cms2_services` `s` where (`s`.`parentId` is null) group by `s`.`companyId`,`s`.`serviceTypeId`;
CREATE OR REPLACE VIEW `crm_customers` AS select `c`.`customerId` AS `customerId`,`c`.`roleId` AS `roleId`,`t`.`title` AS `title`,`c`.`firstName` AS `firstName`,`c`.`lastName` AS `lastName`,`c`.`email` AS `email`,`c`.`phone` AS `phone`,`c`.`address1` AS `address1`,`c`.`address2` AS `address2`,`c`.`city` AS `city`,`c`.`postcode` AS `postcode`,`c`.`countryId` AS `countryId`,`c`.`isSubscribed` AS `isSubscribed`,`c`.`statusId` AS `statusId`,`c`.`dtc` AS `dateCreated`,(case when (`co`.`company_id` is not null) then 1 else 0 end) AS `hasActiveCompany` from ((`cms2_customers` `c` left join `lookup_titles` `t` on((`t`.`titleId` = `c`.`titleId`))) left join (select `c`.`company_id` AS `company_id`,`c`.`customer_id` AS `customer_id`,`c`.`product_id` AS `product_id`,`c`.`is_certificate_printed` AS `is_certificate_printed`,`c`.`is_bronze_cover_letter_printed` AS `is_bronze_cover_letter_printed`,`c`.`is_ma_printed` AS `is_ma_printed`,`c`.`is_ma_cover_letter_printed` AS `is_ma_cover_letter_printed`,`c`.`company_name` AS `company_name`,`c`.`company_number` AS `company_number`,`c`.`authentication_code` AS `authentication_code`,`c`.`incorporation_date` AS `incorporation_date`,`c`.`dissolution_date` AS `dissolution_date`,`c`.`company_category` AS `company_category`,`c`.`jurisdiction` AS `jurisdiction`,`c`.`made_up_date` AS `made_up_date`,`c`.`premise` AS `premise`,`c`.`street` AS `street`,`c`.`thoroughfare` AS `thoroughfare`,`c`.`post_town` AS `post_town`,`c`.`county` AS `county`,`c`.`country` AS `country`,`c`.`postcode` AS `postcode`,`c`.`care_of_name` AS `care_of_name`,`c`.`po_box` AS `po_box`,`c`.`sail_premise` AS `sail_premise`,`c`.`sail_street` AS `sail_street`,`c`.`sail_thoroughfare` AS `sail_thoroughfare`,`c`.`sail_post_town` AS `sail_post_town`,`c`.`sail_county` AS `sail_county`,`c`.`sail_country` AS `sail_country`,`c`.`sail_postcode` AS `sail_postcode`,`c`.`sail_care_of_name` AS `sail_care_of_name`,`c`.`sail_po_box` AS `sail_po_box`,`c`.`sic_code1` AS `sic_code1`,`c`.`sic_code2` AS `sic_code2`,`c`.`sic_code3` AS `sic_code3`,`c`.`sic_code4` AS `sic_code4`,`c`.`sic_description` AS `sic_description`,`c`.`company_status` AS `company_status`,`c`.`country_of_origin` AS `country_of_origin`,`c`.`accounts_ref_date` AS `accounts_ref_date`,`c`.`accounts_overdue` AS `accounts_overdue`,`c`.`accounts_next_period_start_date` AS `accounts_next_period_start_date`,`c`.`accounts_next_period_end_date` AS `accounts_next_period_end_date`,`c`.`accounts_next_due_date` AS `accounts_next_due_date`,`c`.`accounts_last_type` AS `accounts_last_type`,`c`.`accounts_last_period_start_date` AS `accounts_last_period_start_date`,`c`.`accounts_last_made_up_date` AS `accounts_last_made_up_date`,`c`.`returns_next_made_up_date` AS `returns_next_made_up_date`,`c`.`returns_next_due_date` AS `returns_next_due_date`,`c`.`returns_last_made_up_date` AS `returns_last_made_up_date`,`c`.`returns_overdue` AS `returns_overdue`,`c`.`dca_id` AS `dca_id`,`c`.`order_id` AS `order_id`,`c`.`registered_office_id` AS `registered_office_id`,`c`.`service_address_id` AS `service_address_id`,`c`.`nominee_director_id` AS `nominee_director_id`,`c`.`nominee_secretary_id` AS `nominee_secretary_id`,`c`.`nominee_subscriber_id` AS `nominee_subscriber_id`,`c`.`annual_return_id` AS `annual_return_id`,`c`.`change_name_id` AS `change_name_id`,`c`.`ereminder_id` AS `ereminder_id`,`c`.`document_date` AS `document_date`,`c`.`document_id` AS `document_id`,`c`.`accepted_date` AS `accepted_date`,`c`.`cash_back_amount` AS `cash_back_amount`,`c`.`no_psc_reason` AS `no_psc_reason`,`c`.`locked` AS `locked`,`c`.`hidden` AS `hidden`,`c`.`deleted` AS `deleted`,`c`.`etag` AS `etag`,`c`.`dtLastSynced` AS `dtLastSynced`,`c`.`dtc` AS `dtc`,`c`.`dtm` AS `dtm` from `ch_company` `c` where ((`c`.`company_status` = 'Active') and (`c`.`hidden` = 0) and (`c`.`locked` = 0) and (`c`.`deleted` = 0) and (`c`.`company_number` is not null) and (not((`c`.`company_number` like 'DELETED_%')))) group by `c`.`customer_id`) `co` on((`co`.`customer_id` = `c`.`customerId`)));
CREATE OR REPLACE VIEW `crm_companies` AS select `c`.`company_id` AS `company_id`,`c`.`customer_id` AS `customer_id`,`c`.`company_name` AS `company_name`,`c`.`company_number` AS `company_number`,`c`.`incorporation_date` AS `incorporation_date`,`c`.`company_status` AS `company_status`,`c`.`dissolution_date` AS `dissolution_date`,`c`.`company_category` AS `company_category`,`c`.`sic_code1` AS `sic_code1`,`c`.`sic_code2` AS `sic_code2`,`c`.`sic_code3` AS `sic_code3`,`c`.`sic_code4` AS `sic_code4`,`c`.`product_id` AS `product_id`,`c`.`order_id` AS `order_id`,`c`.`hidden` AS `hidden`,`c`.`locked` AS `locked`,`c`.`deleted` AS `deleted`,`c`.`dtm` AS `date_updated` from `ch_company` `c`;
CREATE OR REPLACE VIEW `companies_without_active_services` AS select `c`.`company_id` AS `companyId`,`c`.`customer_id` AS `customerId`,concat(`cc`.`firstName`,' ',`cc`.`lastName`) AS `customerName`,`cc`.`email` AS `email`,`cc`.`roleId` AS `roleId`,`c`.`company_number` AS `companyNumber`,`c`.`company_name` AS `companyName`,`c`.`company_status` AS `companyStatus`,`c`.`product_id` AS `incorporationProductId`,`vp`.`productName` AS `incorporationProductName`,`c`.`incorporation_date` AS `incorporationDate`,`c`.`deleted` AS `deleted`,`s`.`serviceId` AS `lastServiceId`,`s`.`dtStart` AS `lastServiceIdDtStart`,`s`.`dtExpires` AS `lastServiceIdDtExpires`,`c`.`dtc` AS `companyDtc` from (((((`ch_company` `c` left join (select `s`.`companyId` AS `companyId`,max(`s`.`dtExpires`) AS `dtExpires` from ((`cms2_services` `s` left join `cms2_orders_items` `oi` on((`s`.`orderItemId` = `oi`.`orderItemId`))) left join `cms2_orders` `o` on((`s`.`orderId` = `o`.`orderId`))) where (true and (`s`.`stateId` = 'ENABLED') and ((`s`.`dtExpires` + interval 28 day) >= cast(now() as date)) and (`s`.`dtStart` <= cast(now() as date)) and ((`oi`.`isRefund` is null) or ((`oi`.`isRefund` = 0) and (`oi`.`isRefunded` is null)) or (`oi`.`isRefunded` = 0)) and (`s`.`dtExpires` is not null)) group by `s`.`companyId`) `active_services` on((`c`.`company_id` = `active_services`.`companyId`))) left join `cms2_customers` `cc` on((`cc`.`customerId` = `c`.`customer_id`))) left join (select `cms2_services`.`companyId` AS `companyId`,max(`cms2_services`.`serviceId`) AS `last_serviceId` from `cms2_services` where (true and (`cms2_services`.`stateId` = 'ENABLED') and (`cms2_services`.`dtStart` <= cast(now() as date)) and (`cms2_services`.`dtExpires` is not null)) group by `cms2_services`.`companyId`) `ls` on((`c`.`company_id` = `ls`.`companyId`))) left join `cms2_services` `s` on((`ls`.`last_serviceId` = `s`.`serviceId`))) left join `view_products` `vp` on((`vp`.`productId` = `c`.`product_id`))) where (true and (`active_services`.`companyId` is null) and (year(`c`.`dtc`) >= 2018) and (upper(`c`.`company_status`) not in ('DISSOLVED','LIQUIDATION','ACTIVE - PROPOSAL TO STRIKE OFF')) and ((`cc`.`deleted` = 0) or (`cc`.`deleted` is null))) group by `c`.`company_id` order by `c`.`company_status`,`c`.`deleted`,`s`.`dtExpires` desc;
CREATE OR REPLACE VIEW `banking_profile` AS select `chc`.`company_id` AS `company_id`,`chc`.`company_name` AS `company_name`,`chc`.`incorporation_date` AS `incorporation_date`,`chc`.`product_id` AS `product_id`,cast(max(if((`b`.`success` = 1),`b`.`date_sent`,NULL)) as date) AS `barclays_lead_sent`,cast(max(if((`cash`.`bankTypeId` = 'BARCLAYS'),`cash`.`dateAccountOpened`,NULL)) as date) AS `barclays_acc_opened`,cast(max(if(((`cash`.`bankTypeId` = 'BARCLAYS') and (`cash`.`statusId` = 'paid')),`cash`.`datePaid`,NULL)) as date) AS `barclays_paid_date`,max(if(((`cash`.`bankTypeId` = 'BARCLAYS') and (`cash`.`statusId` = 'paid')),`cash`.`amount`,NULL)) AS `barclays_amount_paid`,cast(`ts`.`dtc` as date) AS `tsb_lead_sent`,cast(max(if((`cash`.`bankTypeId` = 'TSB'),`cash`.`dateAccountOpened`,NULL)) as date) AS `tsb_acc_opened`,cast(max(if(((`cash`.`bankTypeId` = 'TSB') and (`cash`.`statusId` = 'paid')),`cash`.`datePaid`,NULL)) as date) AS `tsb_paid_date`,max(if(((`cash`.`bankTypeId` = 'TSB') and (`cash`.`statusId` = 'paid')),`cash`.`amount`,NULL)) AS `tsb_amount_paid`,cast(max(if(((`ti`.`request` is not null) and (`ti`.`error` is null)),`ti`.`dtc`,NULL)) as date) AS `tide_lead_sent`,cast(max(if(((`bs`.`offer_id` = 8) and (`bs`.`processed` = 1)),`bs`.`dtm`,NULL)) as date) AS `cashplus_lead_sent` from (((((`ch_company` `chc` left join `ch_barclays` `b` on((`chc`.`company_id` = `b`.`company_id`))) left join `cms2_cashback` `cash` on((`cash`.`companyId` = `chc`.`company_id`))) left join `ch_tide_leads` `ti` on((`ti`.`companyId` = `chc`.`company_id`))) left join `ch_tsb_leads` `ts` on((`ts`.`companyId` = `chc`.`company_id`))) left join `business_services_lead` `bs` on((`bs`.`company_id` = `chc`.`company_id`))) where ((cast(`chc`.`incorporation_date` as date) >= '2018-01-01') and (`chc`.`product_id` in (1313,1315,1316,1317))) group by `chc`.`company_id`;
CREATE OR REPLACE VIEW `bank_leads_percentage` AS select `bank`.`day` AS `day`,`bank`.`core_bank_ratio_formed` AS `core_bank_ratio_formed` from (select `c`.`month` AS `day`,`c`.`companies_formed` AS `companies_formed`,`c`.`core_formed` AS `core_formed`,`b`.`bank_leads` AS `bank_leads`,`b`.`core_bank_leads` AS `core_bank_leads`,round(((`b`.`core_bank_leads` / `c`.`core_formed`) * 100),0) AS `core_bank_ratio_formed`,`b`.`barclays_leads` AS `barclays_leads`,`b`.`tide_leads` AS `tide_leads`,(`c`.`core_formed` - `b`.`core_bank_leads`) AS `no_bank_core`,round((((`c`.`core_formed` - `b`.`core_bank_leads`) / `c`.`core_formed`) * 100),0) AS `no_bank_ratio_core` from ((select cast(`co`.`incorporation_date` as date) AS `month`,count(0) AS `companies_formed`,sum(if((`co`.`product_id` in (1313,1314,1315,1316,1317)),1,0)) AS `core_formed` from `ch_company` `co` where ((`co`.`incorporation_date` >= '2018-01-01') and (weekday(`co`.`incorporation_date`) < 5) and (`co`.`product_id` is not null) and (`co`.`company_number` is not null)) group by cast(`co`.`incorporation_date` as date)) `c` join (select cast(`cc`.`dtc` as date) AS `month`,count(0) AS `bank_leads`,sum(if((`co`.`product_id` in (1313,1314,1315,1316,1317)),1,0)) AS `core_bank_leads`,sum(if((`cc`.`bankTypeId` = 'BARCLAYS'),1,0)) AS `barclays_leads`,sum(if((`cc`.`bankTypeId` = 'TIDE'),1,0)) AS `tide_leads` from (`cms2_company_customer` `cc` join `ch_company` `co` on((`co`.`company_id` = `cc`.`companyId`))) where ((`cc`.`dtc` >= '2018-01-01') and (weekday(`cc`.`dtc`) < 5)) group by cast(`cc`.`dtc` as date)) `b` on((`b`.`month` = `c`.`month`)))) `bank` where (`bank`.`core_bank_ratio_formed` < 90);
CREATE OR REPLACE VIEW `active_companies` AS select `c`.`company_id` AS `company_id`,`c`.`customer_id` AS `customer_id`,`c`.`product_id` AS `product_id`,`c`.`is_certificate_printed` AS `is_certificate_printed`,`c`.`is_bronze_cover_letter_printed` AS `is_bronze_cover_letter_printed`,`c`.`is_ma_printed` AS `is_ma_printed`,`c`.`is_ma_cover_letter_printed` AS `is_ma_cover_letter_printed`,`c`.`company_name` AS `company_name`,`c`.`company_number` AS `company_number`,`c`.`authentication_code` AS `authentication_code`,`c`.`incorporation_date` AS `incorporation_date`,`c`.`dissolution_date` AS `dissolution_date`,`c`.`company_category` AS `company_category`,`c`.`jurisdiction` AS `jurisdiction`,`c`.`made_up_date` AS `made_up_date`,`c`.`premise` AS `premise`,`c`.`street` AS `street`,`c`.`thoroughfare` AS `thoroughfare`,`c`.`post_town` AS `post_town`,`c`.`county` AS `county`,`c`.`country` AS `country`,`c`.`postcode` AS `postcode`,`c`.`care_of_name` AS `care_of_name`,`c`.`po_box` AS `po_box`,`c`.`sail_premise` AS `sail_premise`,`c`.`sail_street` AS `sail_street`,`c`.`sail_thoroughfare` AS `sail_thoroughfare`,`c`.`sail_post_town` AS `sail_post_town`,`c`.`sail_county` AS `sail_county`,`c`.`sail_country` AS `sail_country`,`c`.`sail_postcode` AS `sail_postcode`,`c`.`sail_care_of_name` AS `sail_care_of_name`,`c`.`sail_po_box` AS `sail_po_box`,`c`.`sic_code1` AS `sic_code1`,`c`.`sic_code2` AS `sic_code2`,`c`.`sic_code3` AS `sic_code3`,`c`.`sic_code4` AS `sic_code4`,`c`.`sic_description` AS `sic_description`,`c`.`company_status` AS `company_status`,`c`.`country_of_origin` AS `country_of_origin`,`c`.`accounts_ref_date` AS `accounts_ref_date`,`c`.`accounts_overdue` AS `accounts_overdue`,`c`.`accounts_next_period_start_date` AS `accounts_next_period_start_date`,`c`.`accounts_next_period_end_date` AS `accounts_next_period_end_date`,`c`.`accounts_next_due_date` AS `accounts_next_due_date`,`c`.`accounts_last_type` AS `accounts_last_type`,`c`.`accounts_last_period_start_date` AS `accounts_last_period_start_date`,`c`.`accounts_last_made_up_date` AS `accounts_last_made_up_date`,`c`.`returns_next_made_up_date` AS `returns_next_made_up_date`,`c`.`returns_next_due_date` AS `returns_next_due_date`,`c`.`returns_last_made_up_date` AS `returns_last_made_up_date`,`c`.`returns_overdue` AS `returns_overdue`,`c`.`dca_id` AS `dca_id`,`c`.`order_id` AS `order_id`,`c`.`registered_office_id` AS `registered_office_id`,`c`.`service_address_id` AS `service_address_id`,`c`.`nominee_director_id` AS `nominee_director_id`,`c`.`nominee_secretary_id` AS `nominee_secretary_id`,`c`.`nominee_subscriber_id` AS `nominee_subscriber_id`,`c`.`annual_return_id` AS `annual_return_id`,`c`.`change_name_id` AS `change_name_id`,`c`.`ereminder_id` AS `ereminder_id`,`c`.`document_date` AS `document_date`,`c`.`document_id` AS `document_id`,`c`.`accepted_date` AS `accepted_date`,`c`.`cash_back_amount` AS `cash_back_amount`,`c`.`no_psc_reason` AS `no_psc_reason`,`c`.`locked` AS `locked`,`c`.`hidden` AS `hidden`,`c`.`deleted` AS `deleted`,`c`.`etag` AS `etag`,`c`.`dtLastSynced` AS `dtLastSynced`,`c`.`dtc` AS `dtc`,`c`.`dtm` AS `dtm` from `ch_company` `c` where ((`c`.`company_status` = 'Active') and (`c`.`hidden` = 0) and (`c`.`locked` = 0) and (`c`.`deleted` = 0) and (`c`.`company_number` is not null) and (not((`c`.`company_number` like 'DELETED_%'))));
CREATE OR REPLACE VIEW `active_companies_profile` AS select `co`.`company_id` AS `company_id`,`co`.`customer_id` AS `customer_id`,`co`.`company_name` AS `company_name`,`co`.`company_number` AS `company_number`,`co`.`incorporation_date` AS `incorporation_date`,`co`.`company_status` AS `company_status`,`co`.`company_category` AS `company_category`,`co`.`sic_code1` AS `sic_code1`,`co`.`sic_code2` AS `sic_code2`,`co`.`sic_code3` AS `sic_code3`,`co`.`sic_code4` AS `sic_code4`,`co`.`productName` AS `productName`,`co`.`company_city` AS `company_city`,`co`.`company_postcode` AS `company_postcode`,`co`.`directors` AS `directors`,if((`cu`.`roleId` = 'normal'),'retail',`cu`.`roleId`) AS `account_type`,`lt`.`title` AS `account_title`,`cu`.`firstName` AS `account_firstname`,`cu`.`lastName` AS `account_lastname`,`cu`.`email` AS `account_email`,`cu`.`phone` AS `account_phone`,`cu`.`city` AS `account_city`,`cu`.`postcode` AS `account_postcode`,`lc`.`country` AS `account_country`,`cu`.`isSubscribed` AS `isSubscribed` from ((((select `co`.`company_id` AS `company_id`,`co`.`customer_id` AS `customer_id`,`co`.`company_name` AS `company_name`,`co`.`company_number` AS `company_number`,`co`.`incorporation_date` AS `incorporation_date`,`co`.`company_status` AS `company_status`,`co`.`company_category` AS `company_category`,`co`.`sic_code1` AS `sic_code1`,`co`.`sic_code2` AS `sic_code2`,`co`.`sic_code3` AS `sic_code3`,`co`.`sic_code4` AS `sic_code4`,`vp`.`productName` AS `productName`,`co`.`post_town` AS `company_city`,`co`.`postcode` AS `company_postcode`,sum(if((`cm`.`type` = 'DIR'),1,0)) AS `directors` from ((`active_companies` `co` left join `ch_company_member` `cm` on((`cm`.`company_id` = `co`.`company_id`))) left join `view_products` `vp` on((`vp`.`productId` = `co`.`product_id`))) where (`cm`.`type` = 'DIR') group by `co`.`company_id`) `co` left join `cms2_customers` `cu` on((`cu`.`customerId` = `co`.`customer_id`))) left join `lookup_titles` `lt` on((`lt`.`titleId` = `cu`.`titleId`))) left join `lookup_countries` `lc` on((`lc`.`countryId` = `cu`.`countryId`))) where (`cu`.`statusId` = 2);
