Entities\Order:
  order{1}:
    __construct: false
    customer: @customer1
    realSubtotal: 51.98
    subtotal: 51.98
    discount: 0.00
    vat: 7.80
    total: 59.78
    dtc: 2014-10-03 09:20:03
    dtm: 2014-10-03 09:20:03
    customerName: @customer1->firstName @customer1->lastName
    customerAddress: @customer1->address1 - @customer1->postCode - @customer1->countryId
    customerPhone: @customer1->phone
    customerEmail: <current()>@example.org
    voucherId: 
    voucherName: 
    description: Companies Made Simple
    statusId: 1
    isRefunded: 
    refundValue: 
    refundCustomerSupportLogin: 
  order{2}:
    __construct: false
    customer: @customer1
    realSubtotal: 49.99
    subtotal: 49.99
    discount: 0.00
    vat: 10.00
    total: 59.99
    dtc: 2014-10-03 09:56:26
    dtm: 2014-10-03 09:56:26
    customerName: @customer1->firstName @customer1->lastName
    customerAddress: @customer1->address1 - @customer1->postCode - @customer1->countryId
    customerPhone: @customer1->phone
    customerEmail: <current()>@example.org
    voucherId: 
    voucherName: 
    description: Companies Made Simple
    statusId: 1
    isRefunded: 
    refundValue: 
    refundCustomerSupportLogin: 
  order{3}:
    __construct: false
    customer: @customer1
    realSubtotal: 40.00
    subtotal: 40.00
    discount: 0.00
    vat: 8.00
    total: 48.00
    dtc: 2014-10-03 12:26:04
    dtm: 2014-10-03 12:26:04
    customerName: @customer1->firstName @customer1->lastName
    customerAddress: @customer1->address1 - @customer1->postCode - @customer1->countryId
    customerPhone: @customer1->phone
    customerEmail: <current()>@example.org
    voucherId: 
    voucherName: 
    description: Companies Made Simple
    statusId: 1
    isRefunded: 
    refundValue: 
    refundCustomerSupportLogin: