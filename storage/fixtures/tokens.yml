Utils\Date (local):
  date:
    __construct: ['2017-03-31']

Entities\Payment\Token:
  token1:
    __construct: false
    cardNumber: 00 6
    cardHolder: PIPPO
    cardType: VISA
    cardExpiryDate: @date
    identifier: 93C4DDFC-B5BB-99F8-C45E-5417B3728EE6
    description: 
    billingSurname: @customer1->lastName
    billingFirstnames: @customer1->firstName
    billingAddress1: @customer1->address1
    billingAddress2: 
    billingAddress3:
    billingCity: @customer1->city
    billingPostCode: @customer1->postCode
    billingCountry: @customer1->countryId
    billingState: 
    billingPhone: 
    deliverySurname: $billingSurname
    deliveryFirstnames: $billingFirstnames
    deliveryAddress1: $billingAddress1
    deliveryAddress2: 
    deliveryAddress3:
    deliveryCity: $billingCity
    deliveryPostCode: $billingPostCode
    deliveryCountry: $billingCountry
    deliveryState: 
    deliveryPhone: 
    isCurrent: 1
    sageStatus: ACTIVE
    customer: @customer1