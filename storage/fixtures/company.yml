Entities\Company:
  company{1..10}:
    __construct: false
    customer: @customer1
    productId: 1315
    isCertificatePrinted: 0
    isBronzeCoverLetterPrinted: 1
    isMaPrinted: 1
    isMaCoverLetterPrinted: 1
    companyName: <company()>
    order: @order1
    registeredOfficeId: 165
    serviceAddressId: 475
    nomineeDirectorId:
    nomineeSecretaryId:
    nomineeSubscriberId:
    annualReturnId: 340
    changeNameId:
    ereminderId: 1
    documentId:
    locked: 0
    hidden: 0
    deleted: 0
    dtc: 2014-10-03 09:20:03
    dtm: 2014-10-03 12:26:05
  company{11..260}:
    __construct: false
    customer: @customer1
    productId: 1315
    isCertificatePrinted: 0
    isBronzeCoverLetterPrinted: 1
    isMaPrinted: 1
    isMaCoverLetterPrinted: 1
    companyName: <company()>
    companyNumber: <randomNumber(8)>
    companyStatus: "Active"
    incorporationDate: <dateTime()>
    accountsNextDueDate: <dateTime()>
    returnsNextDueDate: <dateTime()>
    order: @order1
    registeredOfficeId: 165
    serviceAddressId: 475
    nomineeDirectorId:
    nomineeSecretaryId:
    nomineeSubscriberId:
    annualReturnId: 340
    changeNameId:
    ereminderId: 1
    documentId:
    locked: 0
    hidden: 0
    deleted: 0
    dtc: 2014-10-03 09:20:03
    dtm: 2014-10-03 12:26:05
  company{261..510}:
    __construct: false
    customer: @customer1
    productId: 1315
    isCertificatePrinted: 0
    isBronzeCoverLetterPrinted: 1
    isMaPrinted: 1
    isMaCoverLetterPrinted: 1
    companyName: <company()>
    companyNumber: <randomNumber(8)>
    companyStatus: "Dissolved"
    incorporationDate: <dateTime()>
    accountsNextDueDate: <dateTime()>
    returnsNextDueDate: <dateTime()>
    order: @order1
    registeredOfficeId: 165
    serviceAddressId: 475
    nomineeDirectorId:
    nomineeSecretaryId:
    nomineeSubscriberId:
    annualReturnId: 340
    changeNameId:
    ereminderId: 1
    documentId:
    locked: 0
    hidden: 0
    deleted: 0
    dtc: 2014-10-03 09:20:03
    dtm: 2014-10-03 12:26:05