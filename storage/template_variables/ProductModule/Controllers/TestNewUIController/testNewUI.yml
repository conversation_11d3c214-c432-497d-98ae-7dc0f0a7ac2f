seo:
    title: TEST PRODUCT PAGE | Companies MadeSimple
    description: description about this page
    keywords: Virtual Office, mail forwarding

introductionStrip:
    title: TEST PRODUCT PAGE NEW UI
    lead: This page is using the Bootstrap5
    imgFeature: /front/imgs/img1233.jpg
    imgFeatureDescription: Mail Forwarding
    description|markdown|allow_paragraph: |
        I've got all the 17 components that we use on the PRODUCTS template page and put them here as example.

featuresStrip:
    bgColor: bg-white
    features:
        -
            title: "Component: featuresStrip (1)"
            imgFeature: /front/imgs/anna/ANNA-ICON-UK-Sort-Codex2.png
            description|markdown: |
                Example on: anna-business-banking.html
        -
            title: "Component: featuresStrip (2)"
            imgFeature: /front/imgs/anna/ANNA-ICON-UK-Sort-Codex2.png
            description|markdown: |
                Example on: anna-business-banking.html

additionalFlash:
    postItemRenew:
        message: In order to access this item of post you need to have a Registered Office service for this company. This allows you to use our address as your company's address, plus you'll receive all your official government mail by email. You can purchase the service below:

matrixStrip:
    blocks:
        -
            title: 'Component: matrixStrip (1)'
            description|markdown: |
                Example on: fast-track-business-banking.html
            price: £[price 115]
            buyButton: '[ui name="buy_button" productId="115" size="m" loading="expand-right"]'
            productId: 115
        -
            title: 'Component: matrixStrip (2)'
            description|markdown: |
                Example on: fast-track-business-banking.html
            price: £[price 115]
            buyButton: '[ui name="buy_button" productId="115" size="m" loading="expand-right"]'
            productId: 115
        -
            title: 'Component: matrixStrip (3)'
            description|markdown: |
                Example on: fast-track-business-banking.html
            price: £[price 115]
            buyButton: '[ui name="buy_button" productId="115" size="m" loading="expand-right"]'
            productId: 115

matrixStrip2:
    secondaryBlocks:
        -
            title: 'Component: matrixStrip2 (1)'
            description|markdown: |
                Example on: certificate-of-good-standing.html
            price: £[price 1748]
            buyButton: '[ui name="buy_button" productId="1748" size="m" loading="expand-right"]'
            productId: 1748
        -
            title: 'Component: matrixStrip2 (2)'
            description|markdown: |
                Example on: certificate-of-good-standing.html
            price: £[price 1748]
            buyButton: '[ui name="buy_button" productId="1748" size="m" loading="expand-right"]'
            productId: 1748
        -
            title: 'Component: matrixStrip2 (3)'
            description|markdown: |
                Example on: certificate-of-good-standing.html
            price: £[price 1748]
            buyButton: '[ui name="buy_button" productId="1748" size="m" loading="expand-right"]'
            productId: 1748

secondaryDescription:
    title: 'Component: secondaryDescription'
    description|markdown: |
        Some text here

faqStrip:
    title: 'Component: faqStrip'
    faqBlock:
        -
            title: How much are the delivery costs?
            description|markdown|allow_paragraph: |
                Some text here
        -
            title: Are the company stamp and seal customisable?
            description|markdown|allow_paragraph: |
                Some text here

featuresColLeft:
    features:
        -
            title: 'Component: featuresColLeft'
            description|markdown: |
                Example on: psc-online-register.html 

featuresColRight:
    features:
        -
            title: 'Component: featuresColRight'
            description|markdown: |
                Example on: psc-online-register.html

footer:
    description|markdown: |
        ## Component: footer 

matrixStripGeneralProducts:
    -
        title: 'Component: matrixStripGeneralProducts (1)'
        description|markdown: |
            Example on: general-company-products.html
        price: £[price 567]
        buyButton: '[ui name="buy_button" productId="567" size="m" loading="expand-right"]'
        productId: 567
    -
        title: 'Component: matrixStripGeneralProducts (2)'
        description|markdown: |
            Example on: general-company-products.html
        price: £[price 567]
        buyButton: '[ui name="buy_button" productId="567" size="m" loading="expand-right"]'
        productId: 567
    -
        title: 'Component: matrixStripGeneralProducts (3)'
        description|markdown: |
            Example on: general-company-products.html
        price: £[price 567]
        buyButton: '[ui name="buy_button" productId="567" size="m" loading="expand-right"]'
        productId: 567

generalProductsDescription:
    title: 'Component: generalProductsDescription'

productStrip:
    products:
        -
            class: new-product-margin
            productId: 475
            title: 'Component: productStrip (1)'
            content: '' # content from Ghost or other source
            description|markdown: |
                Example on: company-protection.html
            price: £[price 475]
            buyButton: '[ui name="buy_button" productId="475" size="s" loading="expand-right"]'
        -
            class: new-product-margin
            productId: 475
            title: 'Component: productStrip (2)'
            content: '' # content from Ghost or other source
            description|markdown: |
                Example on: company-protection.html
            price: £[price 475]
            buyButton: '[ui name="buy_button" productId="475" size="s" loading="expand-right"]'
        -
            class: new-product-margin
            productId: 475
            title: 'Component: productStrip (3)'
            content: '' # content from Ghost or other source
            description|markdown: |
                Example on: company-protection.html
            price: £[price 475]
            buyButton: '[ui name="buy_button" productId="475" size="s" loading="expand-right"]'

whyUseROStrip:
    title: 'Component: whyUseROStrip'
    #content: '[ghost slug="company-services-registered-office-whyuserostrip"]'
    description: Example -> company-services-registered-office.html - We can get content from GHOST API if so youwill have to disable the title and put the Ghost tag on content
    lists:
        -   item: 'aaaaaaaa'
        -   item: 'bbbbbbbb'
        -   item: 'bbbbbbbb'

instructionROStrip:
    title: 'Component: instructionROStrip'
    #content: '[ghost slug="company-services-registered-office-instructionrostrip"]'
    description: Example -> company-services-registered-office.html - We can get content from GHOST API

waysToBuyROStrip:
    title: 'Component: waysToBuyROStrip'
    buyWays:
        -
            title: Get the service address service with a company formation package
            description|markdown: |
                Example on: service-address.html (offline)
            price: £[price 1315]
            buttonText: More information
            buttonLink: /page1315en.html
            productId: 475
        -
            title: Combine with our Registered Office Address and save £39.99
            description|markdown: |
                Example on: service-address.html (offline)
            price: £[price 1681]
            buttonText: View bundle
            buttonLink: /rosa-bundle.html
            productId: 1681

featuresBlueStrip:
    features:
        -
            icon: fa-check
            title: 'Component: featuresBlueStrip'
            description|markdown|allow_paragraph: |
                Example on: psc-online-register.html
        -
            icon: fa-check
            title: 'Component: featuresBlueStrip'
            description|markdown|allow_paragraph: |
                Example on: psc-online-register.html
        -
            icon: fa-check
            title: 'Component: featuresBlueStrip'
            description|markdown|allow_paragraph: |
                Example on: psc-online-register.html