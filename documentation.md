### About

- advertisement
- lead generations
- presenting offers and get paid for each lead we sent, 
- customer does not need to open an account to get paid for a lead 
- benefits: we don't need arrange with separated banks, accountants, relevant offers are based on officers data
- advertiser are bidding per lead, bigger advert on first place
- why? test, we don't need to convert a lead we are paid to just send a lead 
- number of different industries, currently we focus on banking    

### How does it work

- we send company details to API and get response with relevant offers
- we display adverts (banking options in our case)
- we send leads to BDG after incorporation (company number is required)
- lead response contains:
    - follow up: how we should deal with ("advertiserContactsCustomer", "advertiserCalls", "advertiserEmails", "advertiserProvidesURL", "customerCalls")
    - amount we get paid for sending a lead
    - status (received, ...)
   

### Technical details

- request to get token
- request to get banking options using requested token
- send a lead with company number and selected advert 
- store whole request and response as CashPlus does
- just one bank lead per company
- [PHASE_2]rejected status -> let customer know
- [Business Data Group - API documentation](http://bdg-api-documentation.s3-website.eu-west-2.amazonaws.com/#operations-tag-Leads)

### Implementation

- just for uk customers based on jurisdiction ("england-wales", "scotland", "wales", "northern-ireland")
- banking page needs to be displayed after officers because bank ads depend on register office and officers data. We can't reuse current offers page because of mandatory design guideline
- we need to submit **Token Request** (`\BusinessDataModule\Clients\ApiClient::token`) with following information (token is required for each request):
    - client_id
    - client_secret
- we need to submit **Advert Request** (`\BusinessDataModule\Clients\ApiClient::adverts`) with following company data:
    - type: advert
    - attributes
        - industrySectors: banking
        - regOfficeAddress
            - postcode
            - country: GBR
        - forwardingAddress
            - postcode
            - country: GBR
        - officers
            - address
                - postcode
                - country: GBR
- we need to display banking adverts based on the **Advert Response** (`\BusinessDataModule\Responses\AdvertsResponse`) using layout from "Guide to the Adverts"
- if they are no adverts just jump to the next page?
- customer selects an advert and we save advert response to database table
    - `table_bdg_leads`
        - business_services_lead.lead_id
        - advertiser_id
        - advertiser_name
        - rules (preferred contact, contact, directors)
        - termsAgreed
        - ...
- once we get to the "Additional Data" we need to merge required `informationSets` for the selected "Bank" and "Business services offers"
- we store data from "Additional Data" page 
- then we run daily cron job wich will submit leads to BDG API
    - for companies matching criteria (`\BusinessServicesModule\Repositories\LeadRepository::findLeadsToProcess`):
        - company number is not null
        - incorporation date is not null
    - we send **Lead Request** (`\BusinessDataModule\Clients\ApiClient::leads`) with data:
        - type: lead
        - attributes
            - externalRef (`table_bdg_leads.id`)
            - advertId
            - companyNumber
            - termsAgreed
            - contact
- save response
    - `table_bdg_leads`
        - ...
        - date_sent
        - request_body
        - response_code
        - response_body
        - amount
        - status
        - follow up
- what about rejections? let customer know?
- what about current cashback? cron jobs?
- what about reminders, sweepers that no bank option was chosen for a company?

### Questions

- rules/datasets: are they all same for banking?
- terms/accepted, only if advert requires it?
    - terms in a new window? should we display checkbox?
- more link (should we display it?)
- adverts w/o log (optional argument)
- request verify = false?
- error response (what?)
- adverts based on postcode -> possibility to not get an advert at all?
- what is postcode resource?
- third party referrer name (wholesale)?
- ?token valid for 30 min?
- zero price (just staging)? can we store it from send lead request or do we need to check lead status?
- what statuses we can get?
- who is a business owner?
- thank you screen/email confirming?
- do we need to check lead status?
- IP restriction?
- country restriction?
- advert request: type: advert? anything else?
- Lead request what is required?
- officersRequired : true (check)
- follow up: industrySector : "banking" (check response affiliate link)
- third party: is it wholesaler?
- just uk customers



