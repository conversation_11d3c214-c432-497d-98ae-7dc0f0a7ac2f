<?php

use Bootstrap\ApplicationLoader;

define('DOCUMENT_ROOT', dirname(__FILE__));

$config = require_once DOCUMENT_ROOT . '/project/bootstrap/default.php';
$config['config_path'] = DOCUMENT_ROOT . DIRECTORY_SEPARATOR . 'project/config/app/config.local.yml';

ini_set('max_execution_time', '-1');
ini_set('memory_limit', '-1');
set_time_limit(0);

$applicationLoader = new ApplicationLoader();
$container = $application = $applicationLoader->load($config);