{"name": "cms", "version": "1.0.0", "description": "cms js modules", "main": "index.js", "directories": {"test": "tests"}, "dependencies": {"@popperjs/core": "^2.11.7", "@tryghost/content-api": "^1.11.16", "@types/mocha": "^7.0.1", "@types/ramda": "^0.26.44", "awesome-typescript-loader": "^5.2.1", "axios": "^0.19.2", "bootstrap": "5.2.2", "clean-css": "^5.3.2", "core-js": "^3.32.1", "firebase": "^9.19.1", "firebaseui": "^6.1.0", "jquery": "^1.12.4", "moment": "^2.29.3", "pako": "^2.1.0", "portal-vue": "^2.1.7", "print-js": "^1.6.0", "ramda": "^0.26.1", "rivets": "^0.9.6", "rxjs": "^6.6.7", "spin.js": "^4.1.1", "ui": "git+ssh://**************/madesimplegroup/ui-v2.git", "vue-class-component": "^7.2.6", "vue-property-decorator": "^8.5.1", "vue-rx": "^6.2.0", "vue-rx-decorators": "0.0.8", "vuex": "^3.6.2", "whatwg-fetch": "^3.6.18"}, "devDependencies": {"@babel/core": "^7.23.2", "@babel/eslint-parser": "^7.22.15", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.2", "@babel/plugin-transform-flow-strip-types": "^7.21.0", "@babel/preset-env": "^7.22.15", "@babel/register": "^7.22.15", "@typescript-eslint/eslint-plugin": "^6.9.0", "@typescript-eslint/parser": "^6.9.0", "autoprefixer": "^9.8.8", "babel-loader": "^8.3.0", "css-loader": "^3.6.0", "eslint": "^8.52.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-flowtype": "^8.0", "file-loader": "^5.1.0", "flow-babel-webpack-plugin": "^1.1.1", "flow-bin": "^0.68.0", "flow-remove-types": "^1.2.3", "flow-typed": "^2.6.2", "jsdom": "^11.11.0", "jsdom-global": "^3.0.2", "mini-css-extract-plugin": "^0.9.0", "mocha": "^5.2.0", "mocha-testdata": "^1.2.0", "parallelshell": "^3.0.2", "postcss-loader": "^3.0.0", "sass": "^1.66.1", "sass-loader": "^8.0.2", "semistandard": "^14.2.3", "source-map-loader": "^0.2.4", "style-loader": "^1.3.0", "ts-loader": "^6.2.2", "typescript": "^3.0.0", "uglify-js": "^3.17.4", "url-loader": "^3.0.0", "vue": "^2.7.14", "vue-loader": "^15.10.2", "vue-styleguidist": "^4.58.0", "vue-template-compiler": "^2.7.14", "watch": "^1.0.2", "webpack": "^4.46.0", "webpack-cli": "^3.3.12", "webpack-livereload-plugin": "^2.3.0", "webpack-merge": "^4.2.2", "webpack-merge-and-include-globally": "^2.3.4"}, "babel": {"presets": [["@babel/preset-env", {"useBuiltIns": "usage", "corejs": {"version": 3}}]], "plugins": ["@babel/proposal-class-properties", "@babel/transform-flow-strip-types", "transform-flow-comments"]}, "semistandard": {"parser": "@babel/eslint-parser", "plugins": ["flowtype"], "globals": ["describe", "context", "before", "beforeEach", "after", "after<PERSON>ach", "it", "expect", "HTMLElement", "Event"]}, "scripts": {"lint": "semistandard 'client/js/**/*.js' 'tests/js/**/*.js'", "lint:fix": "semistandard --fix 'client/js/**/*.js' 'tests/js/**/*.js'", "lint:watch": "watch 'npm run lint' 'www/src' 'tests/js'", "test": "mocha --require jsdom-global/register --require @babel/register 'tests/js/**/*.js'", "test:watch": "watch 'npm run test' 'tests/js'", "compile": "webpack --config webpack.prod.js && rm -rf www/webtemp/*", "compile:clear": "webpack --config webpack.prod.js && rm -rf www/webtemp/*", "watch": "webpack --config webpack.dev.js -w", "compile:dev": "webpack --config webpack.dev.js", "compile:legacy": "webpack --config webpack.legacy.js", "build": "npm run lint && npm run test && npm run compile", "build:watch": "parallelshell 'npm run lint:watch' 'npm run test:watch' 'npm run watch'", "flow": "flow", "flow-typed": "flow-typed", "styleguide": "vue-styleguidist server", "styleguide:build": "vue-styleguidist build", "build:quick": "npm run lint:fix && npm run compile:dev"}, "externals": {"jquery": "j<PERSON><PERSON><PERSON>", "$": "j<PERSON><PERSON><PERSON>", "rivets": "rivets", "moment": "moment"}, "engines": {"node": ">=12.0", "npm": ">=6.0"}, "author": "<PERSON>", "homepage": "https://bitbucket.org/made_simple/cms#readme"}